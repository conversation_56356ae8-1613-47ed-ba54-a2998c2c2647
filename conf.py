import os
from functools import lru_cache
from logging.config import dictConfig

from jinja2.utils import import_string

ENVIRONMENTS = {
    # "production": "app.config.ProductionConfig",
    "local": "app.config.LocalSettings",
    "dev": "app.config.StagingSettings",
    "test": "app.config.TestingSettings",
}

settings = import_string(ENVIRONMENTS[os.getenv("FASTAPI_ENV", "local")])


@lru_cache()
def get_settings():
    """Use utility to get settings from global environment variable to have multiple environments"""

    configuration_settings = settings()

    # Configure log
    dictConfig(configuration_settings.DEFAULT_LOG)

    return configuration_settings
