import functools
import hashlib
import hmac
import os
import time
import urllib.parse

import google.auth.transport.requests
from flask import abort
from google.oauth2 import id_token


class UnauthorizedException(Exception):
    pass


class SecretHelper:
    @classmethod
    def compute_signature(cls, payload: str, secret: str, encoded=True):
        signature = hmac.new(
            secret.encode("utf-8"),
            msg=payload.encode("utf-8"),
            digestmod=hashlib.sha256,
        ).hexdigest()
        return signature.encode("utf-8") if encoded else signature

    @classmethod
    def verify(cls, api_key, signature: bytes):
        return hmac.compare_digest(
            api_key.encode("utf-8") if isinstance(api_key, str) else api_key, signature
        )


class Authorize:
    def __init__(self, **kwargs):
        self._extra_kwargs = kwargs

    def __call__(self, fn):
        if not self.perform_authorization(**self._extra_kwargs):
            raise UnauthorizedException("Access forbidden")

        @functools.wraps(fn)
        def decorated(*args, **kwargs):
            return fn(*args, **kwargs)

        return decorated

    def perform_authorization(self, *args, **kwargs):
        raise NotImplementedError("Not implemented yet!")


class SlackAuthorization(Authorize):
    """Authorization helper method for slack endpoints"""

    signing_secret = os.getenv("SLACK_SIGNING_SECRET")

    def perform_authorization(self, headers, body, **kwargs):
        timestamp = headers.get("X-Slack-Request-Timestamp")

        # Avoid reply attacks if timestamp is not within the last 5 minutes from now
        if int(time.time() - int(timestamp)) > 60 * 5:
            return False

        sig_basestring = f"v0:{timestamp}:{body}"

        signed_signature = "v0=" + SecretHelper.compute_signature(
            sig_basestring, self.signing_secret, encoded=False
        )

        return signed_signature == headers.get("X-Slack-Signature")


class HubspotAuthorization(Authorize):
    """Authorization helper method for hubspot endpoints"""

    client_secret = os.getenv("HUBSPOT_CLIENT_SECRET")

    def perform_authorization(self, request, **kwargs):
        from hubspot.exceptions import (InvalidSignatureTimestampError,
                                        InvalidSignatureVersionError)
        from hubspot.utils.signature import Signature

        try:
            Signature.is_valid(
                signature=request.headers["X-HubSpot-Signature"],
                signature_version=request.headers["X-HubSpot-Signature-Version"],
                http_uri=urllib.parse.unquote(request.url),
                http_method=request.method,
                request_body=request.data.decode("utf-8"),
                client_secret=self.client_secret,
            )
        except (InvalidSignatureVersionError, InvalidSignatureTimestampError):
            return False

        return True


class StripeAuthorization(Authorize):
    event_secret = os.getenv("STRIPE_EVENT_SECRET", "")

    def perform_authorization(self, request, **kwargs):
        import stripe as stripe_sdk

        try:
            payload = request.data
            sig_header = request.headers["STRIPE_SIGNATURE"]

            if hasattr(payload, "decode"):
                payload = payload.decode("utf-8")

            stripe_sdk.WebhookSignature.verify_header(
                payload,
                sig_header,
                self.event_secret,
                stripe_sdk.Webhook.DEFAULT_TOLERANCE,
            )

        except (ValueError, stripe_sdk.error.SignatureVerificationError, KeyError):
            return False

        return True


class ChargebeeAuthorization(Authorize):
    integrity_key = os.getenv("CHARGEBEE_HOOK_INTEGRITY_KEY", "")

    def perform_authorization(self, request, **kwargs):
        request_args = request.args
        return (
            "hook_key" in request_args
            and request_args.get("hook_key") == self.integrity_key
        )


class InternalAuthorization(Authorize):
    """Authorization helper method for internal communications from Slack"""

    def verify_token(self, token: str, audience: [str, None]) -> dict:
        """Verify token signature and return the token payload"""
        request = google.auth.transport.requests.Request()
        payload = id_token.verify_token(token, request=request, audience=audience)
        return payload

    def perform_authorization(self, request, **kwargs):
        try:
            authorization_header = (
                request.headers.get("Authorization", "").strip().strip(",")
            )
            if not authorization_header:
                return False

            parts = authorization_header.split()

            # Check that we have two parts: Bearer + JWT
            if len(parts) != 2:
                return False

            encoded_token = parts[1]
            self.verify_token(encoded_token, None)

            # Check internal command
            return "slack" == request.headers.get("X-Origin-Command")
        except Exception:
            return False


def protected(modes):
    def auth(function):
        for protection_mode in modes:
            try:

                @protection_mode
                def __inner__(*args, **kwargs):
                    # Everything is ok, call the function
                    return function(*args, **kwargs)

                return __inner__
            except UnauthorizedException:
                pass

        # In this case, we can abort the request safely
        abort(403)

    return auth
