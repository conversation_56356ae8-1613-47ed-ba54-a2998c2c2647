import datetime
import decimal
import json
import logging
import os
import uuid

import firebase_admin
from firebase_admin import credentials, firestore
from google.api_core.exceptions import NotFound
from google.cloud import pubsub_v1
from google.cloud.pubsub_v1.publisher.exceptions import MessageTooLargeError
from tenacity import retry, stop_after_attempt, wait_random

logger = logging.getLogger(__name__)

# Environmental variables to set the config
PROJECT_ID = os.getenv("PROJECT_ID")

publisher = pubsub_v1.PublisherClient()


def is_aware(value):
    """
    Determine if a given datetime.datetime is aware.

    The concept is defined in Python's docs:
    https://docs.python.org/library/datetime.html#datetime.tzinfo

    Assuming value.tzinfo is either None or a proper datetime.tzinfo,
    value.utcoffset() implements the appropriate logic.
    """
    return value.utcoffset() is not None


def _get_duration_components(duration):
    days = duration.days
    seconds = duration.seconds
    microseconds = duration.microseconds

    minutes = seconds // 60
    seconds = seconds % 60

    hours = minutes // 60
    minutes = minutes % 60

    return days, hours, minutes, seconds, microseconds


def duration_iso_string(duration):
    """Extract duration from ISO 8601 string date format"""
    if duration < datetime.timedelta(0):
        sign = "-"
        duration *= -1
    else:
        sign = ""

    days, hours, minutes, seconds, microseconds = _get_duration_components(duration)
    ms = ".{:06d}".format(microseconds) if microseconds else ""
    return "{}P{}DT{:02d}H{:02d}M{:02d}{}S".format(
        sign, days, hours, minutes, seconds, ms
    )


class JSONEncoder(json.JSONEncoder):
    """
    JSONEncoder subclass that knows how to encode date/time, decimal types, and
    UUIDs.
    """

    def default(self, o):
        # See "Date Time String Format" in the ECMA-262 specification.
        if isinstance(o, datetime.datetime):
            r = o.isoformat()
            if o.microsecond:
                r = r[:23] + r[26:]
            if r.endswith("+00:00"):
                r = r[:-6] + "Z"
            return r
        elif isinstance(o, datetime.date):
            return o.isoformat()
        elif isinstance(o, datetime.time):
            if is_aware(o):
                raise ValueError("JSON can't represent timezone-aware times.")
            r = o.isoformat()
            if o.microsecond:
                r = r[:12]
            return r
        elif isinstance(o, datetime.timedelta):
            return duration_iso_string(o)
        elif isinstance(o, (decimal.Decimal, uuid.UUID)):
            return str(o)
        else:
            return super().default(o)


def make_notification_message(topic, *, message_content, **attributes):
    """Helper method to create a pubsub message in PULL subscription topic"""

    topic_path = publisher.topic_path(PROJECT_ID, topic)

    try:
        _ = publisher.publish(
            topic_path,
            data=json.dumps(message_content, cls=JSONEncoder).encode("utf-8"),
            **attributes,
        )
    except NotFound:
        logger.error(f"Pub/sub topic {topic} not found on project")
    except TypeError as e:
        logger.error(f"Error publishing message `{str(e)}`")
    except MessageTooLargeError as e:
        logger.error(f"Message size is too large to be sent through pub sub `{str(e)}`")


# @see https://www.timvink.nl/google-cloud-functions/
# @see https://stackoverflow.com/questions/52129628/python-google-cloud-function-connection-reset-by-peer
@retry(stop=stop_after_attempt(3), wait=wait_random(min=1, max=4), reraise=True)
def get_cloud_service():
    # Use the application default credentials
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(
        cred,
        {
            "projectId": PROJECT_ID,
        },
    )
    return firestore.client()
