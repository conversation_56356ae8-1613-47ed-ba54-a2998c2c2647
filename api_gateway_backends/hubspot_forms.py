"""
Backend to listen public front-end requests in order to send data to Hubspot
"""
import json
import logging
import os
import traceback
from datetime import datetime

import firebase_admin
import hubspot
import pytz
import requests
from firebase_admin import credentials, firestore
from hubspot.crm.contacts import ApiException, SimplePublicObjectInput
from marshmallow import Schema, fields
from requests import RequestException
from tenacity import retry, stop_after_attempt, wait_random

from i18n import gettext as _

HUBSPOT_API_KEY = os.getenv("HUBSPOT_API_KEY")
hubspot_client = hubspot.Client.create(access_token=HUBSPOT_API_KEY)
HUBSPOT_URL = "https://api.hubapi.com/{object}/v1/{object_path}"
HUBSPOT_PIPELINE_SPAIN = os.getenv("HUBSPOT_PIPELINE_SPAIN")
HUBSPOT_PIPELINE_PORTUGAL = os.getenv("HUBSPOT_PIPELINE_PORTUGAL")
HUBSPOT_PIPELINE_FRANCE = os.getenv("HUBSPOT_PIPELINE_FRANCE")

HUBSPOT_PIPELINE_MAP = {
    HUBSPOT_PIPELINE_SPAIN: os.getenv("HUBSPOT_STAGE_SPAIN"),
    HUBSPOT_PIPELINE_FRANCE: os.getenv("HUBSPOT_STAGE_FRANCE"),
    HUBSPOT_PIPELINE_PORTUGAL: os.getenv("HUBSPOT_STAGE_PORTUGAL"),
}

OWNER_ID = os.getenv("OWNER_ID")

# Temporal blacklisting emails (email, HTTP response code)
BLACKLISTING_EMAILS = {"<EMAIL>": True}

# Promotional codes database
BRICODEPOT_PROMOTIONAL_CODE_COLLECTION = os.getenv(
    "BRICODEPOT_PROMOTIONAL_CODE_COLLECTION"
)
PROJECT_ID = os.getenv("PROJECT_ID")

logger = logging.getLogger(__name__)


# @see https://www.timvink.nl/google-cloud-functions/
# @see https://stackoverflow.com/questions/52129628/python-google-cloud-function-connection-reset-by-peer
@retry(stop=stop_after_attempt(3), wait=wait_random(min=1, max=4), reraise=True)
def get_cloud_service():
    # Use the application default credentials
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(
        cred,
        {
            "projectId": PROJECT_ID,
        },
    )
    return firestore.client()


try:
    # Initialize firestore
    db = get_cloud_service()
except Exception as err:
    logger.error(
        f"Error initializing firestore client :: {err} - {traceback.print_exc(chain=False)}"
    )
    db = None


def parse_conflict_call(exception_traceback):
    """Parse hubspot error message on conflict to get the existing id"""
    reason_error = (
        json.loads(exception_traceback.body).get("message", "") or ""
    ).split("ID:")[-1]
    try:
        return int(reason_error.strip())
    except ValueError:
        pass

    return None


def email_is_blacklisted(email_address):
    """Check if email is blacklist and return the choosed HTTP code"""

    return BLACKLISTING_EMAILS.get(email_address)


@firestore.transactional
def validate_promotion_code_in_transaction(transaction, db_ref, request, email, phone):
    snapshot = db_ref.get(transaction=transaction)
    if not snapshot.exists:
        return _("Invalid code", request), 404

    if snapshot.exists and snapshot.get("used_at"):
        return _("Code already used", request), 400

    transaction.update(
        db_ref,
        {
            "used_at": datetime.utcnow().replace(tzinfo=pytz.utc),
            "email": email,
            "phone": phone,
        },
    )

    return _("OK", request), 200


class MessageSchema(Schema):
    """Message schema to validate the input"""

    first_name = fields.Str(required=False, allow_none=True)
    last_name = fields.Str(required=False, allow_none=True)
    email = fields.Str(required=False, allow_none=True)
    comment = fields.Str(required=False, allow_none=True)
    phone = fields.Str(required=False, allow_none=True)
    allow_commercial = fields.Boolean(required=False, default=False)
    space = fields.Str(required=False, allow_none=True)
    gclid = fields.Str(required=False, default=False)
    base = fields.Str(required=False, default=False)
    pipeline = fields.Str(required=True, allow_none=False)
    code = fields.Str(required=False, allow_none=True)

    def validate_promotion_code(self, request, data):
        """Validate promotional code if any and return response to frontend to handle it"""
        parsed_data = self.load(data)

        # Get the promotional code
        code = parsed_data.get("code")

        # Get additional metadata
        email = parsed_data.get("email")
        phone = parsed_data.get("phone")

        # Guard condition to go ahead with the flow if code is not present
        if not code:
            return None, 200

        try:
            transaction = db.transaction()
            db_ref = db.collection(BRICODEPOT_PROMOTIONAL_CODE_COLLECTION).document(
                code
            )

            return validate_promotion_code_in_transaction(
                transaction, db_ref, request, email, phone
            )
        except Exception as error:
            logger.warning(
                f"Skipping promotion code due to unexpected error: {error} - {code} - {traceback.print_exc(chain=False)}"
            )

        return None, 200

    def _update_contact_in_hubspot(self, contact_id, code):
        """Create the contact in hubspot"""

        contact_properties = {
            "promo_bricodepot": f";{code}",
        }

        simple_public_object_input = SimplePublicObjectInput(
            properties=contact_properties
        )
        try:
            api_response = hubspot_client.crm.contacts.basic_api.update(
                contact_id=contact_id,
                simple_public_object_input=simple_public_object_input,
            )
            return api_response.id
        except ApiException as error:
            logger.error(
                f"Error updating the contact with promotional code in hubspot: {error}"
            )

        return None

    def _create_contact_in_hubspot(
        self, first_name, last_name, email, phone, allow_commercial, code
    ):
        """Create the contact in hubspot"""

        contact_properties = {
            "email": email,
            "firstname": first_name,
            "lastname": last_name,
            "phone": phone,
            "commercial_auth": allow_commercial,
            "promo_bricodepot": f";{code}",
        }

        simple_public_object_input = SimplePublicObjectInput(
            properties=contact_properties
        )
        try:
            api_response = hubspot_client.crm.contacts.basic_api.create(
                simple_public_object_input=simple_public_object_input
            )
            return api_response.id
        except ApiException as error:
            if error.status == 409:
                contact_id = parse_conflict_call(error)

                # Update it with the code if needed
                if code:
                    self._update_contact_in_hubspot(contact_id, code)
                return contact_id

            logger.error(f"Error creating a contact in hubspot: {error}")

        return None

    def _create_deal_in_hubspot(
        self,
        contact_id,
        deal_name,
        space,
        gclid,
        base,
        pipeline,
        pipeline_stage,
    ):
        """Create a deal in hubspot"""

        try:
            response = requests.request(
                "POST",
                HUBSPOT_URL.format(object="deals", object_path="deal"),
                data=json.dumps(
                    {
                        "associations": {
                            "associatedVids": [contact_id],
                        },
                        "properties": [
                            {"value": pipeline, "name": "pipeline"},
                            {"value": pipeline_stage, "name": "dealstage"},
                            {"value": deal_name, "name": "dealname"},
                            {"value": gclid, "name": "gclid"},
                            {"value": base, "name": "base"},
                            {"value": space, "name": "hired_space"},
                            {"value": True, "name": "form_submitted"},
                        ],
                    }
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {HUBSPOT_API_KEY}",
                },
            )

            response.raise_for_status()
            return response.json().get("dealId")
        except RequestException as error:
            logger.error(f"Error creating a deal in hubspot: {error}")

        return None

    def _create_note_in_deal_in_hubspot(self, deal_id, comment):
        """Create a note in deal"""

        try:
            response = requests.request(
                "POST",
                HUBSPOT_URL.format(object="engagements", object_path="engagements"),
                data=json.dumps(
                    {
                        "engagement": {
                            "active": True,
                            "ownerId": OWNER_ID,  # Not relevant to create notes in hubspot
                            "type": "NOTE",
                        },
                        "associations": {
                            "contactIds": [],
                            "companyIds": [],
                            "dealIds": [deal_id],
                            "ownerIds": [],
                        },
                        "metadata": {
                            "body": f"<b>[BOX2BOT &#129302;]</b> <br> <u>Nuevo formulario recibido:</u> <br><ul><li>Comentario: {comment}</li></ul>"
                        },
                    }
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {HUBSPOT_API_KEY}",
                },
            )

            response.raise_for_status()
        except RequestException as error:
            logger.error(f"Error creating a note in hubspot: {error}")

        return None

    def create_in_hubspot(self, data):
        parsed_data = self.load(data)

        first_name = parsed_data.get("first_name")
        last_name = parsed_data.get("last_name")
        email = parsed_data.get("email")
        code = parsed_data.get("code")

        # Get the pipeline
        pipeline = parsed_data.get("pipeline")
        pipeline_stage = HUBSPOT_PIPELINE_MAP.get(pipeline, None) or None

        # Check if user is blacklisted
        if email_is_blacklisted(email):
            logger.warning(
                f"Email address {email} has been blacklisted. To avoid warning to user, the response code will be 200"
            )
            return True

        if pipeline_stage is None:
            return None

        # First, create the contact in hubspot
        hubspot_contact = self._create_contact_in_hubspot(
            first_name=first_name,
            last_name=last_name,
            email=email,
            phone=parsed_data.get("phone"),
            allow_commercial=parsed_data.get("allow_commercial"),
            code=code,
        )

        if hubspot_contact is None:
            return None

        # Later, create the deal in hubspot
        hubspot_deal = self._create_deal_in_hubspot(
            contact_id=hubspot_contact,
            deal_name=f"{first_name} {last_name}",
            space=parsed_data.get("space"),
            gclid=parsed_data.get("gclid"),
            base=parsed_data.get("base"),
            pipeline=pipeline,
            pipeline_stage=pipeline_stage,
        )

        if hubspot_deal is None:
            return None

        # Finally, create a note
        self._create_note_in_deal_in_hubspot(
            deal_id=hubspot_deal, comment=parsed_data.get("comment")
        )

        return True


def entrypoint(request):
    """Main entry point for hubspot message"""

    # Set CORS headers for the preflight request
    if request.method == "OPTIONS":
        # Allows GET requests from any origin with the Content-Type
        # header and caches preflight response for 3600s
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST",
            "Access-Control-Allow-Headers": "Content-Type, ",
            "Access-Control-Max-Age": "3600",
        }

        return "", 204, headers

    # Set CORS headers for the main request
    headers = {"Access-Control-Allow-Origin": "*"}

    # Only POST method are allowed for this endpoint
    if request.method not in ["POST"]:
        logger.info(f"Method not allowed {request.method}")
        return (
            {"msg": _("Invalid request method", request)},
            400,
            headers,
        )

    try:
        data = request.get_json(silent=True)

        if data is None:
            return {"msg": _("Invalid data", request)}, 400, headers

        # Instantiate the main class to make calls against hubspot
        message_schema = MessageSchema()

        # Validate promotion code if any
        response, status_code = message_schema.validate_promotion_code(request, data)
        if status_code >= 400:
            return {"msg": response}, status_code, headers

        if message_schema.create_in_hubspot(data):
            return {"msg": _("Form created successfully", request)}, 200, headers

        return (
            {"msg": _("Something goes wrong. Please, try again later", request)},
            400,
            headers,
        )
    except Exception as err:
        logger.error(f"Unknown exception :: {err} - {traceback.print_exc(chain=False)}")

    return (
        {"msg": _("Unknown error detected. Please, try again later", request)},
        500,
        headers,
    )
