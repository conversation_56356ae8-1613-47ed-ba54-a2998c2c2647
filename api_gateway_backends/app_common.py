"""
Main entry point to listen notifications from moreapp service and transforming them into pubsub messages to
manage the throughput against our backend system.

API-KEY are validated on the API Gateway side. Moreapp does not send any signature in headers or as arguments, so we will
rely on the gateway.
"""

import logging

import marshmallow
from marshmallow import Schema, fields

logger = logging.getLogger(__name__)


class LocationSchema(Schema):
    latitude = fields.Float(required=False, default=0.0)
    longitude = fields.Float(required=False, default=0.0)


class MetaSchema(Schema):
    id = fields.String(required=False)
    device = fields.String(required=False)
    model = fields.String(required=False)
    os = fields.String(required=False)
    version = fields.String(required=False)
    metadata = fields.Dict(required=False)
    location = fields.Nested(LocationSchema, required=False, allow_none=True)


class BaseSchema(Schema):
    """
    Important note here:
    Do not set an instance of RawDataSchema (i.e. RawDataSchema()) in fields.Nested() or the context will be reused between calls, even if you reinstantiate the initial call (i.e. BodySchema().load(data))
    """

    meta = fields.Nested(MetaSchema, allow_none=True)


class SchemaPreloadMixin:
    @marshmallow.pre_load
    def save_type(self, data, **kwargs):
        """Inject current service type into context to get access in the nested data"""
        self.context["type"] = data["type"]
        return data
