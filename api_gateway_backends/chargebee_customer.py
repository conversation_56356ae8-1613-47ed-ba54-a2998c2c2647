"""Backend to manage the payment link flow"""

import logging
import traceback

import chargebee
import functions_framework
from chargebee_common import (ALLOWED_LOCALES, CURRENCIES,
                              FIRESTORE_PAYMENT_LINK_COLLECTION, LOCALES,
                              build_response, get_chargebee_metadata,
                              get_error_message, update_payment)
from common import get_cloud_service
from flask import jsonify

from i18n import gettext as _

logger = logging.getLogger(__name__)

try:
    # Initialize firestore
    db = get_cloud_service()
except Exception as err:
    logger.error(
        f"Error initializing firestore client :: {err} - {traceback.print_exc(chain=False)}"
    )
    db = None


def set_vat_number(is_customer, vat_number):
    """Set vat number in the right field on customers"""

    if is_customer:
        return {"cf_nif": vat_number, "vat_number": None}
    return {"vat_number": vat_number, "cf_nif": None}


def create_or_update_customer(
    request,
    *,
    customer_id,
    first_name,
    last_name,
    address,
    city,
    state,
    zip,
    country,
    email,
    vat_number,
    is_customer,
    phone,
    locale,
    environment,
    currency,
):
    billing_data = {
        "first_name": first_name,
        "last_name": last_name,
        "line1": address,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "phone": phone,
    }

    # Build the customer payload for vat numbers
    customer_payload = {
        "billing_address": billing_data,
        **set_vat_number(is_customer, vat_number),
    }

    try:
        try:
            # Update billing data info
            chargebee.Customer.update_billing_info(
                customer_id,
                customer_payload,
                env=environment,
            )

            # Reset cf_nif field
            chargebee.Customer.update(customer_id, customer_payload, env=environment)
        except chargebee.api_error.APIError:
            payload = {
                "id": customer_id,
                "first_name": first_name,
                "last_name": last_name,
                "email": email,
                "locale": LOCALES.get(locale) or "en-US",
                "preferred_currency_code": currency,
                "allow_direct_debit": True,
                **customer_payload,
            }

            chargebee.Customer.create(
                payload,
                env=environment,
            )

        status_code = 200
        response = build_response(
            success={
                "msg": _("Operation made successfully", request),
            }
        )
    except chargebee.api_error.APIError as err:
        logger.error(
            f"Error creating or updating the customer in chargebee: {err.json_obj}"
        )

        error_message = get_error_message(err.api_error_code, request)
        status_code = 400
        response = build_response(
            error={
                "reason": error_message,
                "extra": {
                    "param": err.json_obj.get("param"),
                },
            }
        )

    return response, status_code


@functions_framework.http
def entrypoint(request):
    """Main entry point for payment intent"""

    # Set CORS headers for the preflight request
    if request.method == "OPTIONS":
        # Allows GET requests from any origin with the Content-Type
        # header and caches preflight response for an 3600s
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST",
            "Access-Control-Allow-Headers": "Content-Type, X-Chargebee-Payment-Link-Key, ",
            "Access-Control-Max-Age": "3600",
        }

        return "", 204, headers

    # Set CORS headers for the main request
    headers = {"Access-Control-Allow-Origin": "*"}

    # Only POST method are allowed for this endpoint
    if request.method not in ["POST"]:
        logger.info(f"Method not allowed {request.method}")
        return (
            build_response(
                error={
                    "reason": _(
                        "Method {method} not allowed",
                        request,
                    ).format(method=request.method),
                    "extra": None,
                }
            ),
            400,
            headers,
        )

    # Validate input arguments
    data = request.get_json(silent=True)
    first_name = data.get("first_name")
    last_name = data.get("last_name")
    address = data.get("address")
    city = data.get("city")
    state = data.get("state")
    zip_code = data.get("zip_code")
    country = data.get("country")
    vat_number = data.get("vat_number")
    payment_link_key = data.get("key")
    phone = data.get("phone")
    is_customer = data.get("is_customer")

    if not payment_link_key:
        logger.error("Invalid payment link key")
        return (
            build_response(
                error={"reason": _("Invalid payment link key", request), "extra": None}
            ),
            400,
            headers,
        )

    try:
        # Run chargebee payment inside transactional block
        transaction = db.transaction()
        db_ref = db.collection(FIRESTORE_PAYMENT_LINK_COLLECTION).document(
            payment_link_key
        )

        try:
            # Read data from firestore
            data = db_ref.get()

            if not data.exists:
                logger.error("Error finding the link in database")
                return (
                    jsonify(
                        build_response(
                            error={
                                "reason": _("Can't find the link", request),
                                "extra": None,
                            }
                        )
                    ),
                    400,
                    headers,
                )

            # Transform into dict
            data = data.to_dict()
            locale = (data.get("site") or "").upper()
            currency = CURRENCIES.get(locale)

            (
                chargebee_environment,
                adyen_gateway_id,
            ) = get_chargebee_metadata(site=locale)

            # Set default locale as english if site is not allowed
            if locale not in ALLOWED_LOCALES:
                locale = "EN"

            # Mandatory step: set customer data
            response, status_code = create_or_update_customer(
                request,
                customer_id=data.get("contact_id"),
                first_name=first_name,
                last_name=last_name,
                address=address,
                city=city,
                state=state,
                zip=zip_code,
                country=country,
                email=data.get("email"),
                phone=phone,
                vat_number=vat_number,
                is_customer=is_customer,
                locale=locale,
                environment=chargebee_environment,
                currency=currency,
            )

            if status_code == 400:
                # Update transaction
                update_payment(transaction, db_ref, status="pre_failed")

            return jsonify(response), status_code, headers
        except Exception as err:
            # Update transaction
            update_payment(transaction, db_ref, status="pre_failed")
            raise err
    except Exception as err:
        logger.error(f"Unknown exception :: {err} - {traceback.print_exc(chain=False)}")

    return (
        jsonify(
            build_response(
                error={
                    "reason": _(
                        "Unknown error detected. Please, try again later", request
                    ),
                    "extra": None,
                }
            )
        ),
        500,
        headers,
    )
