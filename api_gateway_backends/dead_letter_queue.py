"""
Cloud function to subscribe in order to listen incoming messages from DLQ and send a slack notification to diagnose and check the error
"""
import base64
import logging
import os

import requests

SLACK_URL = os.getenv("SLACK_URL")
logger = logging.getLogger(__name__)


def send_slack_message(data, context, attributes):
    """Build and send slack message"""
    action_id = attributes.get("endpoint", None)

    slack_message = {
        "blocks": [
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": f"*Message id*: {context.event_id}"},
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Publish time*: {context.timestamp}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Event type*: {context.event_type}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Retries*: {attributes.get('CloudPubSubDeadLetterSourceDeliveryCount') or '-'}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Subscription*: {attributes.get('CloudPubSubDeadLetterSourceSubscription') or '-'}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Data*: {data}",
                },
            },
        ]
    }

    if data is not None and action_id:
        slack_message["blocks"].append(
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "style": "primary",
                        "text": {
                            "type": "plain_text",
                            "text": "Send back",
                        },
                        "action_id": action_id,
                        "value": data,
                    },
                ],
            }
        )

    # Make the request to slack
    response = requests.request(
        method="POST",
        url=SLACK_URL,
        json=slack_message,
        headers={"Content-Type": "application/json"},
    )
    response.raise_for_status()

    return response


def entrypoint(event, context):
    """Background Cloud Function to be triggered by Pub/Sub.
    Args:
         event (dict):  The dictionary with data specific to this type of
                        event. The `@type` field maps to
                         `type.googleapis.com/google.pubsub.v1.PubsubMessage`.
                        The `data` field maps to the PubsubMessage data
                        in a base64-encoded string. The `attributes` field maps
                        to the PubsubMessage attributes if any is present.
         context (google.cloud.functions.Context): Metadata of triggering event
                        including `event_id` which maps to the PubsubMessage
                        messageId, `timestamp` which maps to the PubsubMessage
                        publishTime, `event_type` which maps to
                        `google.pubsub.topic.publish`, and `resource` which is
                        a dictionary that describes the service API endpoint
                        pubsub.googleapis.com, the triggering topic's name, and
                        the triggering event type
                        `type.googleapis.com/google.pubsub.v1.PubsubMessage`.
    Returns:
        None. The output is written to Cloud Logging.
    """

    data = None
    if "data" in event:
        data = base64.b64decode(event["data"]).decode("utf-8")

    attributes = event.get("attributes") or {}

    try:
        send_slack_message(data, context, attributes)
    except requests.HTTPError as err:
        logger.error(f"Error sending DLQ message to slack: {err}")

    return "OK", 200
