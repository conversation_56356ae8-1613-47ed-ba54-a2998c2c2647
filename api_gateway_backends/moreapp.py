"""
Main entry point to listen notifications from moreapp service and transforming them into pubsub messages to
manage the throughput against our backend system.

API-KEY are validated on the API Gateway side. Moreapp does not send any signature in headers or as arguments, so we will
rely on the gateway.
"""
import os

from common import make_notification_message

MOREAPP_TOPIC = os.getenv("MOREAPP_TOPIC")


def entrypoint(request):
    from flask import abort

    # Only POST method are allowed for this endpoint
    if request.method != "POST":
        abort(403)

    data = request.get_json(silent=True)

    # Wrap the real call with this inner function to add auth decorator and parse the request
    make_notification_message(
        topic=MOREAPP_TOPIC, message_content=data, endpoint="moreapp"
    )

    return "OK", 200
