"""Backend to manage the payment link flow"""

import logging
import traceback

from chargebee_common import (FIRESTORE_PAYMENT_LINK_COLLECTION,
                              build_response, update_payment)
from common import get_cloud_service
from flask import jsonify

from i18n import gettext as _

logger = logging.getLogger(__name__)

try:
    # Initialize firestore
    db = get_cloud_service()
except Exception as err:
    logger.error(
        f"Error initializing firestore client :: {err} - {traceback.print_exc(chain=False)}"
    )
    db = None


def confirm_failed_payment(transaction, db_ref):
    """Confirm initially failed payment (i.e. 3ds) in order to release the lock and allow restart the payment flow"""
    update_payment(transaction, db_ref, status="failed")
    return build_response(success={"status": "Confirmed"}), 200


def entrypoint(request):
    """Main entry point for releasing transaction"""

    # Set CORS headers for the preflight request
    if request.method == "OPTIONS":
        # Allows GET requests from any origin with the Content-Type
        # header and caches preflight response for an 3600s
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "PATCH",
            "Access-Control-Allow-Headers": "Content-Type, X-Chargebee-Payment-Link-Key, ",
            "Access-Control-Max-Age": "3600",
        }

        return "", 204, headers

    # Set CORS headers for the main request
    headers = {"Access-Control-Allow-Origin": "*"}

    # Only POST method are allowed for this endpoint
    if request.method not in ["PATCH"]:
        logger.info(f"Method not allowed {request.method}")
        return (
            build_response(error={"msg": f"Method {request.method} not allowed"}),
            400,
            headers,
        )

    # Validate input arguments
    data = request.get_json(silent=True)
    payment_link_key = data.get("key")

    if not payment_link_key:
        return (
            build_response(
                error={"reason": _("Invalid payment link key", request), "extra": None}
            ),
            400,
            headers,
        )

    try:
        # Run chargebee payment inside transactional block
        transaction = db.transaction()
        db_ref = db.collection(FIRESTORE_PAYMENT_LINK_COLLECTION).document(
            payment_link_key
        )

        response, status_code = confirm_failed_payment(
            transaction=transaction,
            db_ref=db_ref,
        )

        return jsonify(response), status_code, headers
    except Exception as err:
        logger.error(f"Unknown exception :: {err} - {traceback.print_exc(chain=False)}")

    return (
        jsonify(
            build_response(
                error={
                    "reason": _(
                        "Unknown error detected. Please, try again later", request
                    ),
                    "extra": None,
                }
            )
        ),
        500,
        headers,
    )
