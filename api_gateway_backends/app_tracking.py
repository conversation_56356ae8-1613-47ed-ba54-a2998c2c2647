"""
Main entry point to listen notifications from moreapp service and transforming them into pubsub messages to
manage the throughput against our backend system.

API-KEY are validated on the API Gateway side. Moreapp does not send any signature in headers or as arguments, so we will
rely on the gateway.
"""

import logging
import os
import traceback

import functions_framework
import marshmallow.validate
from app_common import BaseSchema, SchemaPreloadMixin
from common import make_notification_message
from marshmallow import Schema, fields
from marshmallow_oneofschema import OneOfSchema

RIDERS_TOPIC = os.getenv("RIDERS_TOPIC")

logger = logging.getLogger(__name__)


class PlacementSchema(Schema):
    id = fields.String(required=True, allow_none=False)
    location = fields.String(required=False, allow_none=True)
    pallet = fields.Integer(required=False, allow_none=True)
    floor = fields.String(required=False, allow_none=True)
    photos = fields.List(fields.String(), required=False, allow_none=True)


class TrackingSchema(Schema):
    placements = fields.List(
        fields.Nested(PlacementSchema()), required=False, allow_none=True
    )


class MultiSchema(OneOfSchema):
    type_schemas = {
        "tracking": TrackingSchema,
    }

    def get_data_type(self, data):
        return self.context.get(self.type_field)


class RawDataSchema(BaseSchema, SchemaPreloadMixin):
    """
    Important note here:
    Do not set an instance of RawDataSchema (i.e. RawDataSchema()) in fields.Nested() or the context will be reused between calls, even if you reinstantiate the initial call (i.e. BodySchema().load(data))
    """

    id = fields.UUID(required=True, allow_none=True)
    timestamp = fields.DateTime(required=False, format="timestamp_ms")
    rider_id = fields.UUID(required=True)
    contract_id = fields.String(required=True)
    warehouse_id = fields.UUID(required=True)
    team_id = fields.UUID(required=True, allow_none=True)
    type = fields.String(
        required=True,
        validate=marshmallow.validate.OneOf(
            choices=[
                "tracking",
            ]
        ),
    )
    data = fields.Nested(MultiSchema)


class BodySchema(Schema):
    """
    Important note here:
    Do not set an instance of RawDataSchema (i.e. RawDataSchema()) in fields.Nested() or the context will be reused between calls, even if you reinstantiate the initial call (i.e. BodySchema().load(data))
    """

    id = fields.UUID(required=True)
    service_id = fields.String(required=True, allow_none=True)
    raw_data = fields.Nested(RawDataSchema)


@functions_framework.http
def entrypoint(request):
    from flask import abort

    # Only POST method are allowed for this endpoint
    if request.method != "POST":
        abort(403)

    data = request.get_json(silent=True)
    logger.warning(f"DATA: {data}")

    # Validate the data before passing it to the pubsub topic
    try:
        BodySchema().load(data)

        # Wrap the real call with this inner function to add auth decorator and parse the request
        make_notification_message(
            topic=RIDERS_TOPIC, message_content=data, endpoint="riders"
        )

        return "OK", 200
    except marshmallow.ValidationError as err:
        logger.error(f"Validation error: {traceback.print_exc(chain=False)}")
        return err.messages, 400
    except Exception:
        logger.error(f"Unknown error error: {traceback.print_exc(chain=False)}")
        return "Unknown error", 400
