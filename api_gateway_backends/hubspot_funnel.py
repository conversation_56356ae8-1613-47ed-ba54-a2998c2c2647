"""
Backend to listen public front-end requests in order to send data to Hubspot
"""
import json
import logging
import os
import traceback

import functions_framework
import hubspot
import requests
from google.cloud import pubsub_v1
from hubspot.crm.deals import ApiException
from marshmallow import Schema, fields
from requests import RequestException

from i18n import gettext as _

HUBSPOT_API_KEY = os.getenv("HUBSPOT_API_KEY")
hubspot_client = hubspot.Client.create(access_token=HUBSPOT_API_KEY)
pubsub_client = pubsub_v1.PublisherClient()
HUBSPOT_URL = "https://api.hubapi.com/{object}/v1/{object_path}"
HUBSPOT_DEAL_URL = "https://app-eu1.hubspot.com/contacts/25093590/deal/{deal_id}"
PROJECT_ID = "box2box-cloud"
TOPIC_ID = "mailjet-emails"

RECIPIENTS_EMAILS = {
    "ES": [{"Email": "<EMAIL>"}],
    "PT": [{"Email": "<EMAIL>"}],
    "FR": [{"Email": "<EMAIL>"}],
}

HUBSPOT_PIPELINE_SPAIN = os.getenv("HUBSPOT_PIPELINE_SPAIN")
HUBSPOT_PIPELINE_PORTUGAL = os.getenv("HUBSPOT_PIPELINE_PORTUGAL")
HUBSPOT_PIPELINE_FRANCE = os.getenv("HUBSPOT_PIPELINE_FRANCE")

HUBSPOT_PIPELINE_MAP = {
    HUBSPOT_PIPELINE_SPAIN: "ES",
    HUBSPOT_PIPELINE_FRANCE: "FR",
    HUBSPOT_PIPELINE_PORTUGAL: "PT",
}
OWNER_ID = os.getenv("OWNER_ID")

logger = logging.getLogger(__name__)


def parse_conflict_call(exception_traceback):
    """Parse hubspot error message on conflict to get the existing id"""
    reason_error = (
        json.loads(exception_traceback.body).get("message", "") or ""
    ).split("ID:")[-1]
    try:
        return int(reason_error.strip())
    except ValueError:
        pass

    return None


class MessageSchema(Schema):
    """Message schema to validate the input"""

    deal_id = fields.Str(required=True, allow_none=False)
    comment = fields.Str(required=True, allow_none=False)

    def send_email(self, request, deal_id, recipients, sender, content):
        """Send email to warn our support team that book is finished"""
        topic_path = pubsub_client.topic_path(PROJECT_ID, TOPIC_ID)

        content = {
            "subject": _(
                "Booking data updated for deal {deal_id}",
                request,
            ).format(deal_id=deal_id),
            "recipients": recipients,
            "template": "generic_email",
            "sender": sender,
            "context": {"data": content},
        }

        publish_future = pubsub_client.publish(
            topic_path, data=json.dumps(content).encode("utf-8")
        )
        publish_future.result(timeout=20)

    def _get_deal_details(self, deal_id):
        try:
            api_response = hubspot_client.crm.deals.basic_api.get_by_id(
                deal_id=deal_id, properties=["pipeline"]
            )
            return api_response
        except ApiException as error:
            logger.error(
                f"Error retrieving deal details ({deal_id}) from hubspot: {error}"
            )
            raise error

    def create_note_in_deal_in_hubspot(self, deal_id, comment):
        """Create a note in deal"""

        try:
            response = requests.request(
                "POST",
                HUBSPOT_URL.format(object="engagements", object_path="engagements"),
                data=json.dumps(
                    {
                        "engagement": {
                            "active": True,
                            "ownerId": OWNER_ID,  # Not relevant to create notes in hubspot
                            "type": "NOTE",
                        },
                        "associations": {
                            "contactIds": [],
                            "companyIds": [],
                            "dealIds": [deal_id],
                            "ownerIds": [],
                        },
                        "metadata": {
                            "body": f"<b>[BOX2BOT &#129302;]</b> <br> <u>Datos de reserva del cliente:</u> <br><ul>{comment}</ul>"
                        },
                    }
                ),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {HUBSPOT_API_KEY}",
                },
            )

            response.raise_for_status()
        except RequestException as error:
            logger.error(f"Error creating a note in hubspot: {error}")
            raise error

    def create_in_hubspot(self, request, data):
        parsed_data = self.load(data)

        deal_id = parsed_data.get("deal_id")
        comment = parsed_data.get("comment")

        try:
            # Get deal details
            deal_metadata = self._get_deal_details(deal_id)
            pipeline = HUBSPOT_PIPELINE_MAP.get(
                deal_metadata.properties.get("pipeline")
            )

            # Hacky step -> set the accept-language header to handle the subject language
            request.environ["HTTP_ACCEPT_LANGUAGE"] = pipeline

            # Create a note in hubspot
            self.create_note_in_deal_in_hubspot(deal_id=deal_id, comment=comment)

            # Finally, send the email
            self.send_email(
                request,
                deal_id,
                recipients=RECIPIENTS_EMAILS.get(pipeline),
                sender=pipeline,
                content=f"""
                    <ul>
                        <li>url: {HUBSPOT_DEAL_URL.format(deal_id=deal_id)}</li>
                        {comment}
                    <ul>
                """,
            )
            return True
        except (RequestException, ApiException):
            return False


@functions_framework.http
def entrypoint(request):
    """Main entry point for hubspot message"""

    # Set CORS headers for the preflight request
    if request.method == "OPTIONS":
        # Allows GET requests from any origin with the Content-Type
        # header and caches preflight response for 3600s
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST",
            "Access-Control-Allow-Headers": "Content-Type, ",
            "Access-Control-Max-Age": "3600",
        }

        return "", 204, headers

    # Set CORS headers for the main request
    headers = {"Access-Control-Allow-Origin": "*"}

    # Only POST method are allowed for this endpoint
    if request.method not in ["POST"]:
        logger.info(f"Method not allowed {request.method}")
        return (
            {"msg": _("Invalid request method", request)},
            400,
            headers,
        )

    try:
        data = request.get_json(silent=True)

        if data is None:
            return {"msg": _("Invalid data", request)}, 400, headers

        # Instantiate the main class to make calls against hubspot
        message_schema = MessageSchema()
        if message_schema.create_in_hubspot(request, data):
            return {"msg": _("Deal updated successfully", request)}, 200, headers

        return (
            {"msg": _("Something goes wrong. Please, try again later", request)},
            400,
            headers,
        )
    except Exception as err:
        logger.error(f"Unknown exception :: {err} - {traceback.print_exc(chain=False)}")

    return (
        {"msg": _("Unknown error detected. Please, try again later", request)},
        500,
        headers,
    )
