import os
from datetime import <PERSON><PERSON><PERSON>

import pytz
from chargebee.environment import Environment
from firebase_admin import firestore

from i18n import gettext as _

# PAYMENT_LINK_SECRET_KEY = os.getenv("CHARGEBEE_PAYMENT_LINK_SECRET_KEY")
FIRESTORE_PAYMENT_LINK_COLLECTION = os.getenv("FIRESTORE_PAYMENT_LINK_COLLECTION")
LEASE_TIME = 2  # in minutes, time before leasing the lock on transaction

LOCALES = {
    "FR": "fr-FR",
    "ES": "es-ES",
    "PT": "pt-PT",
    "IT": "it-IT",
    "EN": "en-US",
    "MX": "es-MX",
}
CURRENCIES = {
    "ES": "EUR",
    "PT": "EUR",
    "FR": "EUR",
    "IT": "EUR",
    "MX": "MXN",
}

DEFAULT_AMOUNT = {
    "EUR": 100,
    "MXN": 100,
}

CHARGEBEE_SITES = {
    "FR": {
        "api_key": os.getenv("CHARGEBEE_API_KEY_FR"),
        "site": os.getenv("CHARGEBEE_SITE_FR"),
        "adyen_gateway": os.getenv("ADYEN_GATEWAY_ACCOUNT_ID_FR"),
        "gocardless_gateway": os.getenv("GOCARDLESS_GATEWAY_ACCOUNT_ID_FR"),
    },
    "ES": {
        "api_key": os.getenv("CHARGEBEE_API_KEY_ES"),
        "site": os.getenv("CHARGEBEE_SITE_ES"),
        "adyen_gateway": os.getenv("ADYEN_GATEWAY_ACCOUNT_ID_ES"),
        "gocardless_gateway": os.getenv("GOCARDLESS_GATEWAY_ACCOUNT_ID_ES"),
    },
    "PT": {
        "api_key": os.getenv("CHARGEBEE_API_KEY_PT"),
        "site": os.getenv("CHARGEBEE_SITE_PT"),
        "adyen_gateway": os.getenv("ADYEN_GATEWAY_ACCOUNT_ID_PT"),
        "gocardless_gateway": os.getenv("GOCARDLESS_GATEWAY_ACCOUNT_ID_PT"),
    },
    "IT": {
        "api_key": os.getenv("CHARGEBEE_API_KEY_ES"),
        "site": os.getenv("CHARGEBEE_SITE_ES"),
        "adyen_gateway": os.getenv("ADYEN_GATEWAY_ACCOUNT_ID_ES"),
        "gocardless_gateway": os.getenv("GOCARDLESS_GATEWAY_ACCOUNT_ID_ES"),
    },
    "MX": {
        "api_key": os.getenv("CHARGEBEE_API_KEY_MX"),
        "site": os.getenv("CHARGEBEE_SITE_MX"),
        "adyen_gateway": os.getenv("ADYEN_GATEWAY_ACCOUNT_ID_MX"),
        "gocardless_gateway": os.getenv("GOCARDLESS_GATEWAY_ACCOUNT_ID_MX"),
    },
}

ERROR_MESSAGES = {
    "param_wrong_value": lambda request: _("Invalid format", request),
    "payment_processing_failed": lambda request: _(
        "Payment cannot be created as the payment collection failed", request
    ),
    "payment_method_verification_failed": lambda request: _(
        "There's a problem adding the payment method", request
    ),
    "payment_intent_invalid": lambda request: _(
        "Error processing the payment. Try again later", request
    ),
    "internal_error": lambda request: _(
        "Unknown error in the payment gateway. Wait few minutes and try again", request
    ),
    "api_request_limit_exceeded": lambda request: _(
        "Error processing the payment. Try again later", request
    ),
    "internal_temporary_error": lambda request: _(
        "Unknown error in the payment gateway. Wait few minutes and try again", request
    ),
    "default": lambda request: _(
        "Unknown error. Please, wait few minutes and try again", request
    ),
}

ALLOWED_LOCALES = ["EN", "ES", "PT", "FR", "IT", "DE", "MX"]


def get_error_message(message, request):
    """Helper method to get the right error message from chargebee api"""
    try:
        return ERROR_MESSAGES[message](request)
    except KeyError:
        return ERROR_MESSAGES["default"](request)


STATUSES = {200: "success", 237: "in_progress"}


# def validate_integrity_key(payload, hash_key):
#     """Validate payment link integrity key to be sure that it does not be modified"""
#     digest = hmac.new(
#         PAYMENT_LINK_SECRET_KEY.encode("utf-8"),
#         msg=json.dumps(
#             {
#                 "email": payload.get("email"),
#                 "contact_id": payload.get("contact_id"),
#                 "create_date": payload.get("create_date"),
#                 "contract_id": payload.get("contract_id"),
#                 "deposit": payload.get("deposit"),
#                 "valid_domain": payload.get("valid_domain"),
#             },
#         ).encode("utf-8"),
#         digestmod=sha256,
#     ).hexdigest()
#
#     return digest == hash_key


def build_response(success=None, error=None):
    """Helper method to standardize the output response to the client"""

    return {"body": success, "errors": error}


def get_chargebee_metadata(site):
    """Helper method to create the environment object to pass it in every request"""
    chargebee_metadata = CHARGEBEE_SITES.get(site, None) or None
    if chargebee_metadata is not None:
        adyen_gateway_id = chargebee_metadata.pop("adyen_gateway", None)
        return (
            Environment({**chargebee_metadata}),
            adyen_gateway_id,
        )

    return None, None


@firestore.transactional
def check_idempotent(transaction, db_ref, now, request, lease_time=LEASE_TIME):
    """Run this transaction to avoid processing two or more payments for the same customer at the same time"""
    snapshot = db_ref.get(transaction=transaction)

    # Sanitize lease to avoid errors
    now = now.replace(tzinfo=pytz.utc)
    saved_lease = snapshot.get("lease") or now

    if snapshot.exists and (
        snapshot.get("status") == "success" or snapshot.get("is_paid")
    ):
        return False, _("Payment duplicated.", request), 410

    if snapshot.exists and (now > snapshot.get("valid_until")):
        return False, _("Payment link expired.", request), 410

    if snapshot.exists and now < saved_lease and snapshot.get("status") != "failed":
        return (
            False,
            _(
                "Payment in progress. Please, wait {lease_time} minutes and try again.",
                request,
            ).format(lease_time=lease_time),
            400,
        )

    elapsed_time = (now - saved_lease).seconds
    if (
        snapshot.exists
        and snapshot.get("status") == "in_progress"
        and elapsed_time < 300
    ):
        return (
            False,
            _(
                "Payment in progress. Maybe there was an error on confirmation. Please, wait {leased_time} seconds and try again.",
                request,
            ).format(leased_time=300 - elapsed_time),
            400,
        )

    transaction.update(
        db_ref,
        {
            "status": "in_progress",
            "lease": now + timedelta(minutes=lease_time),
        },
    )

    return True, _("Payment successful", request), 200


@firestore.transactional
def update_payment(transaction, db_ref, status="success", tried_with=None):
    """Update payment transaction to release the lock in order to avoid/allow new payments for this event"""
    payload = {"status": status, "is_paid": status == "success"}
    if tried_with is not None:
        payload["payment_source"] = tried_with

    transaction.update(db_ref, payload)
