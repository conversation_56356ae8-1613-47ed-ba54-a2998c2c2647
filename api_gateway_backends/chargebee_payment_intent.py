"""Backend to manage the payment link flow"""
import logging
import traceback

import chargebee
import functions_framework
from chargebee_common import (CURRENCIES, DEFAULT_AMOUNT,
                              FIRESTORE_PAYMENT_LINK_COLLECTION,
                              build_response, get_chargebee_metadata,
                              get_error_message, update_payment)
from common import get_cloud_service
from flask import jsonify

from i18n import gettext as _

logger = logging.getLogger(__name__)

try:
    # Initialize firestore
    db = get_cloud_service()
except Exception as err:
    logger.error(
        f"Error initializing firestore client :: {err} - {traceback.print_exc(chain=False)}"
    )
    db = None


def three_ds_payment_intent(
    request,
    *,
    customer_id,
    amount,
    gateway_id,
    environment,
    currency,
):
    try:
        payment_intent = chargebee.PaymentIntent.create(
            {
                "payment_method_type": "card",
                "customer_id": customer_id,
                "gateway_account_id": gateway_id,
                "amount": amount,  # Authorize the card with 1€/20MXN instead of full amount
                "currency_code": currency,
            },
            env=environment,
        ).payment_intent

        status_code = 237
        response = build_response(
            success={
                "payment_intent": payment_intent.values,
            }
        )
    except chargebee.api_error.APIError as err:
        logger.error(f"Error creating the payment intent: {err.json_obj}")

        status_code = 400
        error_message = get_error_message(err.api_error_code, request)
        response = build_response(
            error={
                "reason": error_message,
                "extra": {
                    "param": err.json_obj.get("param"),
                },
            }
        )

    # Let to front-end managing this scenario in confirmcardpayment
    return response, status_code


@functions_framework.http
def entrypoint(request):
    """Main entry point for payment intent"""

    # Set CORS headers for the preflight request
    if request.method == "OPTIONS":
        # Allows GET requests from any origin with the Content-Type
        # header and caches preflight response for an 3600s
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST",
            "Access-Control-Allow-Headers": "Content-Type, X-Chargebee-Payment-Link-Key, ",
            "Access-Control-Max-Age": "3600",
        }

        return "", 204, headers

    # Set CORS headers for the main request
    headers = {"Access-Control-Allow-Origin": "*"}

    # Only POST method are allowed for this endpoint
    if request.method not in ["POST"]:
        logger.info(f"Method not allowed {request.method}")
        return (
            build_response(
                error={
                    "reason": _(
                        "Method {method} not allowed",
                        request,
                    ).format(method=request.method),
                    "extra": None,
                }
            ),
            400,
            headers,
        )

    # Validate input arguments
    data = request.get_json(silent=True)
    payment_link_key = data.get("key")

    if not payment_link_key:
        logger.error("Invalid payment link key")
        return (
            build_response(
                error={"reason": _("Invalid payment link key", request), "extra": None}
            ),
            400,
            headers,
        )

    try:
        # Run chargebee payment inside transactional block
        transaction = db.transaction()
        db_ref = db.collection(FIRESTORE_PAYMENT_LINK_COLLECTION).document(
            payment_link_key
        )

        try:
            # Read data from firestore
            data = db_ref.get()

            if not data.exists:
                logger.error("Error finding the link in database")
                return (
                    jsonify(
                        build_response(
                            error={
                                "reason": _("Can't find the link", request),
                                "extra": None,
                            }
                        )
                    ),
                    400,
                    headers,
                )

            # Transform into dict
            data = data.to_dict()
            locale = (data.get("site") or "").upper()
            currency = CURRENCIES.get(locale)
            amount = DEFAULT_AMOUNT.get(currency, 100) or 100

            (
                chargebee_environment,
                adyen_gateway_id,
            ) = get_chargebee_metadata(site=locale)

            # If we could update customer data, we try the payment intent if enabled
            response, status_code = three_ds_payment_intent(
                request,
                customer_id=data.get("contact_id"),
                # amount=data.get("deposit"),
                amount=amount,
                gateway_id=adyen_gateway_id,
                environment=chargebee_environment,
                currency=currency,
            )

            if status_code == 400:
                # Update transaction
                update_payment(transaction, db_ref, status="failed")

            return jsonify(response), status_code, headers
        except Exception as err:
            # Update transaction
            update_payment(transaction, db_ref, status="failed")
            raise err
    except Exception as err:
        logger.error(f"Unknown exception :: {err} - {traceback.print_exc(chain=False)}")

    return (
        jsonify(
            build_response(
                error={
                    "reason": _(
                        "Unknown error detected. Please, try again later", request
                    ),
                    "extra": None,
                }
            )
        ),
        500,
        headers,
    )
