.wrapper .container {
    padding-bottom: 2rem;
    padding-top: 2rem;
}

.navigation {
    background: #f4f5f6;
    border-bottom: .1rem solid #d1d1d1;
    display: block;
    height: 5.2rem;
    left: 0;
    max-width: 100%;
    width: 100%;
}

.navigation .container {
    padding-bottom: 0;
    padding-top: 0
}

.navigation .navigation-list {
    list-style: none;
    margin-bottom: 0;
}

.navigation .navigation-item {
    float: left;
    margin-bottom: 0;
    margin-left: 2.5rem;
    position: relative
}

.navigation .navigation-title, .navigation .title {
    color: #606c76;
    position: relative
}

.navigation .navigation-link, .navigation .navigation-title, .navigation .title {
    display: inline;
    font-size: 1.6rem;
    line-height: 5.2rem;
    padding: 0;
    text-decoration: none
}

.row.authorize-button {
    justify-content: center;
}

.event.deletion {
    color: white;
    background-color: red;
}

.event.creation {
    color: #3c763d;
    background-color: #dff0d8;
}

.event.propertyChange {
    background-color: #d8aa65;
    color: #ffffff;
}

.event.propertyChange span:first-child {
    background-color: #08080885;
}

.hidden {
    display: none;
}

.pagination {
    margin: 0rem;
    padding-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: bold;
}
.pagination a {
    border: 0.1rem solid #9b4dca;
    padding: 0.5rem 1.5rem;
    border-right: none;
}
.pagination a:first-child {
    border-radius: 0.4rem;
    padding: 0.5rem 1rem;
    border-radius: 0.4rem 0rem 0rem 0.4rem;
}
.pagination a:last-child {
    border-radius: 0.4rem;
    border-right: 0.1rem solid #9b4dca;
    padding: 0.5rem 1rem;
    border-radius: 0rem 0.4rem 0.4rem 0rem;
}

.pagination a.active {
    background-color: #9b4dca;
    color: #fff;
}

.row.authorize-button {
    justify-content: center;
}

.text-center {
    text-align: center;
}
