version: '3.5'

services:
    zookeeper:
        container_name: php-webhooks-app-zookeeper
        image: zookeeper:3.5
        ports:
            - "2181:2181"
        logging:
            driver: none
        tmpfs: "/datalog"
    kafka:
        container_name: php-webhooks-app-kafka
        image: wurstmeister/kafka:2.12-2.3.0
        ports:
            - "9092:9092"
        depends_on:
            - zookeeper
        logging:
            driver: none
        environment:
            KAFKA_ADVERTISED_HOST_NAME: kafka
            KAFKA_ADVERTISED_PORT: 9092
            KAFKA_CREATE_TOPICS: "events:1:1"
            KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
        volumes:
            - /var/run/docker.sock:/var/run/docker.sock
    web:
        container_name: php-webhooks-app-web
        env_file:
            - .env
        environment:
            KAFKA_BROKER_LIST: kafka:9092
            KAFKA_REFRESH_INTERVAL_MS: 1000
            KAFKA_BROKER_VERSION: 1.0.0
            KAFKA_PRODUCE_INTERVAL: 500
            KAFKA_GROUP_ID: events
            EVENT_TOPIC: events
            DB_HOST: db
            DB_NAME: events
            DB_USERNAME: events
            DB_PASSWORD: events
        build:
            dockerfile: ./Dockerfile
            context: ./
        depends_on:
            - kafka
            - db
        ports:
            - 8999:80
        volumes:
            - ./src:/app/src
            - ./db:/app/db
            - ./sql:/app/sql
    db:
        container_name: php-webhooks-app-db
        image: mysql:8.0
        command: mysqld --default-authentication-plugin=mysql_native_password
        volumes:
            - ./db/mysql:/var/lib/mysql
        ports:
            - 3306:3306
        logging:
            driver: none
        environment:
            MYSQL_ROOT_PASSWORD: root
            MYSQL_DATABASE: events
            MYSQL_USER: events
            MYSQL_PASSWORD: events
    ngrok:
        container_name: php-webhooks-app-ngrok
        image: gtriggiano/ngrok-tunnel
        ports:
            - 4040:4040
        environment:
            TARGET_HOST: web
            TARGET_PORT: 80
        depends_on:
            - web
