const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,

  pages: {
    popup: {
      template: 'public/browser-extension.html',
      entry: './src/popup/main.js',
      title: 'Popup'
    },
    // options: {
    //   template: 'public/browser-extension.html',
    //   entry: './src/options/main.js',
    //   title: 'Options'
    // }
  },

  pluginOptions: {
    browserExtension: {
      manifestTransformer: (manifest) => {
        manifest.content_security_policy = {"extension_pages": "script-src 'self'; object-src 'self'"}
        return manifest;
      },
      componentOptions: {
        background: {
          entry: 'src/background.js'
        },
        contentScripts: {
          entries: {
            'content-script': [
              'src/content-scripts/content-script.js',
              // 'src/content-scripts/listener.js'
            ]
          }
        }
      }
    }
  },
  configureWebpack: {
    devtool: 'cheap-module-source-map'
  }
})
