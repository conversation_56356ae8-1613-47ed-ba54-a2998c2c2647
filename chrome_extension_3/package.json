{"name": "chrome_extension_3", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service build --mode development --watch", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@vue-hero-icons/outline": "^1.7.2", "@vue-hero-icons/solid": "^1.7.2", "axios": "^1.4.0", "core-js": "^3.8.3", "socket.io-client": "^4.7.2", "vue": "^2.6.14", "vue-clickaway": "^2.2.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "node-sass": "^7.0.1", "postcss": "^8.4.24", "sass-loader": "^12.6.0", "tailwindcss": "^3.3.2", "vue-cli-plugin-browser-extension": "npm:@rhilip/vue-cli-plugin-browser-extension@^0.27.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true, "webextensions": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}