{"manifest_version": 3, "name": "__MSG_extName__", "homepage_url": "http://localhost:8080/", "description": "A Vue Browser Extension", "background": {"service_worker": "js/background.js", "module": true}, "default_locale": "en", "action": {"default_title": "__MSG_extName__", "default_icon": {"19": "icons/19.png", "38": "icons/38.png"}}, "permissions": ["tabs", "scripting", "activeTab", "tabCapture"], "web_accessible_resources": [{"resources": ["popup.html", "AudioProcessorWav.js", "index.css"], "matches": ["<all_urls>"]}], "icons": {"16": "icons/16.png", "48": "icons/48.png", "128": "icons/128.png"}}