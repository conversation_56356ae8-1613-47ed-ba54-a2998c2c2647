<template>
  <div
    @click="handleClick()"
    class="flex justify-between items-center p-2 px-3 hover:bg-gray-100 rounded-md"
    :class="{
      disabled: disabled,
    }"
  >
    <div
      class="inline-flex items-center font-medium text-sm text-gray-700 gap-1"
    >
      <slot name="leftSide"></slot>
      <slot></slot>
    </div>
    <span
      v-if="selectedOption && showSelectedOption"
      class="text-indigo-600 right-0 flex items-center justify-end"
    >
      <check-icon class="h-5 w-5" aria-hidden="true"></check-icon>
    </span>
  </div>
</template>
<script>
import { CheckIcon } from "@vue-hero-icons/outline";

export default {
  name: "uiOption",
  props: {
    value: {
      default: null,
    },
    selected: {
      type: Boolean,
      default: false,
    },
    showSelectedOption: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CheckIcon,
  },
  data() {
    return {
      selectedOption: this.selected,
    };
  },
  methods: {
    handleClick() {
      this.$parent.$emit("clicked", this.value);
    },
  },
};
</script>
