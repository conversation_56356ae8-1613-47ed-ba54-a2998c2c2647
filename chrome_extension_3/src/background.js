console.log("BACKGROUND!!!!")

chrome.action.onClicked.addListener(async (tab) => {
  console.log("TABBB ", tab, chrome.runtime.getURL("popup.html"));
  chrome.scripting.executeScript({
    target: { tabId: tab.id },
    files: ["js/content-script.js"]
  });

});

const sleep = (ms = 0) => new Promise((resolve) => setTimeout(resolve, ms));
const checkIfRemoteTabIsOpened = async (url) => {
  const tabs = await chrome.tabs.query({ url });
  console.log("TABSSS ", tabs.length > 0);
  return tabs.length > 0;
}

chrome.runtime.onMessage.addListener(async (request, sender) => {
  const { command, remote_parent_id, room_id } = request;
  const LISTENER_TAB_URL = `chrome-extension://${chrome.runtime.id}/listener.html`;

  console.log("COMMAD ", request);

  switch (command) {
    case "CAPTURE_REMOTE": {
      if (await checkIfRemoteTabIsOpened(LISTENER_TAB_URL)) {
        console.log("Remote tab is already opened");
        return;
      }

      const createdTab = await chrome.tabs.create({
        pinned: true,
        active: false,
        // <--- Important
        url: LISTENER_TAB_URL
      });

      await chrome.tabs.update(createdTab.id, { autoDiscardable: false });
      await sleep(500);
      await chrome.tabs.sendMessage(createdTab.id, {
        command: "CAPTURE_REMOTE",
        remote_parent_id: remote_parent_id,
        room_id: room_id,
      });
      break;
    }
    case "CAPTURE_MICROPHONE_START": {
      console.log("EVENTO AL BACKGROUND!!", sender);
      chrome.tabs.sendMessage(sender.tab.id, {command: "CAPTURE_MICROPHONE_START", remote_parent_id: remote_parent_id,});
      break;
    }
    case "CAPTURE_REMOTE_START": {
      console.log("CAPTURE REMOTE START ", sender.tab.id);
      chrome.tabs.sendMessage(sender.tab.id, {
        command: "CAPTURE_REMOTE_START",
        remote_parent_id: remote_parent_id,
        room_id: room_id,
      });
      break;
    }
    case "CAPTURE_REMOTE_STOP": {
      console.log("CAPTURE REMOTE STOP ", sender.tab.id);
      chrome.tabs.sendMessage(sender.tab.id, {
        command: "CAPTURE_REMOTE_STOP",
        remote_parent_id: remote_parent_id,
        room_id: room_id,
      });
      break;
    }
    default: break;
  }
});