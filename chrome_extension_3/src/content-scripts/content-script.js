//==================
// Functions signature
//==================
const iFrameName = "inceptionai";
const createPopupWindow = () => {
  let iframe = document.getElementById(iFrameName);
  if (iframe) {
    return;
  }

  iframe = document.createElement("iframe");
  iframe.setAttribute('id', 'inceptionai');
  iframe.setAttribute(
    'style',
    'height: 560px;width: 420px;z-index: 2147483650; position:fixed; top:10px;left:10px;border:none;margin:0;'
  );
  iframe.setAttribute('allow', '');
  iframe.src = chrome.runtime.getURL('popup.html');

  document.body.appendChild(iframe);
};

const removePopupWindow = () => {
  const iFrame = document.getElementById(iFrameName);
  if (iFrame) {
    iFrame.parentNode.removeChild(iFrame);
  }
};  

const captureMicrophone = async () => new Promise((resolve) => {
  console.log("HOLELLELELE");
  navigator.mediaDevices.getUserMedia(
    {
      audio: true,
      video: false
    }).then(
      (stream) => {
        console.log("ESTOY AQUI NO?");
        resolve(stream);
      }
    );
});

class Recordable {
  constructor(sampleRate, remoteParentId, room_id, url = "https://internal.movabox.com/parse") {
    console.log("STREAM!!");
    this._sampleRate = sampleRate;
    this._isRunning = false;
    this._url = url;

    this._pcmWorker = null;
    this._downSampledContext = null;
    this._track = null;
    this._remoteParentId = remoteParentId;
    this._room_id = room_id;
  }

  async _createAudioWorkletNode() {
    const stream = await captureMicrophone();
    this._downSampledContext = new AudioContext({
      sampleRate: this._sampleRate,
    });

    await this._downSampledContext.audioWorklet.addModule(
      `chrome-extension://${chrome.runtime.id}/AudioProcessorWav.js`
    );

    console.log("OYEEEE");

    [this._track] = stream.getAudioTracks();
    this._pcmWorker = new AudioWorkletNode(
      this._downSampledContext,
      "pcm-worker",
      {
        outputChannelCount: [2],
        parameterData: {
          sampleSize: this._track.getSettings().sampleSize,
          sampleRate: this._sampleRate,
        },
      }
    );
    const downSampledStream = this._downSampledContext.createMediaStreamSource(stream);
    downSampledStream.connect(this._pcmWorker);

    // Create the listener here
    this._pcmWorker.port.onmessage = async (message) => {
      if (!this._isRunning) {
        return;
      }

      console.log("ME LLEGO EL MENSAKKE ", message);

      const formData = new FormData();
      formData.append(
        "audio-blob",
        new Blob([message.data.encodedAudio])
      );
      formData.append("who", "microphone");
      formData.append("parent", this._remoteParentId);
      formData.append("room_id", this._room_id);
      console.log("PARENT ID - ", this._remoteParentId);

      await fetch(this._url, {
        method: "POST",
        body: formData,
        headers: {
          "Authorization": "Bearer a0e5886d2f94f97cc1058da16e11f024075f9bbd9a13c0b263d17c8496b07fd3"
        }
      });
    };
  }

  start() {
    if (this._isRunning) {
      console.error("Recordable already listening...");
      return;
    }
    this._isRunning = true;
    this._pcmWorker.port.start();
    console.log("OCMF ", this._pcmWorker);
  }

  stop() {
    if (!this._isRunning) {
      console.error("Recordable already paused...");
      return;
    }

    this._isRunning = false;
    this._pcmWorker.port.close();
    this._track.stop();
    this._downSampledContext.close();
  }

  async reset() {
    if (this._isRunning) {
      console.error("Recordable cannot be reset if is running. Stop it first");
      return;
    }
    await this._createAudioWorkletNode();
  }
}

let recordableProxy = null;

chrome.runtime.onMessage.addListener(async (request, sender) => {
  console.log("MENNNSSNSNSNSNSNSNS ", request);
  const { command, remote_parent_id, room_id } = request;
  console.log("MENSAJE RECIBIDO", command, remote_parent_id, room_id, sender, recordableProxy);

  // TODO: handle better microphone and set commands to create a "recorder" feature
  switch (command) {
    case "CAPTURE_MICROPHONE_START": {
      if (recordableProxy) {
        recordableProxy.start();
      } else {
        await recordableProxy.reset();
      }

      break;
    }
    case "CAPTURE_MICROPHONE_STOP": {
      if (recordableProxy) {
        console.log("RECORDABLE ", recordableProxy);
        recordableProxy.stop();
        recordableProxy = null;
      }

      break;
    }
    case "CAPTURE_MICROPHONE": {
      if (!recordableProxy) {
        recordableProxy = new Recordable(16000, remote_parent_id, room_id);
        await recordableProxy.reset();
      }

      console.log("CAPT MICROPHOEN");
      chrome.runtime.sendMessage({
        command: "CAPTURE_MICROPHONE_START",
        remote_parent_id: remote_parent_id,
      });
      break;
    }
    case "CLOSE_WINDOW": {
      removePopupWindow();
      break;
    }
  }
});

//==================
// Run functions
//==================

createPopupWindow();