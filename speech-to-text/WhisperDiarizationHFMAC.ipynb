#%% raw
~/.cache/huggingface/hub  
https://github.com/pyannote/pyannote-audio/blob/develop/tutorials/adapting_pretrained_pipeline.ipynb
https://github.com/wq2012/SpectralCluster/blob/master/spectralcluster/configs.py
https://pvarshney1729.github.io/projects/Report_EE698.pdf
https://open-speech-ekstep.github.io/speaker_clustering/
https://huggingface.co/models?other=Verification
https://wq2012.github.io/awesome-diarization/
https://github.com/guillaumekln/faster-whisper
https://huggingface.co/spaces/vumichien/Whisper_speaker_diarization
#%%
import whisperx
#%%
model_size = "large-v2"
SPEECH_FILE = "./output.wav"
HF_TOKEN = "*************************************"
#%%
model = whisperx.load_model(model_size, device="cpu", compute_type="int8", language="es")
#%%
result = model.transcribe(SPEECH_FILE, batch_size=16)
#%%
result
#%%
model_a, metadata = whisperx.load_align_model(language_code=result["language"], device="cpu")
#%%
result = whisperx.align(result["segments"], model_a, metadata, SPEECH_FILE, "cpu", return_char_alignments=False)
#%%
result
#%%
from pyannote.audio import Pipeline
from typing import Optional, Union
import torch

class DiarizationPipeline(whisperx.DiarizationPipeline):
     def __init__(
        self,
        model_name="pyannote/speaker-diarization@2.1",
        use_auth_token=None,
        device: Optional[Union[str, torch.device]] = "cpu",
    ):
        self.model = Pipeline.from_pretrained(model_name, use_auth_token=use_auth_token)

diarize_model = DiarizationPipeline(use_auth_token=HF_TOKEN, device="cpu")
#%%
diarize_segments = diarize_model(SPEECH_FILE)
#%%
result = whisperx.assign_word_speakers(diarize_segments, result)
#%%
for segment in result["segments"]:
    print(f"{segment['speaker']} - {segment['text'].strip()}")