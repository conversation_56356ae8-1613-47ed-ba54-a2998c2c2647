#%%
from faster_whisper import WhisperModel
import numpy as np
import whisperx
#%%
model_size = "large-v2"
HF_TOKEN = "*************************************"

# Run on GPU with FP16
model = WhisperModel(model_size, device="cpu", compute_type="int8")
modelx = whisperx.load_model(model_size, device="cpu", compute_type="int8", language="es")
#%%
with open("/home/<USER>/Documentos/audio23.wav", "rb") as fp:
    file = fp.read()
#%%
# with open("/home/<USER>/repos/ai-research/speech-to-text/output.wav", "rb") as fp:
#     file = fp.read()
#%%
data = np.frombuffer(file, np.int16).flatten().astype(np.float32) / 32768.0
#%%
segments, info = model.transcribe(data, beam_size=5)
#%%
for segment in segments:
    print("[%.2fs -> %.2fs] %s" % (segment.start, segment.end, segment.text))
#%%
# result = modelx.transcribe("/home/<USER>/Documentos/audio2.webm", batch_size=16)
result = modelx.transcribe(data, batch_size=16)
#%%
result