#%% md
#### Resources
https://cloud.google.com/speech-to-text/docs/multiple-voices?hl=es-419#speech_transcribe_diarization_beta-python
https://cloud.google.com/speech-to-text/docs/transcribe-client-libraries?hl=es-419
https://stackoverflow.com/questions/75729807/speech-to-text-recognise-the-speakers
#%% raw
https://www.youtube.com/watch?v=k8imxcuOnJk -- EN
https://www.youtube.com/watch?v=STP5tBQoyLo -- ES
https://huggingface.co/spaces/vumichien/Whisper_speaker_diarization
#%% md
### Audio conversion and useful commands 
#%% raw
ffprobe output.wav # get info from audio clip
ffmpeg -i output.mp3 -acodec pcm_s16le -f s16le -ac 1 -ar 16000 output.wav # convert .mp3 to .wav with 16Khz sample rate, 1 channel
ffmpeg -i output.mp3 -ss 00:00:00 -to 00:00:10 -c:a pcm_s16le -ac 1 output.wav  # convert .mp3 to .wav with original sample rate (48Khz) and 1 channel
#%% md
### Configure speech to text dependencies
#%%
from google.cloud import speech_v1p1beta1 as speech
import io


SERVICE_ACCOUNT_JSON = "./ai-inception-sa.json"
SPEECH_FILE = "./output_en.wav"
#%% md
### Create an instance of Speechclient 
#%%
client = speech.SpeechClient.from_service_account_json(SERVICE_ACCOUNT_JSON)
#%%
with open(SPEECH_FILE, "rb") as file:
    content = file.read()
#%%
audio = speech.RecognitionAudio(content=content)
diarization_config = speech.SpeakerDiarizationConfig(
    enable_speaker_diarization=True,
    min_speaker_count=2,
    max_speaker_count=2,
)
config = speech.RecognitionConfig(
    # encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
    diarization_config=diarization_config,
    # sample_rate_hertz=16000,
    language_code="en-US",
    use_enhanced=True,
    enable_separate_recognition_per_channel=False,
    audio_channel_count=1,
    enable_word_confidence=True,
    enable_word_time_offsets=True,
    enable_automatic_punctuation=True,
    # A model must be specified to use enhanced model.
    model="latest_long",
)
#%%
response = client.recognize(config=config, audio=audio)
#%%
for result in response.results:
    # The first alternative is the most likely one for this portion.
    print("Transcript: {} - {}".format(result.alternatives[0].transcript, result.channel_tag))

#%%
# The transcript within each result is separate and sequential per result.
# However, the words list within an alternative includes all the words
# from all the results thus far. Thus, to get all the words with speaker
# tags, you only have to take the words list from the last result:
result = response.results[-1]

words_info = result.alternatives[0].words

# Printing out the output:
for word_info in words_info:
    print(
        "word: '{}', speaker_tag: {}, confidence: {}, st: {}, et: {}".format(word_info.word, word_info.speaker_tag, word_info.confidence, word_info.start_time, word_info.end_time)
    )
#%%
