# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-12 09:55+0000\n"
"PO-Revision-Date: 2023-03-08 12:34+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.2.2\n"

#: backoffice_api/settings/base.py:546
msgid "English"
msgstr "Inglés"

#: backoffice_api/settings/base.py:547
msgid "Spanish"
msgstr "Español"

#: backoffice_api/settings/base.py:548
msgid "Portuguese"
msgstr "Portugués"

#: backoffice_api/settings/base.py:549
msgid "French"
msgstr "Francés"

#: backoffice_api/settings/base.py:550
msgid "Italian"
msgstr ""

#: contracts/admin.py:33
msgid "Annex"
msgstr "Anexo"

#: contracts/admin.py:51 contracts/models/contract.py:447
#: contracts/models/service.py:319 payments/models/payments.py:220
msgid "Box"
msgstr "Cajas"

#: contracts/admin.py:69 contracts/models/service.py:320
#: payments/models/payments.py:221
msgid "Moving"
msgstr "Mudanza"

#: contracts/admin.py:104 intranet/admin.py:50
msgid "Site"
msgstr ""

#: contracts/admin.py:109 intranet/admin.py:434
msgid "City"
msgstr ""

#: contracts/admin.py:167
msgid "Photos"
msgstr ""

#: contracts/admin.py:189 contracts/admin.py:310 contracts/admin.py:402
#: contracts/service_templates/contracts.py:170
#: riders/admin_filters/riders.py:21
msgid "Service"
msgstr "Servicio"

#: contracts/admin.py:198
#, fuzzy
#| msgid "Warehouse where is stored"
msgid "Warehouse"
msgstr "Almacén donde está almacenado"

#: contracts/admin.py:200 contracts/admin.py:312 contracts/admin.py:404
#, fuzzy
#| msgid "Created"
msgid "Create quote"
msgstr "Creado"

#: contracts/admin.py:211 contracts/admin.py:321 contracts/admin.py:413
#, fuzzy
#| msgid "No created"
msgid "Service not found. Quote won't be created"
msgstr "No creado"

#: contracts/admin.py:216
msgid "Warehouse not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:222 contracts/admin.py:329 contracts/admin.py:421
msgid "City not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:228 contracts/admin.py:335 contracts/admin.py:427
msgid "Raw data not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:268 contracts/admin.py:370 contracts/admin.py:469
#, fuzzy, python-brace-format
#| msgid "Pick up order created successfully"
msgid "Quote {quote} created in chargebee successfully"
msgstr "Solicitud de entrega creada con éxito"

#: contracts/admin.py:273 contracts/admin.py:375 contracts/admin.py:474
#, python-brace-format
msgid "Quote {quote} updated locally but changes are not in chargebee"
msgstr ""

#: contracts/admin.py:277 contracts/admin.py:379 contracts/admin.py:478
#, fuzzy, python-brace-format
#| msgid "Pick up order created successfully"
msgid "Quote {quote} could not be created locally"
msgstr "Solicitud de entrega creada con éxito"

#: contracts/admin.py:524
msgid "Provider Team"
msgstr ""

#: contracts/admin_filters/warehouses.py:7
msgid "Tracking Options"
msgstr ""

#: contracts/admin_filters/warehouses.py:23
msgid "Has tracking options"
msgstr ""

#: contracts/admin_filters/warehouses.py:23
#, fuzzy
#| msgid "Empty direction"
msgid "Empty tracking options"
msgstr "Dirección vacía"

#: contracts/admin_filters/warehouses.py:47
msgid "Country"
msgstr ""

#: contracts/filters.py:21 intranet/filters/inventory.py:13
msgid "Brings only non-delivered items from db."
msgstr ""

#: contracts/filters.py:27
msgid "Brings only pending items from db."
msgstr ""

#: contracts/filters.py:52
msgid "Brings services from db according to status choice."
msgstr ""

#: contracts/filters.py:58 contracts/filters.py:118
msgid ""
"Brings only services where event date is set to date lower than equals to "
"today."
msgstr ""

#: contracts/filters.py:65
msgid "Brings services from db excluding given services by id"
msgstr ""

#: contracts/filters.py:71
msgid ""
"Brings only services where location of goods in the warehouse is not set yet."
msgstr ""

#: contracts/filters.py:125
msgid "Brings tasks from db excluding given tasks by id"
msgstr ""

#: contracts/filters.py:154
msgid "Brings only movements with no parent movement"
msgstr ""

#: contracts/filters.py:160
msgid "Brings only movements from a specific contract"
msgstr ""

#: contracts/filters.py:166
msgid "Brings only movements from a specific placement (i.e. recursive)"
msgstr ""

#: contracts/mixins.py:10
msgid "View pdf"
msgstr ""

#: contracts/mixins.py:31
msgid "PDF file is not uploaded to remote server"
msgstr ""

#: contracts/mixins.py:42
#, fuzzy
#| msgid "Empty email"
msgid "Empty PDF file"
msgstr "Ningún correo electrónico provisto"

#: contracts/mixins.py:48
msgid "Download"
msgstr ""

#: contracts/mixins.py:50
msgid "PDF file is ready: {}"
msgstr ""

#: contracts/models/contract.py:37
msgid "Spain"
msgstr "España"

#: contracts/models/contract.py:38
msgid "France"
msgstr "Francia"

#: contracts/models/contract.py:39 contracts/utils.py:132
msgid "Portugal"
msgstr "Portugal"

#: contracts/models/contract.py:40
msgid "Italia"
msgstr ""

#: contracts/models/contract.py:41
msgid "Mexico"
msgstr ""

#: contracts/models/contract.py:47 contracts/models/contract.py:150
msgid "External id for this document"
msgstr "Identificador externo para este documento"

#: contracts/models/contract.py:76
msgid "Pick up/Deliver address"
msgstr "Dirección de recogida/entrega"

#: contracts/models/contract.py:92
msgid "Contract Addresses"
msgstr "Direcciones de entrega/recogida"

#: contracts/models/contract.py:114 contracts/models/contract.py:387
#: core/models/product.py:28
msgid "Will be deprecated in future deployments"
msgstr ""

#: contracts/models/contract.py:120 contracts/models/contract.py:393
#: contracts/models/item.py:48
msgid "Warehouse where is stored. Will be deprecated in future deployments"
msgstr ""

#: contracts/models/contract.py:123 contracts/models/contract.py:396
msgid "Hired space in squared meters"
msgstr "Espacio contratado en metros cuadrados"

#: contracts/models/contract.py:127
msgid "Person who has signed the initial_pickup contract"
msgstr ""

#: contracts/models/contract.py:132
msgid "Coupon code to apply the discount to the initial deposit"
msgstr "Código de descuento para aplicar en el contrato tras el pago inicial"

#: contracts/models/contract.py:135
msgid "Flag to check if deposit needs to be applied on this contract"
msgstr ""
"Flag para comprobar si el deposito necesita ser aplicado en este contrato"

#: contracts/models/contract.py:137
msgid "Amount to discount on first subscription"
msgstr "Cantidad a descontar en la primera suscripción"

#: contracts/models/contract.py:141
msgid "Indicates if contract has been imported from legacy system (Odoo)"
msgstr ""
"Nos dice si el contrato ha sido importado de un sistema heredado (Odoo)"

#: contracts/models/contract.py:144
msgid ""
"If this contract should be filtered to display it to customer, even if is "
"imported"
msgstr ""

#: contracts/models/contract.py:146
msgid "For created contracts from intranet, site which belongs it"
msgstr ""

#: contracts/models/contract.py:154
msgid "Odoo id for this contract"
msgstr ""

#: contracts/models/contract.py:184
msgid "Deposit"
msgstr "Señal"

#: contracts/models/contract.py:238
msgid "From id from moreapp"
msgstr ""

#: contracts/models/contract.py:245
msgid "Internal id from moreapp to track every form submitted"
msgstr "Id interno de moreapp para saber los formularios enviados"

#: contracts/models/contract.py:248
msgid "Flag to know if related documents have been uploaded to storage"
msgstr "Flag para saber si el documento ha sido subido al storage"

#: contracts/models/contract.py:250
msgid "If this file has been sent to user"
msgstr "Si este archivo ha sido enviado al usuario"

#: contracts/models/contract.py:251 moreapp/models.py:38
msgid "Date where document was sent to the queue"
msgstr ""

#: contracts/models/contract.py:347
#, python-brace-format
msgid "BOX2BOX - {contract_type} - {display_name}"
msgstr "BOX2BOX {contract_type} {display_name}"

#: contracts/models/contract.py:375 contracts/models/service.py:315
#: payments/models/payments.py:216
msgid "Initial pickup"
msgstr "Recogida"

#: contracts/models/contract.py:376 contracts/models/service.py:316
#: payments/models/payments.py:217
msgid "Extra pickup"
msgstr "Recogida adicional"

#: contracts/models/contract.py:377 contracts/models/service.py:317
#: intranet/models/delivery.py:39 payments/models/payments.py:218
msgid "Partial delivery"
msgstr "Entrega parcial"

#: contracts/models/contract.py:378 contracts/models/service.py:318
#: intranet/models/delivery.py:40 payments/models/payments.py:219
msgid "Final delivery"
msgstr "Entrega final"

#: contracts/models/contract.py:379 contracts/models/service.py:321
#: payments/models/payment_method.py:42 payments/models/payments.py:222
msgid "Unknown"
msgstr "Desconocido"

#: contracts/models/contract.py:405
msgid "Annexes"
msgstr "Anexos"

#: contracts/models/contract.py:426
msgid "Moved space in squared meters"
msgstr "Espacio trasladado en metros cuadrados"

#: contracts/models/contract.py:431
msgid "Move"
msgstr "Mudanza"

#: contracts/models/contract.py:437
msgid "Number of normal boxes"
msgstr "Número de cajas de mudanza"

#: contracts/models/contract.py:438
msgid "Number of wardrobe boxes"
msgstr "Número de cajas armario"

#: contracts/models/contract.py:439
msgid "Number of seal rolls"
msgstr "Número de rollos de precinto"

#: contracts/models/contract.py:440
msgid "Units of bubble paper"
msgstr "Unidades de papel burbuja"

#: contracts/models/contract.py:443
msgid "Boxes"
msgstr "Cajas"

#: contracts/models/item.py:9
msgid "Object description provided in contract"
msgstr "Descripción del objeto dada en el contrato"

#: contracts/models/item.py:21
msgid "Item dimensions in the form of WxHxD"
msgstr "Dimensiones del objeto de la forma WxHxD"

#: contracts/models/item.py:40
msgid "Object barcode tag id"
msgstr "Código de barras del objeto"

#: contracts/models/item.py:41
msgid "Delivery date for this item"
msgstr "Fecha de entrega del objeto"

#: contracts/models/item.py:42
msgid "Pick up date for this item"
msgstr "Fecha de recogida del objeto"

#: contracts/models/item.py:52
msgid "Additional comment for items, added thru customer portal"
msgstr "Comentario adicional añadido a través del portal de clientes"

#: contracts/models/item.py:56
msgid "Temporal id used by the app before creating the item in db"
msgstr ""

#: contracts/models/orders.py:22
msgid "Street level"
msgstr "Pie de calle"

#: contracts/models/orders.py:23 contracts/service_templates/contracts.py:38
msgid "Stairs"
msgstr "Escaleras"

#: contracts/models/orders.py:24
msgid "Small lift"
msgstr "Ascensor pequeño"

#: contracts/models/orders.py:25
msgid "Large lift"
msgstr "Ascensor grande"

#: contracts/models/orders.py:29
msgid "Order date"
msgstr "Fecha de solicitud"

#: contracts/models/orders.py:32
msgid "Access type"
msgstr "Tipo de acceso"

#: contracts/models/orders.py:34
#, fuzzy
#| msgid "Additional comment for items, added thru customer portal"
msgid "Additional comments from the user"
msgstr "Comentario adicional añadido a través del portal de clientes"

#: contracts/models/orders.py:38
#, fuzzy
#| msgid "Chargebee"
msgid "Creation date"
msgstr "Chargebee"

#: contracts/models/service.py:24
msgid "Time slot"
msgstr "Horario"

#: contracts/models/service.py:25 contracts/service_templates/contracts.py:182
msgid "Inventory"
msgstr "Inventario"

#: contracts/models/service.py:26
msgid "Billing cycle"
msgstr "Facturación"

#: contracts/models/service.py:27
msgid "Billing cycle notes"
msgstr "Notas sobre la facturación"

#: contracts/models/service.py:28
msgid "Warehouse location"
msgstr "Ubicación en almacén"

#: contracts/models/service.py:29 contracts/serializers/contract.py:44
msgid "Hired space"
msgstr "Espacio contratado"

#: contracts/models/service.py:30
msgid "Source address"
msgstr "Dirección de origen"

#: contracts/models/service.py:31
msgid "Moving address"
msgstr "Dirección de destino"

#: contracts/models/service.py:32
msgid "Material"
msgstr "Material"

#: contracts/models/service.py:33 contracts/service_templates/boxes.py:55
#: contracts/service_templates/contracts.py:166
msgid "Address"
msgstr "Dirección"

#: contracts/models/service.py:34
msgid "Additional information"
msgstr "Información adicional"

#: contracts/models/service.py:35 contracts/service_templates/contracts.py:171
msgid "Service type"
msgstr "Tipo de servicio"

#: contracts/models/service.py:207 contracts/models/service.py:229
msgid "Student pack 1"
msgstr "Pack 1"

#: contracts/models/service.py:218
msgid "Student pack 2"
msgstr "Pack 2"

#: contracts/models/service.py:325
msgid "Not started"
msgstr ""

#: contracts/models/service.py:326
msgid "In progress"
msgstr ""

#: contracts/models/service.py:327
msgid "Finished"
msgstr ""

#: contracts/models/service.py:334
msgid "Promo pack"
msgstr ""

#: contracts/models/service.py:335
msgid "Especial"
msgstr ""

#: contracts/models/service.py:339
#, fuzzy
#| msgid "Hubspot event"
msgid "Hubspot deal id"
msgstr "Evento de hubspot"

#: contracts/models/service.py:340
#, fuzzy
#| msgid "Email address"
msgid "Service address"
msgstr "Dirección de correo electrónico"

#: contracts/models/service.py:342
msgid "Destination address for movings"
msgstr ""

#: contracts/models/service.py:347 users/models.py:71
msgid "Phone number"
msgstr "Número de teléfono"

#: contracts/models/service.py:348
msgid "Space in m2"
msgstr ""

#: contracts/models/service.py:362
msgid "Location in the warehouse - Additional pickup / Deliveries"
msgstr ""

#: contracts/models/service.py:365
msgid "Hired space in m2 - Additional pickup"
msgstr ""

#: contracts/models/service.py:369
msgid "Event country which this service belongs to"
msgstr ""

#: contracts/models/service.py:372
#, fuzzy
#| msgid "Email address"
msgid "Service address latitude"
msgstr "Dirección de correo electrónico"

#: contracts/models/service.py:373
#, fuzzy
#| msgid "Email address"
msgid "Service address longitude"
msgstr "Dirección de correo electrónico"

#: contracts/models/service.py:376
msgid "Date of submission for the service"
msgstr ""

#: contracts/models/service.py:377
msgid "Date when the service has been updated (if it has been)"
msgstr "Fecha em que el servicio ha sido actualizado"

#: contracts/models/service.py:381
msgid "Promotion code visible on intranet events"
msgstr ""

#: contracts/models/service.py:394
msgid "Rider who has submitted this service"
msgstr ""

#: contracts/models/service.py:402
msgid "Residence associated with this service"
msgstr ""

#: contracts/models/service.py:419
msgid "Warehouse chosen by the rider thru the App"
msgstr ""

#: contracts/models/warehouse.py:22
msgid "Location"
msgstr "Ubicación"

#: contracts/models/warehouse.py:22
msgid "location_description"
msgstr ""
"El rider debe indicar en qué ubicación dentro del almacén se localizan los "
"objetos"

#: contracts/models/warehouse.py:23
msgid "Pallet"
msgstr "Paléts"

#: contracts/models/warehouse.py:23
msgid "pallet_description"
msgstr ""
"El rider debe indicar el número de paléts empleados para almacenar los "
"objetos"

#: contracts/models/warehouse.py:24
msgid "Floor"
msgstr "Planta"

#: contracts/models/warehouse.py:24
msgid "floor_description"
msgstr ""
"Si el almacén tiene varias plantas, debe indicar en qué planta se ubican los "
"objetos"

#: contracts/models/warehouse.py:25 contracts/service_templates/movings.py:16
msgid "Pictures"
msgstr "Fotos"

#: contracts/models/warehouse.py:25
msgid "pictures_description"
msgstr ""
"El rider debe sacar al menos una foto del inventario paletizado en el almacén"

#: contracts/models/warehouse.py:33 contracts/models/warehouse.py:42
msgid ""
"Warehouse id used for moreapp compatibility. It will dropped in future "
"releases"
msgstr ""

#: contracts/models/warehouse.py:44
#, fuzzy
#| msgid "username"
msgid "Warehouse name"
msgstr "Nick de usuario"

#: contracts/models/warehouse.py:49 intranet/models/city.py:25
#: intranet/models/residence.py:23 intranet/models/residence.py:51
msgid "Used to lookup the name"
msgstr ""

#: contracts/models/warehouse.py:55
msgid "Warehouse building canonical name"
msgstr ""

#: contracts/models/warehouse.py:73
msgid "Excel template to notify the warehouses"
msgstr ""

#: contracts/models/warehouse.py:76
msgid ""
"If it is needed to calculate the warehouse differences. Internal usage only"
msgstr ""

#: contracts/models/warehouse.py:81
msgid ""
"If this warehouse can be used to book services. It is preferred this over "
"deleting it due to all implications may have delete it"
msgstr ""

#: contracts/receivers.py:22
#, python-brace-format
msgid "{prefix}pickup order for contract {contract}"
msgstr "{prefix} Solicitud de recogida del contrato {contract}"

#: contracts/receivers.py:60
#, python-brace-format
msgid "{prefix}delivery order for contract {contract}"
msgstr "{prefix} Solicitud de entrega del contrato {contract}"

#: contracts/serializers/delivery.py:41 contracts/serializers/delivery.py:43
#: contracts/serializers/delivery.py:45 contracts/serializers/delivery.py:47
#: contracts/serializers/delivery.py:49 contracts/serializers/pickup.py:86
#: contracts/serializers/pickup.py:88 contracts/serializers/pickup.py:90
#: contracts/serializers/pickup.py:92 contracts/serializers/pickup.py:94
msgid "This field is required"
msgstr "Este campo es requerido"

#: contracts/serializers/delivery.py:54 contracts/serializers/pickup.py:99
#, python-brace-format
msgid "Contract address with {id} does not exist"
msgstr "Dirección cuyo id es {id} no existe"

#: contracts/serializers/delivery.py:89
msgid "You cannot select an item which not belongs to contract"
msgstr "No puedes seleccionar un objeto que no pertenece a este contrato"

#: contracts/serializers/delivery.py:93
#, fuzzy
#| msgid "You cannot select an item which is already selected for pick up"
msgid "You cannot select an item which is already selected for delivery"
msgstr ""
"No puedes seleccionar un objeto que ya ha sido seleccionado para una recogida"

#: contracts/serializers/delivery.py:154 contracts/serializers/pickup.py:138
#, fuzzy
#| msgid "Invalid format"
msgid "Invalid contract id given"
msgstr "Formato incorrecto"

#: contracts/serializers/delivery.py:205 contracts/serializers/pickup.py:175
msgid "If is not provided, will return empty data."
msgstr ""

#: contracts/serializers/inventory.py:16
msgid ""
"If is not provided, will return empty data. In detailed views, this flag is "
"not required."
msgstr ""

#: contracts/serializers/inventory.py:68 contracts/serializers/service.py:81
msgid "Dummy value for the riders app business logic"
msgstr ""

#: contracts/serializers/service.py:186 contracts/serializers/service.py:248
msgid ""
"This is returned as a list if any to backward compatibility with the app"
msgstr ""

#: contracts/serializers/service.py:546
msgid ""
"Show riders who have already submitted the tracking information for this "
"service"
msgstr ""

#: contracts/serializers/warehouse_movement.py:68
msgid "Relocation requires exactly one placement"
msgstr ""

#: contracts/serializers/warehouse_movement.py:86
#: intranet/serializers/warehouse_movement.py:183
#, fuzzy
#| msgid "username"
msgid "Warehouse does not exist"
msgstr "Nick de usuario"

#: contracts/service_templates/base.py:90
#: contracts/service_templates/base.py:91
msgid "Pick up"
msgstr "Recogida"

#: contracts/service_templates/base.py:92
#: contracts/service_templates/base.py:93
msgid "Delivery"
msgstr "Entrega"

#: contracts/service_templates/base.py:96
#: contracts/service_templates/base.py:97
msgid "No inventory picked up"
msgstr "Ningún inventario recogido"

#: contracts/service_templates/base.py:98
#: contracts/service_templates/base.py:99
msgid "No inventory delivered"
msgstr "Ningún inventario entregado"

#: contracts/service_templates/base.py:100
msgid "No pictures taken"
msgstr "Sin fotos"

#: contracts/service_templates/boxes.py:12
#: contracts/service_templates/contracts.py:82
msgid "Seal"
msgstr "Precinto"

#: contracts/service_templates/boxes.py:13
#: contracts/service_templates/boxes.py:15
#: contracts/service_templates/boxes.py:17
#: contracts/service_templates/boxes.py:22
#: contracts/service_templates/boxes.py:24
#: contracts/service_templates/contracts.py:52
#: contracts/service_templates/contracts.py:58
#: contracts/service_templates/contracts.py:69
#: contracts/service_templates/contracts.py:71
#: contracts/service_templates/contracts.py:81
#: contracts/service_templates/contracts.py:83
#: contracts/service_templates/contracts.py:85
msgid "Units"
msgstr "Unidades"

#: contracts/service_templates/boxes.py:14
#: contracts/service_templates/contracts.py:84
msgid "Bubble paper"
msgstr "Papel burbuja"

#: contracts/service_templates/boxes.py:16
#: contracts/service_templates/contracts.py:80
msgid "Blanket"
msgstr "Mantas"

#: contracts/service_templates/boxes.py:21
#: contracts/service_templates/contracts.py:68
msgid "Moving boxes"
msgstr "Cajas de mudanza"

#: contracts/service_templates/boxes.py:21
#: contracts/service_templates/contracts.py:68
msgid "Moving box sales"
msgstr "Venta de cajas mudanza"

#: contracts/service_templates/boxes.py:23
#: contracts/service_templates/contracts.py:70
msgid "Wardrobe boxes"
msgstr "Cajas armario"

#: contracts/service_templates/boxes.py:23
#: contracts/service_templates/contracts.py:70
msgid "Wardrobe box sales"
msgstr "Venta de cajas armario"

#: contracts/service_templates/boxes.py:42
msgid "Box delivery"
msgstr "Albarán de entrega de material"

#: contracts/service_templates/boxes.py:44
#: contracts/service_templates/contracts.py:155
msgid "Contract data"
msgstr "Datos del contrato"

#: contracts/service_templates/boxes.py:45
#: contracts/service_templates/contracts.py:156
msgid "Contract identifier"
msgstr "Número de contrato"

#: contracts/service_templates/boxes.py:48
#: contracts/service_templates/contracts.py:159
msgid "Personal data"
msgstr "Datos personales"

#: contracts/service_templates/boxes.py:49
#: contracts/service_templates/contracts.py:160
msgid "First name"
msgstr "Nombre"

#: contracts/service_templates/boxes.py:50
#: contracts/service_templates/contracts.py:161
msgid "Last name"
msgstr "Apellidos"

#: contracts/service_templates/boxes.py:51
#: contracts/service_templates/contracts.py:162
msgid "National ID"
msgstr "DNI"

#: contracts/service_templates/boxes.py:54
#: contracts/service_templates/contracts.py:165
msgid "Date and address"
msgstr "Fecha y dirección"

#: contracts/service_templates/boxes.py:56
#: contracts/service_templates/contracts.py:167
msgid "Date"
msgstr "Fecha"

#: contracts/service_templates/boxes.py:58
msgid "Boxes service"
msgstr "Entrega de cajas"

#: contracts/service_templates/boxes.py:59
#: contracts/service_templates/contracts.py:187
msgid "Additional Services"
msgstr "Servicios adicionales"

#: contracts/service_templates/boxes.py:61
#: contracts/service_templates/contracts.py:189
msgid "Comment"
msgstr "Comentario"

#: contracts/service_templates/boxes.py:65
#: contracts/service_templates/contracts.py:193 riders/admin.py:376
msgid "Signature"
msgstr "Firmas"

#: contracts/service_templates/boxes.py:66
#: contracts/service_templates/contracts.py:195
msgid "Customer signature"
msgstr "Firma del cliente"

#: contracts/service_templates/boxes.py:67
#: contracts/service_templates/contracts.py:198
msgid "Company signature"
msgstr "Firma de la empresa "

#: contracts/service_templates/conditions.py:15
#: contracts/service_templates/conditions.py:52
#: contracts/service_templates/conditions.py:88
msgid "Terms of service"
msgstr "Términos del servicio"

#: contracts/service_templates/conditions.py:15
#: contracts/service_templates/conditions.py:52
#: contracts/service_templates/conditions.py:88
msgid "Legal conditions"
msgstr "Condiciones legales"

#: contracts/service_templates/conditions.py:17
#: contracts/service_templates/conditions.py:54
#: contracts/service_templates/conditions.py:90
msgid "General conditions"
msgstr "Condiciones generales"

#: contracts/service_templates/conditions.py:21
#: contracts/service_templates/conditions.py:58
#, python-brace-format
msgid "conditions_1 {email}"
msgstr ""
"De acuerdo con lo establecido en el Reglamento General de Protección de "
"Datos 679/2016 y en la Ley Orgánica 3/2018 de 5 de Diciembre de Protección "
"de Datos, le informamos que los datos que nos facilite serán incorporados a "
"una base de datos titularidad de BOX2BOX, S.L, con N.I.F B87985115 y "
"domicilio en C/ Núñez de Balboa, 120-28006 Madrid (Cink Coworking), con la "
"finalidad del mantenimiento y gestión de relaciones comerciales y "
"administrativas. La base jurídica del tratamiento es el cumplimiento de la "
"legislación fiscal, mercantil y contable. No se prevén cesiones y/o "
"transferencias internacionales de datos excepto la sautorizadas por ley o "
"las que autorice con su consentimiento expreso. Puede ejercer sus derechos "
"de acceso, rectificación, supresión (derecho al olvido), limitación de "
"tratamiento, portabilidad de los datos, oposición, y a no ser objeto de "
"decisiones automatizadas dirigiéndose mediante correo postal a BOX2BOX, S.L, "
"C/ Núñez de Balboa,120-28006 Madrid (Cink Coworking) o bien por email <a "
"href='mailto:{email}'>{email}</a> indicando como Asunto “Derechos Ley "
"Protección de Datos”, y adjuntando fotocopia de su DNI. Además, le "
"recordamos que puede dirigirse a la Autoridad de Control en materia de "
"Protección de Datos competente (AEPD, en España) para obtener información "
"adicional o presentar una reclamación."

#: contracts/service_templates/conditions.py:25
#: contracts/service_templates/conditions.py:62
#: contracts/service_templates/conditions.py:96
msgid "title_2"
msgstr "PRIMERA - Objeto del contrato y descripción del servicio"

#: contracts/service_templates/conditions.py:25
#: contracts/service_templates/conditions.py:62
#: contracts/service_templates/conditions.py:96
msgid "conditions_2"
msgstr ""
"El servicio de traslado y almacenamiento contratado se define como el "
"traslado o acarreo desde el domicilio de recogida que se estipule a nuestros "
"centros de almacenaje (utilizando uno o varios modos de transporte) de "
"mobiliario, mercancías o bultos (en adelante, mercancía). La EMPRESA dispone "
"de varios centros de almacenaje ubicados en distintas localizaciones, por lo "
"que el destino de la mercancía y su almacenaje dependerá de la "
"disponibilidad de trasteros aptos para ello en uno u otro centro (esto no "
"supondrá en ningún caso diferencia en el coste a asumir por el CLIENTE). Así "
"mismo, el servicio incluye el posterior traslado o acarreo desde nuestros "
"centros de almacenaje al domicilio de entrega que se determine en el momento "
"en el que el CLIENTE indique y siempre dentro de los límites temporales de "
"la duración del contrato. El servicio se entenderá cumplido en el momento de "
"la entrega de la mercancía objeto del contrato en el domicilio de entrega, "
"en las condiciones pactadas.<br>"

#: contracts/service_templates/conditions.py:26
#: contracts/service_templates/conditions.py:63
#: contracts/service_templates/conditions.py:97
msgid "title_3"
msgstr "SEGUNDA"

#: contracts/service_templates/conditions.py:26
#: contracts/service_templates/conditions.py:63
#: contracts/service_templates/conditions.py:97
msgid "conditions_3"
msgstr ""
"Las prestaciones contratadas consistirán en las referenciadas en la primera "
"condición general, así como todas aquellas que se ajusten a la categoría del "
"servicio convenida con el CLIENTE y que se especificarán en las condiciones "
"particulares de este contrato de existir.<br> "

#: contracts/service_templates/conditions.py:27
#: contracts/service_templates/conditions.py:64
#: contracts/service_templates/conditions.py:98
msgid "title_4"
msgstr "TERCERA"

#: contracts/service_templates/conditions.py:27
#: contracts/service_templates/conditions.py:64
#: contracts/service_templates/conditions.py:98
msgid "conditions_4"
msgstr ""
"La EMPRESA efectuará una sola vez el traslado de los muebles desde el "
"domicilio del CLIENTE al lugar de almacenaje, así como el transporte de "
"estos al domicilio de entrega. Los desplazamientos suplementarios sucesivos "
"serán por cuenta del CLIENTE, quien abonará los gastos que se originen al "
"respecto (a partir de 9€). La ubicación de la mercancía será decisión de la "
"EMPRESA. Dependiendo de la disponibilidad logística, pueden surgir posibles "
"reubicaciones de mercancía entre las distintas bases de almacenaje.<br> No "
"es posible que el CLIENTE acceda al lugar de almacenaje de forma "
"individualizada, sólo nuestra plantilla tiene acceso y será la que, con "
"previa solicitud, entregará la mercancía requerida en el domicilio de "
"recogida inicial u otro.<br>"

#: contracts/service_templates/conditions.py:28
#: contracts/service_templates/conditions.py:65
#: contracts/service_templates/conditions.py:99
msgid "title_5"
msgstr "CUARTA"

#: contracts/service_templates/conditions.py:28
#: contracts/service_templates/conditions.py:65
#: contracts/service_templates/conditions.py:99
msgid "conditions_5"
msgstr ""
"A requerimiento del Transportista, el CLIENTE deberá informar a La EMPRESA "
"de las circunstancias relevantes  para la correcta ejecución de las "
"actividades en qué consiste el servicio contratado, así como de las "
"condiciones de acceso a los locales o viviendas para el personal y vehículos "
"(aparcamientos, altura de la vivienda, pasillos, escaleras, montacargas, "
"posibilidad de uso de ascensores, otros trabajos en curso en los respectivos "
"locales, etc.). En caso de que el edificio no disponga de ascensor y sea "
"necesario para llevar a cabo la prestación que los operarios suban/bajen "
"escaleras o escalones, la EMPRESA cobrará un suplemento que dependerá del "
"volumen de la mercancía a transportar y de la planta donde se realicen los "
"trabajos dada la dificultad añadida por dicha circunstancia.<br><br> <h5 "
"style='font-weight: 800'>4.1</h5><br> La EMPRESA no dispone de capacidad/"
"permisos para la realización de trabajo con cuerdas por ventana, grúa o "
"plataforma, por lo que en caso de que las circunstancias de la recogida o "
"entrega requieran de estos servicios, el CLIENTE deberá hacerse responsable "
"por su cuenta.<br><br> En caso de que sea necesario que los operarios suban "
"y bajen escaleras o escalones, BOX2BOX cobrará un suplemento que dependerá "
"del volumen de la mercancía a transportar y de la planta en la que se halle "
"ubicada la mercancía. El suplemento es de 5€ (IVA incluido) por cada m2 de "
"mercancía transportada multiplicado por cada planta subida o bajada.<br><br> "
"Se considera que para efectuar la carga y/o descarga de los vehículos, estos "
"deben poderse situar en la puerta del lugar de recogida y el de entrega, o "
"como máximo a una distancia no superior a 50 metros a pie, pero si por "
"cualquier circunstancia (estrechez de la calle o avenidas, aparcamiento "
"limitado y prohibido, etc.), no se consiguiera la citada colocación del "
"vehículo y ello implicara un incremento de mano de obra o el uso de medios "
"adicionales de transporte (camionetas u otros elementos aún más ligeros), "
"los gastos que ocasionarán estos servicios complementarios serían de cuenta "
"del CLIENTE (mínimo 50€).<br><br> Tarifas según origen y destino: misma "
"localidad. Si existiera un suplemento en el servicio de recogida, este se "
"aplicará de igual forma en el servicio de entrega.<br><br> Tarifas según "
"origen y destino: punto de recogida y punto de destino no coincidentes. "
"Cuando las localidades de origen y destino fueran distintas entre sí y caso "
"alguna esté fuera del área de servicio de cada ciudad donde se almacene la "
"mercancía (Madrid, Barcelona, Valencia, Málaga o Sevilla), el servicio de "
"desplazamiento tendrá un incremento en el coste de 0,90 euros por kilómetro "
"adicional recorrido (tomando como referencia de origen el centro de la "
"ciudad donde se desarrolla el servicio). Todo ello a excepción de aquellas "
"localidades que se encuentren contempladas dentro del área de servicio que "
"podrá consultar en nuestra página web.<br><br> Otros suplementos: <ul "
"style='list-style-type: disc; padding-left: 32px'><li>Desmontaje y montaje: "
"60€/hora</li><li>Suplemento de aparcamiento: Min 50€ (salvo zonas "
"restringidas o peatonales)</li><li>Suplemento viaje adicional: Min. 100€ "
"(independiente a contrato)</li><li>Punto limpio: Min. 35€ (aumentar en "
"función de volumen)</li><li>Venta de caja: 3€/unidad</li><li>Embalaje de "
"caja: 7€/unidad</li><li>Venta de caja-armario: 8€/unidad</li><li>Embalaje de "
"caja-armario: 12€/unidad</li><li>Suplemento de escaleras: 5€ por m2/piso</"
"li><li>Suplemento de kilometraje: 0,90€/km (ida y vuelta)</li></ul>"

#: contracts/service_templates/conditions.py:29
#: contracts/service_templates/conditions.py:66
#: contracts/service_templates/conditions.py:100
msgid "title_6"
msgstr "QUINTA - Pago y otros gastos"

#: contracts/service_templates/conditions.py:29
#: contracts/service_templates/conditions.py:66
#: contracts/service_templates/conditions.py:100
msgid "conditions_6"
msgstr ""
"El Transportista se compromete a efectuar el traslado y el almacenaje de las "
"mercancías relacionadas en el inventario que ambas partes firman, en el "
"precio y los términos pactados en las condiciones particulares, "
"incrementando, en su caso, con los gastos a los que se refiere la condición "
"CUARTA.<br><br> Correrá a cuenta del CLIENTE las eventuales formalidades "
"administrativas necesarias para ejecutar la mudanza.<br><br> El pago de toda "
"clase de tasas, derechos de aduanas y demás tributos, certificados de origen "
"o de traslado de residencia, gastos consulares, transbordos, etc., "
"necesarios para la ejecución de la mudanza, no están incluidos en el precio "
"del contrato y vendrán a incrementar este. El Transportista viene obligado a "
"justificar documentalmente estos gastos al CLIENTE.<br><br> Salvo pacto en "
"contrario, el pago del servicio se efectuará del siguiente modo: <ul "
"style='list-style-type: disc; padding-left: 32px'> <li>Se efectúa una "
"reserva de nuestros servicios abonándose una señal variable de mínimo 30€ "
"(según el servicio contratado) con una tarjeta de crédito/débito (vía email "
"se envía al CLIENTE un enlace para que introduzca los datos de tarjeta a "
"través de un sistema de verificación de seguridad).</li> <li>Una vez se haya "
"llevado a cabo la recogida se cobra al CLIENTE por los servicios prestados "
"(transporte de recogida, suplementos y primera mensualidad) realizándose un "
"cargo en la tarjeta informada descontando la señal ya abonada.</li> <li>Las "
"mensualidades siguientes se cobran de igual forma, mediante cobro por "
"tarjeta. El pago se efectuará por adelantado cada mes que se haga uso del "
"centro de almacenaje la tarifa aplicable al servicio contratado realizándose "
"el cargo en cuenta pasados 30 o 31 días (según el caso). </li><li>El "
"servicio de almacenamiento se renovará automáticamente y de manera sucesiva "
"al vencimiento de cada mes, semestre o año, según corresponda, salvo que el "
"CLIENTE cancele la renovación del Servicio de almacenamiento con 15 días "
"antes de su vencimiento (para las Cuotas mensuales).</li> <li>La EMPRESA se "
"guarda el derecho a incrementar el coste de la mensualidad y/o transporte "
"una vez pasado los 6 primeros meses de contrato y con un máximo porcentaje "
"del 15% unilateralmente. Dicho aumento se justifica por los costes internos "
"que puedan variar durante el transcurso del contrato. Esta modificación, no "
"superará una frecuencia de dos veces al año.</li> </ul> Finalmente, cuando "
"se realice la entrega se abonará mediante cargo en la tarjeta de crédito o "
"de débito informada lo correspondiente a los gastos de entrega y los "
"suplementos. La EMPRESA entregará al CLIENTE, recibo de las cantidades "
"percibidas, y finalizada la mudanza emitirá factura, en los términos "
"previstos en el R.D. 1496/2003, por el que se aprueba el Reglamento por el "
"que se regulan las obligaciones de facturación, y se modifica el Reglamento "
"del Impuesto sobre el valor añadido.<br> "

#: contracts/service_templates/conditions.py:30
#: contracts/service_templates/conditions.py:67
#: contracts/service_templates/conditions.py:101
msgid "title_7"
msgstr "SEXTA - Duración del contrato"

#: contracts/service_templates/conditions.py:30
#: contracts/service_templates/conditions.py:67
#: contracts/service_templates/conditions.py:101
msgid "conditions_7"
msgstr ""
"La duración mínima del contrato será de 3 meses a partir del día de la "
"recogida de los enseres del CLIENTE. El servicio de almacenamiento se "
"renovará automáticamente y de manera sucesiva al vencimiento de cada mes o "
"año, según corresponda, salvo que el CLIENTE cancele la renovación del "
"servicio de almacenamiento 15 días antes de su vencimiento.<br> Para "
"contratos de 6 o 12 meses con pago mes a mes, el CLIENTE puede optar a un "
"descuento en la mensualidad bajo el compromiso de permanencia por un periodo "
"de 6 o 12 meses. En caso de que el CLIENTE no cumpla este compromiso, deberá "
"liquidar las mensualidades restantes hasta completar el periodo mínimo "
"acordado, sobre el cual se aplicó el descuento. <br> Una vez finalizado el "
"primer compromiso de estancia, el contrato se renovará automáticamente por "
"el mismo periodo acordado inicialmente. Sin embargo, en esta renovación, el "
"CLIENTE tiene la opción de terminar el contrato antes de finalizar el nuevo "
"periodo completo. Si decide terminar anticipadamente, deberá reembolsar el "
"descuento disfrutado en las mensualidades transcurridas del periodo sucesivo."

#: contracts/service_templates/conditions.py:31
#: contracts/service_templates/conditions.py:68
#: contracts/service_templates/conditions.py:102
msgid "title_8"
msgstr "SÉPTIMA - Incumplimiento de la obligación de pago por parte del CLIENTE"

#: contracts/service_templates/conditions.py:31
#: contracts/service_templates/conditions.py:68
#: contracts/service_templates/conditions.py:102
msgid "conditions_8"
msgstr ""
"El CLIENTE debe mantener la información de pago actualizada. En caso de que "
"la tarjeta caduque, La EMPRESA enviará al CLIENTE una notificación para su "
"actualización, debiendo este corregir la situación a la mayor brevedad "
"posible.<br><br> En caso de impago o devolución del recibo domiciliado "
"correspondiente a una mensualidad, se aplicará un recargo del 15% sobre la "
"tarifa pactada por cada mes en el que se produzca dicha incidencia. Para "
"resolver la situación de impago, La EMPRESA podrá trasladar los datos del "
"CLIENTE a una agencia de recobro.<br><br> Si el CLIENTE no regulariza el "
"pago en el plazo máximo de dos (2) meses desde el requerimiento efectuado "
"por La EMPRESA, esta podrá optar por resolver el contrato de forma "
"unilateral. En tal caso, las mercancías almacenadas se considerarán "
"abandonadas, en virtud de lo dispuesto en el artículo 460 del Código Civil, "
"al entenderse que el CLIENTE ha dejado voluntariamente de ejercer el poder "
"de hecho sobre las mismas.<br><br> A partir de ese momento, La EMPRESA podrá "
"disponer de dichos bienes según estime conveniente, sin más limitaciones que "
"las establecidas por la legislación aplicable.<br> "

#: contracts/service_templates/conditions.py:32
#: contracts/service_templates/conditions.py:69
#: contracts/service_templates/conditions.py:103
msgid "title_9"
msgstr "OCTAVA - Responsabilidades"

#: contracts/service_templates/conditions.py:32
#: contracts/service_templates/conditions.py:69
#: contracts/service_templates/conditions.py:103
msgid "conditions_9"
msgstr ""
"El CLIENTE declara bajo su responsabilidad que las mercancías bultos o "
"mobiliario que son objeto del traslado y almacenaje son mercancías "
"generales, de curso legal, del mismo modo declara que no son productos "
"inflamables, explosivos ni productos alimentarios perecederos. Declara así "
"mismo que es conocedor del contenido de los bultos, mercancías o mobiliarios "
"almacenados y que ostenta responsabilidad plena sobre ellos.<br><br> Cuando "
"el CLIENTE nos facilite las mercancías embaladas en una caja/bolsa/mochila/"
"maleta o similares cerrada y la EMPRESA Transportista no conozca su "
"contenido, está no se hará responsable de contenidos perdidos o dañados."
"<br><br> Recomendamos no transportar y guardar televisores u otros objetos "
"electrónicos con las mismas características sin su caja original. El "
"transporte y la manipulación de estos artículos conlleva ciertos riesgos que "
"no se pueden evitar si el embalaje empleado no es el propio del artículo. Si "
"el CLIENTE decide incluir el televisor / objetos electrónicos en el "
"inventario sin proporcionar la caja original de los mismos, la EMPRESA no se "
"hace responsable de posibles daños producidos en transporte o almacenaje."
"<br><br> El CLIENTE es responsable de proteger la mercancía adecuadamente y "
"que el embalaje utilizado es correcto, respondiendo de aquél que sea "
"inadecuado, defectuoso o mal empleado, ya causé daño o perjuicio a las "
"mercancías, ya a los equipos de manipulación o medios de transporte, ya a "
"cualesquiera otros bienes o personas. Se pone a disposición del CLIENTE por "
"parte de BOX2BOX S.L nuestro propio servicio de embalaje cuya tarifa será a "
"partir de 10 euros (a razón del espacio en metros cuadrados). En caso de que "
"el CLIENTE no optara ni por uno ni por otro sistema de embalaje, la "
"mercancía no estará cubierta por nuestro seguro.<br> "

#: contracts/service_templates/conditions.py:33
#: contracts/service_templates/conditions.py:70
#: contracts/service_templates/conditions.py:104
msgid "title_10"
msgstr "NOVENA - Inventario"

#: contracts/service_templates/conditions.py:33
#: contracts/service_templates/conditions.py:70
#: contracts/service_templates/conditions.py:104
msgid "conditions_10"
msgstr ""
"La EMPRESA se compromete a elaborar un inventario de los muebles y enseres "
"objeto de la mudanza, entregando copia sellada y firmada por persona "
"responsable de la EMPRESA, al CLIENTE. Previamente a la recogida, El CLIENTE "
"hará declaración detallada de los objetos que puedan tener un valor "
"particular, siendo de obligado cumplimiento advertir sobre aquellos cuyo "
"valor unitario supere la cantidad de 500 euros. Además, queda prohibido el "
"almacenaje y transporte de aquellos bienes de carácter artístico, histórico "
"o de colección.<br><br> Asimismo, cuando a juicio de la EMPRESA "
"Transportista exista la posibilidad de deterioro de los bienes objeto de la "
"mudanza, bien por encontrarse en mal estado o porque deban ser sometidos a "
"manipulaciones que puedan entrañar peligro, se expresará claramente esta "
"circunstancia en el inventario, con declinación, en su caso, de "
"responsabilidad por parte de la EMPRESA Transportista.<br> "

#: contracts/service_templates/conditions.py:34
#: contracts/service_templates/conditions.py:71
#: contracts/service_templates/conditions.py:105
msgid "title_11"
msgstr "DÉCIMA - Seguro sobre las mercancías"

#: contracts/service_templates/conditions.py:34
#: contracts/service_templates/conditions.py:71
#: contracts/service_templates/conditions.py:105
msgid "conditions_11"
msgstr ""
"La EMPRESA se compromete a asegurar las mercancías transportadas y "
"reflejadas en el inventario por un valor total de 3.000 euros máximo. Así "
"mismo, se aplica un valor máximo de 500€ por objeto/mueble/pieza.<br><br> Si "
"la mercancía, que fuera a ser desplazada, almacenada y/o manipulada, tuviera "
"un valor total superior a 3.000 euros, el CLIENTE tendrá la opción de "
"aportar seguro privado que cubra el exceso del valor de la misma. La EMPRESA "
"Transportista, informará al CLIENTE del nombre de la compañía aseguradora "
"con la que se haya suscrito el seguro y del número de póliza.<br> "

#: contracts/service_templates/conditions.py:35
#: contracts/service_templates/conditions.py:72
#: contracts/service_templates/conditions.py:106
msgid "title_12"
msgstr "UNDÉCIMA - Recepción de la mudanza"

#: contracts/service_templates/conditions.py:35
#: contracts/service_templates/conditions.py:72
#: contracts/service_templates/conditions.py:106
msgid "conditions_12"
msgstr ""
"Concluida la mudanza, el CLIENTE, deberá firmar al Transportista copia del "
"inventario, a los efectos de dar su conformidad sobre la recepción de la "
"totalidad de muebles, enseres y cajas que fueron objeto de la mudanza. Una "
"vez que el CLIENTE reciba las mercancías y, con ello, la prestación del "
"servicio llegue a su fin (total o parcialmente, en su caso), el CLIENTE "
"dispondrá de un plazo de 48 horas, para reclamar a la EMPRESA Transportista "
"los daños en muebles, mercancías o bultos que haya podido constatar. "
"Transcurrido dicho plazo no se admitirá reclamación alguna.<br><br> En caso "
"de que el CLIENTE solicite la recogida de sus enseres directamente en las "
"instalaciones, con un transportista ajeno a La EMPRESA, deberá notificar a "
"la persona responsable de dicho transporte la obligación de verificar antes "
"de la carga posibles desperfectos. Una vez haya salido todo del almacén, La "
"EMPRESA no se hace responsable de futuras reclamaciones pues se entiende que "
"puedan derivar del transporte ajeno a La EMPRESA. <br>"

#: contracts/service_templates/conditions.py:37
#: contracts/service_templates/conditions.py:74
#: contracts/service_templates/conditions.py:108
msgid "title_13"
msgstr "DUODÉCIMA - Anulación del contrato y aplazamiento de su ejecución"

#: contracts/service_templates/conditions.py:38
#: contracts/service_templates/conditions.py:75
#, python-brace-format
msgid "conditions_13 {email}"
msgstr ""
"La cancelación o aplazamiento del servicio de traslado de muebles y enseres "
"por indicación del CLIENTE con un preaviso inferior a 72 horas dará lugar a "
"una indemnización a favor de la EMPRESA de un importe de 90€ (IVA INCLUIDO) "
"si se trata de la recogida inicial de las pertenencias del CLIENTE. Para "
"otros servicios de transporte dicha indemnización se fija en lo equivalente "
"al coste de desplazamiento y recursos logísticos que se hayan puesto a "
"disposición para llevar a término el desplazamiento necesario. Si la "
"notificación se produce con más de 72 horas laborables de antelación a la "
"fecha pactada de inicio de la mudanza, no procederá indemnización por "
"ninguna de las partes.<br><br> La realización del servicio se acordará en "
"una franja horaria indicada por la EMPRESA. El CLIENTE deberá estar "
"disponible en el domicilio en dicho rango horario, de lo contrario, se "
"procederá a una penalización de 20€ pasados los primeros 20 minutos de "
"espera. A partir de 50 minutos, la penalización será de 50€. En el caso de "
"que El Transportista esté esperando y el CLIENTE no responda al teléfono/"
"telefonillo, se procederá al cobro de 90€ y a la cancelación inmediata de la "
"recogida/entrega (el Servicio).<br><br> Tras la contratación del servicio el "
"CLIENTE podrá cancelar y desistir de dicha contratación en un plazo máximo "
"de 14 días hábiles, con o sin causa justificada, sin ningún tipo de "
"penalización, siempre y cuando el Servicio no haya sido realizado. Si la "
"cancelación del servicio se produce dentro de las 72 horas anteriores a la "
"fecha en la que se haya programado la recogida de la mercancía, BOX2BOX "
"tendrá derecho a percibir el coste de desplazamiento, así como aquellos "
"gastos en los que haya incurrido para la ejecución del traslado programado, "
"y del que habrá sido informado previamente el CLIENTE (90€).<br><br> Para "
"ejercer el derecho de desistimiento, deberás notificárnoslo por correo "
"electrónico a <a href='mailto:{email}'>{email}</a> la decisión de desistir "
"del contrato mediante una declaración inequívoca.<br><br> Al email debes "
"adjuntar un archivo en el que se incluya la siguiente información: <br> "
"<span style='display:inline-block; width:100%;'>A la atención de BOX2BOX</"
"span> <span style='display:inline-block; width:100%;'>Por la presente le "
"comunico/comunicamos (*) que desisto de mi/desistimos de nuestro (*)  "
"contrato de compraventa del siguiente producto (*):</span><span "
"style='display:inline-block; width:100%; padding-left:32px;'>— pedido el (*)/"
"recibido el (*)</span><span style='display:inline-block; width:100%; padding-"
"left:32px;'>— nombre del consumidor o consumidores</span><span "
"style='display:inline-block; width:100%; padding-left:32px;'>— Dirección del "
"consumidor o de los consumidores</span><span style='display: inline-block; "
"width:100%; padding-left:32px;'>— Fecha</span> "

#: contracts/service_templates/conditions.py:42
#: contracts/service_templates/conditions.py:79
#: contracts/service_templates/conditions.py:111
msgid "title_14"
msgstr "DECIMOTERCERA - Comunicaciones"

#: contracts/service_templates/conditions.py:42
#: contracts/service_templates/conditions.py:79
#: contracts/service_templates/conditions.py:111
msgid "conditions_14"
msgstr ""
"Toda notificación u otra comunicación que una parte haya de hacer a la otra "
"conforme a este contrato,o en relación con el mismo, se efectuará por "
"escrito mediante cualquier medio que pruebe tanto su contenido como su "
"recepción por el destinatario indicado. Toda notificación realizada de este "
"modo surtirá efecto desde la fecha de su recepción."

#: contracts/service_templates/conditions.py:43
#: contracts/service_templates/conditions.py:112
msgid "title_15"
msgstr "DECIMOCUARTA - Fuero"

#: contracts/service_templates/conditions.py:43
#: contracts/service_templates/conditions.py:112
msgid "conditions_15"
msgstr ""
"Para la resolución de cualquier controversia que suscite entre las partes en "
"relación con la interpretación y aplicación del presente contrato, o de "
"alguna de las estipulaciones, serán competentes los Juzgados y Tribunales de "
"Madrid. "

#: contracts/service_templates/conditions.py:94
#, fuzzy
#| msgid "conditions_10"
msgid "conditions_1"
msgstr ""
"La EMPRESA se compromete a elaborar un inventario de los muebles y enseres "
"objeto de la mudanza, entregando copia sellada y firmada por persona "
"responsable de la EMPRESA, al CLIENTE. Previamente a la recogida, El CLIENTE "
"hará declaración detallada de los objetos que puedan tener un valor "
"particular, siendo de obligado cumplimiento advertir sobre aquellos cuyo "
"valor unitario supere la cantidad de 500 euros. Además, queda prohibido el "
"almacenaje y transporte de aquellos bienes de carácter artístico, histórico "
"o de colección.<br><br> Asimismo, cuando a juicio de la EMPRESA "
"Transportista exista la posibilidad de deterioro de los bienes objeto de la "
"mudanza, bien por encontrarse en mal estado o porque deban ser sometidos a "
"manipulaciones que puedan entrañar peligro, se expresará claramente esta "
"circunstancia en el inventario, con declinación, en su caso, de "
"responsabilidad por parte de la EMPRESA Transportista.<br> "

#: contracts/service_templates/conditions.py:109
msgid "conditions_13"
msgstr ""
"La cancelación o aplazamiento del servicio de traslado de muebles y enseres "
"por indicación del Cliente con unpreaviso inferior a 24 horas dará lugar a "
"una indemnización a favor de la Empresa Transportista de un importe de50€ "
"(IVA INCLUIDO) si se trata de la recogida inicial de las pertenencias del "
"cliente. Para otros servicios detransporte dicha indemnización se fija en lo "
"equivalente al coste de desplazamiento y recursos logísticos que sehayan "
"puesto a disposición para llevar a término el desplazamiento necesario. Si "
"la notificación se produce conmás de 24 horas de antelación a la fecha "
"pactada de inicio de la mudanza, no procederá indemnización porninguna de "
"las partes.<br><br>Tras la contratación del servicio el Cliente podrá "
"cancelar y desistir de dicha contratación en un plazo máximo de14 días "
"hábiles, con o sin causa justificada, sin ningún tipo de penalización, "
"siempre y cuando el Servicio no hayasido realizado. Si la cancelación del "
"servicio se produce dentro de las 24 horas anteriores a la fecha en la que "
"sehaya programado la recogida de la mercancía, BOX2BOX tendrá derecho a "
"percibir el coste de desplazamiento, asícomo aquellos gastos en los que haya "
"incurrido para la ejecución del traslado programado, y del que habrá "
"sidoinformado previamente el CLIENTE.<br><br>Para ejercer el derecho de "
"desistimiento, deberás notificárnoslo por correo electrónico &nbsp; <a "
"href='mailto:<EMAIL>'><EMAIL></a> la "
"decisión de desistir del contrato mediante una declaración inequívoca."
"<br><br>Al email debes adjuntar un archivo en el que se incluya la siguiente "
"información:<br><br>  <span style='display:inline-block; width:100%;'>A la "
"atención de BOX2BOX</span>  <span style='display:inline-block; "
"width:100%;'>Por la presente le comunico/comunicamos (*) que desisto de mi/"
"desistimos de nuestro (*)  contrato de compraventa del siguiente producto "
"(*):</span>  <span style='display:inline-block; width:100%;'>— pedido el (*)/"
"recibido el (*)</span>  <span style='display:inline-block; width:100%;'>— "
"nombre del consumidor o consumidores</span>  <span style='display:inline-"
"block; width:100%;'>— Dirección del consumidor o de los consumidores</span>  "
"<span style='display:inline-block; width:100%;'>— Fecha</span>"

#: contracts/service_templates/conditions.py:113
msgid "title_16"
msgstr ""

#: contracts/service_templates/conditions.py:113
msgid "conditions_16"
msgstr ""

#: contracts/service_templates/conditions.py:114
msgid "title_17"
msgstr ""

#: contracts/service_templates/conditions.py:114
msgid "conditions_17"
msgstr ""

#: contracts/service_templates/conditions.py:115
msgid "title_18"
msgstr ""

#: contracts/service_templates/conditions.py:115
msgid "conditions_18"
msgstr ""

#: contracts/service_templates/conditions.py:116
msgid "title_19"
msgstr ""

#: contracts/service_templates/conditions.py:116
msgid "conditions_19"
msgstr ""

#: contracts/service_templates/conditions.py:117
#, fuzzy
#| msgid "title_2"
msgid "title_20"
msgstr "PRIMERA - Objeto del contrato y descripción del servicio"

#: contracts/service_templates/conditions.py:117
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_20"
msgstr ""
"El servicio de traslado y almacenamiento contratado se define como el "
"traslado o acarreo desde el domicilio de recogida que se estipule a nuestros "
"centros de almacenaje (utilizando uno o varios modos de transporte) de "
"mobiliario, mercancías o bultos (en adelante, mercancía). La EMPRESA dispone "
"de varios centros de almacenaje ubicados en distintas localizaciones, por lo "
"que el destino de la mercancía y su almacenaje dependerá de la "
"disponibilidad de trasteros aptos para ello en uno u otro centro (esto no "
"supondrá en ningún caso diferencia en el coste a asumir por el CLIENTE). Así "
"mismo, el servicio incluye el posterior traslado o acarreo desde nuestros "
"centros de almacenaje al domicilio de entrega que se determine en el momento "
"en el que el CLIENTE indique y siempre dentro de los límites temporales de "
"la duración del contrato. El servicio se entenderá cumplido en el momento de "
"la entrega de la mercancía objeto del contrato en el domicilio de entrega, "
"en las condiciones pactadas.<br>"

#: contracts/service_templates/conditions.py:118
#, fuzzy
#| msgid "title_2"
msgid "title_21"
msgstr "PRIMERA - Objeto del contrato y descripción del servicio"

#: contracts/service_templates/conditions.py:118
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_21"
msgstr ""
"El servicio de traslado y almacenamiento contratado se define como el "
"traslado o acarreo desde el domicilio de recogida que se estipule a nuestros "
"centros de almacenaje (utilizando uno o varios modos de transporte) de "
"mobiliario, mercancías o bultos (en adelante, mercancía). La EMPRESA dispone "
"de varios centros de almacenaje ubicados en distintas localizaciones, por lo "
"que el destino de la mercancía y su almacenaje dependerá de la "
"disponibilidad de trasteros aptos para ello en uno u otro centro (esto no "
"supondrá en ningún caso diferencia en el coste a asumir por el CLIENTE). Así "
"mismo, el servicio incluye el posterior traslado o acarreo desde nuestros "
"centros de almacenaje al domicilio de entrega que se determine en el momento "
"en el que el CLIENTE indique y siempre dentro de los límites temporales de "
"la duración del contrato. El servicio se entenderá cumplido en el momento de "
"la entrega de la mercancía objeto del contrato en el domicilio de entrega, "
"en las condiciones pactadas.<br>"

#: contracts/service_templates/conditions.py:119
#, fuzzy
#| msgid "title_2"
msgid "title_22"
msgstr "PRIMERA - Objeto del contrato y descripción del servicio"

#: contracts/service_templates/conditions.py:119
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_22"
msgstr ""
"El servicio de traslado y almacenamiento contratado se define como el "
"traslado o acarreo desde el domicilio de recogida que se estipule a nuestros "
"centros de almacenaje (utilizando uno o varios modos de transporte) de "
"mobiliario, mercancías o bultos (en adelante, mercancía). La EMPRESA dispone "
"de varios centros de almacenaje ubicados en distintas localizaciones, por lo "
"que el destino de la mercancía y su almacenaje dependerá de la "
"disponibilidad de trasteros aptos para ello en uno u otro centro (esto no "
"supondrá en ningún caso diferencia en el coste a asumir por el CLIENTE). Así "
"mismo, el servicio incluye el posterior traslado o acarreo desde nuestros "
"centros de almacenaje al domicilio de entrega que se determine en el momento "
"en el que el CLIENTE indique y siempre dentro de los límites temporales de "
"la duración del contrato. El servicio se entenderá cumplido en el momento de "
"la entrega de la mercancía objeto del contrato en el domicilio de entrega, "
"en las condiciones pactadas.<br>"

#: contracts/service_templates/conditions.py:120
#, fuzzy
#| msgid "title_2"
msgid "title_23"
msgstr "PRIMERA - Objeto del contrato y descripción del servicio"

#: contracts/service_templates/conditions.py:120
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_23"
msgstr ""
"El servicio de traslado y almacenamiento contratado se define como el "
"traslado o acarreo desde el domicilio de recogida que se estipule a nuestros "
"centros de almacenaje (utilizando uno o varios modos de transporte) de "
"mobiliario, mercancías o bultos (en adelante, mercancía). La EMPRESA dispone "
"de varios centros de almacenaje ubicados en distintas localizaciones, por lo "
"que el destino de la mercancía y su almacenaje dependerá de la "
"disponibilidad de trasteros aptos para ello en uno u otro centro (esto no "
"supondrá en ningún caso diferencia en el coste a asumir por el CLIENTE). Así "
"mismo, el servicio incluye el posterior traslado o acarreo desde nuestros "
"centros de almacenaje al domicilio de entrega que se determine en el momento "
"en el que el CLIENTE indique y siempre dentro de los límites temporales de "
"la duración del contrato. El servicio se entenderá cumplido en el momento de "
"la entrega de la mercancía objeto del contrato en el domicilio de entrega, "
"en las condiciones pactadas.<br>"

#: contracts/service_templates/contracts.py:13
msgid "Furniture packaging"
msgstr "Embalaje de muebles"

#: contracts/service_templates/contracts.py:13
msgid "Furniture packaging service"
msgstr "Servicio de embalaje de muebles"

#: contracts/service_templates/contracts.py:17
msgid "Furniture assembly/disassembly"
msgstr "Montaje o desmontaje de muebles"

#: contracts/service_templates/contracts.py:18
msgid "Furniture assembly/disassembly service"
msgstr "Servicio de montaje/desmontaje de muebles"

#: contracts/service_templates/contracts.py:23
#: contracts/service_templates/contracts.py:30
msgid "Time spent"
msgstr "Tiempo invertido (horas)"

#: contracts/service_templates/contracts.py:36
msgid "Assembled/Disassembled furniture"
msgstr "Descripción"

#: contracts/service_templates/contracts.py:38
msgid "Stairs service"
msgstr "Servicio de escaleras"

#: contracts/service_templates/contracts.py:39
msgid "Floors"
msgstr "Plantas"

#: contracts/service_templates/contracts.py:43
msgid "Volume moved in m²"
msgstr "Volumen transportado en m²"

#: contracts/service_templates/contracts.py:49
msgid "Packaging of moving boxes"
msgstr "Embalaje de cajas mudanza"

#: contracts/service_templates/contracts.py:50
msgid "Packaging of moving boxes service"
msgstr "Servicio de embalaje de cajas mudanza"

#: contracts/service_templates/contracts.py:55
msgid "Packaging of wardrobe boxes"
msgstr "Embalaje de cajas armario"

#: contracts/service_templates/contracts.py:56
msgid "Packaging of wardrobe boxes service"
msgstr "Servicio de embalaje de cajas armario"

#: contracts/service_templates/contracts.py:59
msgid "Parked withing a range of 50m"
msgstr "Distancia superior a 50m al domicilio"

#: contracts/service_templates/contracts.py:59
msgid "Parking"
msgstr "Aparcamiento"

#: contracts/service_templates/contracts.py:60
msgid "Kilometers"
msgstr "Kilómetros"

#: contracts/service_templates/contracts.py:64
msgid "Distance"
msgstr "Distancia"

#: contracts/service_templates/contracts.py:72
msgid "Clean spot"
msgstr "Punto limpio"

#: contracts/service_templates/contracts.py:76
msgid "Volume moved to clean spot"
msgstr "Volumen trasladado al punto limpio (m²)"

#: contracts/service_templates/contracts.py:86
msgid "Additional stop"
msgstr "Parada adicional"

#: contracts/service_templates/contracts.py:87
msgid "Comments"
msgstr "Comentario"

#: contracts/service_templates/contracts.py:150
#, python-brace-format
msgid "{type} delivery note"
msgstr "Albarán de {type}"

#: contracts/service_templates/contracts.py:174
msgid "Space in m²"
msgstr "Espacio en m²"

#: contracts/service_templates/contracts.py:183
msgid "Code"
msgstr "Código"

#: contracts/service_templates/contracts.py:183
msgid "Description"
msgstr "Descripción"

#: contracts/service_templates/movings.py:15
msgid "Moving service"
msgstr "Albarán de Mudanza"

#: contracts/utils.py:89
msgid "Madrid"
msgstr "Madrid"

#: contracts/utils.py:90
msgid "Porto"
msgstr "Oporto"

#: contracts/utils.py:91
msgid "Sevilla"
msgstr "Sevilla"

#: contracts/utils.py:92
msgid "Barcelona"
msgstr "Barcelona"

#: contracts/utils.py:93
msgid "Malaga"
msgstr "Málaga"

#: contracts/utils.py:94
msgid "Valencia"
msgstr "Valencia"

#: contracts/utils.py:95
msgid "Milano"
msgstr ""

#: contracts/utils.py:96
msgid "Paris"
msgstr "París"

#: contracts/utils.py:97
msgid "Lisboa"
msgstr "Lisboa"

#: contracts/utils.py:98
msgid "Queretaro"
msgstr ""

#: contracts/utils.py:99
msgid "Granada"
msgstr ""

#: contracts/utils.py:100
msgid "Salamanca"
msgstr ""

#: contracts/utils.py:101
msgid "Bilbao"
msgstr ""

#: contracts/utils.py:102
msgid "San Sebastian"
msgstr ""

#: contracts/utils.py:103
msgid "Pamplona"
msgstr ""

#: contracts/utils.py:121 payments/models/payments.py:57
msgid "Monthly"
msgstr "Mensual"

#: contracts/utils.py:122
msgid "Quarterly"
msgstr "Trimestral"

#: contracts/utils.py:123 payments/models/payments.py:58
msgid "Biannual"
msgstr "Semestral"

#: contracts/utils.py:124
msgid "Annual"
msgstr "Anual"

#: contracts/utils.py:131
msgid "Default"
msgstr ""

#: contracts/views/service.py:308
msgid "Accepted"
msgstr "Aceptado"

#: contracts/views/service.py:335
msgid "Released"
msgstr ""

#: contracts/views/service.py:352 contracts/views/service.py:369
#: riders/models/riders.py:48
msgid "Discarded"
msgstr ""

#: core/chargebee_errors.py:4
msgid "Invalid format"
msgstr "Formato incorrecto"

#: core/chargebee_errors.py:5
msgid "Payment cannot be created as the payment collection failed"
msgstr ""
"El pago no se ha podido crear debido a algún problema con el método de pago"

#: core/chargebee_errors.py:6
msgid "There's a problem adding the payment method"
msgstr "Ha habido un problema añadiendo el método de pago"

#: core/chargebee_errors.py:7 core/chargebee_errors.py:9
msgid "Unknown error in the payment gateway. Wait few minutes and try again"
msgstr ""
"Error desconocido devuelto por la pasarela de pago. Por favor, espera unos "
"minutos y vuélvelo a intentar"

#: core/chargebee_errors.py:8
msgid "Error processing the payment. Try again later"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: core/chargebee_errors.py:10
msgid "Unknown error. Please, wait few minutes and try again"
msgstr ""
"Error desconocido. Por favor, espera unos minutos y vuelve a intentarlo"

#: core/exceptions.py:36
#, fuzzy
#| msgid "Invalid format"
msgid "Invalid input."
msgstr "Formato incorrecto"

#: core/handlers.py:26
msgid "Not found"
msgstr ""

#: core/handlers.py:35
msgid ""
"It seems there is a conflict with your input data. Maybe is due to a "
"duplicated or deleted entry. Please, review your data and try again"
msgstr ""

#: core/handlers.py:45
msgid "There is a version conflict on update. Reload the data and try again"
msgstr ""

#: core/mixins.py:80
#, fuzzy
#| msgid "This field is required"
msgid "version is required"
msgstr "Este campo es requerido"

#: core/models/event_log.py:22
msgid "Unknown event"
msgstr "Evento desconocido"

#: core/models/event_log.py:23
msgid "Hubspot event"
msgstr "Evento de hubspot"

#: core/models/event_log.py:24
msgid "Moreapp event"
msgstr "Evento de moreapp"

#: core/models/event_log.py:25
#, fuzzy
#| msgid "Moreapp event"
msgid "Moreapp inventory event"
msgstr "Evento de moreapp"

#: core/models/event_log.py:26
#, fuzzy
#| msgid "Moreapp event"
msgid "Riderapp event"
msgstr "Evento de moreapp"
#: core/models/event_log.py:27
msgid "Chargebee event"
msgstr "Evento de chargebee"

#: core/models/event_log.py:28
#, fuzzy
#| msgid "Moreapp event"
msgid "Microservice event"
msgstr "Evento de moreapp"

#: core/models/event_log.py:29
#, fuzzy
#| msgid "Moreapp event"
msgid "Storage event"
msgstr "Evento de moreapp"

#: core/models/moloni.py:20 intranet/models/delivery.py:38
#, fuzzy
#| msgid "Empty email"
msgid "Empty"
msgstr "Ningún correo electrónico provisto"

#: core/models/moloni.py:21
#, fuzzy
#| msgid "Customer's address"
msgid "Customer"
msgstr "Dirección del cliente"

#: core/models/moloni.py:22 payments/admin.py:37
msgid "Invoice"
msgstr "Factura"

#: core/models/moloni.py:23
#, fuzzy
#| msgid "Credit"
msgid "Credit note"
msgstr "Crédito"

#: core/models/product.py:16
msgid "One time"
msgstr "Pago único"

#: core/models/product.py:34
msgid "For old odoo planes"
msgstr ""

#: core/models/temporary_deletion_entries.py:13
msgid "Used to retrieve the ids to be deleted"
msgstr ""

#: core/models/temporary_deletion_entries.py:14
msgid "Id to be deleted"
msgstr ""

#: core/models/temporary_deletion_entries.py:16
msgid "Table name to search the ids to delete"
msgstr ""

#: core/notifications.py:49
#, python-brace-format
msgid "Service {contract_id}"
msgstr ""

#: core/recursive_model.py:42
msgid ""
"If this FK is set means this is an old entry which is replaced by a new entry"
msgstr ""

#: core/recursive_model.py:54
msgid ""
"FK to improve lookups setting the most up-to-date entry related with this "
"object"
msgstr ""

#: core/serializers.py:27
msgid ""
"Required to get the access thru the API Gateway in production environment"
msgstr ""

#: core/utils.py:391
msgid "Yes"
msgstr "Sí"

#: core/utils.py:392
msgid "No"
msgstr "No"

#: core/views.py:105
msgid "X-Chunk-Index and X-Total-Chunks are required"
msgstr ""

#: core/views.py:123
msgid "Token is required on subsequent requests"
msgstr ""

#: core/views.py:132
msgid "No ids provided"
msgstr ""

#: core/views.py:151
#, fuzzy
#| msgid "Pick upPick up order created successfully"
msgid "All chunks processed successfully"
msgstr "Solicitud de entrega creada con éxito"

#: core/views.py:153
msgid "Chunk processed"
msgstr ""

#: intranet/admin.py:66 riders/models/riders.py:43
msgid "Sent"
msgstr ""

#: intranet/admin.py:100 riders/admin.py:366 users/admin.py:69
msgid "Personal info"
msgstr "Información personal"

#: intranet/admin.py:108 users/admin.py:83
msgid "User Image"
msgstr "Avatar"

#: intranet/admin.py:109
msgid "Misc"
msgstr ""

#: intranet/admin.py:110 riders/admin.py:374 users/admin.py:84
msgid "Important dates"
msgstr "Fechas importantes"

#: intranet/admin.py:111 riders/admin.py:377 users/admin.py:90
msgid "Permissions"
msgstr "Permisos"

#: intranet/admin.py:113 riders/admin.py:379 users/admin.py:92
msgid "Django Admin Permissions"
msgstr "Permisos de Django Admin"

#: intranet/admin.py:132 users/admin.py:120
msgid "Avatar"
msgstr "Avatar"

#: intranet/admin.py:260
msgid "Team"
msgstr ""

#: intranet/filters/calendars.py:23
#, fuzzy
#| msgid "Company name "
msgid "Bring calendars by name"
msgstr "Nombre de la empresa "

#: intranet/filters/calendars.py:33
msgid "Bring calendars from a specific city"
msgstr ""

#: intranet/filters/calendars.py:43
msgid "Bring calendars by user email"
msgstr ""

#: intranet/filters/calendars.py:53
msgid "Bring calendars by rider email"
msgstr ""

#: intranet/filters/calendars.py:63
msgid "Bring calendars that are created on remote"
msgstr ""

#: intranet/filters/calendars.py:73
msgid "Filter only calendars without ownership"
msgstr ""

#: intranet/filters/calendars.py:83
msgid "Filter only calendars which belong to a given user"
msgstr ""

#: intranet/filters/city.py:22
msgid "Bring cities by name"
msgstr ""

#: intranet/filters/city.py:32
msgid "Bring cities by created at date"
msgstr ""

#: intranet/filters/city.py:42
msgid "Bring cities by riders email"
msgstr ""

#: intranet/filters/residence.py:24
msgid "Filter residences by name"
msgstr ""

#: intranet/filters/residence.py:34 intranet/filters/residence.py:74
msgid "Filter residences by group"
msgstr ""

#: intranet/filters/residence.py:44
msgid "Filter residences by city"
msgstr ""

#: intranet/filters/residence.py:54
msgid "Filter residences by created at date"
msgstr ""

#: intranet/filters/residence.py:64
msgid "Filter residences by country"
msgstr ""

#: intranet/filters/residence_group.py:22
msgid "Filter residence groups by name"
msgstr ""

#: intranet/filters/residence_group.py:32
msgid "Filter residence groups by created at date"
msgstr ""

#: intranet/filters/reviews.py:24 intranet/filters/warehouse_movements.py:66
msgid "Bring placements from a specific city"
msgstr ""

#: intranet/filters/reviews.py:34
msgid "Bring placements by created at date"
msgstr ""

#: intranet/filters/reviews.py:44
msgid "Bring enabled systems only"
msgstr ""

#: intranet/filters/reviews.py:54
msgid "Bring specific systems only"
msgstr ""

#: intranet/filters/rider.py:24
msgid "Bring riders by first name or last name"
msgstr ""

#: intranet/filters/rider.py:34
msgid "Bring riders by email"
msgstr ""

#: intranet/filters/rider.py:44
msgid "Bring only active riders"
msgstr ""

#: intranet/filters/rider.py:54
msgid "Bring riders by date joined"
msgstr ""

#: intranet/filters/rider.py:64
msgid "Bring riders from a specific city"
msgstr ""

#: intranet/filters/rider.py:74
msgid "Bring riders from specific warehouses"
msgstr ""

#: intranet/filters/rider.py:84
msgid "Bring not disabled riders"
msgstr ""

#: intranet/filters/rider.py:94
msgid "Bring riders associated with a specific calendar"
msgstr ""

#: intranet/filters/service.py:23
#, fuzzy
#| msgid "Service type"
msgid "Bring services by type"
msgstr "Tipo de servicio"

#: intranet/filters/service.py:33
msgid "Bring services from a specific contract"
msgstr ""

#: intranet/filters/service.py:43
msgid "Bring services from a specific city"
msgstr ""

#: intranet/filters/service.py:53 intranet/filters/task.py:56
#, fuzzy
#| msgid "Moreapp event"
msgid "Bring services by event date"
msgstr "Evento de moreapp"

#: intranet/filters/service.py:63 intranet/filters/task.py:66
msgid "Bring services by a given rider"
msgstr ""

#: intranet/filters/service.py:73 intranet/filters/task.py:76
msgid "Bring services by riders email"
msgstr ""

#: intranet/filters/service.py:83 intranet/filters/task.py:86
msgid "Bring services by created at date"
msgstr ""

#: intranet/filters/service.py:93
msgid "Bring services by updated at date"
msgstr ""

#: intranet/filters/service.py:103 intranet/filters/task.py:96
msgid "Bring services by status"
msgstr ""

#: intranet/filters/service.py:113
msgid "Bring services from specific warehouses"
msgstr ""

#: intranet/filters/service.py:123 intranet/filters/task.py:106
msgid "Bring services from specific calendars"
msgstr ""

#: intranet/filters/service.py:133 intranet/filters/task.py:116
msgid "Bring services with or without events"
msgstr ""

#: intranet/filters/service.py:143
msgid "Bring services from specific residences groups"
msgstr ""

#: intranet/filters/service.py:153
msgid "Bring services from specific residences"
msgstr ""

#: intranet/filters/task.py:46
msgid "Bring services by title"
msgstr ""

#: intranet/filters/user.py:24
msgid "Filter by another backoffice allowed users"
msgstr ""

#: intranet/filters/user.py:30
msgid "Exclude user from query"
msgstr ""

#: intranet/filters/warehouse.py:24
#, fuzzy
#| msgid "username"
msgid "Bring warehouses by name"
msgstr "Nick de usuario"

#: intranet/filters/warehouse.py:34
msgid "Bring warehouses from a specific city"
msgstr ""

#: intranet/filters/warehouse.py:44
msgid "Bring warehouses by created at date"
msgstr ""

#: intranet/filters/warehouse.py:54
msgid "Bring warehouses by providers email"
msgstr ""

#: intranet/filters/warehouse.py:64
msgid "Bring warehouses by riders email"
msgstr ""

#: intranet/filters/warehouse.py:74
msgid "Bring current warehouses only"
msgstr ""

#: intranet/filters/warehouse.py:84
msgid "Bring warehouses which allows warehouse differences only"
msgstr ""

#: intranet/filters/warehouse.py:91
msgid "Bring warehouses with related FKs: calendars and riders"
msgstr ""

#: intranet/filters/warehouse.py:97
msgid "Bring providers with related FKs: user"
msgstr ""

#: intranet/filters/warehouse.py:103
msgid "Bring providers filtering by default field with related FKs: user"
msgstr ""

#: intranet/filters/warehouse_differences.py:24
msgid "Bring warehouse differences from specific warehouses"
msgstr ""

#: intranet/filters/warehouse_differences.py:34
msgid "Bring warehouse differences from a specific city"
msgstr ""

#: intranet/filters/warehouse_differences.py:44
msgid "Bring warehouse differences by number of results"
msgstr ""

#: intranet/filters/warehouse_differences.py:54
msgid "Bring warehouse differences created at date"
msgstr ""

#: intranet/filters/warehouse_movements.py:26
msgid "Bring placements from a specific contracts based on the pk"
msgstr ""

#: intranet/filters/warehouse_movements.py:36
msgid ""
"Bring placements from a specific contracts based on the contract_document_id"
msgstr ""

#: intranet/filters/warehouse_movements.py:46
msgid "Bring placements from specific warehouses"
msgstr ""

#: intranet/filters/warehouse_movements.py:56
msgid "Bring placements from a specific location"
msgstr ""

#: intranet/filters/warehouse_movements.py:76
msgid "Bring placements by sent at date"
msgstr ""

#: intranet/filters/warehouse_movements.py:86
msgid "Bring placements by number of pallets"
msgstr ""

#: intranet/filters/warehouse_movements.py:96
msgid "Bring current placements only"
msgstr ""

#: intranet/filters/warehouse_movements.py:106
msgid "Bring placements from a specific placement (i.e. recursive)"
msgstr ""

#: intranet/filters/warehouse_movements.py:116
msgid "Bring placements which belong to active contracts"
msgstr ""

#: intranet/filters/warehouse_user.py:24
msgid "Bring warehouses from specific warehouse users"
msgstr ""

#: intranet/filters/warehouse_user.py:34
msgid "Bring warehouse users by email"
msgstr ""

#: intranet/filters/warehouse_user.py:44
msgid "Bring warehouse users by created at date"
msgstr ""

#: intranet/models/budget_estimator.py:18 intranet/models/delivery.py:45
msgid "Attribute to keep the whole view data"
msgstr ""

#: intranet/models/budget_estimator.py:25
msgid "Inventory resume data to upload in hubspot"
msgstr ""

#: intranet/models/budget_estimator.py:27
msgid "If this budget estimation is in hubspot"
msgstr ""

#: intranet/models/budget_estimator.py:31
msgid "If this budget estimation is before Mexico refactoring"
msgstr ""

#: intranet/models/budget_estimator.py:37
msgid "Site where user is allowed to be from intranet"
msgstr ""

#: intranet/models/budget_estimator.py:46
msgid ""
"In order to publish the estimation in Hubspot, a valid contract is needed"
msgstr ""

#: intranet/models/calendar.py:12
#, fuzzy
#| msgid "Company name "
msgid "Calendar name"
msgstr "Nombre de la empresa "

#: intranet/models/calendar.py:13
msgid "Calendar id on google calendar"
msgstr ""

#: intranet/models/calendar.py:15
msgid "UI color to display this calendar in hexadecimal"
msgstr ""

#: intranet/models/calendar.py:45
msgid "Created by the user"
msgstr ""

#: intranet/models/calendar.py:47
msgid "Calendar linked to this event"
msgstr ""

#: intranet/models/calendar.py:61
msgid "Used to identify the kind of service"
msgstr ""

#: intranet/models/calendar.py:68
msgid "Blocked slot for a given service"
msgstr ""

#: intranet/models/calendar.py:157
msgid "If this rider must be shown as default in the event creation"
msgstr ""

#: intranet/models/city.py:15
msgid "City visible name"
msgstr ""

#: intranet/models/city.py:17
msgid "City canonical name"
msgstr ""

#: intranet/models/delivery.py:41
msgid "User requested delivery"
msgstr "Entrega solicitada por el cliente"

#: intranet/models/delivery.py:77 intranet/models/delivery.py:107
msgid "Precondition failed to build the excel"
msgstr ""

#: intranet/models/delivery.py:80
msgid "Empty data to build the excel file"
msgstr ""

#: intranet/models/delivery.py:83
#, fuzzy
#| msgid "Pick up/Delivery date"
msgid "Empty delivery date"
msgstr "Fecha de recogida/entrega"

#: intranet/models/delivery.py:110
msgid "User name is required to send the inventory email to Logwin warehouse"
msgstr ""

#: intranet/models/delivery.py:113 intranet/models/delivery.py:181
msgid ""
"Warehouse does not have a valid city and it is mandatory to set up the "
"sender on emails"
msgstr ""

#: intranet/models/delivery.py:122
#, python-brace-format
msgid ""
"Delivery email skipped temporarily because warehouse is not defined: {model}"
msgstr ""

#: intranet/models/delivery.py:128
msgid "There was an error building the excel to send it to the warehouse"
msgstr ""

#: intranet/models/delivery.py:145
#, python-brace-format
msgid "{request} - {contract_id}"
msgstr ""

#: intranet/models/delivery.py:178
msgid "Precondition failed to send the cancellation email"
msgstr ""

#: intranet/models/delivery.py:191
#, python-brace-format
msgid ""
"Cancellation of delivery email skipped temporarily because warehouse is not "
"defined: {model}"
msgstr ""

#: intranet/models/delivery.py:201
#, python-brace-format
msgid "{request} - {contract_id} - Canceled"
msgstr "{request} - {contract_id} - Cancelada"

#: intranet/models/global_settings.py:37
msgid "0.5"
msgstr ""

#: intranet/models/global_settings.py:38
msgid "1"
msgstr ""

#: intranet/models/global_settings.py:39
msgid "2"
msgstr ""

#: intranet/models/global_settings.py:40
msgid "3"
msgstr ""

#: intranet/models/global_settings.py:41
msgid "4"
msgstr ""

#: intranet/models/global_settings.py:42
msgid "5"
msgstr ""

#: intranet/models/global_settings.py:43
msgid "6"
msgstr ""

#: intranet/models/global_settings.py:44
msgid "7.5"
msgstr ""

#: intranet/models/global_settings.py:45
msgid "9"
msgstr ""

#: intranet/models/global_settings.py:46
msgid "12"
msgstr ""

#: intranet/models/global_settings.py:47
msgid "15"
msgstr ""

#: intranet/models/global_settings.py:48
msgid "20"
msgstr ""

#: intranet/models/global_settings.py:49
msgid "25"
msgstr ""

#: intranet/models/global_settings.py:50
msgid "30"
msgstr ""

#: intranet/models/global_settings.py:51
msgid "35"
msgstr ""

#: intranet/models/global_settings.py:52
msgid "40"
msgstr ""

#: intranet/models/global_settings.py:53
msgid "45"
msgstr ""

#: intranet/models/global_settings.py:54
msgid "50"
msgstr ""

#: intranet/models/global_settings.py:55
msgid "50+"
msgstr ""

#: intranet/models/global_settings.py:67
msgid "Global Settings"
msgstr ""

#: intranet/models/payment_reminder.py:24 intranet/models/warehouse_user.py:12
#: users/models.py:67
msgid "Email address"
msgstr "Dirección de correo electrónico"

#: intranet/models/payment_reminder.py:37
msgid "Used to avoid duplicated slack events to be handled"
msgstr ""

#: intranet/models/payment_reminder.py:65
#, python-brace-format
msgid "BOX2BOX - Your pickup is coming {date}"
msgstr "BOX2BOX - Tu recogida se está acercando {date}"

#: intranet/models/residence.py:13
msgid "Residence group name"
msgstr ""

#: intranet/models/residence.py:16
msgid "Group canonical name"
msgstr ""

#: intranet/models/residence.py:44
msgid "Residence visible name"
msgstr ""

#: intranet/models/residence.py:45
#, fuzzy
#| msgid "Email address"
msgid "Residence address"
msgstr "Dirección de correo electrónico"

#: intranet/models/reviews.py:25
msgid "Google"
msgstr ""

#: intranet/models/reviews.py:26
msgid "Trustpilot"
msgstr ""

#: intranet/models/reviews.py:37
msgid "Review link to send by email on contract signing"
msgstr ""

#: intranet/models/userproxy.py:22 riders/models/riders.py:209
msgid "User"
msgstr ""

#: intranet/models/userproxy.py:43
#, python-brace-format
msgid "{prefix} Activate your account to get access in the Backoffice portal"
msgstr "{prefix} Activa tu cuenta para tener acceso al Backoffice"

#: intranet/models/warehouse_differences.py:28
msgid "Pending"
msgstr "Pendiente"

#: intranet/models/warehouse_differences.py:29 payments/models/payments.py:67
msgid "Running"
msgstr ""

#: intranet/models/warehouse_differences.py:30 payments/models/payments.py:48
#: payments/models/payments.py:203
msgid "Failed"
msgstr "Fallado"

#: intranet/models/warehouse_differences.py:31
msgid "Success"
msgstr "Exitoso"

#: intranet/models/warehouse_differences.py:46
msgid "Warehouse difference between warehouse and our space"
msgstr ""

#: intranet/models/warehouse_differences.py:48
msgid "Sum of warehouse space"
msgstr ""

#: intranet/models/warehouse_differences.py:49
msgid "Sum of our space"
msgstr ""

#: intranet/models/warehouse_movement.py:35
msgid "Where the goods are located inside the building "
msgstr ""

#: intranet/models/warehouse_movement.py:37
#, fuzzy
#| msgid "Number of normal boxes"
msgid "Number of pallets occupied"
msgstr "Número de cajas de mudanza"

#: intranet/models/warehouse_movement.py:39
msgid "Floor where the goods are located"
msgstr ""

#: intranet/models/warehouse_movement.py:40
#, fuzzy
#| msgid "Invalid activation account URL"
msgid "Datetime when this tracking was sent"
msgstr "URL de activación de la cuenta inválida"

#: intranet/models/warehouse_movement.py:48
msgid ""
"Internal tracking id to avoid duplicated movements in case of the same "
"movement is handled twice or more times"
msgstr ""

#: intranet/models/warehouse_user.py:36
msgid "Check this if the warehouse user must be showed as default on events"
msgstr ""

#: intranet/models/warehouse_user.py:39
msgid "If this user must be notified when sending the moreapp email"
msgstr ""

#: intranet/serializers/budget_estimation.py:25
msgid "Fill in only if contract is null"
msgstr ""

#: intranet/serializers/budget_estimation.py:31
msgid ""
"If contract_document_id is filled in, it will have priority over this "
"attribute"
msgstr ""

#: intranet/serializers/budget_estimation.py:49
msgid "A valid contract is needed in order to create a budget estimation"
msgstr ""

#: intranet/serializers/calendar.py:218
msgid "You are trying to share this calendar with non allowed users"
msgstr ""

#: intranet/serializers/calendar.py:247
msgid ""
"You are trying to share this calendar or set as default users on this "
"calendar with non allowed users"
msgstr ""

#: intranet/serializers/calendar_scheduler.py:11
msgid "Title"
msgstr ""

#: intranet/serializers/city.py:44 intranet/serializers/city.py:67
#, fuzzy
#| msgid "An user with that email already exists."
msgid "The city already exists"
msgstr "Este correo electrónico ya existe."

#: intranet/serializers/event.py:294
msgid "Warehouse id required when pickup or delivery is selected"
msgstr ""

#: intranet/serializers/event.py:438
#, fuzzy
#| msgid "Payment cannot be created as the payment collection failed"
msgid "Start time cannot be greater than end time"
msgstr ""
"El pago no se ha podido crear debido a algún problema con el método de pago"

#: intranet/serializers/event.py:451
#, fuzzy
#| msgid "Delivery date for this item"
msgid "Riders to add to this team"
msgstr "Fecha de entrega del objeto"

#: intranet/serializers/event.py:484
msgid "Service, task or blank slot is required to create events"
msgstr ""

#: intranet/serializers/event.py:489 intranet/serializers/event.py:795
msgid "Only one from service, task and blank_slot can be specified"
msgstr ""

#: intranet/serializers/event.py:493
#, fuzzy
#| msgid "Payment cannot be created as the payment collection failed"
msgid "Task and city cannot be set at the same time"
msgstr ""
"El pago no se ha podido crear debido a algún problema con el método de pago"

#: intranet/serializers/event.py:497 intranet/serializers/event.py:804
msgid "Calendar is not set but the teams are"
msgstr ""

#: intranet/serializers/event.py:512
#, python-brace-format
msgid "Non valid teams {teams}"
msgstr ""

#: intranet/serializers/event.py:517
#, fuzzy
#| msgid "Invalid format"
msgid "Invalid city"
msgstr "Formato incorrecto"

#: intranet/serializers/event.py:524 intranet/serializers/event.py:824
msgid "Service is mandatory to link providers to the event"
msgstr ""

#: intranet/serializers/event.py:531 intranet/serializers/event.py:831
msgid "Warehouse is mandatory to link providers to the event"
msgstr ""

#: intranet/serializers/event.py:766
msgid "Needed to delete the old event in calendar"
msgstr ""

#: intranet/serializers/event.py:772 intranet/serializers/event.py:775
msgid "Needed to delete the old booked slot event in calendar"
msgstr ""

#: intranet/serializers/global_settings.py:42
#, fuzzy
#| msgid "Billing cycle"
msgid "Invalid billing cycle"
msgstr "Facturación"

#: intranet/serializers/global_settings.py:48
msgid "Not enough time to set up the billing cycle"
msgstr ""

#: intranet/serializers/history/base_history.py:29
msgid ""
"* +: create\n"
"* -: delete\n"
"* ~: update"
msgstr ""

#: intranet/serializers/residence.py:75 intranet/serializers/residence.py:104
#, fuzzy
#| msgid "A user with that username already exists."
msgid "The residence already exists"
msgstr "Este correo electrónico ya existe."

#: intranet/serializers/residence_group.py:41
#: intranet/serializers/residence_group.py:64
msgid "The residence group already exists"
msgstr ""

#: intranet/serializers/reviews.py:57
#, fuzzy
#| msgid "Invalid format"
msgid "Invalid email"
msgstr "Formato incorrecto"

#: intranet/serializers/reviews.py:63
#, fuzzy
#| msgid "Invalid format"
msgid "Invalid URL"
msgstr "Formato incorrecto"

#: intranet/serializers/reviews.py:78
#, python-brace-format
msgid "The system ({system}) already exists in the selected city"
msgstr ""

#: intranet/serializers/rider.py:204
msgid "Pending accounts cannot be activated. Please, activate it first."
msgstr ""

#: intranet/serializers/rider.py:268
msgid "You are trying to share calendars with an invalid rider"
msgstr ""

#: intranet/serializers/rider.py:276
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "You are trying to share calendars with non allowed users"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/serializers/team.py:41
msgid "Choosing all services/riders is mandatory to set date and city as well"
msgstr ""

#: intranet/serializers/user.py:61 intranet/serializers/user.py:67
#: users/auth/serializers.py:163 users/auth/serializers.py:169
msgid "Invalid activation account URL"
msgstr "URL de activación de la cuenta inválida"

#: intranet/serializers/user.py:64 users/auth/serializers.py:166
msgid "Account is already active"
msgstr "La cuenta ya está activa"

#: intranet/serializers/warehouse_differences.py:66
msgid ""
"There's already another process to check discrepancies between warehouse and "
"us. Please, wait if it is running or choose another date. "
msgstr ""

#: intranet/serializers/warehouse_movement.py:145
#, fuzzy
#| msgid "Invalid format"
msgid "Invalid contract"
msgstr "Formato incorrecto"

#: intranet/serializers/warehouse_movement.py:149
#, fuzzy
#| msgid "Warehouse where is stored"
msgid "Invalid warehouse"
msgstr "Almacén donde está almacenado"

#: intranet/views/calendar.py:105
msgid "Remote calendar could not be deleted"
msgstr ""

#: intranet/views/calendar.py:125
#, fuzzy
#| msgid "Account is already active"
msgid "Calendar is already created"
msgstr "La cuenta ya está activa"

#: intranet/views/calendar.py:145
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating the calendar on remote source"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/calendar.py:149 intranet/views/calendar.py:181
#: intranet/views/calendar.py:226 intranet/views/calendar.py:250
#: intranet/views/reviews.py:165 intranet/views/rider.py:199
#: intranet/views/service.py:264 intranet/views/team.py:126
#: intranet/views/team.py:164 intranet/views/warehouse.py:290 users/views.py:93
msgid "OK"
msgstr ""

#: intranet/views/calendar.py:254
msgid "Visibility could not be changed"
msgstr ""

#: intranet/views/calendar_scheduler.py:45
#: intranet/views/calendar_scheduler.py:58
msgid "Error retrieving google calendars"
msgstr ""

#: intranet/views/calendar_scheduler.py:63
msgid "Error retrieving google calendars due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:83
#: intranet/views/calendar_scheduler.py:117
msgid "Error creating the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:121
msgid "Error creating event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:132
#: intranet/views/calendar_scheduler.py:153
msgid "Error updating the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:157
msgid "Error updating event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:168
#: intranet/views/calendar_scheduler.py:188
msgid "Error deleting the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:192
msgid "Error deleting event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:238
#, python-brace-format
msgid "Error performing full sync from calendar api: {calendar_id}"
msgstr ""

#: intranet/views/calendar_scheduler.py:338
msgid "Error performing partial sync in calendar"
msgstr ""

#: intranet/views/calendar_scheduler.py:343
msgid "Error performing a partial sync in calendar due to rate limit"
msgstr ""

#: intranet/views/chargebee.py:45 users/auth/custom_jwt/authentication.py:60
#: users/auth/custom_jwt/tokens.py:202
msgid "User not found"
msgstr ""

#: intranet/views/chargebee.py:54
#, fuzzy
#| msgid "Unknown error. Please, try again later."
msgid "Error fetching chargebee subscription data"
msgstr "Error desconocido. Por favor, inténtelo de nuevo más tarde."

#: intranet/views/city.py:51
msgid ""
"The city has been already used, so it won't be deleted to prevent errors"
msgstr ""

#: intranet/views/contracts.py:149
#, fuzzy
#| msgid "Unknown error. Please, try again later."
msgid "Error fetching chargebee invoices. Please, try again later"
msgstr "Error desconocido. Por favor, inténtelo de nuevo más tarde."

#: intranet/views/events.py:246
msgid "Remote calendar event could not be deleted"
msgstr ""

#: intranet/views/events.py:272
#, fuzzy
#| msgid "Account is already active"
msgid "Event is already created in remote calendar"
msgstr "La cuenta ya está activa"

#: intranet/views/events.py:278
msgid "Event doesn't have any service, task or blank_slot linked to it"
msgstr ""

#: intranet/views/events.py:350
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating the event on remote calendar"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/events.py:355 intranet/views/rider_configuration.py:86
#: intranet/views/rider_configuration.py:133
#: intranet/views/rider_configuration.py:199
#: intranet/views/rider_configuration.py:258
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Concurrent operation. Try again later."
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/events.py:360 intranet/views/rider_configuration.py:91
#: intranet/views/rider_configuration.py:138
#: intranet/views/rider_configuration.py:204
#: intranet/views/rider_configuration.py:263
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error processing the request. Try again later."
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/events.py:364
#, fuzzy
#| msgid "No created"
msgid "Event created"
msgstr "No creado"

#: intranet/views/hubspot.py:168
msgid "Error fetching hubspot data"
msgstr ""

#: intranet/views/residence_groups.py:53
msgid ""
"The residence group has residences associated with it, so it won't be "
"deleted to prevent errors"
msgstr ""

#: intranet/views/rider.py:115
msgid ""
"The rider cannot be deleted because it already has finished services or "
"tasks. Please, deactivate it instead"
msgstr ""

#: intranet/views/rider.py:196
#, fuzzy
#| msgid "Account is already active"
msgid "Account already activated"
msgstr "La cuenta ya está activa"

#: intranet/views/rider.py:202
msgid "KO"
msgstr ""

#: intranet/views/rider_configuration.py:82
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating the API key"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/rider_configuration.py:117
msgid "API Key cannot be revoked because does not exist"
msgstr ""

#: intranet/views/rider_configuration.py:129
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error revoking the API key"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/rider_configuration.py:168 riders/admin.py:180
#: riders/receivers.py:43
#, python-brace-format
msgid "Service account for rider conf {rider}"
msgstr ""

#: intranet/views/rider_configuration.py:194
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating the service account"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/rider_configuration.py:224
msgid "Service Account cannot be deleted because does not exist"
msgstr ""

#: intranet/views/rider_configuration.py:253
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error deleting the service account"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: intranet/views/service.py:237
msgid "Finished services cannot be notified to riders"
msgstr ""

#: intranet/views/service.py:257
msgid "Error sending notification"
msgstr ""

#: intranet/views/service.py:267
msgid "Error notifying riders"
msgstr ""

#: intranet/views/team.py:147
#, python-brace-format
msgid "Error sending push notification to rider with error code {error}"
msgstr ""

#: intranet/views/team.py:155
msgid "Wrong token provided or missing configuration"
msgstr ""

#: intranet/views/warehouse.py:94
msgid ""
"The warehouse has been already used, so it won't be deleted to prevent errors"
msgstr ""

#: intranet/views/warehouse_differences.py:64
msgid "You cannot retry it because has not failed"
msgstr ""

#: intranet/views/warehouse_differences.py:72
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating the task. Try again later"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: moreapp/admin.py:19
msgid "Service Type"
msgstr ""

#: moreapp/admin.py:23
msgid "Send email"
msgstr ""

#: moreapp/admin.py:28
#, fuzzy
#| msgid "Activation email could not be sent to {user}"
msgid "Warehouse notification email could not be sent. Check logs for details"
msgstr "Correo de activación no pudo mandarse a {user}"

#: moreapp/admin.py:34
#, fuzzy
#| msgid "Activation email sent to {user} successfully"
msgid "Warehouse notification email sent successfully"
msgstr "Correo de activación enviado a {user} con éxito"

#: moreapp/models.py:25
#, fuzzy
#| msgid "Moreapp event"
msgid "Moreapp"
msgstr "Evento de moreapp"

#: moreapp/models.py:26
#, fuzzy
#| msgid "Moreapp event"
msgid "Ridersapp"
msgstr "Evento de moreapp"

#: moreapp/models.py:37
#, fuzzy
#| msgid "If this file has been sent to user"
msgid "If this file has been sent"
msgstr "Si este archivo ha sido enviado al usuario"

#: moreapp/models.py:97
#, python-brace-format
msgid ""
"Customer notification {contract_id} BOX2BOX SL {base} - SERVICE {service} - "
"MOVEMENT {date}"
msgstr ""
"Notificación Cliente {contract_id} BOX2BOX SL {base} - SERVICIO {service} - "
"MOVIMIENTO {date}"

#: payments/admin.py:25 payments/admin.py:43 payments/admin.py:138
msgid "Total"
msgstr "Total"

#: payments/admin.py:62 payments/admin.py:82
#: webhooks/serializers/serializers.py:29
msgid "Chargebee id"
msgstr ""

#: payments/admin.py:86
#, fuzzy
#| msgid "Chargebee"
msgid "Create in chargebee"
msgstr "Chargebee"

#: payments/admin.py:91
#, fuzzy, python-brace-format
#| msgid "Pick up order created successfully"
msgid "Quote {quote} created successfully"
msgstr "Solicitud de entrega creada con éxito"

#: payments/admin.py:93
#, python-brace-format
msgid "Quote {quote} not created"
msgstr ""

#: payments/models/billing_data.py:26
msgid "User first name. This may not be the same as the user's name."
msgstr ""
"Nombre del usuario. No tiene porqué coincidir con el usuario ya registrado."

#: payments/models/billing_data.py:32
msgid "User last name. This may not be the same as the user's last name."
msgstr ""
"Apellido del usuario. No tiene porqué coincidir con el usuario ya registrado."

#: payments/models/billing_data.py:34
#, fuzzy
#| msgid "Company name "
msgid "Company name"
msgstr "Nombre de la empresa "

#: payments/models/billing_data.py:35
msgid "Customer's address"
msgstr "Dirección del cliente"

#: payments/models/coupon.py:14
msgid "ES"
msgstr ""

#: payments/models/coupon.py:15
msgid "FR"
msgstr ""

#: payments/models/coupon.py:16
msgid "PT"
msgstr ""

#: payments/models/coupon.py:17
msgid "MX"
msgstr ""

#: payments/models/coupon.py:22
#, fuzzy
#| msgid "External id for this document"
msgid "Internal name for this coupon"
msgstr "Identificador externo para este documento"

#: payments/models/coupon.py:23
msgid "Chargebee id for this coupon"
msgstr ""

#: payments/models/coupon.py:28
msgid "Chargebee site where the coupon has been created"
msgstr ""

#: payments/models/invoice.py:18 payments/models/payments.py:204
msgid "Created"
msgstr "Creado"

#: payments/models/invoice.py:19
msgid "Voided"
msgstr ""

#: payments/models/invoice.py:20
msgid "Processing"
msgstr ""

#: payments/models/invoice.py:24
msgid "Date when invoice has been emitted"
msgstr "Fecha en la que la factura ha sido emitida"

#: payments/models/invoice.py:25
#, fuzzy
#| msgid "Remote invoice number"
msgid "Remote reference number"
msgstr "Número de factura"

#: payments/models/invoice.py:26
msgid "Chargebee remote id"
msgstr "Identificador de factura de chargebee"

#: payments/models/invoice.py:62
msgid "Indicates that is an invoice from deposit"
msgstr "Nos dice si es una factura generada a través del pago de la señal"

#: payments/models/invoice.py:66
msgid "Related transaction to this payment"
msgstr ""

#: payments/models/invoice.py:68
msgid "Last date of applied fee due to chargebacks"
msgstr ""

#: payments/models/payment_method.py:39
msgid "Credit"
msgstr "Crédito"

#: payments/models/payment_method.py:40
msgid "Prepaid"
msgstr "Prepago"

#: payments/models/payment_method.py:41
msgid "Debit"
msgstr "Débito"

#: payments/models/payment_method.py:56
msgid "American Express"
msgstr "American Express"

#: payments/models/payment_method.py:57
msgid "Diners Club"
msgstr "Diners Club"

#: payments/models/payment_method.py:58
msgid "Discover"
msgstr "Discover"

#: payments/models/payment_method.py:59
msgid "Jcb"
msgstr "Jcb"

#: payments/models/payment_method.py:60
msgid "Mastercard"
msgstr "Mastercard"

#: payments/models/payment_method.py:61
msgid "Unionpay"
msgstr "Unionpay"

#: payments/models/payment_method.py:62
msgid "Visa"
msgstr "Visa"

#: payments/models/payment_method.py:63
msgid "Other"
msgstr "Other"

#: payments/models/payments.py:46 payments/models/payments.py:202
msgid "No created"
msgstr "No creado"

#: payments/models/payments.py:47
msgid "Active"
msgstr "Activa"

#: payments/models/payments.py:49
msgid "Canceled"
msgstr "Cancelado"

#: payments/models/payments.py:59
msgid "Yearly"
msgstr ""

#: payments/models/payments.py:66
msgid "Not running"
msgstr ""

#: payments/models/payments.py:71 payments/models/payments.py:227
#, fuzzy
#| msgid "Subscription id in external service"
msgid "Remote id in external service"
msgstr "Identificador de la suscripción en el servicio externo (Chargebee)"

#: payments/models/payments.py:87
msgid "Current billing cycle to trigger the free month. Every 6 months"
msgstr ""

#: payments/models/payments.py:91
msgid "If this subscription has a free month allowed"
msgstr ""

#: payments/models/payments.py:94
msgid "If this subscription has to be cancel in chargebee"
msgstr ""

#: payments/models/payments.py:99
msgid "Indicates if subscription has been imported from legacy system (Odoo)"
msgstr ""
"Nos dice si la suscripción ha sido importada de un sistema heredado (Odoo)"

#: payments/models/payments.py:104
msgid ""
"Indicates that subscription is in free month. If this is set true would "
"means the next month is free and recurring interval is 5"
msgstr ""

#: payments/models/payments.py:240
#, fuzzy
#| msgid "Internal id from moreapp to avoid duplicate entries"
msgid ""
"Internal id from moreapp to avoid create duplicated quotes for the same form"
msgstr "Identificador interno de moreapp para evitar entradas duplicadas"

#: payments/serializers/billing_data.py:34
#: payments/serializers/billing_data.py:66
#: payments/serializers/payment_method.py:111
#: payments/serializers/payment_method.py:160 payments/views.py:299
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "User is not created in payment processor platform. Try again later"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: payments/serializers/credit_note.py:25
msgid ""
"If is not provided, will return credit notes related to the user who made "
"the request. In detailed views, this flag is used to filter the whole "
"queryset from database and is used as base to get the concrete object."
msgstr ""

#: payments/serializers/invoice.py:25
msgid ""
"If is not provided, will return invoices related to the user who made the "
"request. In detailed views, this flag is used to filter the whole queryset "
"from database and is used as base to get the concrete object."
msgstr ""

#: payments/serializers/payment_method.py:116
msgid "User already has a payment method. You should update it instead"
msgstr ""

#: payments/views.py:83 payments/views.py:174 payments/views.py:188
#: payments/views.py:196 payments/views.py:210
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error retrieving invoice. Try again later."
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: payments/views.py:97 payments/views.py:106 payments/views.py:120
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error retrieving credit note. Try again later."
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: payments/views.py:256 payments/views.py:276
#, fuzzy
#| msgid "Unknown error. Please, try again later."
msgid "Error updating billing data . Please try again, try again later"
msgstr "Error desconocido. Por favor, inténtelo de nuevo más tarde."

#: payments/views.py:291
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating the payment intent"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: payments/views.py:311
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error updating the payment method. Try again later"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: payments/views.py:361
#, fuzzy
#| msgid "Error processing the payment. Try again later"
msgid "Error creating payment method. Please try again, try again later"
msgstr "Error procesando el pago. Por favor, prueba de nuevo más tarde"

#: payments/views.py:384
#, fuzzy
#| msgid "Unknown error. Please, try again later."
msgid "Error updating payment method. Please try again later"
msgstr "Error desconocido. Por favor, inténtelo de nuevo más tarde."

#: riders/admin.py:92
msgid "Generate API Key"
msgstr ""

#: riders/admin.py:97
msgid "An existing api key is present. Please, revoke it first"
msgstr ""

#: riders/admin.py:110
#, python-brace-format
msgid "Unknown error generating the api key: {msg}"
msgstr ""

#: riders/admin.py:115
msgid "API Key generated"
msgstr ""

#: riders/admin.py:117
msgid "Revoke API Key"
msgstr ""

#: riders/admin.py:121
msgid "Missing Google API key. Create it first."
msgstr ""

#: riders/admin.py:133
#, python-brace-format
msgid "Unknown error revoking the api key: {msg}"
msgstr ""

#: riders/admin.py:138
msgid "API Key revoked"
msgstr ""

#: riders/admin.py:140
msgid "Renew API Key"
msgstr ""

#: riders/admin.py:158
#, python-brace-format
msgid "Unknown error renewing the api key: {msg}"
msgstr ""

#: riders/admin.py:163
msgid "API Key renewed (created + revoked)"
msgstr ""

#: riders/admin.py:165
#, fuzzy
#| msgid "Created"
msgid "Create SA"
msgstr "Creado"

#: riders/admin.py:171
msgid "An existing Service account is present. Please, remove it first"
msgstr ""

#: riders/admin.py:202 riders/admin.py:298
#, python-brace-format
msgid "Unknown error generating the service account: {msg}"
msgstr ""

#: riders/admin.py:209
#, fuzzy
#| msgid "No created"
msgid "Service account created"
msgstr "No creado"

#: riders/admin.py:211
msgid "Delete SA"
msgstr ""

#: riders/admin.py:216
msgid "Missing service account. Please, create it first"
msgstr ""

#: riders/admin.py:240
#, python-brace-format
msgid "Unknown error deleting the service account: {msg}"
msgstr ""

#: riders/admin.py:247
msgid "Service account deleted"
msgstr ""

#: riders/admin.py:249
msgid "Disable SA"
msgstr ""

#: riders/admin.py:254 riders/admin.py:283
msgid "Missing service account. Please create it first"
msgstr ""

#: riders/admin.py:259
msgid "Cannot disabled non enabled service accounts"
msgstr ""

#: riders/admin.py:269
#, python-brace-format
msgid "Unknown error disabling the service account: {msg}"
msgstr ""

#: riders/admin.py:276
msgid "Service account disabled"
msgstr ""

#: riders/admin.py:278
msgid "Enable SA"
msgstr ""

#: riders/admin.py:288
msgid "Cannot enable non disabled service accounts"
msgstr ""

#: riders/admin.py:305
msgid "Service account generated"
msgstr ""

#: riders/admin.py:375
msgid "Preferred country"
msgstr ""

#: riders/admin.py:395 users/admin.py:127
#, fuzzy
#| msgid "Empty email"
msgid "Email"
msgstr "Ningún correo electrónico provisto"

#: riders/admin_filters/riders.py:7
msgid "Linked"
msgstr ""

#: riders/admin_filters/riders.py:22
msgid "Task"
msgstr ""

#: riders/auth/forms.py:34 users/auth/forms.py:40
#, python-brace-format
msgid "{prefix}Recovery your password"
msgstr "{prefix}Recupera tu contraseña"

#: riders/auth/serializers.py:42
msgid "OTP token not found"
msgstr ""

#: riders/auth/serializers.py:65
msgid "Invalid OTP token"
msgstr ""

#: riders/filters/warehouse.py:11
msgid "Bring warehouses where tracking options are set"
msgstr ""

#: riders/filters/warehouse.py:17
msgid "Bring warehouses that belong to a given city"
msgstr ""

#: riders/models/riders.py:42
msgid "Not sent"
msgstr ""

#: riders/models/riders.py:48
msgid "Set"
msgstr ""

#: riders/models/riders.py:54
msgid "Last time that notification was sent"
msgstr ""

#: riders/models/riders.py:56
msgid "Time where service was accepted by the rider"
msgstr ""

#: riders/models/riders.py:59
msgid "Flag to hide a service from pending services in "
msgstr ""

#: riders/models/riders.py:65
msgid ""
"Raw response from app, used to allow different responses for tasks or "
"whatever related FK"
msgstr ""

#: riders/models/riders.py:72
msgid ""
"Flag to know if this rider has submitted the inventory location on a given "
"warehouse"
msgstr ""

#: riders/models/riders.py:86
msgid "Team has no rider which it is mandatory to send push notifications"
msgstr ""

#: riders/models/riders.py:89
msgid "Service linked is mandatory to send push notifications"
msgstr ""

#: riders/models/riders.py:108
#, fuzzy
#| msgid "No created"
msgid "Not created"
msgstr "No creado"

#: riders/models/riders.py:109
msgid "Enabled"
msgstr ""

#: riders/models/riders.py:110
msgid "Disabled"
msgstr ""

#: riders/models/riders.py:133
msgid "Manual flag to set when service account has the needed roles granted"
msgstr ""

#: riders/models/riders.py:230
#, python-brace-format
msgid "{prefix} Activate your account to get access in the Riders app"
msgstr "{prefix} Activa tu cuenta en Box2box"

#: riders/serializers/riders.py:51
msgid "Warehouses which belong to this city"
msgstr ""

#: riders/serializers/riders.py:52
#, fuzzy
#| msgid "Warehouse location"
msgid "Warehouses with tracking options"
msgstr "Ubicación en almacén"

#: riders/views.py:32 riders/views.py:50
msgid "Deprecated once all riders use the v2.1.0 version of the app"
msgstr ""

#: templates/admin/searchable_dropdown_filter.html:31
#, python-format
msgid " By %(filter_title)s "
msgstr ""

#: templates/otp/login.html:38
msgid "Please correct the error below."
msgstr ""

#: templates/otp/login.html:38
msgid "Please correct the errors below."
msgstr ""

#: templates/otp/login.html:54
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: templates/otp/login.html:73
msgid "OTP Token:"
msgstr ""

#: templates/otp/login.html:78
msgid "Forgotten your password or username?"
msgstr ""

#: templates/otp/login.html:82
msgid "Log in"
msgstr ""

#: users/admin.py:26
msgid "Contract"
msgstr "Contrato"

#: users/admin.py:78
msgid "Customer area"
msgstr ""

#: users/admin.py:85
msgid "Hubspot"
msgstr "Hubspot"

#: users/admin.py:86
msgid "Chargebee"
msgstr "Chargebee"

#: users/admin.py:87
msgid "Moloni"
msgstr ""

#: users/admin.py:88
msgid "Personal area"
msgstr ""

#: users/admin.py:89
msgid "Inheritance"
msgstr ""

#: users/admin.py:134
msgid "Schedule activation account email for this user"
msgstr ""

#: users/admin.py:139
#, python-brace-format
msgid "Activation email scheduled to {user} successfully"
msgstr "Correo de activación planificado para el {user} con éxito"

#: users/admin.py:144
#, python-brace-format
msgid "Activation email could not be scheduled for the {user}"
msgstr ""

#: users/admin.py:163
msgid "Send activation account email"
msgstr ""

#: users/admin.py:168
#, python-brace-format
msgid "Activation email could not be sent to {user}"
msgstr "Correo de activación no pudo mandarse a {user}"

#: users/admin.py:174
#, python-brace-format
msgid "Activation email sent to {user} successfully"
msgstr "Correo de activación enviado a {user} con éxito"

#: users/auth/custom_jwt/authentication.py:43
msgid "Given token not valid for any token type"
msgstr ""

#: users/auth/custom_jwt/authentication.py:55
msgid "Token contained no recognizable user identification"
msgstr ""

#: users/auth/custom_jwt/authentication.py:63
msgid "User is inactive"
msgstr ""

#: users/auth/custom_jwt/tokens.py:69
msgid "Invalid algorithm specified"
msgstr ""

#: users/auth/custom_jwt/tokens.py:71 users/auth/custom_jwt/tokens.py:97
msgid "Token is invalid or expired"
msgstr ""

#: users/auth/custom_jwt/tokens.py:82
msgid "Cannot create token with no type or lifetime"
msgstr ""

#: users/auth/serializers.py:74
msgid "Must include email and password"
msgstr ""

#: users/auth/serializers.py:91 users/auth/serializers.py:107
#: users/auth/serializers.py:117
msgid "Unable to log in with provided credentials."
msgstr ""

#: users/auth/serializers.py:102
msgid "User is not allowed to access to this site."
msgstr ""

#: users/auth/views.py:69
msgid "Error generating token for requested user"
msgstr ""

#: users/models.py:28
msgid "Date when activation account email was sent"
msgstr ""

#: users/models.py:39 users/models.py:200
#, python-brace-format
msgid "{prefix} Activate your account in Box2box"
msgstr "{prefix} Activa tu cuenta en Box2box"

#: users/models.py:56
msgid "username"
msgstr "Nick de usuario"

#: users/models.py:59
msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

#: users/models.py:63
msgid "A user with that username already exists."
msgstr "Este correo electrónico ya existe."

#: users/models.py:73
msgid "Used to enforce to user to change password and fill missing data"
msgstr ""
"Utilizado para obligar al usuario a que cambie la contraseña y rellene los "
"datos que faltan"

#: users/models.py:78
msgid "User identifier in external CRM"
msgstr "Identificador del usuario en el CRM"

#: users/models.py:80
msgid "Chargebee identifier"
msgstr "Identificador de Chargebee"

#: users/models.py:82
msgid "active"
msgstr "Activa"

#: users/models.py:85
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Indica si el usuario debe ser tratado como activo. Desmarque esta opción en "
"lugar de borrar la cuenta."

#: users/models.py:88
msgid "Source domain from which user has signed in"
msgstr "Dominio de origen por el cual ha sido registrado el usuario"

#: users/models.py:91
msgid "Customer identifier in moloni software billing"
msgstr ""

#: users/models.py:99
msgid "Handle third-party software duplicity on emails"
msgstr ""

#: users/models.py:102
msgid "Customer preferred language"
msgstr ""

#: users/models.py:106
msgid "First name set by the customer"
msgstr ""

#: users/models.py:109
msgid "Last name set by the customer"
msgstr ""

#: users/models.py:118
msgid "date leave"
msgstr ""

#: users/serializers/user_serializers.py:205
msgid "Wrong password"
msgstr "Contraseña incorrecta"

#: users/serializers/user_serializers.py:210
msgid "An user with that email already exists."
msgstr "Este correo electrónico ya existe."

#: users/views.py:64
msgid "Email changed"
msgstr "Correo electrónico cambiado"

#: webhooks/callbacks/hubspot_public_forms.py:199
msgid "Thank you for your request! We will contact you as soon as possible. 😀"
msgstr ""
"¡Gracias por tu solicitud! En breve nos pondremos en contacto contigo. 😀"

#: webhooks/callbacks/hubspot_public_forms.py:233
msgid "Congrats! You have a new collaborator"
msgstr "Enhorabuena, tienes un nuevo colaborador"

#: webhooks/callbacks/microservices.py:150
msgid "Your payment was successful!"
msgstr "¡Pago de la señal recibido!"

#: webhooks/callbacks/mixins/transaction_handlers.py:159
#, python-brace-format
msgid ""
"[CHARGEBACK] Fee on the transaction {transaction} and invoice {invoice} due "
"to chargeback"
msgstr ""

#: webhooks/callbacks/mixins/transaction_handlers.py:266
#, python-brace-format
msgid ""
"[CHARGEBACK] Fee on the invoice {invoice} due to chargeback coming from card"
msgstr ""

#: webhooks/callbacks/ridersapp_ext/base.py:15
msgid "Quote is linked to a service with 'Especial' billing cycle"
msgstr ""

#: webhooks/callbacks/ridersapp_ext/base.py:18
msgid "Quote is linked to a service with 'Promo pack' billing cycle"
msgstr ""

#: webhooks/serializers/serializers.py:30
msgid "Internal id"
msgstr "Id interno"

#: webhooks/views.py:361
#, python-brace-format
msgid "Congrats! A B2B lead wants a quote - {deal_id}"
msgstr "Enhorabuena, un lead B2B quiere presupuesto - {deal_id}."

#: webhooks/views.py:416
#, python-brace-format
msgid "New form submission from deal {deal_id}"
msgstr "Nuevo formulario de {deal_id}"

#: webhooks/views.py:502
#, python-brace-format
msgid "Budget request from {deal_id}"
msgstr "Solicitud de presupuesto de {deal_id}"

#: webhooks/views.py:524
#, python-brace-format
msgid "Booking data updated for deal {deal_id}"
msgstr "Datos de reserva actualizados para el deal {deal_id}"

#, fuzzy
#~| msgid "Date when the service has been updated (if it has been)"
#~ msgid "Date when the service has been scheduled in the calendar"
#~ msgstr "Fecha em que el servicio ha sido actualizado"

#~ msgid "Warehouse where is stored"
#~ msgstr "Almacén donde está almacenado"

#~ msgid "Empty list of items is not allowed. Please, set at least one."
#~ msgstr ""
#~ "No se permite una lista vacía de objetos. Por favor, añade al menos un "
#~ "objeto."

#~ msgid "You cannot select an item which is already selected for pick up"
#~ msgstr ""
#~ "No puedes seleccionar un objeto que ya ha sido seleccionado para una "
#~ "recogida"

#~ msgid ""
#~ "You cannot update the payment method because does not exists. Please, "
#~ "create it first."
#~ msgstr ""
#~ "No puedes actualizar el método de pago porque no existe. Por favor, "
#~ "créalo primero."

#~ msgid "Payment method cannot be updated because gateway id is missing"
#~ msgstr ""
#~ "Método de pago no puede actualizarse porque falta el identificador de la "
#~ "pasarela de pago"

#~ msgid "Unknown error. Please, try again later."
#~ msgstr "Error desconocido. Por favor, inténtelo de nuevo más tarde."

#~ msgid "Google cloud storage destination file"
#~ msgstr "Archivo de destino en Google cloud storage"

#~ msgid "Pick up/Delivery date"
#~ msgstr "Fecha de recogida/entrega"

#~ msgid "Order type: pickup or delivery"
#~ msgstr "Tipo de solicitud: recogida o entrega"

#~ msgid "Pickup"
#~ msgstr "Recogida"

#~ msgid "Delivery order created successfully"
#~ msgstr "Solicitud de recogida creada con éxito"

#~ msgid "Monthly (1), Biannual (6), Annual (12)"
#~ msgstr "Mensual (1), Semestral (6), Anual (12)"

#~ msgid "{settings.ACCOUNT_EMAIL_SUBJECT_PREFIX}Recovery your password"
#~ msgstr "{settings.ACCOUNT_EMAIL_SUBJECT_PREFIX}Recupera tu contraseña"
