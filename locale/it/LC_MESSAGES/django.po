# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-19 13:20+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backoffice_api/settings/base.py:582
msgid "English"
msgstr ""

#: backoffice_api/settings/base.py:583
msgid "Spanish"
msgstr ""

#: backoffice_api/settings/base.py:584
msgid "Portuguese"
msgstr ""

#: backoffice_api/settings/base.py:585
msgid "French"
msgstr ""

#: backoffice_api/settings/base.py:586
msgid "Italian"
msgstr ""

#: contracts/admin.py:24
msgid "Annex"
msgstr "Allegato"

#: contracts/admin.py:42 contracts/models/contract.py:412
#: contracts/models/service.py:180 payments/models/payments.py:215
msgid "Box"
msgstr "Scatola"

#: contracts/admin.py:60 contracts/models/service.py:181
#: payments/models/payments.py:216
msgid "Moving"
msgstr "Trasloco"

#: contracts/admin.py:94 intranet/admin.py:42
msgid "Site"
msgstr ""

#: contracts/admin.py:164
msgid "Create quote"
msgstr ""

#: contracts/admin.py:175
msgid "Moreapp annex found. Quote won't be created"
msgstr ""

#: contracts/admin.py:209
#, python-brace-format
msgid "Quote {quote} created in chargebee successfully"
msgstr ""

#: contracts/admin.py:214
#, python-brace-format
msgid "Quote {quote} updated locally but changes are not in chargebee"
msgstr ""

#: contracts/admin.py:218
#, python-brace-format
msgid "Quote {quote} could not be created locally"
msgstr ""

#: contracts/filters.py:20 intranet/filters/inventory.py:13
msgid "Brings only non-delivered items from db."
msgstr ""

#: contracts/filters.py:26
msgid "Brings only pending items from db."
msgstr ""

#: contracts/filters.py:51
msgid "Brings services from db according to status choice."
msgstr ""

#: contracts/filters.py:57 contracts/filters.py:96
msgid ""
"Brings only services where event date is set to date lower than equals to "
"today."
msgstr ""

#: contracts/filters.py:64
msgid "Brings services from db excluding given services by id"
msgstr ""

#: contracts/filters.py:103
msgid "Brings tasks from db excluding given tasks by id"
msgstr ""

#: contracts/models/contract.py:37
msgid "Spain"
msgstr ""

#: contracts/models/contract.py:38
msgid "France"
msgstr ""

#: contracts/models/contract.py:39
msgid "Portugal"
msgstr ""

#: contracts/models/contract.py:40
msgid "Italia"
msgstr ""

#: contracts/models/contract.py:41
msgid "Mexico"
msgstr ""

#: contracts/models/contract.py:47 contracts/models/contract.py:139
msgid "External id for this document"
msgstr ""

#: contracts/models/contract.py:76
msgid "Pick up/Deliver address"
msgstr ""

#: contracts/models/contract.py:92
msgid "Contract Addresses"
msgstr ""

#: contracts/models/contract.py:112 contracts/models/contract.py:366
msgid "Hired space in squared meters"
msgstr ""

#: contracts/models/contract.py:116
msgid "Person who has signed the initial_pickup contract"
msgstr ""

#: contracts/models/contract.py:121
msgid "Coupon code to apply the discount to the initial deposit"
msgstr ""

#: contracts/models/contract.py:124
msgid "Flag to check if deposit needs to be applied on this contract"
msgstr ""

#: contracts/models/contract.py:126
msgid "Amount to discount on first subscription"
msgstr ""

#: contracts/models/contract.py:130
msgid "Indicates if contract has been imported from legacy system (Odoo)"
msgstr ""

#: contracts/models/contract.py:133
msgid ""
"If this contract should be filtered to display it to customer, even if is "
"imported"
msgstr ""

#: contracts/models/contract.py:135
msgid "For created contracts from intranet, site which belongs it"
msgstr ""

#: contracts/models/contract.py:143
msgid "Odoo id for this contract"
msgstr ""

#: contracts/models/contract.py:172
msgid "Deposit"
msgstr ""

#: contracts/models/contract.py:222
msgid "From id from moreapp"
msgstr ""

#: contracts/models/contract.py:229
msgid "Internal id from moreapp to track every form submitted"
msgstr ""

#: contracts/models/contract.py:232
msgid "Flag to know if related documents have been uploaded to storage"
msgstr ""

#: contracts/models/contract.py:234
msgid "If this file has been sent to user"
msgstr ""

#: contracts/models/contract.py:235 moreapp/models.py:39
msgid "Date where document was sent to the queue"
msgstr ""

#: contracts/models/contract.py:328
#, python-brace-format
msgid "BOX2BOX - {contract_type} - {display_name}"
msgstr ""

#: contracts/models/contract.py:356 contracts/models/service.py:176
#: payments/models/payments.py:211
msgid "Initial pickup"
msgstr "Ritiro iniziale"

#: contracts/models/contract.py:357 contracts/models/service.py:177
#: payments/models/payments.py:212
msgid "Extra pickup"
msgstr "Ritiro aggiuntivo"

#: contracts/models/contract.py:358 contracts/models/service.py:178
#: intranet/models/delivery.py:40 payments/models/payments.py:213
msgid "Partial delivery"
msgstr "Consegna parziale"

#: contracts/models/contract.py:359 contracts/models/service.py:179
#: intranet/models/delivery.py:41 payments/models/payments.py:214
msgid "Final delivery"
msgstr "Consegna finale"

#: contracts/models/contract.py:360 contracts/models/service.py:182
#: payments/models/payment_method.py:42 payments/models/payments.py:217
msgid "Unknown"
msgstr ""

#: contracts/models/contract.py:372
msgid "Annexes"
msgstr ""

#: contracts/models/contract.py:391
msgid "Moved space in squared meters"
msgstr ""

#: contracts/models/contract.py:396
msgid "Move"
msgstr "Traslochi"

#: contracts/models/contract.py:402
msgid "Number of normal boxes"
msgstr ""

#: contracts/models/contract.py:403
msgid "Number of wardrobe boxes"
msgstr ""

#: contracts/models/contract.py:404
msgid "Number of seal rolls"
msgstr ""

#: contracts/models/contract.py:405
msgid "Units of bubble paper"
msgstr ""

#: contracts/models/contract.py:408
msgid "Boxes"
msgstr "Scatole"

#: contracts/models/item.py:9
msgid "Object description provided in contract"
msgstr ""

#: contracts/models/item.py:21
msgid "Item dimensions in the form of WxHxD"
msgstr ""

#: contracts/models/item.py:40
msgid "Object barcode tag id"
msgstr ""

#: contracts/models/item.py:41
msgid "Delivery date for this item"
msgstr ""

#: contracts/models/item.py:42
msgid "Pick up date for this item"
msgstr ""

#: contracts/models/item.py:44
msgid "Warehouse where is stored"
msgstr ""

#: contracts/models/item.py:47
msgid "Additional comment for items, added thru customer portal"
msgstr ""

#: contracts/models/item.py:51
msgid "Temporal id used by the app before creating the item in db"
msgstr ""

#: contracts/models/orders.py:22
msgid "Street level"
msgstr ""

#: contracts/models/orders.py:23 contracts/service_templates/contracts.py:40
msgid "Stairs"
msgstr "Scale"

#: contracts/models/orders.py:24
msgid "Small lift"
msgstr "Piccolo ascensore"

#: contracts/models/orders.py:25
msgid "Large lift"
msgstr "Grande ascensore"

#: contracts/models/orders.py:29
msgid "Order date"
msgstr ""

#: contracts/models/orders.py:32
msgid "Access type"
msgstr "Tipo di accesso"

#: contracts/models/orders.py:34
msgid "Additional comments from the user"
msgstr ""

#: contracts/models/orders.py:38
msgid "Creation date"
msgstr ""

#: contracts/models/service.py:14
msgid "Time slot"
msgstr "Orario"

#: contracts/models/service.py:15 contracts/service_templates/contracts.py:176
msgid "Inventory"
msgstr "Inventario"

#: contracts/models/service.py:16
msgid "Billing cycle"
msgstr "Fatturazione"

#: contracts/models/service.py:17
msgid "Warehouse location"
msgstr "Ubicazione nel magazzino"

#: contracts/models/service.py:18 contracts/serializers/contract.py:42
msgid "Hired space"
msgstr "Spazio contrattato"

#: contracts/models/service.py:19
#, fuzzy
#| msgid "Moving address"
msgid "Source address"
msgstr "Indirizzo di trasloco"

#: contracts/models/service.py:20
msgid "Moving address"
msgstr "Indirizzo di trasloco"

#: contracts/models/service.py:21
msgid "Material"
msgstr "Materiale"

#: contracts/models/service.py:22 contracts/service_templates/boxes.py:57
#: contracts/service_templates/contracts.py:164
msgid "Address"
msgstr "Indirizzo"

#: contracts/models/service.py:23
msgid "Additional information"
msgstr "Informazioni aggiuntive"

#: contracts/models/service.py:24 contracts/service_templates/contracts.py:169
msgid "Service type"
msgstr "Tipo di servizi"

#: contracts/models/service.py:186
msgid "Not started"
msgstr ""

#: contracts/models/service.py:187
msgid "In progress"
msgstr ""

#: contracts/models/service.py:188
msgid "Finished"
msgstr ""

#: contracts/models/service.py:196 core/models/product.py:19
#: payments/models/payments.py:54
msgid "Monthly"
msgstr ""

#: contracts/models/service.py:197 core/models/product.py:20
#: payments/models/payments.py:55
msgid "Biannual"
msgstr ""

#: contracts/models/service.py:198 core/models/product.py:21
msgid "Annual"
msgstr ""

#: contracts/models/service.py:202
msgid "Hubspot deal id"
msgstr ""

#: contracts/models/service.py:203
msgid "Service address"
msgstr ""

#: contracts/models/service.py:205
msgid "Destination address for movings"
msgstr ""

#: contracts/models/service.py:210 users/models.py:70
msgid "Phone number"
msgstr ""

#: contracts/models/service.py:211
msgid "Space in m2"
msgstr ""

#: contracts/models/service.py:225
msgid "Location in the warehouse - Additional pickup"
msgstr ""

#: contracts/models/service.py:228
msgid "Hired space in m2 - Additional pickup"
msgstr ""

#: contracts/models/service.py:232
msgid "Event country which this service belongs to"
msgstr ""

#: contracts/models/service.py:235
#, fuzzy
#| msgid "Source address"
msgid "Service address latitude"
msgstr "Indirizzo di origine"

#: contracts/models/service.py:236
#, fuzzy
#| msgid "Source address"
msgid "Service address longitude"
msgstr "Indirizzo di origine"

#: contracts/models/service.py:239
msgid "Date of submission for the service"
msgstr ""

#: contracts/models/service.py:240
msgid "Date when the service has been updated (if it has been)"
msgstr ""

#: contracts/models/service.py:252
msgid "Rider who has submitted this service"
msgstr ""

#: contracts/models/warehouse.py:11
msgid "Warehouse id"
msgstr ""

#: contracts/models/warehouse.py:13
msgid "Warehouse name"
msgstr ""

#: contracts/models/warehouse.py:30
msgid "Check this if rider must be showed as default on events"
msgstr ""

#: contracts/receivers.py:22
#, python-brace-format
msgid "{prefix}pickup order for contract {contract}"
msgstr "{prefix}richiesta di ritiro del contratto {contract}"

#: contracts/receivers.py:60
#, python-brace-format
msgid "{prefix}delivery order for contract {contract}"
msgstr "{prefix}richiesta di consegna del contratto {contract}"

#: contracts/serializers/delivery.py:40 contracts/serializers/delivery.py:42
#: contracts/serializers/delivery.py:44 contracts/serializers/delivery.py:46
#: contracts/serializers/delivery.py:48 contracts/serializers/pickup.py:85
#: contracts/serializers/pickup.py:87 contracts/serializers/pickup.py:89
#: contracts/serializers/pickup.py:91 contracts/serializers/pickup.py:93
msgid "This field is required"
msgstr ""

#: contracts/serializers/delivery.py:53 contracts/serializers/pickup.py:98
#, python-brace-format
msgid "Contract address with {id} does not exist"
msgstr ""

#: contracts/serializers/delivery.py:88
msgid "You cannot select an item which not belongs to contract"
msgstr ""

#: contracts/serializers/delivery.py:92
msgid "You cannot select an item which is already selected for delivery"
msgstr ""

#: contracts/serializers/delivery.py:153 contracts/serializers/pickup.py:137
msgid "Invalid contract id given"
msgstr ""

#: contracts/serializers/delivery.py:204 contracts/serializers/pickup.py:174
msgid "If is not provided, will return empty data."
msgstr ""

#: contracts/serializers/inventory.py:14
msgid ""
"If is not provided, will return empty data. In detailed views, this flag is "
"not required."
msgstr ""

#: contracts/serializers/inventory.py:60 contracts/serializers/service.py:78
msgid "Dummy value for the riders app business logic"
msgstr ""

#: contracts/serializers/service.py:160 contracts/serializers/service.py:220
msgid ""
"This is returned as a list if any to backward compatibility with the app"
msgstr ""

#: contracts/service_templates/base.py:85
#: contracts/service_templates/base.py:86
msgid "Pick up"
msgstr "Ritiro"

#: contracts/service_templates/base.py:87
#: contracts/service_templates/base.py:88
msgid "Delivery"
msgstr "Consegna"

#: contracts/service_templates/base.py:91
#: contracts/service_templates/base.py:92
msgid "No inventory picked up"
msgstr "Nessun inventario ritirato"

#: contracts/service_templates/base.py:93
#: contracts/service_templates/base.py:94
msgid "No inventory delivered"
msgstr "Nessun inventario consegnato"

#: contracts/service_templates/base.py:95
msgid "No pictures taken"
msgstr "Nessuna foto scattata"

#: contracts/service_templates/boxes.py:14
#: contracts/service_templates/contracts.py:84
msgid "Seal"
msgstr "Sigillo"

#: contracts/service_templates/boxes.py:15
#: contracts/service_templates/boxes.py:17
#: contracts/service_templates/boxes.py:19
#: contracts/service_templates/boxes.py:24
#: contracts/service_templates/boxes.py:26
#: contracts/service_templates/contracts.py:54
#: contracts/service_templates/contracts.py:60
#: contracts/service_templates/contracts.py:71
#: contracts/service_templates/contracts.py:73
#: contracts/service_templates/contracts.py:83
#: contracts/service_templates/contracts.py:85
#: contracts/service_templates/contracts.py:87
msgid "Units"
msgstr "Unità"

#: contracts/service_templates/boxes.py:16
#: contracts/service_templates/contracts.py:86
msgid "Bubble paper"
msgstr "Pluriball"

#: contracts/service_templates/boxes.py:18
#: contracts/service_templates/contracts.py:82
msgid "Blanket"
msgstr "Coperta"

#: contracts/service_templates/boxes.py:23
#: contracts/service_templates/contracts.py:70
msgid "Moving boxes"
msgstr "Scatole trasloco"

#: contracts/service_templates/boxes.py:23
#: contracts/service_templates/contracts.py:70
msgid "Moving box sales"
msgstr "Vendita di scatole trasloco"

#: contracts/service_templates/boxes.py:25
#: contracts/service_templates/contracts.py:72
msgid "Wardrobe boxes"
msgstr "Scatole-armadio"

#: contracts/service_templates/boxes.py:25
#: contracts/service_templates/contracts.py:72
msgid "Wardrobe box sales"
msgstr "Vendita di scatole-armadio"

#: contracts/service_templates/boxes.py:44
msgid "Box delivery"
msgstr "Bolla di consegna della scatola"

#: contracts/service_templates/boxes.py:46
#: contracts/service_templates/contracts.py:153
msgid "Contract data"
msgstr "Dati del contratto"

#: contracts/service_templates/boxes.py:47
#: contracts/service_templates/contracts.py:154
msgid "Contract identifier"
msgstr "Número de contratto"

#: contracts/service_templates/boxes.py:50
#: contracts/service_templates/contracts.py:157
msgid "Personal data"
msgstr "Dati personali"

#: contracts/service_templates/boxes.py:51
#: contracts/service_templates/contracts.py:158
msgid "First name"
msgstr "Nome"

#: contracts/service_templates/boxes.py:52
#: contracts/service_templates/contracts.py:159
msgid "Last name"
msgstr "Cognome"

#: contracts/service_templates/boxes.py:53
#: contracts/service_templates/contracts.py:160
msgid "National ID"
msgstr "ID"

#: contracts/service_templates/boxes.py:56
#: contracts/service_templates/contracts.py:163
msgid "Date and address"
msgstr "Data e indirizzo"

#: contracts/service_templates/boxes.py:58
#: contracts/service_templates/contracts.py:165
msgid "Date"
msgstr "Data"

#: contracts/service_templates/boxes.py:60
msgid "Boxes service"
msgstr "Consegna di scatole"

#: contracts/service_templates/boxes.py:61
#: contracts/service_templates/contracts.py:181
msgid "Additional Services"
msgstr "Servizi supplementari"

#: contracts/service_templates/boxes.py:63
#: contracts/service_templates/contracts.py:183
msgid "Comment"
msgstr "Commenti"

#: contracts/service_templates/boxes.py:67
#: contracts/service_templates/contracts.py:187 riders/admin.py:375
msgid "Signature"
msgstr "Firma"

#: contracts/service_templates/boxes.py:69
#: contracts/service_templates/contracts.py:189
msgid "Customer signature"
msgstr "Firma cliente"

#: contracts/service_templates/boxes.py:76
#: contracts/service_templates/contracts.py:196
msgid "Company signature"
msgstr "Firma azienda"

#: contracts/service_templates/conditions.py:15
#: contracts/service_templates/conditions.py:51
msgid "Terms of service"
msgstr "Condizioni di servizio"

#: contracts/service_templates/conditions.py:15
#: contracts/service_templates/conditions.py:51
msgid "Legal conditions"
msgstr "Condizioni legali"

#: contracts/service_templates/conditions.py:17
#: contracts/service_templates/conditions.py:53
msgid "General conditions"
msgstr "Condizioni generali"

#: contracts/service_templates/conditions.py:21
#, python-brace-format
msgid "conditions_1 {email}"
msgstr ""
"In conformità con le disposizioni del Regolamento generale sulla protezione "
"dei dati 679/2016 e della Legge organica 3/2018 del 5 dicembre sulla "
"protezione dei dati, ti informiamo che i dati forniti saranno incorporati in "
"un database di proprietà di BOX2BOX, S.L, con N.I.F B87985115 e indirizzo in "
"CALLE RIOS ROSAS 36 – 6IZQ – 28003 Madrid, con lo scopo di mantenere e "
"gestire i rapporti commerciali e amministrativi. La base giuridica del "
"trattamento è il rispetto della normativa fiscale, commerciale e contabile. "
"Non sono previsti incarichi e/o trasferimenti internazionali di dati se non "
"quelli autorizzati dalla legge o quelli autorizzati con il Suo espresso "
"consenso. Puoi esercitare i tuoi diritti di accesso, rettifica, "
"cancellazione (diritto all'oblio), limitazione d'el trattamento, portabilità "
"dei dati, opposizione e non essere soggetto a decisioni automatizzate "
"scrivendo a BOX2BOX, S.L, CALLE RIOS ROSAS 36 – 6IZQ – 28003 Madrid o "
"tramite e-mail <a href='mailto:{email}'>{email}</a> indicando come oggetto "
"&quot;Diritti della legge sulla protezione dei dati&quot; e allegando una "
"fotocopia del tuo documento d'identità. Inoltre, ti ricordiamo che puoi "
"contattare l'autorità competente per il controllo della protezione dei dati "
"(AEPD, in Spagna) per ottenere ulteriori informazioni o presentare un "
"reclamo.<br> "

#: contracts/service_templates/conditions.py:25
#: contracts/service_templates/conditions.py:59
msgid "title_2"
msgstr "PRIMO - Oggetto del contratto e descrizione del servizio"

#: contracts/service_templates/conditions.py:25
#: contracts/service_templates/conditions.py:59
msgid "conditions_2"
msgstr ""
"Il servizio di trasferimento e deposito oggetto del contratto è definito "
"come il trasferimento o il trasporto dall'indirizzo di ritiro stabilito ai "
"nostri centri di deposito (utilizzando uno o più mezzi di trasporto) di "
"mobili, merci o pacchi (di seguito, merci).<br><br> La Società dispone di "
"più centri di stoccaggio dislocati in luoghi diversi, pertanto la "
"destinazione della merce e il suo stoccaggio dipenderanno dalla "
"disponibilità di locali di stoccaggio idonei alla stessa nell'uno o "
"nell'altro centro (ciò non comporterà una differenza di costo da assumersi "
"da parte il cliente). Allo stesso modo, il servizio comprende il successivo "
"trasferimento o trasporto dai nostri centri di stoccaggio all'indirizzo di "
"consegna che viene determinato nel momento in cui il cliente lo indica e "
"sempre nei limiti di tempo della durata del contratto. Il servizio si "
"intende concluso al momento della consegna della merce oggetto del contratto "
"all'indirizzo di consegna, alle condizioni pattuite.<br> "

#: contracts/service_templates/conditions.py:26
#: contracts/service_templates/conditions.py:60
msgid "title_3"
msgstr "SECONDO"

#: contracts/service_templates/conditions.py:26
#: contracts/service_templates/conditions.py:60
msgid "conditions_3"
msgstr ""
"I servizi appaltati saranno costituiti da quelli cui si fa riferimento nella "
"prima condizione generale, nonché da tutti quelli conformi alla categoria di "
"servizi concordati con il cliente e che saranno specificati nelle condizioni "
"particolari del presente contratto se esistenti.<br> "

#: contracts/service_templates/conditions.py:27
#: contracts/service_templates/conditions.py:61
msgid "title_4"
msgstr "TERZO"

#: contracts/service_templates/conditions.py:27
#: contracts/service_templates/conditions.py:61
msgid "conditions_4"
msgstr ""
"L'azienda si occuperà del trasferimento dei mobili dall'abitazione del "
"cliente al luogo di deposito, nonché del trasporto degli stessi fino "
"all'indirizzo di consegna. I successivi viaggi supplementari saranno a "
"carico del Cliente, che si farà carico delle spese che ne derivano (a "
"partire da 9€).<br><br> La posizione della merce sarà la decisione della "
"società. A seconda della disponibilità logistica, possono verificarsi "
"possibili spostamenti di merci tra le diverse basi di stoccaggio.<br><br> "
"Non è possibile per il cliente accedere individualmente al luogo di "
"deposito, solo il nostro personale ha accesso e sarà lui che, su richiesta, "
"consegnerà la merce richiesta all'indirizzo di ritiro iniziale o altro.<br> "

#: contracts/service_templates/conditions.py:28
#: contracts/service_templates/conditions.py:62
msgid "title_5"
msgstr "QUARTO"

#: contracts/service_templates/conditions.py:28
#: contracts/service_templates/conditions.py:62
msgid "conditions_5"
msgstr ""
"Su richiesta del Trasportatore, il Cliente deve informare la Società delle "
"circostanze rilevanti per la corretta esecuzione delle attività in cui "
"consiste il servizio appaltato, nonché delle condizioni di accesso ai locali "
"o alloggi per il personale e i veicoli (auto parchi, altezza della casa, "
"corridoi, scale, ascensori, possibilità di utilizzo di ascensori, altri "
"lavori in corso nei rispettivi locali, ecc.). Nel caso in cui lo stabile non "
"sia dotato di ascensore e sia necessario che gli operatori salgano/scendano "
"scale o gradini per effettuare il servizio, l'azienda addebiterà un "
"supplemento che dipenderà dal volume della merce da trasportare trasportati "
"e il pavimento dove vengono eseguiti, il lavoro data la difficoltà "
"aggiuntiva di detta circostanza.<br><br> <h5 style='font-weight: 800'>4.1</"
"h5><br>  L'azienda non ha la capacità/permessi per eseguire lavori con funi "
"su finestra, gru o piattaforma, quindi nel caso in cui le circostanze della "
"raccolta o della consegna richiedano questi servizi, il cliente deve essere "
"responsabile per il proprio conto.<br><br> Nel caso in cui sia necessario "
"per gli operatori salire e scendere scale o gradini, BOX2BOX addebiterà un "
"supplemento che dipenderà dal volume della merce da trasportare e dal piano "
"dove si trova la merce. Il supplemento è di 5€ (IVA inclusa) per ogni m2 di "
"merce trasportata moltiplicato per ogni piano rialzato o ribassato.<br><br> "
"Si ritiene che per il carico e/o lo scarico dei mezzi gli stessi dovranno "
"poter essere ubicati alla porta dei luoghi di ritiro e riconsegna, ovvero ad "
"una distanza massima non superiore a 50 metri a piedi, ma se per eventuali "
"(strettezza della strada o dei viali, sosta limitata e vietata, ecc.), non "
"si realizza la suddetta collocazione del veicolo e ciò comporta un aumento "
"di manodopera o l'utilizzo di ulteriori mezzi di trasporto (furgoni o altri "
"elementi anche più leggeri) , le spese che questi servizi complementari "
"causeranno saranno a carico del cliente (minimo € 50).<br><br> Tariffe "
"secondo origine e destinazione: stessa località. Se è previsto un "
"supplemento nel servizio di ritiro, verrà applicato allo stesso modo nel "
"servizio di consegna.<br><br> Tariffe per origine e destinazione: punto di "
"raccolta e punto di destinazione non coincidono. Quando i luoghi di origine "
"e di destinazione sono diversi tra loro e se uno di essi si trova al di "
"fuori dell'area di servizio di ciascuna città in cui è immagazzinata la "
"merce (Madrid, Barcellona, Valencia, Malaga o Siviglia), il servizio di "
"spedizione sarà hanno una maggiorazione del costo di 0,90 euro per ogni "
"ulteriore chilometro percorso (prendendo come riferimento di provenienza il "
"centro della città dove si effettua il servizio). Tutto questo ad eccezione "
"di quelle località che sono contemplate all'interno dell'area di servizio "
"che puoi consultare sul nostro sito web.<br><br> Altri supplementi: <ul "
"style='list-style-type: disc; padding-left: 32px'> <li>Smontaggio e "
"montaggio: 60€/ora</li> <li>Supplemento parcheggio: 50€ min (escluse zone a "
"traffico limitato o pedonali)</li> <li>Supplemento viaggio aggiuntivo: Min. "
"€ 100 (indipendentemente dal contratto)</li> <li>Punto pulito: Min. € 35 "
"(aumento in base al volume)</li> <li>Vendita box: 3 €/unità</li> "
"<li>Imballaggio della scatola: € 7/unità</li> <li>Vendita box-armadio: 8 €/"
"unità</li> <li>Imballo scatola-armadio: 12 €/unità</li> <li>Supplemento "
"scale: 5€ al m2/piano</li> <li>Supplemento chilometrico: 0,90€/km</li> </ul> "

#: contracts/service_templates/conditions.py:29
#: contracts/service_templates/conditions.py:63
msgid "title_6"
msgstr "QUINTO - Pagamento e altre spese"

#: contracts/service_templates/conditions.py:29
#: contracts/service_templates/conditions.py:63
msgid "conditions_6"
msgstr ""
"Il Vettore si impegna ad effettuare il trasferimento e l'immagazzinamento "
"delle merci elencate nell'inventario che entrambe le parti sottoscrivono, al "
"prezzo e nei termini pattuiti nelle condizioni particolari, maggiorato, se "
"del caso, delle spese di cui alla QUARTA condizione.<br><br> Eventuali "
"formalità amministrative necessarie per effettuare il trasloco saranno a "
"carico del Cliente. Il pagamento di tutti i tipi di tasse, dazi doganali e "
"altre tasse, certificati di origine o trasferimento di residenza, spese "
"consolari, trasbordi, ecc., necessari per l'esecuzione del trasloco, non "
"sono compresi nel prezzo del contratto e verranno ad aumentare Questo. Il "
"Vettore è tenuto a documentare tali spese al Cliente.<br><br> Salvo diverso "
"accordo, il pagamento del servizio sarà effettuato come segue: <ul "
"style='list-style-type: disc; padding-left: 32px'>  <li>Una prenotazione dei "
"nostri servizi viene effettuata pagando un segnale variabile di € 5-€ 50 (a "
"seconda del servizio contrattato) con una carta di credito/debito (un link "
"viene inviato al cliente via e-mail per inserire i dettagli della carta "
"attraverso una verifica di sicurezza sistema).</li> <li>Una volta effettuato "
"l'incasso, al cliente vengono addebitati i servizi forniti (trasporto "
"incasso, supplementi e prima mensilità), effettuando un addebito sulla carta "
"informata, scontando il segnale già pagato.</li> <li>Le rate mensili "
"successive vengono addebitate allo stesso modo, tramite addebito su carta. "
"Il pagamento verrà effettuato per ogni mese di utilizzo del centro di "
"stoccaggio, la tariffa applicabile al servizio contrattato, con l'addebito "
"sul conto dopo 30 o 31 giorni (a seconda dei casi). La risoluzione del "
"contratto per la fornitura del servizio sarà effettuata 15 giorni prima del "
"pagamento della successiva rata mensile.</li> <li>Il servizio di stoccaggio "
"si rinnoverà automaticamente e successivamente alla scadenza di ogni mese, "
"semestre o anno, a seconda dei casi, a meno che il CLIENTE non disdica il "
"rinnovo del servizio di stoccaggio 15 giorni prima della sua scadenza (per i "
"Canoni mensili) o fino al mese prima della sua scadenza semestrale/annuale "
"(per rate semestrali e annuali).</li> Infine, al momento della consegna, "
"l'importo corrispondente alle spese di consegna e ai supplementi sarà pagato "
"mediante addebito sulla carta di credito o debito informata. La Società "
"consegnerà al Cliente ricevuta degli importi ricevuti e, una volta concluso "
"il trasloco, emetterà fattura, nei termini previsti dal R.D. 1496/2003, che "
"approva il Regolamento disciplinante gli obblighi di fatturazione e modifica "
"il Regolamento Iva. "

#: contracts/service_templates/conditions.py:30
#: contracts/service_templates/conditions.py:64
msgid "title_7"
msgstr "SESTO - Durata del contratto"

#: contracts/service_templates/conditions.py:30
#: contracts/service_templates/conditions.py:64
msgid "conditions_7"
msgstr ""
"La durata minima del contratto sarà di 3 mesi dal giorno del ritiro dei beni "
"del Cliente. Il servizio di stoccaggio si rinnoverà automaticamente e "
"successivamente alla scadenza di ogni mese o anno, a seconda dei casi, a "
"meno che il CLIENTE non disdica il rinnovo del servizio di stoccaggio 15 "
"giorni prima della sua scadenza.<br> "

#: contracts/service_templates/conditions.py:31
#: contracts/service_templates/conditions.py:65
msgid "title_8"
msgstr "SETTIMO- Violazione dell'obbligo di pagamento da parte del CLIENTE"

#: contracts/service_templates/conditions.py:31
#: contracts/service_templates/conditions.py:65
msgid "conditions_8"
msgstr ""
"Il CLIENTE deve mantenere aggiornate le informazioni di pagamento, se la "
"carta scade BOX2BOX invierà al CLIENTE una notifica per informarlo e il "
"CLIENTE deve contattare BOX2BOX per concordare la correzione del problema."
"<br><br> In caso di mancato pagamento o restituzione della bolletta di "
"addebito diretto per una rata mensile, verrà applicata una maggiorazione del "
"15% alla tariffa pattuita per ogni mese in cui si è verificato l'incidente. "
"Per risolvere il mancato pagamento, la società trasferirà i dati del cliente "
"a un'agenzia di riscossione. Se il cliente non regolarizza questa situazione "
"entro un periodo massimo di 2 mesi dalla richiesta di pagamento da parte "
"della Società, il presente contratto si risolverà automaticamente e la merce "
"depositata presso le nostre strutture sarà intesa come &quot;"
"abbandono&quot;, il tutto sotto la protezione dell'articolo 460 c.c. e "
"comprendendo che tale condotta ed atto del cliente traduce la sua volontà di "
"cessare di esercitare il potere di fatto che aveva sui beni, quindi questi "
"essendo idonei all'occupazione da parte di BOX2BOX S.L. La Società disporrà "
"dei beni come riterrà opportuno senza limitazioni diverse da quelle "
"stabilite dalla Legge.<br> "

#: contracts/service_templates/conditions.py:32
#: contracts/service_templates/conditions.py:66
msgid "title_9"
msgstr "OTTAVO - Responsabilità"

#: contracts/service_templates/conditions.py:32
#: contracts/service_templates/conditions.py:66
msgid "conditions_9"
msgstr ""
"Il Cliente dichiara sotto la propria responsabilità che i beni, colli o "
"mobili oggetto di cessione e deposito sono merci generiche, aventi corso "
"legale, allo stesso modo dichiara che non sono prodotti alimentari "
"infiammabili, esplosivi o deperibili. Dichiara inoltre di essere a "
"conoscenza del contenuto dei colli, merci o mobili immagazzinati e di "
"assumersene la piena responsabilità.<br><br> Quando il Cliente ci fornisce "
"merce imballata in una scatola chiusa/borsa/zaino/valigia o simili e la "
"Società di Trasporto non ne conosce il contenuto, non sarà responsabile per "
"il contenuto smarrito o danneggiato.annate.<br><br> Si consiglia di non "
"trasportare e conservare televisori o altri oggetti elettronici con le "
"stesse caratteristiche sprovvisti della loro scatola originale. Il trasporto "
"e la movimentazione di questi articoli comporta alcuni rischi che non "
"possono essere evitati se l'imballaggio utilizzato non è quello "
"dell'articolo. Se il CLIENTE decide di inserire nell'inventario gli oggetti "
"televisivi/elettronici senza fornire la scatola originale dello stesso, la "
"SOCIETÀ non è responsabile per eventuali danni causati nel trasporto o nella "
"conservazione.<br><br> Il cliente è responsabile di proteggere adeguatamente "
"la merce e che l'imballaggio utilizzato sia corretto, rispondendo di ciò che "
"è inadeguato, difettoso o utilizzato in modo improprio, se ha causato danni "
"o danni alla merce, alle attrezzature di movimentazione o ai mezzi di "
"trasporto, se a qualsiasi altro beni o persone.<br><br> BOX2BOX S.L mette a "
"disposizione del cliente il nostro servizio di imballaggio la cui tariffa "
"sarà a partire da 10 euro (alla tariffa dello spazio in metri). Nel caso in "
"cui il cliente non opti per l'uno o l'altro sistema di imballaggio, la merce "
"non sarà coperta dalla nostra assicurazione.<br> "

#: contracts/service_templates/conditions.py:33
#: contracts/service_templates/conditions.py:67
msgid "title_10"
msgstr "NONO - Inventario"

#: contracts/service_templates/conditions.py:33
#: contracts/service_templates/conditions.py:67
msgid "conditions_10"
msgstr ""
"L'Impresa di Trasporti si impegna a redigere un inventario dei mobili e dei "
"beni oggetto del trasloco, consegnandone copia timbrata e firmata dal "
"responsabile dell'impresa, al Cliente. Prima del ritiro, il Cliente farà una "
"dichiarazione dettagliata degli oggetti che possono avere un valore "
"particolare, essendo obbligatorio avvertire di quelli il cui valore unitario "
"supera l'importo di 500 euro. Inoltre, è vietato il deposito e il trasporto "
"di quei beni di natura artistica, storica o da collezione.<br><br> "
"Parimenti, quando a giudizio dell'Impresa di Trasporto sussiste la "
"possibilità di deterioramento dei beni oggetto del trasloco, vuoi perché in "
"cattive condizioni, vuoi perché devono essere sottoposti a manipolazioni che "
"possonopossa comportare pericolo, tale circostanza sarà chiaramente espressa "
"nell'inventario, con esonero di responsabilità, ove opportuno, da parte "
"dell'Impresa di Trasporto.<br> "

#: contracts/service_templates/conditions.py:34
#: contracts/service_templates/conditions.py:68
msgid "title_11"
msgstr "DECIMO - Assicurazione sulla merce"

#: contracts/service_templates/conditions.py:34
#: contracts/service_templates/conditions.py:68
msgid "conditions_11"
msgstr ""
"L'Impresa di Trasporti si impegna ad assicurare le merci trasportate e "
"riportate nell'inventario per un valore complessivo massimo di 3.000 euro. "
"Allo stesso modo, viene applicato un valore massimo di € 500 per oggetto/"
"mobile/pezzo.<br><br> Qualora la merce, che doveva essere movimentata, "
"immagazzinata e/o movimentata, avesse un valore complessivo superiore a "
"3.000 euro, il Cliente avrà la possibilità di stipulare un'assicurazione "
"privata che copra l'eccedenza di valore della stessa.<br><br> L'Impresa di "
"Trasporto comunicherà al CLIENTE il nome della compagnia assicurativa presso "
"la quale è stata stipulata l'assicurazione e il numero di polizza.<br> "

#: contracts/service_templates/conditions.py:35
#: contracts/service_templates/conditions.py:69
msgid "title_12"
msgstr "UNDICESIMO - Ricezione del trasloco"

#: contracts/service_templates/conditions.py:35
#: contracts/service_templates/conditions.py:69
msgid "conditions_12"
msgstr ""
"Una volta completato il trasloco, il Cliente dovrà firmare al Trasportatore "
"una copia dell'inventario, al fine di dare il proprio assenso al ricevimento "
"di tutti i mobili, oggetti e casse oggetto del trasloco. Una volta che il "
"Cliente riceve la merce e, con essa, termina (totalmente o parzialmente, se "
"del caso) la fornitura del servizio, il Cliente avrà un periodo di 48 ore "
"per Reclamare dall'Azienda di Trasporto eventuali danni a mobili, merci o "
"colli che abbiate potuto verificare. Trascorso tale termine, nessun reclamo "
"sarà accettato.<br> "

#: contracts/service_templates/conditions.py:37
#: contracts/service_templates/conditions.py:71
msgid "title_13"
msgstr ""
"DODICESIMO - Risoluzione del contratto e differimento della sua esecuzione"

#: contracts/service_templates/conditions.py:38
#: contracts/service_templates/conditions.py:72
#, python-brace-format
msgid "conditions_13 {email}"
msgstr ""
"La disdetta o il rinvio del servizio di trasferimento mobili e cose su "
"indicazione del Cliente con un preavviso inferiore alle 24 ore darà luogo ad "
"un indennizzo a favore dell'Azienda di Trasporto di un importo di € 50 (IVA "
"INCLUSA) qualora si tratti di incasso iniziale degli effetti personali del "
"cliente. Per gli altri servizi di trasporto,<br><br> tale compenso è fissato "
"all'equivalente del costo del viaggio e delle risorse logistiche messe a "
"disposizione per compiere il viaggio necessario. Se la notifica avviene più "
"di 24 ore lavorative prima della data concordata per l'inizio del trasloco, "
"non ci sarà alcun compenso per nessuna delle parti.<br><br> L'esecuzione del "
"servizio sarà concordata in una fascia oraria indicata dalla Società. Il "
"Cliente deve essere reperibile a casa in tale fascia oraria, in caso "
"contrario verrà applicata una penale di € 20 dopo i primi 20 minuti di "
"attesa. Dopo 50 minuti, la penale sarà di 50€. Nel caso in cui il Corriere "
"sia in attesa e il cliente non risponda al telefono, verrà effettuato "
"l'addebito di 50€ e l'immediato annullamento del ritiro. Dopo aver contratto "
"il servizio, il Cliente può annullare e recedere da detto contratto entro un "
"periodo massimo di 14 giorni lavorativi, con o senza giustificato motivo, "
"senza alcun tipo di penale, a condizione che il Servizio non sia stato "
"eseguito. Se la cancellazione del servizio avviene entro le 24 ore "
"precedenti la data in cui è stato programmato il ritiro della merce, BOX2BOX "
"avrà diritto a ricevere il costo del viaggio, nonché quelle spese sostenute "
"per l'esecuzione del programmato trasferimento, e di cui il Cliente sarà "
"stato preventivamente informato.<br><br> Per esercitare il diritto di "
"recesso, è necessario comunicarci tramite e-mail <a href='mailto:"
"{email}'>{email}</a> la decisione di recedere dal contratto mediante una "
"dichiarazione esplicita.<br><br> È necessario allegare un file all'e-mail "
"che includa le seguenti informazioni: <br> <span style='display:inline-"
"block; width:100%;'>All'attenzione di BOX2BOX</span>  <span style='display:"
"inline-block; width:100%;'>Con la presente comunico a Lei/noi (*) che recede "
"dal mio/noi recede dal nostro (*) contratto di vendita per il seguente "
"prodotto (*):</span> <span style='display:inline-block; width:100%; padding-"
"left:32px;'>— ordinato il (*)/ricevuto il (*)</span> <span style='display:"
"inline-block; width:100%; padding-left:32px;'>— nome del consumatore o dei "
"consumatori</span> <span style='display:inline-block; width:100%; padding-"
"left:32px;'>— Indirizzo del consumatore o dei consumatori</span> <span "
"style='display: inline-block; width:100%; padding-left:32px;'>— Data</span>  "

#: contracts/service_templates/conditions.py:42
#: contracts/service_templates/conditions.py:74
msgid "title_14"
msgstr "TREDICESIMO - Competenza"

#: contracts/service_templates/conditions.py:42
#: contracts/service_templates/conditions.py:74
msgid "conditions_14"
msgstr ""
"Per la risoluzione di qualsiasi controversia che sorga tra le parti in "
"relazione all'interpretazione eall'applicazione del presente contratto, o di "
"una qualsiasi delle clausole, saranno competenti le Corti e iTribunali di "
"Madrid.<br>"

#: contracts/service_templates/conditions.py:75
#| msgid "title_15"
msgid "title_15"
msgstr ""

#: contracts/service_templates/conditions.py:75
#| msgid "conditions_15"
msgid "conditions_15"
msgstr ""

#: contracts/service_templates/conditions.py:76
#| msgid "title_16"
msgid "title_16"
msgstr ""

#: contracts/service_templates/conditions.py:76
#| msgid "conditions_6"
msgid "conditions_16"
msgstr ""

#: contracts/service_templates/conditions.py:77
#| msgid "title_17"
msgid "title_17"
msgstr ""

#: contracts/service_templates/conditions.py:77
#| msgid "conditions_17"
msgid "conditions_17"
msgstr ""

#: contracts/service_templates/conditions.py:78
#| msgid "title_18"
msgid "title_18"
msgstr ""

#: contracts/service_templates/conditions.py:78
#| msgid "conditions_8"
msgid "conditions_18"
msgstr ""

#: contracts/service_templates/conditions.py:79
#| msgid "title_19"
msgid "title_19"
msgstr ""

#: contracts/service_templates/conditions.py:79
#| msgid "conditions_19"
msgid "conditions_19"
msgstr ""

#: contracts/service_templates/contracts.py:15
msgid "Furniture packaging"
msgstr "Imballaggio di mobili"

#: contracts/service_templates/contracts.py:15
msgid "Furniture packaging service"
msgstr "Servizi di imballaggio di mobili"

#: contracts/service_templates/contracts.py:19
msgid "Furniture assembly/disassembly"
msgstr "Montaggio o smontaggio di mobili"

#: contracts/service_templates/contracts.py:20
msgid "Furniture assembly/disassembly service"
msgstr "Servizi di montaggio o smontaggio di mobili"

#: contracts/service_templates/contracts.py:25
#: contracts/service_templates/contracts.py:32
msgid "Time spent"
msgstr "Tempo impiegato"

#: contracts/service_templates/contracts.py:38
msgid "Assembled/Disassembled furniture"
msgstr "Descrizione"

#: contracts/service_templates/contracts.py:40
msgid "Stairs service"
msgstr "Servizio scale"

#: contracts/service_templates/contracts.py:41
msgid "Floors"
msgstr "Número di piani"

#: contracts/service_templates/contracts.py:45
msgid "Volume moved in m²"
msgstr "Volume in m²"

#: contracts/service_templates/contracts.py:51
msgid "Packaging of moving boxes"
msgstr "Imballaggio scatole trasloco"

#: contracts/service_templates/contracts.py:52
msgid "Packaging of moving boxes service"
msgstr "Servizi di imballaggio scatole trasloco"

#: contracts/service_templates/contracts.py:57
msgid "Packaging of wardrobe boxes"
msgstr "Imballaggio scatole-armadio"

#: contracts/service_templates/contracts.py:58
msgid "Packaging of wardrobe boxes service"
msgstr "Servizi di imballaggio scatole-armadio"

#: contracts/service_templates/contracts.py:61
msgid "Parked withing a range of 50m"
msgstr "Distanza superiore a 50m del domicilio"

#: contracts/service_templates/contracts.py:61
msgid "Parking"
msgstr "Parcheggio"

#: contracts/service_templates/contracts.py:62
msgid "Kilometers"
msgstr "Chilometraggio"

#: contracts/service_templates/contracts.py:66
msgid "Distance"
msgstr "Distanza"

#: contracts/service_templates/contracts.py:74
msgid "Clean spot"
msgstr "Smaltimento rifiuti"

#: contracts/service_templates/contracts.py:78
msgid "Volume moved to clean spot"
msgstr "Volume smaltite\t"

#: contracts/service_templates/contracts.py:88
msgid "Additional stop"
msgstr "Fermata supplementare"

#: contracts/service_templates/contracts.py:89
msgid "Comments"
msgstr "Commenti"

#: contracts/service_templates/contracts.py:148
#, python-brace-format
msgid "{type} delivery note"
msgstr "Bolla di consegna di {type}"

#: contracts/service_templates/contracts.py:168
#: riders/admin_filters/riders.py:21
msgid "Service"
msgstr "Servizi"

#: contracts/service_templates/contracts.py:171
msgid "Space in m²"
msgstr "Spazio in m²"

#: contracts/service_templates/contracts.py:177
msgid "Code"
msgstr "Codice"

#: contracts/service_templates/contracts.py:177
msgid "Description"
msgstr "Descrizione"

#: contracts/service_templates/movings.py:17
msgid "Moving service"
msgstr "Bolla di consegna di trasloco"

#: contracts/service_templates/movings.py:18
msgid "Pictures"
msgstr "Foto"

#: contracts/utils.py:141
msgid "Madrid"
msgstr ""

#: contracts/utils.py:142
msgid "Porto"
msgstr ""

#: contracts/utils.py:143
msgid "Sevilla"
msgstr ""

#: contracts/utils.py:144
msgid "Barcelona"
msgstr ""

#: contracts/utils.py:145
msgid "Malaga"
msgstr ""

#: contracts/utils.py:146
msgid "Valencia"
msgstr ""

#: contracts/utils.py:147
msgid "Milano"
msgstr ""

#: contracts/utils.py:148
msgid "Paris"
msgstr ""

#: contracts/utils.py:149
msgid "Lisboa"
msgstr ""

#: contracts/utils.py:150
msgid "Queretaro"
msgstr ""

#: contracts/views/service.py:238
#, fuzzy
#| msgid "Access type"
msgid "Accepted"
msgstr "Tipo di accesso"

#: contracts/views/service.py:265
msgid "Released"
msgstr ""

#: contracts/views/service.py:282
msgid "Discarded"
msgstr ""

#: core/chargebee_errors.py:4
msgid "Invalid format"
msgstr ""

#: core/chargebee_errors.py:5
msgid "Payment cannot be created as the payment collection failed"
msgstr ""

#: core/chargebee_errors.py:6
msgid "There's a problem adding the payment method"
msgstr ""

#: core/chargebee_errors.py:7 core/chargebee_errors.py:9
msgid "Unknown error in the payment gateway. Wait few minutes and try again"
msgstr ""

#: core/chargebee_errors.py:8
msgid "Error processing the payment. Try again later"
msgstr ""

#: core/chargebee_errors.py:10
msgid "Unknown error. Please, wait few minutes and try again"
msgstr ""

#: core/exceptions.py:35
msgid "Invalid input."
msgstr ""

#: core/handlers.py:25
msgid "Not found"
msgstr ""

#: core/handlers.py:34
msgid ""
"It seems there is a conflict with your input data. Maybe is due to a "
"duplicated or deleted entry. Please, review your data and try again"
msgstr ""

#: core/models/event_log.py:22
msgid "Unknown event"
msgstr ""

#: core/models/event_log.py:23
msgid "Hubspot event"
msgstr ""

#: core/models/event_log.py:24
msgid "Moreapp event"
msgstr ""

#: core/models/event_log.py:25
msgid "Moreapp inventory event"
msgstr ""

#: core/models/event_log.py:26
msgid "Riderapp event"
msgstr ""

#: core/models/event_log.py:27
msgid "Chargebee event"
msgstr ""

#: core/models/event_log.py:28
msgid "Microservice event"
msgstr ""

#: core/models/event_log.py:29
msgid "Storage event"
msgstr ""

#: core/models/moloni.py:20 intranet/models/delivery.py:39
msgid "Empty"
msgstr ""

#: core/models/moloni.py:21
msgid "Customer"
msgstr ""

#: core/models/moloni.py:22 payments/admin.py:37
msgid "Invoice"
msgstr ""

#: core/models/moloni.py:23
msgid "Credit note"
msgstr ""

#: core/models/product.py:18
msgid "One time"
msgstr ""

#: core/notifications.py:49
#, python-brace-format
msgid "Service {contract_id}"
msgstr ""

#: core/serializers.py:27
msgid ""
"Required to get the access thru the API Gateway in production environment"
msgstr ""

#: core/utils.py:366
msgid "Yes"
msgstr "Sì"

#: core/utils.py:367
msgid "No"
msgstr "No"

#: intranet/admin.py:58 riders/models/riders.py:43
msgid "Sent"
msgstr ""

#: intranet/admin.py:92 riders/admin.py:365 users/admin.py:69
msgid "Personal info"
msgstr ""

#: intranet/admin.py:100 users/admin.py:83
msgid "User Image"
msgstr ""

#: intranet/admin.py:101
msgid "Misc"
msgstr ""

#: intranet/admin.py:102 riders/admin.py:373 users/admin.py:84
msgid "Important dates"
msgstr ""

#: intranet/admin.py:103 riders/admin.py:376 users/admin.py:90
msgid "Permissions"
msgstr ""

#: intranet/admin.py:105 riders/admin.py:378 users/admin.py:92
msgid "Django Admin Permissions"
msgstr ""

#: intranet/admin.py:124 users/admin.py:120
msgid "Avatar"
msgstr ""

#: intranet/admin.py:210
msgid "Team"
msgstr ""

#: intranet/filters/calendars.py:12
msgid "Filter by the user who makes the request"
msgstr ""

#: intranet/filters/calendars.py:15
msgid "Filter only calendars without ownership"
msgstr ""

#: intranet/filters/calendars.py:18
msgid "Filter only calendars which belong to a given city"
msgstr ""

#: intranet/filters/service.py:16
msgid "Date when the service has been scheduled in the calendar"
msgstr ""

#: intranet/filters/service.py:24
msgid "Brings services filtered by cities"
msgstr ""

#: intranet/filters/service.py:30
msgid "Filter teams by calendar"
msgstr ""

#: intranet/filters/user.py:24
msgid "Filter by another backoffice allowed users"
msgstr ""

#: intranet/filters/user.py:30
msgid "Exclude user from query"
msgstr ""

#: intranet/filters/warehouse.py:12
msgid "Bring warehouses with related FKs: calendars and riders"
msgstr ""

#: intranet/filters/warehouse.py:18
msgid "Bring warehouses that belong to a given city"
msgstr ""

#: intranet/filters/warehouse_differences.py:13
msgid "Brings only differences from a specific warehouse"
msgstr ""

#: intranet/filters/warehouse_differences.py:18
msgid "Filter by month. You can pass a full date (YYYY-MM-DD)"
msgstr ""

#: intranet/models/budget_estimator.py:18 intranet/models/delivery.py:46
msgid "Attribute to keep the whole view data"
msgstr ""

#: intranet/models/budget_estimator.py:25
msgid "Inventory resume data to upload in hubspot"
msgstr ""

#: intranet/models/budget_estimator.py:27
msgid "If this budget estimation is in hubspot"
msgstr ""

#: intranet/models/budget_estimator.py:31
msgid "If this budget estimation is before Mexico refactoring"
msgstr ""

#: intranet/models/budget_estimator.py:37
msgid "Site where user is allowed to be from intranet"
msgstr ""

#: intranet/models/budget_estimator.py:46
msgid ""
"In order to publish the estimation in Hubspot, a valid contract is needed"
msgstr ""

#: intranet/models/calendar.py:12
msgid "City id"
msgstr ""

#: intranet/models/calendar.py:14
msgid "Calendar id on google calendar"
msgstr ""

#: intranet/models/calendar.py:16
msgid "UI color to display this calendar in hexadecimal"
msgstr ""

#: intranet/models/calendar.py:45
msgid "Created by the user"
msgstr ""

#: intranet/models/calendar.py:47
msgid "Calendar linked to this event"
msgstr ""

#: intranet/models/calendar.py:61
msgid "Used to identify the kind of service"
msgstr ""

#: intranet/models/delivery.py:42
msgid "User requested delivery"
msgstr "Consegna richiesta dal cliente"

#: intranet/models/delivery.py:77 intranet/models/delivery.py:106
msgid "Precondition failed to build the excel"
msgstr ""

#: intranet/models/delivery.py:80
msgid "Empty data to build the excel file"
msgstr ""

#: intranet/models/delivery.py:83
msgid "Empty delivery date"
msgstr ""

#: intranet/models/delivery.py:109
msgid "User name is required to send the inventory email to Logwin warehouse"
msgstr ""

#: intranet/models/delivery.py:114
#, python-brace-format
msgid ""
"Delivery email skipped temporarily because warehouse is not defined: {model}"
msgstr ""

#: intranet/models/delivery.py:120
msgid "There was an error building the excel to send it to the warehouse"
msgstr ""

#: intranet/models/delivery.py:137
#, python-brace-format
msgid "{request} - {contract_id}"
msgstr ""

#: intranet/models/delivery.py:169
msgid "Precondition failed to send the cancellation email"
msgstr ""

#: intranet/models/delivery.py:175
#, python-brace-format
msgid ""
"Cancellation of delivery email skipped temporarily because warehouse is not "
"defined: {model}"
msgstr ""

#: intranet/models/delivery.py:185
#, python-brace-format
msgid "{request} - {contract_id} - Canceled"
msgstr "{request} - {contract_id} - Annullato"

#: intranet/models/payment_reminder.py:24 users/models.py:66
msgid "Email address"
msgstr ""

#: intranet/models/payment_reminder.py:37
msgid "Used to avoid duplicated slack events to be handled"
msgstr ""

#: intranet/models/payment_reminder.py:65
#, python-brace-format
msgid "BOX2BOX - Your pickup is coming {date}"
msgstr "BOX2BOX - Il vostro ritiro si avvicina {date}"

#: intranet/models/userproxy.py:22 riders/models/riders.py:198
msgid "User"
msgstr ""

#: intranet/models/userproxy.py:43
#, python-brace-format
msgid "{prefix} Activate your account to get access in the Backoffice portal"
msgstr ""

#: intranet/models/warehouse_differences.py:28
msgid "Pending"
msgstr ""

#: intranet/models/warehouse_differences.py:29 payments/models/payments.py:64
msgid "Running"
msgstr ""

#: intranet/models/warehouse_differences.py:30 payments/models/payments.py:45
#: payments/models/payments.py:198
msgid "Failed"
msgstr ""

#: intranet/models/warehouse_differences.py:31
msgid "Success"
msgstr ""

#: intranet/models/warehouse_differences.py:47
msgid "Warehouse difference between warehouse and our space"
msgstr ""

#: intranet/models/warehouse_differences.py:49
msgid "Sum of warehouse space"
msgstr ""

#: intranet/models/warehouse_differences.py:50
msgid "Sum of our space"
msgstr ""

#: intranet/serializers/budget_estimation.py:23
msgid "Fill in only if contract is null"
msgstr ""

#: intranet/serializers/budget_estimation.py:29
msgid ""
"If contract_document_id is filled in, it will have priority over this "
"attribute"
msgstr ""

#: intranet/serializers/budget_estimation.py:47
msgid "A valid contract is needed in order to create a budget estimation"
msgstr ""

#: intranet/serializers/calendar.py:186
msgid "You are trying to share this calendar with non allowed users"
msgstr ""

#: intranet/serializers/calendar_scheduler.py:11
msgid "Title"
msgstr ""

#: intranet/serializers/event.py:180
msgid "Warehouse id required when pickup or delivery is selected"
msgstr ""

#: intranet/serializers/event.py:237
msgid "Start time cannot be greater than end time"
msgstr ""

#: intranet/serializers/event.py:249
msgid "Riders to add to this team"
msgstr ""

#: intranet/serializers/event.py:272
msgid "Service or task is required to create events"
msgstr ""

#: intranet/serializers/event.py:276
msgid "Only one from service and task can be specified, not both"
msgstr ""

#: intranet/serializers/event.py:281 intranet/serializers/event.py:444
msgid "Calendar is not set but the teams are"
msgstr ""

#: intranet/serializers/event.py:415
msgid "Needed to delete the old event in calendar"
msgstr ""

#: intranet/serializers/event.py:432
msgid "Event cannot be updated because is not created on the remote calendar"
msgstr ""

#: intranet/serializers/event.py:436
msgid "Service and task cannot be set together"
msgstr ""

#: intranet/serializers/history/base_history.py:29
msgid ""
"* +: create\n"
"* -: delete\n"
"* ~: update"
msgstr ""

#: intranet/serializers/rider.py:152
msgid "Pending accounts cannot be activated. Please, activate it first."
msgstr ""

#: intranet/serializers/team.py:39
msgid "Choosing all services/riders is mandatory to set date and city as well"
msgstr ""

#: intranet/serializers/user.py:60 intranet/serializers/user.py:66
#: users/auth/serializers.py:163 users/auth/serializers.py:169
msgid "Invalid activation account URL"
msgstr ""

#: intranet/serializers/user.py:63 users/auth/serializers.py:166
msgid "Account is already active"
msgstr ""

#: intranet/serializers/warehouse_differences.py:41
msgid ""
"There's already another process to check discrepancies between warehouse and "
"us. Please, wait if it is running or choose another date. "
msgstr ""

#: intranet/views/calendar.py:101
msgid "Remote calendar could not be deleted"
msgstr ""

#: intranet/views/calendar.py:121
msgid "Calendar is already created"
msgstr ""

#: intranet/views/calendar.py:141
msgid "Error creating the calendar on remote source"
msgstr ""

#: intranet/views/calendar.py:145 intranet/views/calendar.py:177
#: intranet/views/calendar.py:201 intranet/views/rider.py:173
#: intranet/views/service.py:180 intranet/views/team.py:112
#: intranet/views/team.py:150 users/views.py:93
msgid "OK"
msgstr ""

#: intranet/views/calendar.py:205
msgid "Visibility could not be changed"
msgstr ""

#: intranet/views/calendar_scheduler.py:45
#: intranet/views/calendar_scheduler.py:58
msgid "Error retrieving google calendars"
msgstr ""

#: intranet/views/calendar_scheduler.py:63
msgid "Error retrieving google calendars due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:83
#: intranet/views/calendar_scheduler.py:117
msgid "Error creating the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:121
msgid "Error creating event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:132
#: intranet/views/calendar_scheduler.py:153
msgid "Error updating the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:157
msgid "Error updating event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:168
#: intranet/views/calendar_scheduler.py:188
msgid "Error deleting the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:192
msgid "Error deleting event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:238
#, python-brace-format
msgid "Error performing full sync from calendar api: {calendar_id}"
msgstr ""

#: intranet/views/calendar_scheduler.py:338
msgid "Error performing partial sync in calendar"
msgstr ""

#: intranet/views/calendar_scheduler.py:343
msgid "Error performing a partial sync in calendar due to rate limit"
msgstr ""

#: intranet/views/chargebee.py:45 users/auth/custom_jwt/authentication.py:60
#: users/auth/custom_jwt/tokens.py:202
msgid "User not found"
msgstr ""

#: intranet/views/chargebee.py:54
msgid "Error fetching chargebee subscription data"
msgstr ""

#: intranet/views/contracts.py:149
msgid "Error fetching chargebee invoices. Please, try again later"
msgstr ""

#: intranet/views/events.py:198
msgid "Remote calendar event could not be deleted"
msgstr ""

#: intranet/views/events.py:223
msgid "Event is already created in remote calendar"
msgstr ""

#: intranet/views/events.py:229
msgid "Event doesn't have any service or task linked to it"
msgstr ""

#: intranet/views/events.py:267
msgid "Error creating the event on remote calendar"
msgstr ""

#: intranet/views/events.py:272 intranet/views/rider_configuration.py:86
#: intranet/views/rider_configuration.py:133
#: intranet/views/rider_configuration.py:199
#: intranet/views/rider_configuration.py:258
msgid "Concurrent operation. Try again later."
msgstr ""

#: intranet/views/events.py:277 intranet/views/rider_configuration.py:91
#: intranet/views/rider_configuration.py:138
#: intranet/views/rider_configuration.py:204
#: intranet/views/rider_configuration.py:263
msgid "Error processing the request. Try again later."
msgstr ""

#: intranet/views/events.py:281
msgid "Event created"
msgstr ""

#: intranet/views/hubspot.py:136
msgid "Error fetching hubspot data"
msgstr ""

#: intranet/views/rider.py:176
msgid "KO"
msgstr ""

#: intranet/views/rider_configuration.py:82
msgid "Error creating the API key"
msgstr ""

#: intranet/views/rider_configuration.py:117
msgid "API Key cannot be revoked because does not exist"
msgstr ""

#: intranet/views/rider_configuration.py:129
msgid "Error revoking the API key"
msgstr ""

#: intranet/views/rider_configuration.py:168 riders/admin.py:179
#: riders/receivers.py:43
#, python-brace-format
msgid "Service account for rider conf {rider}"
msgstr ""

#: intranet/views/rider_configuration.py:194
msgid "Error creating the service account"
msgstr ""

#: intranet/views/rider_configuration.py:224
msgid "Service Account cannot be deleted because does not exist"
msgstr ""

#: intranet/views/rider_configuration.py:253
msgid "Error deleting the service account"
msgstr ""

#: intranet/views/service.py:153
msgid "Finished services cannot be notified to riders"
msgstr ""

#: intranet/views/service.py:173
msgid "Error sending notification"
msgstr ""

#: intranet/views/service.py:183
msgid "Error notifying riders"
msgstr ""

#: intranet/views/team.py:133
#, python-brace-format
msgid "Error sending push notification to rider with error code {error}"
msgstr ""

#: intranet/views/team.py:141
msgid "Wrong token provided or missing configuration"
msgstr ""

#: intranet/views/warehouse_differences.py:51
msgid "You cannot retry it because is already running"
msgstr ""

#: intranet/views/warehouse_differences.py:59
msgid "Error creating the task. Try again later"
msgstr ""

#: moreapp/admin.py:16
msgid "Service Type"
msgstr ""

#: moreapp/models.py:26
msgid "Moreapp"
msgstr ""

#: moreapp/models.py:27
msgid "Ridersapp"
msgstr ""

#: moreapp/models.py:38
msgid "If this file has been sent"
msgstr ""

#: moreapp/models.py:92
#, python-brace-format
msgid ""
"Customer notification {contract_id} BOX2BOX SL {base} - SERVICE {service} - "
"MOVEMENT {date}"
msgstr ""
"Avviso ai clienti {contract_id} BOX2BOX SL {base} - SERVIZIO {service} - "
"MOVIMENTO {date}"

#: payments/admin.py:25 payments/admin.py:43 payments/admin.py:138
msgid "Total"
msgstr ""

#: payments/admin.py:62 payments/admin.py:82
#: webhooks/serializers/serializers.py:29
msgid "Chargebee id"
msgstr ""

#: payments/admin.py:86
msgid "Create in chargebee"
msgstr ""

#: payments/admin.py:91
#, python-brace-format
msgid "Quote {quote} created successfully"
msgstr ""

#: payments/admin.py:93
#, python-brace-format
msgid "Quote {quote} not created"
msgstr ""

#: payments/models/billing_data.py:26
msgid "User first name. This may not be the same as the user's name."
msgstr ""

#: payments/models/billing_data.py:32
msgid "User last name. This may not be the same as the user's last name."
msgstr ""

#: payments/models/billing_data.py:34
msgid "Company name"
msgstr ""

#: payments/models/billing_data.py:35
msgid "Customer's address"
msgstr ""

#: payments/models/invoice.py:18 payments/models/payments.py:199
msgid "Created"
msgstr ""

#: payments/models/invoice.py:19
msgid "Voided"
msgstr ""

#: payments/models/invoice.py:20
msgid "Processing"
msgstr ""

#: payments/models/invoice.py:24
msgid "Date when invoice has been emitted"
msgstr ""

#: payments/models/invoice.py:25
msgid "Remote reference number"
msgstr ""

#: payments/models/invoice.py:26
msgid "Chargebee remote id"
msgstr ""

#: payments/models/invoice.py:62
msgid "Indicates that is an invoice from deposit"
msgstr ""

#: payments/models/invoice.py:66
msgid "Related transaction to this payment"
msgstr ""

#: payments/models/invoice.py:68
msgid "Last date of applied fee due to chargebacks"
msgstr ""

#: payments/models/payment_method.py:39
msgid "Credit"
msgstr ""

#: payments/models/payment_method.py:40
msgid "Prepaid"
msgstr ""

#: payments/models/payment_method.py:41
msgid "Debit"
msgstr ""

#: payments/models/payment_method.py:56
msgid "American Express"
msgstr ""

#: payments/models/payment_method.py:57
msgid "Diners Club"
msgstr ""

#: payments/models/payment_method.py:58
msgid "Discover"
msgstr ""

#: payments/models/payment_method.py:59
msgid "Jcb"
msgstr ""

#: payments/models/payment_method.py:60
msgid "Mastercard"
msgstr ""

#: payments/models/payment_method.py:61
msgid "Unionpay"
msgstr ""

#: payments/models/payment_method.py:62
msgid "Visa"
msgstr ""

#: payments/models/payment_method.py:63
msgid "Other"
msgstr ""

#: payments/models/payments.py:43 payments/models/payments.py:197
msgid "No created"
msgstr ""

#: payments/models/payments.py:44
msgid "Active"
msgstr ""

#: payments/models/payments.py:46
msgid "Canceled"
msgstr ""

#: payments/models/payments.py:56
msgid "Yearly"
msgstr ""

#: payments/models/payments.py:63
msgid "Not running"
msgstr ""

#: payments/models/payments.py:68 payments/models/payments.py:222
msgid "Remote id in external service"
msgstr ""

#: payments/models/payments.py:84
msgid "Current billing cycle to trigger the free month. Every 6 months"
msgstr ""

#: payments/models/payments.py:88
msgid "If this subscription has a free month allowed"
msgstr ""

#: payments/models/payments.py:91
msgid "If this subscription has to be cancel in chargebee"
msgstr ""

#: payments/models/payments.py:96
msgid "Indicates if subscription has been imported from legacy system (Odoo)"
msgstr ""

#: payments/models/payments.py:101
msgid ""
"Indicates that subscription is in free month. If this is set true would "
"means the next month is free and recurring interval is 5"
msgstr ""

#: payments/models/payments.py:235
msgid ""
"Internal id from moreapp to avoid create duplicated quotes for the same form"
msgstr ""

#: payments/serializers/billing_data.py:33
#: payments/serializers/billing_data.py:65
#: payments/serializers/payment_method.py:110
#: payments/serializers/payment_method.py:161 payments/views.py:299
msgid "User is not created in payment processor platform. Try again later"
msgstr ""

#: payments/serializers/credit_note.py:24
msgid ""
"If is not provided, will return credit notes related to the user who made "
"the request. In detailed views, this flag is used to filter the whole "
"queryset from database and is used as base to get the concrete object."
msgstr ""

#: payments/serializers/invoice.py:24
msgid ""
"If is not provided, will return invoices related to the user who made the "
"request. In detailed views, this flag is used to filter the whole queryset "
"from database and is used as base to get the concrete object."
msgstr ""

#: payments/serializers/payment_method.py:115
msgid "User already has a payment method. You should update it instead"
msgstr ""

#: payments/views.py:83 payments/views.py:174 payments/views.py:188
#: payments/views.py:196 payments/views.py:210
msgid "Error retrieving invoice. Try again later."
msgstr ""

#: payments/views.py:97 payments/views.py:106 payments/views.py:120
msgid "Error retrieving credit note. Try again later."
msgstr ""

#: payments/views.py:256 payments/views.py:276
msgid "Error updating billing data . Please try again, try again later"
msgstr ""

#: payments/views.py:291
msgid "Error creating the payment intent"
msgstr ""

#: payments/views.py:311
msgid "Error updating the payment method. Try again later"
msgstr ""

#: payments/views.py:361
msgid "Error creating payment method. Please try again, try again later"
msgstr ""

#: payments/views.py:384
msgid "Error updating payment method. Please try again later"
msgstr ""

#: riders/admin.py:91
msgid "Generate API Key"
msgstr ""

#: riders/admin.py:96
msgid "An existing api key is present. Please, revoke it first"
msgstr ""

#: riders/admin.py:109
#, python-brace-format
msgid "Unknown error generating the api key: {msg}"
msgstr ""

#: riders/admin.py:114
msgid "API Key generated"
msgstr ""

#: riders/admin.py:116
msgid "Revoke API Key"
msgstr ""

#: riders/admin.py:120
msgid "Missing Google API key. Create it first."
msgstr ""

#: riders/admin.py:132
#, python-brace-format
msgid "Unknown error revoking the api key: {msg}"
msgstr ""

#: riders/admin.py:137
msgid "API Key revoked"
msgstr ""

#: riders/admin.py:139
msgid "Renew API Key"
msgstr ""

#: riders/admin.py:157
#, python-brace-format
msgid "Unknown error renewing the api key: {msg}"
msgstr ""

#: riders/admin.py:162
msgid "API Key renewed (created + revoked)"
msgstr ""

#: riders/admin.py:164
msgid "Create SA"
msgstr ""

#: riders/admin.py:170
msgid "An existing Service account is present. Please, remove it first"
msgstr ""

#: riders/admin.py:201 riders/admin.py:297
#, python-brace-format
msgid "Unknown error generating the service account: {msg}"
msgstr ""

#: riders/admin.py:208
msgid "Service account created"
msgstr ""

#: riders/admin.py:210
msgid "Delete SA"
msgstr ""

#: riders/admin.py:215
msgid "Missing service account. Please, create it first"
msgstr ""

#: riders/admin.py:239
#, python-brace-format
msgid "Unknown error deleting the service account: {msg}"
msgstr ""

#: riders/admin.py:246
msgid "Service account deleted"
msgstr ""

#: riders/admin.py:248
msgid "Disable SA"
msgstr ""

#: riders/admin.py:253 riders/admin.py:282
msgid "Missing service account. Please create it first"
msgstr ""

#: riders/admin.py:258
msgid "Cannot disabled non enabled service accounts"
msgstr ""

#: riders/admin.py:268
#, python-brace-format
msgid "Unknown error disabling the service account: {msg}"
msgstr ""

#: riders/admin.py:275
msgid "Service account disabled"
msgstr ""

#: riders/admin.py:277
msgid "Enable SA"
msgstr ""

#: riders/admin.py:287
msgid "Cannot enable non disabled service accounts"
msgstr ""

#: riders/admin.py:304
msgid "Service account generated"
msgstr ""

#: riders/admin.py:374
msgid "Preferred country"
msgstr ""

#: riders/admin.py:394 users/admin.py:127
msgid "Email"
msgstr ""

#: riders/admin_filters/riders.py:7
msgid "Linked"
msgstr ""

#: riders/admin_filters/riders.py:22
msgid "Task"
msgstr ""

#: riders/models/riders.py:42
msgid "Not sent"
msgstr ""

#: riders/models/riders.py:50
msgid "Last time that notification was sent"
msgstr ""

#: riders/models/riders.py:52
msgid "Time where service was accepted by the rider"
msgstr ""

#: riders/models/riders.py:55
msgid "Flag to hide a service from pending services in "
msgstr ""

#: riders/models/riders.py:61
msgid ""
"Raw response from app, used to allow different responses for tasks or "
"whatever related FK"
msgstr ""

#: riders/models/riders.py:75
msgid "Team has no rider which it is mandatory to send push notifications"
msgstr ""

#: riders/models/riders.py:78
msgid "Service linked is mandatory to send push notifications"
msgstr ""

#: riders/models/riders.py:97
msgid "Not created"
msgstr ""

#: riders/models/riders.py:98
msgid "Enabled"
msgstr ""

#: riders/models/riders.py:99
msgid "Disabled"
msgstr ""

#: riders/models/riders.py:122
msgid "Manual flag to set when service account has the needed roles granted"
msgstr ""

#: riders/models/riders.py:219
#, python-brace-format
msgid "{prefix} Activate your account to get access in the Riders app"
msgstr "{prefix} Attivate il vostro account su Box2box"

#: riders/serializers/riders.py:58
msgid "Warehouses which belong to this city"
msgstr ""

#: templates/otp/login.html:38
msgid "Please correct the error below."
msgstr ""

#: templates/otp/login.html:38
msgid "Please correct the errors below."
msgstr ""

#: templates/otp/login.html:54
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: templates/otp/login.html:73
msgid "OTP Token:"
msgstr ""

#: templates/otp/login.html:78
msgid "Forgotten your password or username?"
msgstr ""

#: templates/otp/login.html:82
msgid "Log in"
msgstr ""

#: users/admin.py:26
msgid "Contract"
msgstr ""

#: users/admin.py:78
msgid "Customer area"
msgstr ""

#: users/admin.py:85
msgid "Hubspot"
msgstr ""

#: users/admin.py:86
msgid "Chargebee"
msgstr ""

#: users/admin.py:87
msgid "Moloni"
msgstr ""

#: users/admin.py:88
msgid "Personal area"
msgstr ""

#: users/admin.py:89
msgid "Inheritance"
msgstr ""

#: users/admin.py:134
msgid "Schedule activation account email for this user"
msgstr ""

#: users/admin.py:139
#, python-brace-format
msgid "Activation email scheduled to {user} successfully"
msgstr ""

#: users/admin.py:144
#, python-brace-format
msgid "Activation email could not be scheduled for the {user}"
msgstr ""

#: users/admin.py:163
msgid "Send activation account email"
msgstr ""

#: users/admin.py:168
#, python-brace-format
msgid "Activation email could not be sent to {user}"
msgstr ""

#: users/admin.py:174
#, python-brace-format
msgid "Activation email sent to {user} successfully"
msgstr ""

#: users/auth/custom_jwt/authentication.py:43
msgid "Given token not valid for any token type"
msgstr ""

#: users/auth/custom_jwt/authentication.py:55
msgid "Token contained no recognizable user identification"
msgstr ""

#: users/auth/custom_jwt/authentication.py:63
msgid "User is inactive"
msgstr ""

#: users/auth/custom_jwt/tokens.py:69
msgid "Invalid algorithm specified"
msgstr ""

#: users/auth/custom_jwt/tokens.py:71 users/auth/custom_jwt/tokens.py:97
msgid "Token is invalid or expired"
msgstr ""

#: users/auth/custom_jwt/tokens.py:82
msgid "Cannot create token with no type or lifetime"
msgstr ""

#: users/auth/forms.py:24
#, python-brace-format
msgid "{prefix}Recovery your password"
msgstr "{prefix}Recupera la tua password"

#: users/auth/serializers.py:74
msgid "Must include email and password"
msgstr ""

#: users/auth/serializers.py:91 users/auth/serializers.py:107
#: users/auth/serializers.py:117
msgid "Unable to log in with provided credentials."
msgstr ""

#: users/auth/serializers.py:102
msgid "User is not allowed to access to this site."
msgstr ""

#: users/auth/views.py:69
msgid "Error generating token for requested user"
msgstr ""

#: users/models.py:27
msgid "Date when activation account email was sent"
msgstr ""

#: users/models.py:38 users/models.py:196
#, python-brace-format
msgid "{prefix} Activate your account in Box2box"
msgstr "{prefix} Attivate il vostro account su Box2box"

#: users/models.py:55
msgid "username"
msgstr ""

#: users/models.py:58
msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

#: users/models.py:62
msgid "A user with that username already exists."
msgstr ""

#: users/models.py:72
msgid "Used to enforce to user to change password and fill missing data"
msgstr ""

#: users/models.py:77
msgid "User identifier in external CRM"
msgstr ""

#: users/models.py:79
msgid "Chargebee identifier"
msgstr ""

#: users/models.py:81
msgid "active"
msgstr ""

#: users/models.py:84
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

#: users/models.py:87
msgid "Source domain from which user has signed in"
msgstr ""

#: users/models.py:90
msgid "Customer identifier in moloni software billing"
msgstr ""

#: users/models.py:98
msgid "Handle third-party software duplicity on emails"
msgstr ""

#: users/models.py:101
msgid "Customer preferred language"
msgstr ""

#: users/models.py:105
msgid "First name set by the customer"
msgstr ""

#: users/models.py:108
msgid "Last name set by the customer"
msgstr ""

#: users/models.py:117
msgid "date leave"
msgstr ""

#: users/serializers/user_serializers.py:204
msgid "Wrong password"
msgstr ""

#: users/serializers/user_serializers.py:209
msgid "An user with that email already exists."
msgstr ""

#: users/views.py:64
msgid "Email changed"
msgstr ""

#: webhooks/callbacks/hubspot_public_forms.py:191
msgid "Thank you for your request! We will contact you as soon as possible. 😀"
msgstr "Grazie per la vostra richiesta! Vi contatteremo a breve. 😀"

#: webhooks/callbacks/hubspot_public_forms.py:225
msgid "Congrats! You have a new collaborator"
msgstr ""

#: webhooks/callbacks/microservices.py:44
msgid "Your payment was successful!"
msgstr "Acconto ricevuto!"

#: webhooks/callbacks/mixins/transaction_handlers.py:124
#, python-brace-format
msgid ""
"[CHARGEBACK] Fee on the transaction {transaction} and invoice {invoice} due "
"to chargeback"
msgstr ""

#: webhooks/callbacks/mixins/transaction_handlers.py:228
#, python-brace-format
msgid ""
"[CHARGEBACK] Fee on the invoice {invoice} due to chargeback coming from card"
msgstr ""

#: webhooks/serializers/serializers.py:30
msgid "Internal id"
msgstr ""

#: webhooks/views.py:361
#, python-brace-format
msgid "Congrats! A B2B lead wants a quote - {deal_id}"
msgstr ""

#: webhooks/views.py:416
#, python-brace-format
msgid "New form submission from deal {deal_id}"
msgstr "Nuovo formulario da {deal_id}"

#: webhooks/views.py:481
#, python-brace-format
msgid "Budget request from {deal_id}"
msgstr "Richiesta di budget da {deal_id}"

#: webhooks/views.py:503
#, python-brace-format
msgid "Booking data updated for deal {deal_id}"
msgstr "Dati di prenotazione aggiornati per {deal_id}"
