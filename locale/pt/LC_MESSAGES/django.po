# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-12 09:56+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backoffice_api/settings/base.py:546
msgid "English"
msgstr ""

#: backoffice_api/settings/base.py:547
msgid "Spanish"
msgstr ""

#: backoffice_api/settings/base.py:548
msgid "Portuguese"
msgstr ""

#: backoffice_api/settings/base.py:549
msgid "French"
msgstr ""

#: backoffice_api/settings/base.py:550
msgid "Italian"
msgstr ""

#: contracts/admin.py:33
msgid "Annex"
msgstr "Anexo"

#: contracts/admin.py:51 contracts/models/contract.py:447
#: contracts/models/service.py:319 payments/models/payments.py:220
msgid "Box"
msgstr "Caixa"

#: contracts/admin.py:69 contracts/models/service.py:320
#: payments/models/payments.py:221
msgid "Moving"
msgstr "Mudança"

#: contracts/admin.py:104 intranet/admin.py:50
msgid "Site"
msgstr ""

#: contracts/admin.py:109 intranet/admin.py:434
msgid "City"
msgstr ""

#: contracts/admin.py:167
msgid "Photos"
msgstr ""

#: contracts/admin.py:189 contracts/admin.py:310 contracts/admin.py:402
#: contracts/service_templates/contracts.py:170
#: riders/admin_filters/riders.py:21
msgid "Service"
msgstr "Serviço"

#: contracts/admin.py:198
#, fuzzy
#| msgid "Warehouse location"
msgid "Warehouse"
msgstr "Localização no armazém"

#: contracts/admin.py:200 contracts/admin.py:312 contracts/admin.py:404
msgid "Create quote"
msgstr ""

#: contracts/admin.py:211 contracts/admin.py:321 contracts/admin.py:413
msgid "Service not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:216
msgid "Warehouse not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:222 contracts/admin.py:329 contracts/admin.py:421
msgid "City not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:228 contracts/admin.py:335 contracts/admin.py:427
msgid "Raw data not found. Quote won't be created"
msgstr ""

#: contracts/admin.py:268 contracts/admin.py:370 contracts/admin.py:469
#, python-brace-format
msgid "Quote {quote} created in chargebee successfully"
msgstr ""

#: contracts/admin.py:273 contracts/admin.py:375 contracts/admin.py:474
#, python-brace-format
msgid "Quote {quote} updated locally but changes are not in chargebee"
msgstr ""

#: contracts/admin.py:277 contracts/admin.py:379 contracts/admin.py:478
#, python-brace-format
msgid "Quote {quote} could not be created locally"
msgstr ""

#: contracts/admin.py:524
msgid "Provider Team"
msgstr ""

#: contracts/admin_filters/warehouses.py:7
msgid "Tracking Options"
msgstr ""

#: contracts/admin_filters/warehouses.py:23
msgid "Has tracking options"
msgstr ""

#: contracts/admin_filters/warehouses.py:23
msgid "Empty tracking options"
msgstr ""

#: contracts/admin_filters/warehouses.py:47
msgid "Country"
msgstr ""

#: contracts/filters.py:21 intranet/filters/inventory.py:13
msgid "Brings only non-delivered items from db."
msgstr ""

#: contracts/filters.py:27
msgid "Brings only pending items from db."
msgstr ""

#: contracts/filters.py:52
msgid "Brings services from db according to status choice."
msgstr ""

#: contracts/filters.py:58 contracts/filters.py:118
msgid ""
"Brings only services where event date is set to date lower than equals to "
"today."
msgstr ""

#: contracts/filters.py:65
msgid "Brings services from db excluding given services by id"
msgstr ""

#: contracts/filters.py:71
msgid ""
"Brings only services where location of goods in the warehouse is not set yet."
msgstr ""

#: contracts/filters.py:125
msgid "Brings tasks from db excluding given tasks by id"
msgstr ""

#: contracts/filters.py:154
msgid "Brings only movements with no parent movement"
msgstr ""

#: contracts/filters.py:160
msgid "Brings only movements from a specific contract"
msgstr ""

#: contracts/filters.py:166
msgid "Brings only movements from a specific placement (i.e. recursive)"
msgstr ""

#: contracts/mixins.py:10
msgid "View pdf"
msgstr ""

#: contracts/mixins.py:31
msgid "PDF file is not uploaded to remote server"
msgstr ""

#: contracts/mixins.py:42
msgid "Empty PDF file"
msgstr ""

#: contracts/mixins.py:48
msgid "Download"
msgstr ""

#: contracts/mixins.py:50
msgid "PDF file is ready: {}"
msgstr ""

#: contracts/models/contract.py:37
msgid "Spain"
msgstr ""

#: contracts/models/contract.py:38
msgid "France"
msgstr ""

#: contracts/models/contract.py:39 contracts/utils.py:132
msgid "Portugal"
msgstr ""

#: contracts/models/contract.py:40
msgid "Italia"
msgstr ""

#: contracts/models/contract.py:41
msgid "Mexico"
msgstr ""

#: contracts/models/contract.py:47 contracts/models/contract.py:150
msgid "External id for this document"
msgstr ""

#: contracts/models/contract.py:76
msgid "Pick up/Deliver address"
msgstr ""

#: contracts/models/contract.py:92
msgid "Contract Addresses"
msgstr ""

#: contracts/models/contract.py:114 contracts/models/contract.py:387
#: core/models/product.py:28
msgid "Will be deprecated in future deployments"
msgstr ""

#: contracts/models/contract.py:120 contracts/models/contract.py:393
#: contracts/models/item.py:48
msgid "Warehouse where is stored. Will be deprecated in future deployments"
msgstr ""

#: contracts/models/contract.py:123 contracts/models/contract.py:396
msgid "Hired space in squared meters"
msgstr ""

#: contracts/models/contract.py:127
msgid "Person who has signed the initial_pickup contract"
msgstr ""

#: contracts/models/contract.py:132
msgid "Coupon code to apply the discount to the initial deposit"
msgstr ""

#: contracts/models/contract.py:135
msgid "Flag to check if deposit needs to be applied on this contract"
msgstr ""

#: contracts/models/contract.py:137
msgid "Amount to discount on first subscription"
msgstr ""

#: contracts/models/contract.py:141
msgid "Indicates if contract has been imported from legacy system (Odoo)"
msgstr ""

#: contracts/models/contract.py:144
msgid ""
"If this contract should be filtered to display it to customer, even if is "
"imported"
msgstr ""

#: contracts/models/contract.py:146
msgid "For created contracts from intranet, site which belongs it"
msgstr ""

#: contracts/models/contract.py:154
msgid "Odoo id for this contract"
msgstr ""

#: contracts/models/contract.py:184
msgid "Deposit"
msgstr "Depósito"

#: contracts/models/contract.py:238
msgid "From id from moreapp"
msgstr ""

#: contracts/models/contract.py:245
msgid "Internal id from moreapp to track every form submitted"
msgstr ""

#: contracts/models/contract.py:248
msgid "Flag to know if related documents have been uploaded to storage"
msgstr ""

#: contracts/models/contract.py:250
msgid "If this file has been sent to user"
msgstr ""

#: contracts/models/contract.py:251 moreapp/models.py:38
msgid "Date where document was sent to the queue"
msgstr ""

#: contracts/models/contract.py:347
#, python-brace-format
msgid "BOX2BOX - {contract_type} - {display_name}"
msgstr "BOX2BOX - {contract_type} - {display_name}"

#: contracts/models/contract.py:375 contracts/models/service.py:315
#: payments/models/payments.py:216
msgid "Initial pickup"
msgstr "Recolha"

#: contracts/models/contract.py:376 contracts/models/service.py:316
#: payments/models/payments.py:217
msgid "Extra pickup"
msgstr "Recolha adicional"

#: contracts/models/contract.py:377 contracts/models/service.py:317
#: intranet/models/delivery.py:39 payments/models/payments.py:218
msgid "Partial delivery"
msgstr "Entrega parcial"

#: contracts/models/contract.py:378 contracts/models/service.py:318
#: intranet/models/delivery.py:40 payments/models/payments.py:219
msgid "Final delivery"
msgstr "Entrega final"

#: contracts/models/contract.py:379 contracts/models/service.py:321
#: payments/models/payment_method.py:42 payments/models/payments.py:222
msgid "Unknown"
msgstr ""

#: contracts/models/contract.py:405
msgid "Annexes"
msgstr ""

#: contracts/models/contract.py:426
msgid "Moved space in squared meters"
msgstr ""

#: contracts/models/contract.py:431
msgid "Move"
msgstr "Mudança"

#: contracts/models/contract.py:437
msgid "Number of normal boxes"
msgstr ""

#: contracts/models/contract.py:438
msgid "Number of wardrobe boxes"
msgstr ""

#: contracts/models/contract.py:439
msgid "Number of seal rolls"
msgstr ""

#: contracts/models/contract.py:440
msgid "Units of bubble paper"
msgstr ""

#: contracts/models/contract.py:443
msgid "Boxes"
msgstr "Caixas"

#: contracts/models/item.py:9
msgid "Object description provided in contract"
msgstr ""

#: contracts/models/item.py:21
msgid "Item dimensions in the form of WxHxD"
msgstr ""

#: contracts/models/item.py:40
msgid "Object barcode tag id"
msgstr ""

#: contracts/models/item.py:41
msgid "Delivery date for this item"
msgstr ""

#: contracts/models/item.py:42
msgid "Pick up date for this item"
msgstr ""

#: contracts/models/item.py:52
msgid "Additional comment for items, added thru customer portal"
msgstr ""

#: contracts/models/item.py:56
msgid "Temporal id used by the app before creating the item in db"
msgstr ""

#: contracts/models/orders.py:22
msgid "Street level"
msgstr "Nivel da rua"

#: contracts/models/orders.py:23 contracts/service_templates/contracts.py:38
msgid "Stairs"
msgstr "Escadas"

#: contracts/models/orders.py:24
msgid "Small lift"
msgstr "Elevador pequeno"

#: contracts/models/orders.py:25
msgid "Large lift"
msgstr "Elevador grande"

#: contracts/models/orders.py:29
msgid "Order date"
msgstr ""

#: contracts/models/orders.py:32
msgid "Access type"
msgstr "Tipo de aceso"

#: contracts/models/orders.py:34
msgid "Additional comments from the user"
msgstr ""

#: contracts/models/orders.py:38
msgid "Creation date"
msgstr ""

#: contracts/models/service.py:24
msgid "Time slot"
msgstr "Horário"

#: contracts/models/service.py:25 contracts/service_templates/contracts.py:182
msgid "Inventory"
msgstr "Inventário"

#: contracts/models/service.py:26
msgid "Billing cycle"
msgstr "Faturação"

#: contracts/models/service.py:27
#, fuzzy
#| msgid "Billing cycle"
msgid "Billing cycle notes"
msgstr "Faturação"

#: contracts/models/service.py:28
msgid "Warehouse location"
msgstr "Localização no armazém"

#: contracts/models/service.py:29 contracts/serializers/contract.py:44
msgid "Hired space"
msgstr "Espaço contratado"

#: contracts/models/service.py:30
#, fuzzy
#| msgid "Moving address"
msgid "Source address"
msgstr "Endereço de mudança"

#: contracts/models/service.py:31
msgid "Moving address"
msgstr "Endereço de mudança"

#: contracts/models/service.py:32
msgid "Material"
msgstr "Material"

#: contracts/models/service.py:33 contracts/service_templates/boxes.py:55
#: contracts/service_templates/contracts.py:166
msgid "Address"
msgstr "Endereço"

#: contracts/models/service.py:34
msgid "Additional information"
msgstr "Informação adicional"

#: contracts/models/service.py:35 contracts/service_templates/contracts.py:171
msgid "Service type"
msgstr "Tipo de serviço"

#: contracts/models/service.py:207 contracts/models/service.py:229
msgid "Student pack 1"
msgstr "Pack 1"

#: contracts/models/service.py:218
msgid "Student pack 2"
msgstr "Pack 2"

#: contracts/models/service.py:325
msgid "Not started"
msgstr ""

#: contracts/models/service.py:326
msgid "In progress"
msgstr ""

#: contracts/models/service.py:327
msgid "Finished"
msgstr ""

#: contracts/models/service.py:334
msgid "Promo pack"
msgstr ""

#: contracts/models/service.py:335
msgid "Especial"
msgstr ""

#: contracts/models/service.py:339
msgid "Hubspot deal id"
msgstr ""

#: contracts/models/service.py:340
msgid "Service address"
msgstr ""

#: contracts/models/service.py:342
msgid "Destination address for movings"
msgstr ""

#: contracts/models/service.py:347 users/models.py:71
msgid "Phone number"
msgstr ""

#: contracts/models/service.py:348
msgid "Space in m2"
msgstr ""

#: contracts/models/service.py:362
msgid "Location in the warehouse - Additional pickup / Deliveries"
msgstr ""

#: contracts/models/service.py:365
msgid "Hired space in m2 - Additional pickup"
msgstr ""

#: contracts/models/service.py:369
msgid "Event country which this service belongs to"
msgstr ""

#: contracts/models/service.py:372
#, fuzzy
#| msgid "Source address"
msgid "Service address latitude"
msgstr "Endereço de origem"

#: contracts/models/service.py:373
#, fuzzy
#| msgid "Source address"
msgid "Service address longitude"
msgstr "Endereço de origem"

#: contracts/models/service.py:376
msgid "Date of submission for the service"
msgstr ""

#: contracts/models/service.py:377
msgid "Date when the service has been updated (if it has been)"
msgstr ""

#: contracts/models/service.py:381
msgid "Promotion code visible on intranet events"
msgstr ""

#: contracts/models/service.py:394
msgid "Rider who has submitted this service"
msgstr ""

#: contracts/models/service.py:402
msgid "Residence associated with this service"
msgstr ""

#: contracts/models/service.py:419
msgid "Warehouse chosen by the rider thru the App"
msgstr ""

#: contracts/models/warehouse.py:22
msgid "Location"
msgstr ""

#: contracts/models/warehouse.py:22
#, fuzzy
#| msgid "Description"
msgid "location_description"
msgstr "Descrição"

#: contracts/models/warehouse.py:23
msgid "Pallet"
msgstr ""

#: contracts/models/warehouse.py:23
#, fuzzy
#| msgid "Description"
msgid "pallet_description"
msgstr "Descrição"

#: contracts/models/warehouse.py:24
#, fuzzy
#| msgid "Floors"
msgid "Floor"
msgstr "Pisos"

#: contracts/models/warehouse.py:24
#, fuzzy
#| msgid "Description"
msgid "floor_description"
msgstr "Descrição"

#: contracts/models/warehouse.py:25 contracts/service_templates/movings.py:16
msgid "Pictures"
msgstr "Fotos"

#: contracts/models/warehouse.py:25
#, fuzzy
#| msgid "Description"
msgid "pictures_description"
msgstr "Descrição"

#: contracts/models/warehouse.py:33 contracts/models/warehouse.py:42
msgid ""
"Warehouse id used for moreapp compatibility. It will dropped in future "
"releases"
msgstr ""

#: contracts/models/warehouse.py:44
msgid "Warehouse name"
msgstr ""

#: contracts/models/warehouse.py:49 intranet/models/city.py:25
#: intranet/models/residence.py:23 intranet/models/residence.py:51
msgid "Used to lookup the name"
msgstr ""

#: contracts/models/warehouse.py:55
msgid "Warehouse building canonical name"
msgstr ""

#: contracts/models/warehouse.py:73
msgid "Excel template to notify the warehouses"
msgstr ""

#: contracts/models/warehouse.py:76
msgid ""
"If it is needed to calculate the warehouse differences. Internal usage only"
msgstr ""

#: contracts/models/warehouse.py:81
msgid ""
"If this warehouse can be used to book services. It is preferred this over "
"deleting it due to all implications may have delete it"
msgstr ""

#: contracts/receivers.py:22
#, python-brace-format
msgid "{prefix}pickup order for contract {contract}"
msgstr "{prefix}Solicitação de recolha do contrato {contract}"

#: contracts/receivers.py:60
#, python-brace-format
msgid "{prefix}delivery order for contract {contract}"
msgstr "{prefix}Solicitação de entrega do contrato {contract}"

#: contracts/serializers/delivery.py:41 contracts/serializers/delivery.py:43
#: contracts/serializers/delivery.py:45 contracts/serializers/delivery.py:47
#: contracts/serializers/delivery.py:49 contracts/serializers/pickup.py:86
#: contracts/serializers/pickup.py:88 contracts/serializers/pickup.py:90
#: contracts/serializers/pickup.py:92 contracts/serializers/pickup.py:94
msgid "This field is required"
msgstr ""

#: contracts/serializers/delivery.py:54 contracts/serializers/pickup.py:99
#, python-brace-format
msgid "Contract address with {id} does not exist"
msgstr ""

#: contracts/serializers/delivery.py:89
msgid "You cannot select an item which not belongs to contract"
msgstr ""

#: contracts/serializers/delivery.py:93
msgid "You cannot select an item which is already selected for delivery"
msgstr ""

#: contracts/serializers/delivery.py:154 contracts/serializers/pickup.py:138
msgid "Invalid contract id given"
msgstr ""

#: contracts/serializers/delivery.py:205 contracts/serializers/pickup.py:175
msgid "If is not provided, will return empty data."
msgstr ""

#: contracts/serializers/inventory.py:16
msgid ""
"If is not provided, will return empty data. In detailed views, this flag is "
"not required."
msgstr ""

#: contracts/serializers/inventory.py:68 contracts/serializers/service.py:81
msgid "Dummy value for the riders app business logic"
msgstr ""

#: contracts/serializers/service.py:186 contracts/serializers/service.py:248
msgid ""
"This is returned as a list if any to backward compatibility with the app"
msgstr ""

#: contracts/serializers/service.py:546
msgid ""
"Show riders who have already submitted the tracking information for this "
"service"
msgstr ""

#: contracts/serializers/warehouse_movement.py:68
msgid "Relocation requires exactly one placement"
msgstr ""

#: contracts/serializers/warehouse_movement.py:86
#: intranet/serializers/warehouse_movement.py:183
#, fuzzy
#| msgid "Warehouse location"
msgid "Warehouse does not exist"
msgstr "Localização no armazém"

#: contracts/service_templates/base.py:90
#: contracts/service_templates/base.py:91
msgid "Pick up"
msgstr "Recolha"

#: contracts/service_templates/base.py:92
#: contracts/service_templates/base.py:93
msgid "Delivery"
msgstr "Entrega"

#: contracts/service_templates/base.py:96
#: contracts/service_templates/base.py:97
msgid "No inventory picked up"
msgstr "Não há inventário recolhido"

#: contracts/service_templates/base.py:98
#: contracts/service_templates/base.py:99
msgid "No inventory delivered"
msgstr "Não há inventário entregue"

#: contracts/service_templates/base.py:100
msgid "No pictures taken"
msgstr "Sem fotos"

#: contracts/service_templates/boxes.py:12
#: contracts/service_templates/contracts.py:82
msgid "Seal"
msgstr "Selo"

#: contracts/service_templates/boxes.py:13
#: contracts/service_templates/boxes.py:15
#: contracts/service_templates/boxes.py:17
#: contracts/service_templates/boxes.py:22
#: contracts/service_templates/boxes.py:24
#: contracts/service_templates/contracts.py:52
#: contracts/service_templates/contracts.py:58
#: contracts/service_templates/contracts.py:69
#: contracts/service_templates/contracts.py:71
#: contracts/service_templates/contracts.py:81
#: contracts/service_templates/contracts.py:83
#: contracts/service_templates/contracts.py:85
msgid "Units"
msgstr "Unidades"

#: contracts/service_templates/boxes.py:14
#: contracts/service_templates/contracts.py:84
msgid "Bubble paper"
msgstr "Envolvimento da bolha"

#: contracts/service_templates/boxes.py:16
#: contracts/service_templates/contracts.py:80
msgid "Blanket"
msgstr "Cobertores"

#: contracts/service_templates/boxes.py:21
#: contracts/service_templates/contracts.py:68
msgid "Moving boxes"
msgstr "Caixas de mudança"

#: contracts/service_templates/boxes.py:21
#: contracts/service_templates/contracts.py:68
msgid "Moving box sales"
msgstr "Caixas de mudança entregues"

#: contracts/service_templates/boxes.py:23
#: contracts/service_templates/contracts.py:70
msgid "Wardrobe boxes"
msgstr "Caixas armario"

#: contracts/service_templates/boxes.py:23
#: contracts/service_templates/contracts.py:70
msgid "Wardrobe box sales"
msgstr "Caixas armario entregues"

#: contracts/service_templates/boxes.py:42
msgid "Box delivery"
msgstr "Nota de entrega de material"

#: contracts/service_templates/boxes.py:44
#: contracts/service_templates/contracts.py:155
msgid "Contract data"
msgstr "Dados de contrato"

#: contracts/service_templates/boxes.py:45
#: contracts/service_templates/contracts.py:156
msgid "Contract identifier"
msgstr "Número do contrato"

#: contracts/service_templates/boxes.py:48
#: contracts/service_templates/contracts.py:159
msgid "Personal data"
msgstr "Dados pessoais"

#: contracts/service_templates/boxes.py:49
#: contracts/service_templates/contracts.py:160
msgid "First name"
msgstr "Nome"

#: contracts/service_templates/boxes.py:50
#: contracts/service_templates/contracts.py:161
msgid "Last name"
msgstr "Apelidos"

#: contracts/service_templates/boxes.py:51
#: contracts/service_templates/contracts.py:162
msgid "National ID"
msgstr "Cartão de Cidadão"

#: contracts/service_templates/boxes.py:54
#: contracts/service_templates/contracts.py:165
msgid "Date and address"
msgstr "Data é endereço"

#: contracts/service_templates/boxes.py:56
#: contracts/service_templates/contracts.py:167
msgid "Date"
msgstr "Data"

#: contracts/service_templates/boxes.py:58
msgid "Boxes service"
msgstr "Serviço de caixas"

#: contracts/service_templates/boxes.py:59
#: contracts/service_templates/contracts.py:187
msgid "Additional Services"
msgstr "Serviços adicionais"

#: contracts/service_templates/boxes.py:61
#: contracts/service_templates/contracts.py:189
msgid "Comment"
msgstr "Comentário"

#: contracts/service_templates/boxes.py:65
#: contracts/service_templates/contracts.py:193 riders/admin.py:376
msgid "Signature"
msgstr "Assinatura"

#: contracts/service_templates/boxes.py:66
#: contracts/service_templates/contracts.py:195
msgid "Customer signature"
msgstr "Assinatura cliente"

#: contracts/service_templates/boxes.py:67
#: contracts/service_templates/contracts.py:198
msgid "Company signature"
msgstr "Assinatura empresa"

#: contracts/service_templates/conditions.py:15
#: contracts/service_templates/conditions.py:52
#: contracts/service_templates/conditions.py:88
msgid "Terms of service"
msgstr "TERMOS E CONDIÇÕES GERAIS"

#: contracts/service_templates/conditions.py:15
#: contracts/service_templates/conditions.py:52
#: contracts/service_templates/conditions.py:88
msgid "Legal conditions"
msgstr "Condições legais"

#: contracts/service_templates/conditions.py:17
#: contracts/service_templates/conditions.py:54
#: contracts/service_templates/conditions.py:90
msgid "General conditions"
msgstr "TERMOS E CONDIÇÕES GERAIS"

#: contracts/service_templates/conditions.py:21
#: contracts/service_templates/conditions.py:58
#, python-brace-format
msgid "conditions_1 {email}"
msgstr ""
"Em conformidade com as disposições do Regulamento Geral de Protecção de "
"Dados 679/2016 e da Lei Orgânica 3/2018 de 5 de Dezembro sobre Protecção de "
"Dados, informamos que os dados fornecidos serão incorporados numa base de "
"dados propriedade da STUFF2BOX UNIP LDA, com Tax Number ********* e endereço "
"em Av. Fontes Pereira Melo - Palácio Sotto Mayor Galería A 16, C.P. "
"1050-121, LISBOA, com o objetivo de manter e gerir as relações comerciais e "
"administrativas. A base legal para o processamento é o cumprimento da "
"legislação fiscal, comercial e contabilística. Não estão previstas cessões e/"
"ou transferências internacionais de dados, excepto as autorizadas por lei ou "
"as autorizadas com o seu consentimento expresso. Pode exercer os seus "
"direitos de acesso, rectificação, eliminação (direito a ser esquecido), "
"limitação do tratamento, portabilidade dos dados, oposição, e não estar "
"sujeito a decisões automatizadas enviando uma carta para STUFF 2BOX UNIP "
"LDA, Av. Fontes Pereira Melo - Palácio Sotto Mayor Galería A 16, C.P. "
"1050-121, LISBOA ou por e-mail para <a href='mailto:{email}'>{email}</a> "
"indicando como assunto \"Rights Data Protection Act\", e anexando uma "
"fotocópia do seu bilhete de identidade. Além disso, lembramos-lhe que pode "
"contactar a Autoridade de Controlo de Protecção de Dados competente para "
"obter informações adicionais ou para apresentar uma queixa. "

#: contracts/service_templates/conditions.py:25
#: contracts/service_templates/conditions.py:62
#: contracts/service_templates/conditions.py:96
msgid "title_2"
msgstr "PRIMEIRO - Objeto de contrato e descrição de serviço"

#: contracts/service_templates/conditions.py:25
#: contracts/service_templates/conditions.py:62
#: contracts/service_templates/conditions.py:96
msgid "conditions_2"
msgstr ""
"O serviço de transferência e armazenamento contratado é definido como a "
"transferência ou transporte do endereço de recolha para os nossos centros de "
"armazenamento (utilizando um ou mais meios de transporte) de mobiliário, "
"bens ou embalagens (doravante designados por mercadoria).<br><br> A EMPRESA "
"tem vários centros de armazenamento localizados em locais diferentes, pelo "
"que o destino dos bens e o seu armazenamento dependerá da disponibilidade de "
"espaço de armazenamento adequado num ou noutro centro (isto não fará "
"qualquer diferença no custo a ser suportado pelo CLIENTE). Da mesma forma, o "
"serviço inclui a transferência ou transporte subsequente dos nossos centros "
"de armazenamento para o endereço de entrega a ser determinado no momento "
"indicado pelo CLIENTE e sempre dentro do prazo do contrato. O serviço será "
"considerado concluído no momento da entrega dos bens abrangidos pelo "
"contrato no endereço de entrega, nas condições acordadas.<br> "

#: contracts/service_templates/conditions.py:26
#: contracts/service_templates/conditions.py:63
#: contracts/service_templates/conditions.py:97
msgid "title_3"
msgstr "SEGUNDO."

#: contracts/service_templates/conditions.py:26
#: contracts/service_templates/conditions.py:63
#: contracts/service_templates/conditions.py:97
msgid "conditions_3"
msgstr ""
"Os serviços contratados serão os referidos na cláusula anterior, bem como "
"todos os que se enquadram na categoria dos serviços acordados com o CLIENTE "
"e que serão especificados nas condições particulares do presente "
"contrato<br> "

#: contracts/service_templates/conditions.py:27
#: contracts/service_templates/conditions.py:64
#: contracts/service_templates/conditions.py:98
msgid "title_4"
msgstr "TERCEIRO."

#: contracts/service_templates/conditions.py:27
#: contracts/service_templates/conditions.py:64
#: contracts/service_templates/conditions.py:98
msgid "conditions_4"
msgstr ""
"A EMPRESA apenas fará o transporte do mobiliário uma vez da casa do CLIENTE "
"para o local de armazenamento,assim como do mobiliário para o endereço de "
"entrega. As viagens adicionais subsequentes serão por conta do CLIENTE, que "
"pagará os custos incorridos a este respeito (a partir de 9 euros).<br><br> A "
"localização dos bens será decidida pela EMPRESA. Dependendo da "
"disponibilidade logística, poderá ser possível relocalizar os bens entre "
"diferentes locais de armazenamento.<br><br> Não é possível ao CLIENTE aceder "
"ao local de armazenamento individualmente, apenas o nosso pessoal tem acesso "
"e, mediante pedido, entregará a mercadoria requerida no endereço de recolha "
"inicial ou noutro local.<br> "

#: contracts/service_templates/conditions.py:28
#: contracts/service_templates/conditions.py:65
#: contracts/service_templates/conditions.py:99
msgid "title_5"
msgstr "QUARTO."

#: contracts/service_templates/conditions.py:28
#: contracts/service_templates/conditions.py:65
#: contracts/service_templates/conditions.py:99
msgid "conditions_5"
msgstr ""
"A pedido do Transportador, o CLIENTE deve informar A EMPRESA das "
"circunstâncias relevantes para a correcta execução das actividades em que "
"consiste o serviço contratado, bem como das condições de acesso ao local ou "
"habitações do pessoal e veículos (lugares de estacionamento, altura da "
"habitação, corredores, escadas, elevadores de carga, possibilidade de "
"utilizar elevadores, outros trabalhos em curso nas respectivas instalações, "
"etc.). Caso o edifício não disponha de elevador e seja necessário que o "
"serviço seja efectuado pelos operadores para subir/ descer escadas ou "
"degraus, a EMPRESA cobrará um suplemento, que dependerá do volume de "
"mercadorias a transportar e do piso onde o trabalho for realizado, dada a "
"dificuldade acrescida devido a esta circunstância.<br><br>  <h5 style='font-"
"weight: 800'>4.1</h5><br>  A EMPRESA não tem capacidade/autorizações para "
"realizar trabalhos com corda por janela, grua ou plataforma, pelo que no "
"caso de as circunstâncias da recolha ou entrega exigirem estes serviços, o "
"CLIENTE será responsável por eles por conta própria.<br><br> No caso de ser "
"necessário que os operadores subam e desçam escadas ou degraus, a BOX2BOX "
"cobrará um suplemento que dependerá do volume da mercadoria a ser "
"transportada e do piso em que a mercadoria se encontra. O suplemento é de 5 "
"euros (IVA incluído) por cada m2 de mercadorias transportadas multiplicado "
"por cada andar subido ou descido.<br><br> Considera-se que, para carregar e/"
"ou descarregar os veículos, estes devem poder ser localizados na porta do "
"local de recolha e entrega, ou a uma distância máxima não superior a 50 "
"metros a pé, mas se, por qualquer razão (ruas ou avenidas estreitas, "
"estacionamento limitado e proibido, etc.), não for possível alcançar a "
"distância acima referida. ), o referido posicionamento do veículo não "
"poderia ser alcançado e isto implicaria um aumento da mão-de-obra ou a "
"utilização de meios de transporte adicionais (carrinhas ou outros elementos "
"ainda mais leves), os custos incorridos por estes serviços adicionais seriam "
"suportados pelo CLIENTE (mínimo 50 euros).<br><br> Taxas de acordo com a "
"origem e destino:<br> <ul style='list-style-type: disc; padding-left: 32px'> "
"<li>Mesmo local. Se houver um suplemento para o serviço de recolha, este "
"será aplicado da mesma forma ao serviço de entrega.</li> <li>O ponto de "
"recolha e o ponto de destino não coincidem. Quando os locais de origem e "
"destino são diferentes uns dos outros e se algum deles estiver fora da área "
"de serviço de cada cidade onde a mercadoria é armazenada (Lisboa, Porto), o "
"serviço de viagem terá um aumento no custo de 0,90 euros por kilómetro "
"adicional percorrido (tomando como ponto de origem o centro da cidade onde o "
"serviço é efectuado). Tudo isto com excepção dos locais que estão incluídos "
"na área de serviço que podem ser consultados no nosso website.</li> </ul> "
"Outros suplementos: <ul style='list-style-type: disc; padding-left: 32px'> "
"<li>Desmontagem geral: 60€/hora</li> <li>Suplemento de estacionamento: "
"mínimo 50€ (Com excepção de zonas inalcançáveis, nesse caso o valor podeser "
"superior)</li> <li>Suplemento viagem adicional: mínimo 100€ (independente ao "
"contrato)</li> <li>Reciclagem: 35€/m²</li> <li>Venda de caixa de mudança: 3€/"
"unidade</li> <li>Embalagem de caixas de mudança: 7€/unidade</li> "
"<li>Suplemento de escadas: 5€/m²/andar</li> <li>Suplemento de kilómetros "
"0.90€/km (ida e volta)</li> </ul> "

#: contracts/service_templates/conditions.py:29
#: contracts/service_templates/conditions.py:66
#: contracts/service_templates/conditions.py:100
msgid "title_6"
msgstr "QUINTO. - Pagamento e outros gastos"

#: contracts/service_templates/conditions.py:29
#: contracts/service_templates/conditions.py:66
#: contracts/service_templates/conditions.py:100
msgid "conditions_6"
msgstr ""
"O Transportador compromete-se a efectuar a remoção e armazenagem dos bens "
"constantes do inventário assinado por ambas as partes, ao preço e nos termos "
"acordados nas condições específicas, acrescido, se for caso disso, dos "
"custos referidos na condição QUATRO.<br><br> O CLIENTE será responsável por "
"quaisquer formalidades administrativas necessárias para levar a cabo a "
"remoção. O pagamento de todos os tipos de impostos, direitos aduaneiros e "
"outros impostos, certificados de origem ou transferência de residência, "
"despesas consulares, transbordos, etc., necessários para a execução da "
"mudança, não estão incluídos no preço do contrato e irão aumentá-lo. O "
"Transportador é obrigado a fornecer prova documental dessas despesas ao "
"CLIENTE.<br><br> Salvo acordo em contrário, o pagamento do serviço deve ser "
"efectuado da seguinte forma: <ul style='list-style-type: disc; padding-left: "
"32px'> <li>Uma reserva dos nossos serviços é feita através do pagamento de "
"um depósito variável de mínimo 30 euros  (dependendo do serviço contratado) "
"com um cartão de crédito/débito (através de e-mail é enviado um link para o "
"CLIENTE para introduzir os detalhes do cartão através de um sistema de "
"verificação de segurança).</li> <li>Uma vez finalizada a recolha dos bens, "
"são debitados os restantes serviços prestados (transporte da cobrança, "
"suplementos e primeiro pagamento mensal) e o cartão informado é debitado, "
"deduzindo o depósito já pago.</li> <li>Os pagamentos mensais subsequentes "
"são cobrados da mesma forma, por cartão. O pagamento será efetuado "
"anticipadamente por cada mês de utilização do centro de armazenagem à taxa "
"aplicável ao serviço contratado, sendo a taxa efectuada após 30 ou 31 dias "
"(dependendo do caso). A rescisão do contrato de prestação de serviços deve "
"ser feita 15 dias antes do pagamento do próximo pagamento mensal.</li> <li>O "
"serviço de armazenamento será renovado automática e sucessivamente no termo "
"de cada mês, semestre ou ano, conforme o caso, a menos que o CLIENTE cancele "
"a renovação do serviço de armazenamento 15 dias antes do seu termo (para "
"prestações mensais) ou até ao mês anterior ao seu termo semestral/anual "
"(para prestações semestrais e anuais).</li><li>A EMPRESA reserva-se o "
"direito de aumentar unilateralmente o valor da mensalidade e/ou do "
"transporte após os primeiros 6 meses de contrato, percentagem máximo de 15%. "
"Este aumento é justificado por custos internos que podem variar no decurso "
"do contrato. Esta alteração não pode exceder duas vezes por ano. Finalmente, "
"quando a entrega é feita, o cartão de crédito ou débito informado será "
"cobrado pelos custos de entrega e suplementos. A EMPRESA fornecerá ao "
"CLIENTE um recibo pelos montantes recebidos, e uma vez concluída a remoção, "
"emitirá uma factura.</li> </ul> Finalmente, quando a entrega é feita, o "
"cartão de crédito ou débito informado será cobrado pelos custos de entrega e "
"suplementos. A EMPRESA fornecerá ao CLIENTE um recibo pelos montantes "
"recebidos, e uma vez concluída a remoção, emitirá uma factura.<br> "

#: contracts/service_templates/conditions.py:30
#: contracts/service_templates/conditions.py:67
#: contracts/service_templates/conditions.py:101
msgid "title_7"
msgstr "SEXTO. - Duração de contrato"

#: contracts/service_templates/conditions.py:30
#: contracts/service_templates/conditions.py:67
#: contracts/service_templates/conditions.py:101
msgid "conditions_7"
msgstr ""
"A duração mínima do contrato é de 3 meses a partir do dia da recolha dos "
"pertences do CLIENTE. O serviço de armazenamento será renovado automática e "
"sucessivamente no termo de cada mês ou ano, conforme o caso, a menos que o "
"CLIENTE cancele a renovação do serviço de armazenamento 15 dias antes do seu "
"termo.<br><br> Para os contratos de 6 ou 12 meses com pagamento mensal, o "
"CLIENTE pode optar por um desconto no pagamento mensal sob o compromisso de "
"permanência por um período de 6 ou 12 meses. Caso o CLIENTE não cumpra este "
"compromisso, deverá pagar as restantes mensalidades até completar o período "
"mínimo acordado, sobre o qual foi aplicado o desconto.<br><br>Uma vez "
"terminado o primeiro compromisso de estadia, o contrato será automaticamente "
"renovado pelo mesmo período inicialmente acordado. No entanto, nesta "
"renovação, o CLIENTE tem a opção de rescindir o contrato antes do final do "
"novo período completo. Caso decida rescindir o contrato antecipadamente, "
"terá de reembolsar o desconto usufruído nas mensalidades decorridas do "
"período sucessivo.<br>"

#: contracts/service_templates/conditions.py:31
#: contracts/service_templates/conditions.py:68
#: contracts/service_templates/conditions.py:102
msgid "title_8"
msgstr ""
"SÉPTIMO. - Incumprimento da obrigação de pagamento por parte do CLIENTE"

#: contracts/service_templates/conditions.py:31
#: contracts/service_templates/conditions.py:68
#: contracts/service_templates/conditions.py:102
msgid "conditions_8"
msgstr ""
"O CLIENTE deve manter as informações de pagamento atualizadas, se o cartão "
"expirar a BOX2BOX enviará ao CLIENTE uma notificação para o informar e o "
"CLIENTE deve contactar a BOX2BOX para que o problema seja corrigido.<br><br> "
"Em caso de não pagamento ou devolução de uma factura de débito directo "
"mensal, será aplicada uma sobretaxa de 15% à taxa acordada para cada mês em "
"que o incidente ocorra. A fim de resolver o não pagamento, a EMPRESA "
"transferirá os dados do CLIENTE para uma agência de cobranças. Se o CLIENTE "
"não regularizar esta situação num prazo máximo de 2 meses após o pedido de "
"pagamento da EMPRESA, este contrato será automaticamente rescindido e os "
"bens armazenados nas nossas instalações serão considerados como \"abandonados"
"\", tudo isto sob a protecção do artigo 1267º do Código Civil e no "
"entendimento de que esta conduta e atuação por parte do CLIENTE implica que "
"este já não deseja exercer o poder de facto que tinha sobre os bens, que são "
"portanto adequados para ocupação pela STUFF2BOX S. L. A EMPRESA disporá dos "
"bens de acordo com os termos do contrato e as condições do contrato. A "
"EMPRESA deve dispor dos bens como entender, sem outras limitações para além "
"das estabelecidas por Lei. <br> "

#: contracts/service_templates/conditions.py:32
#: contracts/service_templates/conditions.py:69
#: contracts/service_templates/conditions.py:103
msgid "title_9"
msgstr "OITAVO. - Responsabilidades"

#: contracts/service_templates/conditions.py:32
#: contracts/service_templates/conditions.py:69
#: contracts/service_templates/conditions.py:103
msgid "conditions_9"
msgstr ""
"O CLIENTE declara sob a sua responsabilidade que os bens, embalagens ou "
"mobiliário que são objecto da transferência e armazenamento são bens gerais, "
"de curso legal, e declara também que não são produtos inflamáveis, "
"explosivos ou alimentos perecíveis. Declara também que está ciente do "
"conteúdo das embalagens, bens ou mobiliário armazenados e que é totalmente "
"responsável por eles.<br><br> Se o CLIENTE nos fornecer mercadoria embalada "
"numa caixa/saco/mochila/ mala fechada ou similar e o Transportador não tiver "
"conhecimento do conteúdo, o Transportador não será responsável pelo conteúdo "
"perdido ou danificado. <br><br> O CLIENTE é responsável pela protecção "
"adequada dos bens e que a embalagem utilizada é correcta, sendo responsável "
"pelo que é inadequado, defeituoso ou mal utilizado, que cause danos ou "
"prejuízos aos bens, ou ao equipamento de manuseamento ou meios de "
"transporte, ou a quaisquer outros bens ou pessoas. A STUFF2BOX S.L. "
"fornecerá ao CLIENTE o seu próprio serviço de embalagem, cujo preço será de "
"mínimo de 10 euros (com base no espaço em metros quadrados). No caso de o "
"CLIENTE não optar por um ou outro sistema de embalagem, a mercadoria não "
"será coberta pelo nosso seguro. <br><br> Recomendamos não transportar e "
"guardar TV e/ou objetos eletrônicos com as mesmas características sem a sua "
"caixa original. O transporte e a manipulação deste artigos tem vários riscos "
"e não se pode evitar se a embalagem utilizada não é a própria do artigo. Se "
"o CLIENTE decide incluir a TV/objetos electrónicos no inventario sem "
"proporcionar a caixa original de dos mesmos. A EMPRESA não será responsável "
"caso ocorra algum dano produzido no transporte ou na armazenagem.<br> "

#: contracts/service_templates/conditions.py:33
#: contracts/service_templates/conditions.py:70
#: contracts/service_templates/conditions.py:104
msgid "title_10"
msgstr "NONO. - Inventario"

#: contracts/service_templates/conditions.py:33
#: contracts/service_templates/conditions.py:70
#: contracts/service_templates/conditions.py:104
msgid "conditions_10"
msgstr ""
"A EMPRESA de Transportes compromete-se a elaborar um inventário dos móveis e "
"pertences que são objecto da remoção, entregando uma cópia, carimbada e "
"assinada pela pessoa responsável pela EMPRESA, ao CLIENTE. Antes da recolha, "
"o CLIENTE deve fazer uma declaração detalhada dos objectos que possam ter um "
"determinado valor, sendo obrigatório avisar sobre aqueles cujo valor "
"unitário exceda o montante de 500 euros. Além disso, é proibido o "
"armazenamento e transporte de bens de natureza artística, histórica ou de "
"coleção. Bens como TVs devem ser guardadas com a sua caixa e embalagem "
"original. Caso não dispor da mesma a equipa protege a TV ao máximo mas a "
"EMPRESA não se fará responsável por possíveis danos.<br><br> Do mesmo modo, "
"quando, na opinião da EMPRESA, existe a possibilidade de as mercadorias a "
"serem transportadas poderem ser danificadas, quer por se encontrarem em mau "
"estado ou por serem sujeitas a manipulações que possam implicar perigo, esta "
"circunstância deve ser claramente indicada no inventário, com a EMPRESA a "
"declinar qualquer responsabilidade, quando aplicável.<br> "

#: contracts/service_templates/conditions.py:34
#: contracts/service_templates/conditions.py:71
#: contracts/service_templates/conditions.py:105
msgid "title_11"
msgstr "DÉCIMO. - Seguro sobre a mercadoria"

#: contracts/service_templates/conditions.py:34
#: contracts/service_templates/conditions.py:71
#: contracts/service_templates/conditions.py:105
msgid "conditions_11"
msgstr ""
"A EMPRESA compromete-se a assegurar os bens a transportar enumerados no "
"inventário por um valor total máximo de 2.500 euros, além disso, o valor "
"máximo aplicado a um único objeto/móvel/peça é de 500 euros informando o "
"CLIENTE do nome da companhia de seguros com a qual o seguro foi contratado e "
"o número da apólice. O seguro está contratado com a companhia AGEAS, com a "
"seguinte apólice: 0084.10.202558. As coberturas incluem roubo ou furto, "
"incêndio, fenómenos atmosféricos (incluindo risco sísmico) e "
"responsabilidade civil.<br><br> Se os bens a transportar, armazenar e/ou "
"tratar tiverem um valor total superior a 2.500 euros, o CLIENTE terá a opção "
"de providenciar um seguro privado para cobrir o valor excedente dos bens."
"<br> "

#: contracts/service_templates/conditions.py:35
#: contracts/service_templates/conditions.py:72
#: contracts/service_templates/conditions.py:106
msgid "title_12"
msgstr "DÉCIMO PRIMEIRO. - Recepção da mudança"

#: contracts/service_templates/conditions.py:35
#: contracts/service_templates/conditions.py:72
#: contracts/service_templates/conditions.py:106
msgid "conditions_12"
msgstr ""
"Uma vez concluída a remoção, o CLIENTE deve assinar uma cópia do inventário "
"ao Transportador, a fim de dar o seu acordo sobre a recepção de todos os "
"móveis, pertences e caixas que foram objeto da remoção. Uma vez que o "
"CLIENTE tenha recebido os bens e o serviço tenha sido concluído (total ou "
"parcialmente, se aplicável), o CLIENTE terá um período de 48 horas para "
"reclamar qualquer dano ao mobiliário, bens ou embalagens que possam ter sido "
"detectados da EMPRESA de Transportes. Uma vez decorrido este período, "
"nenhuma reclamação será aceite.<br><br> Caso o CLIENTE solicite a recolha "
"dos seus pertences diretamente nas instalações, junto de um transportador "
"diferente da EMPRESA, deverá notificar o responsável pelo referido "
"transporte da obrigação de verificar possíveis danos antes do carregamento. "
"Uma vez que tudo tenha saído do armazém, a EMPRESA não se responsabiliza por "
"reclamações futuras, pois entende-se que as mesmas possam surgir do "
"transporte para fora da EMPRESA.<br>"

#: contracts/service_templates/conditions.py:37
#: contracts/service_templates/conditions.py:74
#: contracts/service_templates/conditions.py:108
msgid "title_13"
msgstr "DÉCIMO SEGUNDO. - Anulação do contrato e aditamento de termo"

#: contracts/service_templates/conditions.py:38
#: contracts/service_templates/conditions.py:75
#, python-brace-format
msgid "conditions_13 {email}"
msgstr ""
"O cancelamento ou adiamento do serviço de remoção de mobiliário e pertences "
"a pedido do CLIENTE com menos de 72 horas de antecedência dará lugar a uma "
"indemnização a favor da EMPRESA de Transportes no montante de 90 euros (IVA "
"INCLUÍDO) no caso da recolha inicial dos pertences do CLIENTE. Para outros "
"serviços de transporte, esta compensação é fixada no equivalente ao custo da "
"viagem e dos recursos logísticos que foram disponibilizados para completar a "
"viagem necessária. Se a notificação for feita mais de 72 horas úteis antes "
"da data acordada para o início da remoção, nenhuma das partes pagará "
"qualquer indemnização.<br><br> A execução do serviço deve ser acordada num "
"horário indicado pela EMPRESA. O CLIENTE deve estar disponível na morada "
"nesse intervalo de tempo, caso contrário, haverá uma penalização de 20 euros "
"após os primeiros 20 minutos de espera. Após 50 minutos, a penalização será "
"de 50 euros. No caso do Transportador estar à espera e o CLIENTE não atender "
"o telefone/telefone, será cobrada uma taxa de 90 euros e a recolha será "
"imediatamente cancelada.<br><br> Após a contratação do serviço, o CLIENTE "
"pode cancelar e desistir do contrato num prazo máximo de 14 dias úteis, com "
"ou sem justa causa, sem qualquer penalização, desde que o Serviço não tenha "
"sido executado. Se o cancelamento do serviço ocorrer nas 24 horas anteriores "
"à data prevista para a recolha da mercadoria, a EMPRESA terá direito a "
"receber o custo da viagem, bem como as despesas incorridas para a execução "
"da transferência programada, das quais o CLIENTE deverá ter sido previamente "
"informado (90 euros).<br> Para exercer o direito de rescisão, deve notificar-"
"nos por e-mail <a href='mailto:{email}'>{email}</a> a decisão de rescisão do "
"contrato através de uma declaração inequívoca. Deve anexar um ficheiro "
"contendo as seguintes informações ao e-mail:<br> <span style='display:inline-"
"block; width:100%;'>À atenção da BOX2BOX,</span>  <span style='display:"
"inline-block; width:100%;'>Eu/nós informamos (*) que desisto(desistimos) do "
"meu/nosso (*) contrato de venda do seguinte produto (*):</span> <span "
"style='display:inline-block; width:100%; padding-left:32px;'>— pedido em (*)/"
"recebido em (*)</span> <span style='display:inline-block; width:100%; "
"padding-left:32px;'>— nome do(s) consumidor(es)</span> <span style='display:"
"inline-block; width:100%; padding-left:32px;'>— Endereço do(s) "
"consumidor(es)</span> <span style='display: inline-block; width:100%; "
"padding-left:32px;'>— Data</span>  "

#: contracts/service_templates/conditions.py:42
#: contracts/service_templates/conditions.py:79
#: contracts/service_templates/conditions.py:111
msgid "title_14"
msgstr "DÉCIMO TERCEIRA. - Comunicações"

#: contracts/service_templates/conditions.py:42
#: contracts/service_templates/conditions.py:79
#: contracts/service_templates/conditions.py:111
msgid "conditions_14"
msgstr ""
"Qualquer notificação ou outra comunicação que uma parte deva fazer à outra "
"nos termos ou em relação a este contrato será feita por escrito por qualquer "
"meio que comprove tanto o seu conteúdo como o seu recebimento pelo "
"destinatário indicado. Qualquer notificação feita desta forma produzirá "
"efeitos a partir da data do seu recebimento.<br>"

#: contracts/service_templates/conditions.py:43
#: contracts/service_templates/conditions.py:112
msgid "title_15"
msgstr "DÉCIMO QUARTO. - Jurisdição"

#: contracts/service_templates/conditions.py:43
#: contracts/service_templates/conditions.py:112
msgid "conditions_15"
msgstr ""
"No omisso aplica-se a legislação aplicável, designadamente os artigos 366º e "
"seguintes do Código Comercial e 1185º e seguintes do Código Civil.<br> Para "
"resolução de todos os litígios decorrentes da interpretação, validade e "
"execução do contrato fica estipulada a competência do Tribunal da Comarca de "
"Lisboa, com expressa renúncia a qualquer outro.<br> "

#: contracts/service_templates/conditions.py:94
#, fuzzy
#| msgid "conditions_10"
msgid "conditions_1"
msgstr ""
"A EMPRESA de Transportes compromete-se a elaborar um inventário dos móveis e "
"pertences que são objecto da remoção, entregando uma cópia, carimbada e "
"assinada pela pessoa responsável pela EMPRESA, ao CLIENTE. Antes da recolha, "
"o CLIENTE deve fazer uma declaração detalhada dos objectos que possam ter um "
"determinado valor, sendo obrigatório avisar sobre aqueles cujo valor "
"unitário exceda o montante de 500 euros. Além disso, é proibido o "
"armazenamento e transporte de bens de natureza artística, histórica ou de "
"coleção. Bens como TVs devem ser guardadas com a sua caixa e embalagem "
"original. Caso não dispor da mesma a equipa protege a TV ao máximo mas a "
"EMPRESA não se fará responsável por possíveis danos.<br><br> Do mesmo modo, "
"quando, na opinião da EMPRESA, existe a possibilidade de as mercadorias a "
"serem transportadas poderem ser danificadas, quer por se encontrarem em mau "
"estado ou por serem sujeitas a manipulações que possam implicar perigo, esta "
"circunstância deve ser claramente indicada no inventário, com a EMPRESA a "
"declinar qualquer responsabilidade, quando aplicável.<br> "

#: contracts/service_templates/conditions.py:109
#, fuzzy
#| msgid "conditions_3"
msgid "conditions_13"
msgstr ""
"Os serviços contratados serão os referidos na cláusula anterior, bem como "
"todos os que se enquadram na categoria dos serviços acordados com o CLIENTE "
"e que serão especificados nas condições particulares do presente "
"contrato<br> "

#: contracts/service_templates/conditions.py:113
msgid "title_16"
msgstr ""

#: contracts/service_templates/conditions.py:113
msgid "conditions_16"
msgstr ""

#: contracts/service_templates/conditions.py:114
msgid "title_17"
msgstr ""

#: contracts/service_templates/conditions.py:114
msgid "conditions_17"
msgstr ""

#: contracts/service_templates/conditions.py:115
msgid "title_18"
msgstr ""

#: contracts/service_templates/conditions.py:115
msgid "conditions_18"
msgstr ""

#: contracts/service_templates/conditions.py:116
msgid "title_19"
msgstr ""

#: contracts/service_templates/conditions.py:116
msgid "conditions_19"
msgstr ""

#: contracts/service_templates/conditions.py:117
#, fuzzy
#| msgid "title_2"
msgid "title_20"
msgstr "PRIMEIRO - Objeto de contrato e descrição de serviço"

#: contracts/service_templates/conditions.py:117
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_20"
msgstr ""
"O serviço de transferência e armazenamento contratado é definido como a "
"transferência ou transporte do endereço de recolha para os nossos centros de "
"armazenamento (utilizando um ou mais meios de transporte) de mobiliário, "
"bens ou embalagens (doravante designados por mercadoria).<br><br> A EMPRESA "
"tem vários centros de armazenamento localizados em locais diferentes, pelo "
"que o destino dos bens e o seu armazenamento dependerá da disponibilidade de "
"espaço de armazenamento adequado num ou noutro centro (isto não fará "
"qualquer diferença no custo a ser suportado pelo CLIENTE). Da mesma forma, o "
"serviço inclui a transferência ou transporte subsequente dos nossos centros "
"de armazenamento para o endereço de entrega a ser determinado no momento "
"indicado pelo CLIENTE e sempre dentro do prazo do contrato. O serviço será "
"considerado concluído no momento da entrega dos bens abrangidos pelo "
"contrato no endereço de entrega, nas condições acordadas.<br> "

#: contracts/service_templates/conditions.py:118
#, fuzzy
#| msgid "title_2"
msgid "title_21"
msgstr "PRIMEIRO - Objeto de contrato e descrição de serviço"

#: contracts/service_templates/conditions.py:118
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_21"
msgstr ""
"O serviço de transferência e armazenamento contratado é definido como a "
"transferência ou transporte do endereço de recolha para os nossos centros de "
"armazenamento (utilizando um ou mais meios de transporte) de mobiliário, "
"bens ou embalagens (doravante designados por mercadoria).<br><br> A EMPRESA "
"tem vários centros de armazenamento localizados em locais diferentes, pelo "
"que o destino dos bens e o seu armazenamento dependerá da disponibilidade de "
"espaço de armazenamento adequado num ou noutro centro (isto não fará "
"qualquer diferença no custo a ser suportado pelo CLIENTE). Da mesma forma, o "
"serviço inclui a transferência ou transporte subsequente dos nossos centros "
"de armazenamento para o endereço de entrega a ser determinado no momento "
"indicado pelo CLIENTE e sempre dentro do prazo do contrato. O serviço será "
"considerado concluído no momento da entrega dos bens abrangidos pelo "
"contrato no endereço de entrega, nas condições acordadas.<br> "

#: contracts/service_templates/conditions.py:119
#, fuzzy
#| msgid "title_2"
msgid "title_22"
msgstr "PRIMEIRO - Objeto de contrato e descrição de serviço"

#: contracts/service_templates/conditions.py:119
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_22"
msgstr ""
"O serviço de transferência e armazenamento contratado é definido como a "
"transferência ou transporte do endereço de recolha para os nossos centros de "
"armazenamento (utilizando um ou mais meios de transporte) de mobiliário, "
"bens ou embalagens (doravante designados por mercadoria).<br><br> A EMPRESA "
"tem vários centros de armazenamento localizados em locais diferentes, pelo "
"que o destino dos bens e o seu armazenamento dependerá da disponibilidade de "
"espaço de armazenamento adequado num ou noutro centro (isto não fará "
"qualquer diferença no custo a ser suportado pelo CLIENTE). Da mesma forma, o "
"serviço inclui a transferência ou transporte subsequente dos nossos centros "
"de armazenamento para o endereço de entrega a ser determinado no momento "
"indicado pelo CLIENTE e sempre dentro do prazo do contrato. O serviço será "
"considerado concluído no momento da entrega dos bens abrangidos pelo "
"contrato no endereço de entrega, nas condições acordadas.<br> "

#: contracts/service_templates/conditions.py:120
#, fuzzy
#| msgid "title_2"
msgid "title_23"
msgstr "PRIMEIRO - Objeto de contrato e descrição de serviço"

#: contracts/service_templates/conditions.py:120
#, fuzzy
#| msgid "conditions_2"
msgid "conditions_23"
msgstr ""
"O serviço de transferência e armazenamento contratado é definido como a "
"transferência ou transporte do endereço de recolha para os nossos centros de "
"armazenamento (utilizando um ou mais meios de transporte) de mobiliário, "
"bens ou embalagens (doravante designados por mercadoria).<br><br> A EMPRESA "
"tem vários centros de armazenamento localizados em locais diferentes, pelo "
"que o destino dos bens e o seu armazenamento dependerá da disponibilidade de "
"espaço de armazenamento adequado num ou noutro centro (isto não fará "
"qualquer diferença no custo a ser suportado pelo CLIENTE). Da mesma forma, o "
"serviço inclui a transferência ou transporte subsequente dos nossos centros "
"de armazenamento para o endereço de entrega a ser determinado no momento "
"indicado pelo CLIENTE e sempre dentro do prazo do contrato. O serviço será "
"considerado concluído no momento da entrega dos bens abrangidos pelo "
"contrato no endereço de entrega, nas condições acordadas.<br> "

#: contracts/service_templates/contracts.py:13
msgid "Furniture packaging"
msgstr "Embalagem de móveis"

#: contracts/service_templates/contracts.py:13
msgid "Furniture packaging service"
msgstr "Serviço de embalagem de mobiliário"

#: contracts/service_templates/contracts.py:17
msgid "Furniture assembly/disassembly"
msgstr "Montagem - desmontagem de móveis"

#: contracts/service_templates/contracts.py:18
msgid "Furniture assembly/disassembly service"
msgstr "Serviço de montagem - desmontagem de móveis"

#: contracts/service_templates/contracts.py:23
#: contracts/service_templates/contracts.py:30
msgid "Time spent"
msgstr "Tempo gasto"

#: contracts/service_templates/contracts.py:36
msgid "Assembled/Disassembled furniture"
msgstr "Descrição"

#: contracts/service_templates/contracts.py:38
msgid "Stairs service"
msgstr "Serviço de escadas"

#: contracts/service_templates/contracts.py:39
msgid "Floors"
msgstr "Pisos"

#: contracts/service_templates/contracts.py:43
msgid "Volume moved in m²"
msgstr "Volume em m²"

#: contracts/service_templates/contracts.py:49
msgid "Packaging of moving boxes"
msgstr "Embalagem da caixa móvel"

#: contracts/service_templates/contracts.py:50
msgid "Packaging of moving boxes service"
msgstr "Serviço de embalagem da caixa móvel"

#: contracts/service_templates/contracts.py:55
msgid "Packaging of wardrobe boxes"
msgstr "Embalagem da caixa armário"

#: contracts/service_templates/contracts.py:56
msgid "Packaging of wardrobe boxes service"
msgstr "Serviço de embalagem da caixa armário"

#: contracts/service_templates/contracts.py:59
msgid "Parked withing a range of 50m"
msgstr "Distância de casa superior a 50m\t"

#: contracts/service_templates/contracts.py:59
msgid "Parking"
msgstr "Estacionamento"

#: contracts/service_templates/contracts.py:60
msgid "Kilometers"
msgstr "Quilometragem"

#: contracts/service_templates/contracts.py:64
msgid "Distance"
msgstr "Distância"

#: contracts/service_templates/contracts.py:72
msgid "Clean spot"
msgstr "Ponto limpo"

#: contracts/service_templates/contracts.py:76
msgid "Volume moved to clean spot"
msgstr "Volume transferido para o ponto limpo"

#: contracts/service_templates/contracts.py:86
msgid "Additional stop"
msgstr "Paragem adicional"

#: contracts/service_templates/contracts.py:87
msgid "Comments"
msgstr "Comentários"

#: contracts/service_templates/contracts.py:150
#, python-brace-format
msgid "{type} delivery note"
msgstr "Nota de entrega de {type}"

#: contracts/service_templates/contracts.py:174
msgid "Space in m²"
msgstr "Espaço em m²"

#: contracts/service_templates/contracts.py:183
msgid "Code"
msgstr "Código"

#: contracts/service_templates/contracts.py:183
msgid "Description"
msgstr "Descrição"

#: contracts/service_templates/movings.py:15
msgid "Moving service"
msgstr "Nota de entrega de mudança"

#: contracts/utils.py:89
msgid "Madrid"
msgstr ""

#: contracts/utils.py:90
msgid "Porto"
msgstr ""

#: contracts/utils.py:91
msgid "Sevilla"
msgstr ""

#: contracts/utils.py:92
msgid "Barcelona"
msgstr ""

#: contracts/utils.py:93
msgid "Malaga"
msgstr ""

#: contracts/utils.py:94
msgid "Valencia"
msgstr ""

#: contracts/utils.py:95
msgid "Milano"
msgstr ""

#: contracts/utils.py:96
msgid "Paris"
msgstr ""

#: contracts/utils.py:97
msgid "Lisboa"
msgstr ""

#: contracts/utils.py:98
msgid "Queretaro"
msgstr ""

#: contracts/utils.py:99
msgid "Granada"
msgstr ""

#: contracts/utils.py:100
msgid "Salamanca"
msgstr ""

#: contracts/utils.py:101
msgid "Bilbao"
msgstr ""

#: contracts/utils.py:102
msgid "San Sebastian"
msgstr ""

#: contracts/utils.py:103
msgid "Pamplona"
msgstr ""

#: contracts/utils.py:121 payments/models/payments.py:57
msgid "Monthly"
msgstr ""

#: contracts/utils.py:122
msgid "Quarterly"
msgstr ""

#: contracts/utils.py:123 payments/models/payments.py:58
msgid "Biannual"
msgstr ""

#: contracts/utils.py:124
msgid "Annual"
msgstr ""

#: contracts/utils.py:131
msgid "Default"
msgstr ""

#: contracts/views/service.py:308
#, fuzzy
#| msgid "Access type"
msgid "Accepted"
msgstr "Tipo de aceso"

#: contracts/views/service.py:335
msgid "Released"
msgstr ""

#: contracts/views/service.py:352 contracts/views/service.py:369
#: riders/models/riders.py:48
msgid "Discarded"
msgstr ""

#: core/chargebee_errors.py:4
msgid "Invalid format"
msgstr ""

#: core/chargebee_errors.py:5
msgid "Payment cannot be created as the payment collection failed"
msgstr ""

#: core/chargebee_errors.py:6
msgid "There's a problem adding the payment method"
msgstr ""

#: core/chargebee_errors.py:7 core/chargebee_errors.py:9
msgid "Unknown error in the payment gateway. Wait few minutes and try again"
msgstr ""

#: core/chargebee_errors.py:8
msgid "Error processing the payment. Try again later"
msgstr ""

#: core/chargebee_errors.py:10
msgid "Unknown error. Please, wait few minutes and try again"
msgstr ""

#: core/exceptions.py:36
msgid "Invalid input."
msgstr ""

#: core/handlers.py:26
msgid "Not found"
msgstr ""

#: core/handlers.py:35
msgid ""
"It seems there is a conflict with your input data. Maybe is due to a "
"duplicated or deleted entry. Please, review your data and try again"
msgstr ""

#: core/handlers.py:45
msgid "There is a version conflict on update. Reload the data and try again"
msgstr ""

#: core/mixins.py:80
msgid "version is required"
msgstr ""

#: core/models/event_log.py:22
msgid "Unknown event"
msgstr ""

#: core/models/event_log.py:23
msgid "Hubspot event"
msgstr ""

#: core/models/event_log.py:24
msgid "Moreapp event"
msgstr ""

#: core/models/event_log.py:25
msgid "Moreapp inventory event"
msgstr ""

#: core/models/event_log.py:26
msgid "Riderapp event"
msgstr ""

#: core/models/event_log.py:27
msgid "Chargebee event"
msgstr ""

#: core/models/event_log.py:28
msgid "Microservice event"
msgstr ""

#: core/models/event_log.py:29
msgid "Storage event"
msgstr ""

#: core/models/moloni.py:20 intranet/models/delivery.py:38
msgid "Empty"
msgstr ""

#: core/models/moloni.py:21
msgid "Customer"
msgstr ""

#: core/models/moloni.py:22 payments/admin.py:37
msgid "Invoice"
msgstr ""

#: core/models/moloni.py:23
msgid "Credit note"
msgstr ""

#: core/models/product.py:16
msgid "One time"
msgstr ""

#: core/models/product.py:34
msgid "For old odoo planes"
msgstr ""

#: core/models/temporary_deletion_entries.py:13
msgid "Used to retrieve the ids to be deleted"
msgstr ""

#: core/models/temporary_deletion_entries.py:14
msgid "Id to be deleted"
msgstr ""

#: core/models/temporary_deletion_entries.py:16
msgid "Table name to search the ids to delete"
msgstr ""

#: core/notifications.py:49
#, python-brace-format
msgid "Service {contract_id}"
msgstr ""

#: core/recursive_model.py:42
msgid ""
"If this FK is set means this is an old entry which is replaced by a new entry"
msgstr ""

#: core/recursive_model.py:54
msgid ""
"FK to improve lookups setting the most up-to-date entry related with this "
"object"
msgstr ""

#: core/serializers.py:27
msgid ""
"Required to get the access thru the API Gateway in production environment"
msgstr ""

#: core/utils.py:391
msgid "Yes"
msgstr "Sim"

#: core/utils.py:392
msgid "No"
msgstr "Não"

#: core/views.py:105
msgid "X-Chunk-Index and X-Total-Chunks are required"
msgstr ""

#: core/views.py:123
msgid "Token is required on subsequent requests"
msgstr ""

#: core/views.py:132
msgid "No ids provided"
msgstr ""

#: core/views.py:151
msgid "All chunks processed successfully"
msgstr ""

#: core/views.py:153
msgid "Chunk processed"
msgstr ""

#: intranet/admin.py:66 riders/models/riders.py:43
msgid "Sent"
msgstr ""

#: intranet/admin.py:100 riders/admin.py:366 users/admin.py:69
msgid "Personal info"
msgstr ""

#: intranet/admin.py:108 users/admin.py:83
msgid "User Image"
msgstr ""

#: intranet/admin.py:109
msgid "Misc"
msgstr ""

#: intranet/admin.py:110 riders/admin.py:374 users/admin.py:84
msgid "Important dates"
msgstr ""

#: intranet/admin.py:111 riders/admin.py:377 users/admin.py:90
msgid "Permissions"
msgstr ""

#: intranet/admin.py:113 riders/admin.py:379 users/admin.py:92
msgid "Django Admin Permissions"
msgstr ""

#: intranet/admin.py:132 users/admin.py:120
msgid "Avatar"
msgstr ""

#: intranet/admin.py:260
msgid "Team"
msgstr ""

#: intranet/filters/calendars.py:23
msgid "Bring calendars by name"
msgstr ""

#: intranet/filters/calendars.py:33
msgid "Bring calendars from a specific city"
msgstr ""

#: intranet/filters/calendars.py:43
msgid "Bring calendars by user email"
msgstr ""

#: intranet/filters/calendars.py:53
msgid "Bring calendars by rider email"
msgstr ""

#: intranet/filters/calendars.py:63
msgid "Bring calendars that are created on remote"
msgstr ""

#: intranet/filters/calendars.py:73
msgid "Filter only calendars without ownership"
msgstr ""

#: intranet/filters/calendars.py:83
msgid "Filter only calendars which belong to a given user"
msgstr ""

#: intranet/filters/city.py:22
msgid "Bring cities by name"
msgstr ""

#: intranet/filters/city.py:32
msgid "Bring cities by created at date"
msgstr ""

#: intranet/filters/city.py:42
msgid "Bring cities by riders email"
msgstr ""

#: intranet/filters/residence.py:24
msgid "Filter residences by name"
msgstr ""

#: intranet/filters/residence.py:34 intranet/filters/residence.py:74
msgid "Filter residences by group"
msgstr ""

#: intranet/filters/residence.py:44
msgid "Filter residences by city"
msgstr ""

#: intranet/filters/residence.py:54
msgid "Filter residences by created at date"
msgstr ""

#: intranet/filters/residence.py:64
msgid "Filter residences by country"
msgstr ""

#: intranet/filters/residence_group.py:22
msgid "Filter residence groups by name"
msgstr ""

#: intranet/filters/residence_group.py:32
msgid "Filter residence groups by created at date"
msgstr ""

#: intranet/filters/reviews.py:24 intranet/filters/warehouse_movements.py:66
msgid "Bring placements from a specific city"
msgstr ""

#: intranet/filters/reviews.py:34
msgid "Bring placements by created at date"
msgstr ""

#: intranet/filters/reviews.py:44
msgid "Bring enabled systems only"
msgstr ""

#: intranet/filters/reviews.py:54
msgid "Bring specific systems only"
msgstr ""

#: intranet/filters/rider.py:24
msgid "Bring riders by first name or last name"
msgstr ""

#: intranet/filters/rider.py:34
msgid "Bring riders by email"
msgstr ""

#: intranet/filters/rider.py:44
msgid "Bring only active riders"
msgstr ""

#: intranet/filters/rider.py:54
msgid "Bring riders by date joined"
msgstr ""

#: intranet/filters/rider.py:64
msgid "Bring riders from a specific city"
msgstr ""

#: intranet/filters/rider.py:74
msgid "Bring riders from specific warehouses"
msgstr ""

#: intranet/filters/rider.py:84
msgid "Bring not disabled riders"
msgstr ""

#: intranet/filters/rider.py:94
msgid "Bring riders associated with a specific calendar"
msgstr ""

#: intranet/filters/service.py:23
#, fuzzy
#| msgid "Service type"
msgid "Bring services by type"
msgstr "Tipo de serviço"

#: intranet/filters/service.py:33
msgid "Bring services from a specific contract"
msgstr ""

#: intranet/filters/service.py:43
msgid "Bring services from a specific city"
msgstr ""

#: intranet/filters/service.py:53 intranet/filters/task.py:56
msgid "Bring services by event date"
msgstr ""

#: intranet/filters/service.py:63 intranet/filters/task.py:66
msgid "Bring services by a given rider"
msgstr ""

#: intranet/filters/service.py:73 intranet/filters/task.py:76
msgid "Bring services by riders email"
msgstr ""

#: intranet/filters/service.py:83 intranet/filters/task.py:86
msgid "Bring services by created at date"
msgstr ""

#: intranet/filters/service.py:93
msgid "Bring services by updated at date"
msgstr ""

#: intranet/filters/service.py:103 intranet/filters/task.py:96
msgid "Bring services by status"
msgstr ""

#: intranet/filters/service.py:113
msgid "Bring services from specific warehouses"
msgstr ""

#: intranet/filters/service.py:123 intranet/filters/task.py:106
msgid "Bring services from specific calendars"
msgstr ""

#: intranet/filters/service.py:133 intranet/filters/task.py:116
msgid "Bring services with or without events"
msgstr ""

#: intranet/filters/service.py:143
msgid "Bring services from specific residences groups"
msgstr ""

#: intranet/filters/service.py:153
msgid "Bring services from specific residences"
msgstr ""

#: intranet/filters/task.py:46
msgid "Bring services by title"
msgstr ""

#: intranet/filters/user.py:24
msgid "Filter by another backoffice allowed users"
msgstr ""

#: intranet/filters/user.py:30
msgid "Exclude user from query"
msgstr ""

#: intranet/filters/warehouse.py:24
msgid "Bring warehouses by name"
msgstr ""

#: intranet/filters/warehouse.py:34
msgid "Bring warehouses from a specific city"
msgstr ""

#: intranet/filters/warehouse.py:44
msgid "Bring warehouses by created at date"
msgstr ""

#: intranet/filters/warehouse.py:54
msgid "Bring warehouses by providers email"
msgstr ""

#: intranet/filters/warehouse.py:64
msgid "Bring warehouses by riders email"
msgstr ""

#: intranet/filters/warehouse.py:74
msgid "Bring current warehouses only"
msgstr ""

#: intranet/filters/warehouse.py:84
msgid "Bring warehouses which allows warehouse differences only"
msgstr ""

#: intranet/filters/warehouse.py:91
msgid "Bring warehouses with related FKs: calendars and riders"
msgstr ""

#: intranet/filters/warehouse.py:97
msgid "Bring providers with related FKs: user"
msgstr ""

#: intranet/filters/warehouse.py:103
msgid "Bring providers filtering by default field with related FKs: user"
msgstr ""

#: intranet/filters/warehouse_differences.py:24
msgid "Bring warehouse differences from specific warehouses"
msgstr ""

#: intranet/filters/warehouse_differences.py:34
msgid "Bring warehouse differences from a specific city"
msgstr ""

#: intranet/filters/warehouse_differences.py:44
msgid "Bring warehouse differences by number of results"
msgstr ""

#: intranet/filters/warehouse_differences.py:54
msgid "Bring warehouse differences created at date"
msgstr ""

#: intranet/filters/warehouse_movements.py:26
msgid "Bring placements from a specific contracts based on the pk"
msgstr ""

#: intranet/filters/warehouse_movements.py:36
msgid ""
"Bring placements from a specific contracts based on the contract_document_id"
msgstr ""

#: intranet/filters/warehouse_movements.py:46
msgid "Bring placements from specific warehouses"
msgstr ""

#: intranet/filters/warehouse_movements.py:56
msgid "Bring placements from a specific location"
msgstr ""

#: intranet/filters/warehouse_movements.py:76
msgid "Bring placements by sent at date"
msgstr ""

#: intranet/filters/warehouse_movements.py:86
msgid "Bring placements by number of pallets"
msgstr ""

#: intranet/filters/warehouse_movements.py:96
msgid "Bring current placements only"
msgstr ""

#: intranet/filters/warehouse_movements.py:106
msgid "Bring placements from a specific placement (i.e. recursive)"
msgstr ""

#: intranet/filters/warehouse_movements.py:116
msgid "Bring placements which belong to active contracts"
msgstr ""

#: intranet/filters/warehouse_user.py:24
msgid "Bring warehouses from specific warehouse users"
msgstr ""

#: intranet/filters/warehouse_user.py:34
msgid "Bring warehouse users by email"
msgstr ""

#: intranet/filters/warehouse_user.py:44
msgid "Bring warehouse users by created at date"
msgstr ""

#: intranet/models/budget_estimator.py:18 intranet/models/delivery.py:45
msgid "Attribute to keep the whole view data"
msgstr ""

#: intranet/models/budget_estimator.py:25
msgid "Inventory resume data to upload in hubspot"
msgstr ""

#: intranet/models/budget_estimator.py:27
msgid "If this budget estimation is in hubspot"
msgstr ""

#: intranet/models/budget_estimator.py:31
msgid "If this budget estimation is before Mexico refactoring"
msgstr ""

#: intranet/models/budget_estimator.py:37
msgid "Site where user is allowed to be from intranet"
msgstr ""

#: intranet/models/budget_estimator.py:46
msgid ""
"In order to publish the estimation in Hubspot, a valid contract is needed"
msgstr ""

#: intranet/models/calendar.py:12
msgid "Calendar name"
msgstr ""

#: intranet/models/calendar.py:13
msgid "Calendar id on google calendar"
msgstr ""

#: intranet/models/calendar.py:15
msgid "UI color to display this calendar in hexadecimal"
msgstr ""

#: intranet/models/calendar.py:45
msgid "Created by the user"
msgstr ""

#: intranet/models/calendar.py:47
msgid "Calendar linked to this event"
msgstr ""

#: intranet/models/calendar.py:61
msgid "Used to identify the kind of service"
msgstr ""

#: intranet/models/calendar.py:68
msgid "Blocked slot for a given service"
msgstr ""

#: intranet/models/calendar.py:157
msgid "If this rider must be shown as default in the event creation"
msgstr ""

#: intranet/models/city.py:15
msgid "City visible name"
msgstr ""

#: intranet/models/city.py:17
msgid "City canonical name"
msgstr ""

#: intranet/models/delivery.py:41
msgid "User requested delivery"
msgstr "Entrega solicitada pelo cliente"

#: intranet/models/delivery.py:77 intranet/models/delivery.py:107
msgid "Precondition failed to build the excel"
msgstr ""

#: intranet/models/delivery.py:80
msgid "Empty data to build the excel file"
msgstr ""

#: intranet/models/delivery.py:83
msgid "Empty delivery date"
msgstr ""

#: intranet/models/delivery.py:110
msgid "User name is required to send the inventory email to Logwin warehouse"
msgstr ""

#: intranet/models/delivery.py:113 intranet/models/delivery.py:181
msgid ""
"Warehouse does not have a valid city and it is mandatory to set up the "
"sender on emails"
msgstr ""

#: intranet/models/delivery.py:122
#, python-brace-format
msgid ""
"Delivery email skipped temporarily because warehouse is not defined: {model}"
msgstr ""

#: intranet/models/delivery.py:128
msgid "There was an error building the excel to send it to the warehouse"
msgstr ""

#: intranet/models/delivery.py:145
#, python-brace-format
msgid "{request} - {contract_id}"
msgstr ""

#: intranet/models/delivery.py:178
msgid "Precondition failed to send the cancellation email"
msgstr ""

#: intranet/models/delivery.py:191
#, python-brace-format
msgid ""
"Cancellation of delivery email skipped temporarily because warehouse is not "
"defined: {model}"
msgstr ""

#: intranet/models/delivery.py:201
#, python-brace-format
msgid "{request} - {contract_id} - Canceled"
msgstr "{request} - {contract_id} - Cancelado"

#: intranet/models/global_settings.py:37
msgid "0.5"
msgstr ""

#: intranet/models/global_settings.py:38
msgid "1"
msgstr ""

#: intranet/models/global_settings.py:39
msgid "2"
msgstr ""

#: intranet/models/global_settings.py:40
msgid "3"
msgstr ""

#: intranet/models/global_settings.py:41
msgid "4"
msgstr ""

#: intranet/models/global_settings.py:42
msgid "5"
msgstr ""

#: intranet/models/global_settings.py:43
msgid "6"
msgstr ""

#: intranet/models/global_settings.py:44
msgid "7.5"
msgstr ""

#: intranet/models/global_settings.py:45
msgid "9"
msgstr ""

#: intranet/models/global_settings.py:46
msgid "12"
msgstr ""

#: intranet/models/global_settings.py:47
msgid "15"
msgstr ""

#: intranet/models/global_settings.py:48
msgid "20"
msgstr ""

#: intranet/models/global_settings.py:49
msgid "25"
msgstr ""

#: intranet/models/global_settings.py:50
msgid "30"
msgstr ""

#: intranet/models/global_settings.py:51
msgid "35"
msgstr ""

#: intranet/models/global_settings.py:52
msgid "40"
msgstr ""

#: intranet/models/global_settings.py:53
msgid "45"
msgstr ""

#: intranet/models/global_settings.py:54
msgid "50"
msgstr ""

#: intranet/models/global_settings.py:55
msgid "50+"
msgstr ""

#: intranet/models/global_settings.py:67
msgid "Global Settings"
msgstr ""

#: intranet/models/payment_reminder.py:24 intranet/models/warehouse_user.py:12
#: users/models.py:67
msgid "Email address"
msgstr ""

#: intranet/models/payment_reminder.py:37
msgid "Used to avoid duplicated slack events to be handled"
msgstr ""

#: intranet/models/payment_reminder.py:65
#, python-brace-format
msgid "BOX2BOX - Your pickup is coming {date}"
msgstr "BOX2BOX - A sua recolha está próxima {date}"

#: intranet/models/residence.py:13
msgid "Residence group name"
msgstr ""

#: intranet/models/residence.py:16
msgid "Group canonical name"
msgstr ""

#: intranet/models/residence.py:44
msgid "Residence visible name"
msgstr ""

#: intranet/models/residence.py:45
#, fuzzy
#| msgid "Moving address"
msgid "Residence address"
msgstr "Endereço de mudança"

#: intranet/models/reviews.py:25
msgid "Google"
msgstr ""

#: intranet/models/reviews.py:26
msgid "Trustpilot"
msgstr ""

#: intranet/models/reviews.py:37
msgid "Review link to send by email on contract signing"
msgstr ""

#: intranet/models/userproxy.py:22 riders/models/riders.py:209
msgid "User"
msgstr ""

#: intranet/models/userproxy.py:43
#, python-brace-format
msgid "{prefix} Activate your account to get access in the Backoffice portal"
msgstr ""

#: intranet/models/warehouse_differences.py:28
msgid "Pending"
msgstr ""

#: intranet/models/warehouse_differences.py:29 payments/models/payments.py:67
msgid "Running"
msgstr ""

#: intranet/models/warehouse_differences.py:30 payments/models/payments.py:48
#: payments/models/payments.py:203
msgid "Failed"
msgstr ""

#: intranet/models/warehouse_differences.py:31
msgid "Success"
msgstr ""

#: intranet/models/warehouse_differences.py:46
msgid "Warehouse difference between warehouse and our space"
msgstr ""

#: intranet/models/warehouse_differences.py:48
msgid "Sum of warehouse space"
msgstr ""

#: intranet/models/warehouse_differences.py:49
msgid "Sum of our space"
msgstr ""

#: intranet/models/warehouse_movement.py:35
msgid "Where the goods are located inside the building "
msgstr ""

#: intranet/models/warehouse_movement.py:37
msgid "Number of pallets occupied"
msgstr ""

#: intranet/models/warehouse_movement.py:39
msgid "Floor where the goods are located"
msgstr ""

#: intranet/models/warehouse_movement.py:40
msgid "Datetime when this tracking was sent"
msgstr ""

#: intranet/models/warehouse_movement.py:48
msgid ""
"Internal tracking id to avoid duplicated movements in case of the same "
"movement is handled twice or more times"
msgstr ""

#: intranet/models/warehouse_user.py:36
msgid "Check this if the warehouse user must be showed as default on events"
msgstr ""

#: intranet/models/warehouse_user.py:39
msgid "If this user must be notified when sending the moreapp email"
msgstr ""

#: intranet/serializers/budget_estimation.py:25
msgid "Fill in only if contract is null"
msgstr ""

#: intranet/serializers/budget_estimation.py:31
msgid ""
"If contract_document_id is filled in, it will have priority over this "
"attribute"
msgstr ""

#: intranet/serializers/budget_estimation.py:49
msgid "A valid contract is needed in order to create a budget estimation"
msgstr ""

#: intranet/serializers/calendar.py:218
msgid "You are trying to share this calendar with non allowed users"
msgstr ""

#: intranet/serializers/calendar.py:247
msgid ""
"You are trying to share this calendar or set as default users on this "
"calendar with non allowed users"
msgstr ""

#: intranet/serializers/calendar_scheduler.py:11
msgid "Title"
msgstr ""

#: intranet/serializers/city.py:44 intranet/serializers/city.py:67
msgid "The city already exists"
msgstr ""

#: intranet/serializers/event.py:294
msgid "Warehouse id required when pickup or delivery is selected"
msgstr ""

#: intranet/serializers/event.py:438
msgid "Start time cannot be greater than end time"
msgstr ""

#: intranet/serializers/event.py:451
msgid "Riders to add to this team"
msgstr ""

#: intranet/serializers/event.py:484
msgid "Service, task or blank slot is required to create events"
msgstr ""

#: intranet/serializers/event.py:489 intranet/serializers/event.py:795
msgid "Only one from service, task and blank_slot can be specified"
msgstr ""

#: intranet/serializers/event.py:493
msgid "Task and city cannot be set at the same time"
msgstr ""

#: intranet/serializers/event.py:497 intranet/serializers/event.py:804
msgid "Calendar is not set but the teams are"
msgstr ""

#: intranet/serializers/event.py:512
#, python-brace-format
msgid "Non valid teams {teams}"
msgstr ""

#: intranet/serializers/event.py:517
msgid "Invalid city"
msgstr ""

#: intranet/serializers/event.py:524 intranet/serializers/event.py:824
msgid "Service is mandatory to link providers to the event"
msgstr ""

#: intranet/serializers/event.py:531 intranet/serializers/event.py:831
msgid "Warehouse is mandatory to link providers to the event"
msgstr ""

#: intranet/serializers/event.py:766
msgid "Needed to delete the old event in calendar"
msgstr ""

#: intranet/serializers/event.py:772 intranet/serializers/event.py:775
msgid "Needed to delete the old booked slot event in calendar"
msgstr ""

#: intranet/serializers/global_settings.py:42
#, fuzzy
#| msgid "Billing cycle"
msgid "Invalid billing cycle"
msgstr "Faturação"

#: intranet/serializers/global_settings.py:48
msgid "Not enough time to set up the billing cycle"
msgstr ""

#: intranet/serializers/history/base_history.py:29
msgid ""
"* +: create\n"
"* -: delete\n"
"* ~: update"
msgstr ""

#: intranet/serializers/residence.py:75 intranet/serializers/residence.py:104
msgid "The residence already exists"
msgstr ""

#: intranet/serializers/residence_group.py:41
#: intranet/serializers/residence_group.py:64
msgid "The residence group already exists"
msgstr ""

#: intranet/serializers/reviews.py:57
msgid "Invalid email"
msgstr ""

#: intranet/serializers/reviews.py:63
msgid "Invalid URL"
msgstr ""

#: intranet/serializers/reviews.py:78
#, python-brace-format
msgid "The system ({system}) already exists in the selected city"
msgstr ""

#: intranet/serializers/rider.py:204
msgid "Pending accounts cannot be activated. Please, activate it first."
msgstr ""

#: intranet/serializers/rider.py:268
msgid "You are trying to share calendars with an invalid rider"
msgstr ""

#: intranet/serializers/rider.py:276
msgid "You are trying to share calendars with non allowed users"
msgstr ""

#: intranet/serializers/team.py:41
msgid "Choosing all services/riders is mandatory to set date and city as well"
msgstr ""

#: intranet/serializers/user.py:61 intranet/serializers/user.py:67
#: users/auth/serializers.py:163 users/auth/serializers.py:169
msgid "Invalid activation account URL"
msgstr ""

#: intranet/serializers/user.py:64 users/auth/serializers.py:166
msgid "Account is already active"
msgstr ""

#: intranet/serializers/warehouse_differences.py:66
msgid ""
"There's already another process to check discrepancies between warehouse and "
"us. Please, wait if it is running or choose another date. "
msgstr ""

#: intranet/serializers/warehouse_movement.py:145
msgid "Invalid contract"
msgstr ""

#: intranet/serializers/warehouse_movement.py:149
msgid "Invalid warehouse"
msgstr ""

#: intranet/views/calendar.py:105
msgid "Remote calendar could not be deleted"
msgstr ""

#: intranet/views/calendar.py:125
msgid "Calendar is already created"
msgstr ""

#: intranet/views/calendar.py:145
msgid "Error creating the calendar on remote source"
msgstr ""

#: intranet/views/calendar.py:149 intranet/views/calendar.py:181
#: intranet/views/calendar.py:226 intranet/views/calendar.py:250
#: intranet/views/reviews.py:165 intranet/views/rider.py:199
#: intranet/views/service.py:264 intranet/views/team.py:126
#: intranet/views/team.py:164 intranet/views/warehouse.py:290 users/views.py:93
msgid "OK"
msgstr ""

#: intranet/views/calendar.py:254
msgid "Visibility could not be changed"
msgstr ""

#: intranet/views/calendar_scheduler.py:45
#: intranet/views/calendar_scheduler.py:58
msgid "Error retrieving google calendars"
msgstr ""

#: intranet/views/calendar_scheduler.py:63
msgid "Error retrieving google calendars due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:83
#: intranet/views/calendar_scheduler.py:117
msgid "Error creating the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:121
msgid "Error creating event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:132
#: intranet/views/calendar_scheduler.py:153
msgid "Error updating the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:157
msgid "Error updating event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:168
#: intranet/views/calendar_scheduler.py:188
msgid "Error deleting the event"
msgstr ""

#: intranet/views/calendar_scheduler.py:192
msgid "Error deleting event in calendar due to rate limit"
msgstr ""

#: intranet/views/calendar_scheduler.py:238
#, python-brace-format
msgid "Error performing full sync from calendar api: {calendar_id}"
msgstr ""

#: intranet/views/calendar_scheduler.py:338
msgid "Error performing partial sync in calendar"
msgstr ""

#: intranet/views/calendar_scheduler.py:343
msgid "Error performing a partial sync in calendar due to rate limit"
msgstr ""

#: intranet/views/chargebee.py:45 users/auth/custom_jwt/authentication.py:60
#: users/auth/custom_jwt/tokens.py:202
msgid "User not found"
msgstr ""

#: intranet/views/chargebee.py:54
msgid "Error fetching chargebee subscription data"
msgstr ""

#: intranet/views/city.py:51
msgid ""
"The city has been already used, so it won't be deleted to prevent errors"
msgstr ""

#: intranet/views/contracts.py:149
msgid "Error fetching chargebee invoices. Please, try again later"
msgstr ""

#: intranet/views/events.py:246
msgid "Remote calendar event could not be deleted"
msgstr ""

#: intranet/views/events.py:272
msgid "Event is already created in remote calendar"
msgstr ""

#: intranet/views/events.py:278
msgid "Event doesn't have any service, task or blank_slot linked to it"
msgstr ""

#: intranet/views/events.py:350
msgid "Error creating the event on remote calendar"
msgstr ""

#: intranet/views/events.py:355 intranet/views/rider_configuration.py:86
#: intranet/views/rider_configuration.py:133
#: intranet/views/rider_configuration.py:199
#: intranet/views/rider_configuration.py:258
msgid "Concurrent operation. Try again later."
msgstr ""

#: intranet/views/events.py:360 intranet/views/rider_configuration.py:91
#: intranet/views/rider_configuration.py:138
#: intranet/views/rider_configuration.py:204
#: intranet/views/rider_configuration.py:263
msgid "Error processing the request. Try again later."
msgstr ""

#: intranet/views/events.py:364
msgid "Event created"
msgstr ""

#: intranet/views/hubspot.py:168
msgid "Error fetching hubspot data"
msgstr ""

#: intranet/views/residence_groups.py:53
msgid ""
"The residence group has residences associated with it, so it won't be "
"deleted to prevent errors"
msgstr ""

#: intranet/views/rider.py:115
msgid ""
"The rider cannot be deleted because it already has finished services or "
"tasks. Please, deactivate it instead"
msgstr ""

#: intranet/views/rider.py:196
msgid "Account already activated"
msgstr ""

#: intranet/views/rider.py:202
msgid "KO"
msgstr ""

#: intranet/views/rider_configuration.py:82
msgid "Error creating the API key"
msgstr ""

#: intranet/views/rider_configuration.py:117
msgid "API Key cannot be revoked because does not exist"
msgstr ""

#: intranet/views/rider_configuration.py:129
msgid "Error revoking the API key"
msgstr ""

#: intranet/views/rider_configuration.py:168 riders/admin.py:180
#: riders/receivers.py:43
#, python-brace-format
msgid "Service account for rider conf {rider}"
msgstr ""

#: intranet/views/rider_configuration.py:194
msgid "Error creating the service account"
msgstr ""

#: intranet/views/rider_configuration.py:224
msgid "Service Account cannot be deleted because does not exist"
msgstr ""

#: intranet/views/rider_configuration.py:253
msgid "Error deleting the service account"
msgstr ""

#: intranet/views/service.py:237
msgid "Finished services cannot be notified to riders"
msgstr ""

#: intranet/views/service.py:257
msgid "Error sending notification"
msgstr ""

#: intranet/views/service.py:267
msgid "Error notifying riders"
msgstr ""

#: intranet/views/team.py:147
#, python-brace-format
msgid "Error sending push notification to rider with error code {error}"
msgstr ""

#: intranet/views/team.py:155
msgid "Wrong token provided or missing configuration"
msgstr ""

#: intranet/views/warehouse.py:94
msgid ""
"The warehouse has been already used, so it won't be deleted to prevent errors"
msgstr ""

#: intranet/views/warehouse_differences.py:64
msgid "You cannot retry it because has not failed"
msgstr ""

#: intranet/views/warehouse_differences.py:72
msgid "Error creating the task. Try again later"
msgstr ""

#: moreapp/admin.py:19
msgid "Service Type"
msgstr ""

#: moreapp/admin.py:23
msgid "Send email"
msgstr ""

#: moreapp/admin.py:28
msgid "Warehouse notification email could not be sent. Check logs for details"
msgstr ""

#: moreapp/admin.py:34
msgid "Warehouse notification email sent successfully"
msgstr ""

#: moreapp/models.py:25
msgid "Moreapp"
msgstr ""

#: moreapp/models.py:26
msgid "Ridersapp"
msgstr ""

#: moreapp/models.py:37
msgid "If this file has been sent"
msgstr ""

#: moreapp/models.py:97
#, python-brace-format
msgid ""
"Customer notification {contract_id} BOX2BOX SL {base} - SERVICE {service} - "
"MOVEMENT {date}"
msgstr ""
"Notificação Cliente {contract_id} BOX2BOX SL {base} - SERVIÇO {service} - "
"MOVIMENTO {date}"

#: payments/admin.py:25 payments/admin.py:43 payments/admin.py:138
msgid "Total"
msgstr ""

#: payments/admin.py:62 payments/admin.py:82
#: webhooks/serializers/serializers.py:29
msgid "Chargebee id"
msgstr ""

#: payments/admin.py:86
msgid "Create in chargebee"
msgstr ""

#: payments/admin.py:91
#, python-brace-format
msgid "Quote {quote} created successfully"
msgstr ""

#: payments/admin.py:93
#, python-brace-format
msgid "Quote {quote} not created"
msgstr ""

#: payments/models/billing_data.py:26
msgid "User first name. This may not be the same as the user's name."
msgstr ""

#: payments/models/billing_data.py:32
msgid "User last name. This may not be the same as the user's last name."
msgstr ""

#: payments/models/billing_data.py:34
msgid "Company name"
msgstr ""

#: payments/models/billing_data.py:35
msgid "Customer's address"
msgstr ""

#: payments/models/coupon.py:14
msgid "ES"
msgstr ""

#: payments/models/coupon.py:15
msgid "FR"
msgstr ""

#: payments/models/coupon.py:16
msgid "PT"
msgstr ""

#: payments/models/coupon.py:17
msgid "MX"
msgstr ""

#: payments/models/coupon.py:22
msgid "Internal name for this coupon"
msgstr ""

#: payments/models/coupon.py:23
msgid "Chargebee id for this coupon"
msgstr ""

#: payments/models/coupon.py:28
msgid "Chargebee site where the coupon has been created"
msgstr ""

#: payments/models/invoice.py:18 payments/models/payments.py:204
msgid "Created"
msgstr ""

#: payments/models/invoice.py:19
msgid "Voided"
msgstr ""

#: payments/models/invoice.py:20
msgid "Processing"
msgstr ""

#: payments/models/invoice.py:24
msgid "Date when invoice has been emitted"
msgstr ""

#: payments/models/invoice.py:25
msgid "Remote reference number"
msgstr ""

#: payments/models/invoice.py:26
msgid "Chargebee remote id"
msgstr ""

#: payments/models/invoice.py:62
msgid "Indicates that is an invoice from deposit"
msgstr ""

#: payments/models/invoice.py:66
msgid "Related transaction to this payment"
msgstr ""

#: payments/models/invoice.py:68
msgid "Last date of applied fee due to chargebacks"
msgstr ""

#: payments/models/payment_method.py:39
msgid "Credit"
msgstr ""

#: payments/models/payment_method.py:40
msgid "Prepaid"
msgstr ""

#: payments/models/payment_method.py:41
msgid "Debit"
msgstr ""

#: payments/models/payment_method.py:56
msgid "American Express"
msgstr ""

#: payments/models/payment_method.py:57
msgid "Diners Club"
msgstr ""

#: payments/models/payment_method.py:58
msgid "Discover"
msgstr ""

#: payments/models/payment_method.py:59
msgid "Jcb"
msgstr ""

#: payments/models/payment_method.py:60
msgid "Mastercard"
msgstr ""

#: payments/models/payment_method.py:61
msgid "Unionpay"
msgstr ""

#: payments/models/payment_method.py:62
msgid "Visa"
msgstr ""

#: payments/models/payment_method.py:63
msgid "Other"
msgstr ""

#: payments/models/payments.py:46 payments/models/payments.py:202
msgid "No created"
msgstr ""

#: payments/models/payments.py:47
msgid "Active"
msgstr ""

#: payments/models/payments.py:49
msgid "Canceled"
msgstr ""

#: payments/models/payments.py:59
msgid "Yearly"
msgstr ""

#: payments/models/payments.py:66
msgid "Not running"
msgstr ""

#: payments/models/payments.py:71 payments/models/payments.py:227
msgid "Remote id in external service"
msgstr ""

#: payments/models/payments.py:87
msgid "Current billing cycle to trigger the free month. Every 6 months"
msgstr ""

#: payments/models/payments.py:91
msgid "If this subscription has a free month allowed"
msgstr ""

#: payments/models/payments.py:94
msgid "If this subscription has to be cancel in chargebee"
msgstr ""

#: payments/models/payments.py:99
msgid "Indicates if subscription has been imported from legacy system (Odoo)"
msgstr ""

#: payments/models/payments.py:104
msgid ""
"Indicates that subscription is in free month. If this is set true would "
"means the next month is free and recurring interval is 5"
msgstr ""

#: payments/models/payments.py:240
msgid ""
"Internal id from moreapp to avoid create duplicated quotes for the same form"
msgstr ""

#: payments/serializers/billing_data.py:34
#: payments/serializers/billing_data.py:66
#: payments/serializers/payment_method.py:111
#: payments/serializers/payment_method.py:160 payments/views.py:299
msgid "User is not created in payment processor platform. Try again later"
msgstr ""

#: payments/serializers/credit_note.py:25
msgid ""
"If is not provided, will return credit notes related to the user who made "
"the request. In detailed views, this flag is used to filter the whole "
"queryset from database and is used as base to get the concrete object."
msgstr ""

#: payments/serializers/invoice.py:25
msgid ""
"If is not provided, will return invoices related to the user who made the "
"request. In detailed views, this flag is used to filter the whole queryset "
"from database and is used as base to get the concrete object."
msgstr ""

#: payments/serializers/payment_method.py:116
msgid "User already has a payment method. You should update it instead"
msgstr ""

#: payments/views.py:83 payments/views.py:174 payments/views.py:188
#: payments/views.py:196 payments/views.py:210
msgid "Error retrieving invoice. Try again later."
msgstr ""

#: payments/views.py:97 payments/views.py:106 payments/views.py:120
msgid "Error retrieving credit note. Try again later."
msgstr ""

#: payments/views.py:256 payments/views.py:276
msgid "Error updating billing data . Please try again, try again later"
msgstr ""

#: payments/views.py:291
msgid "Error creating the payment intent"
msgstr ""

#: payments/views.py:311
msgid "Error updating the payment method. Try again later"
msgstr ""

#: payments/views.py:361
msgid "Error creating payment method. Please try again, try again later"
msgstr ""

#: payments/views.py:384
msgid "Error updating payment method. Please try again later"
msgstr ""

#: riders/admin.py:92
msgid "Generate API Key"
msgstr ""

#: riders/admin.py:97
msgid "An existing api key is present. Please, revoke it first"
msgstr ""

#: riders/admin.py:110
#, python-brace-format
msgid "Unknown error generating the api key: {msg}"
msgstr ""

#: riders/admin.py:115
msgid "API Key generated"
msgstr ""

#: riders/admin.py:117
msgid "Revoke API Key"
msgstr ""

#: riders/admin.py:121
msgid "Missing Google API key. Create it first."
msgstr ""

#: riders/admin.py:133
#, python-brace-format
msgid "Unknown error revoking the api key: {msg}"
msgstr ""

#: riders/admin.py:138
msgid "API Key revoked"
msgstr ""

#: riders/admin.py:140
msgid "Renew API Key"
msgstr ""

#: riders/admin.py:158
#, python-brace-format
msgid "Unknown error renewing the api key: {msg}"
msgstr ""

#: riders/admin.py:163
msgid "API Key renewed (created + revoked)"
msgstr ""

#: riders/admin.py:165
msgid "Create SA"
msgstr ""

#: riders/admin.py:171
msgid "An existing Service account is present. Please, remove it first"
msgstr ""

#: riders/admin.py:202 riders/admin.py:298
#, python-brace-format
msgid "Unknown error generating the service account: {msg}"
msgstr ""

#: riders/admin.py:209
msgid "Service account created"
msgstr ""

#: riders/admin.py:211
msgid "Delete SA"
msgstr ""

#: riders/admin.py:216
msgid "Missing service account. Please, create it first"
msgstr ""

#: riders/admin.py:240
#, python-brace-format
msgid "Unknown error deleting the service account: {msg}"
msgstr ""

#: riders/admin.py:247
msgid "Service account deleted"
msgstr ""

#: riders/admin.py:249
msgid "Disable SA"
msgstr ""

#: riders/admin.py:254 riders/admin.py:283
msgid "Missing service account. Please create it first"
msgstr ""

#: riders/admin.py:259
msgid "Cannot disabled non enabled service accounts"
msgstr ""

#: riders/admin.py:269
#, python-brace-format
msgid "Unknown error disabling the service account: {msg}"
msgstr ""

#: riders/admin.py:276
msgid "Service account disabled"
msgstr ""

#: riders/admin.py:278
msgid "Enable SA"
msgstr ""

#: riders/admin.py:288
msgid "Cannot enable non disabled service accounts"
msgstr ""

#: riders/admin.py:305
msgid "Service account generated"
msgstr ""

#: riders/admin.py:375
msgid "Preferred country"
msgstr ""

#: riders/admin.py:395 users/admin.py:127
msgid "Email"
msgstr ""

#: riders/admin_filters/riders.py:7
msgid "Linked"
msgstr ""

#: riders/admin_filters/riders.py:22
msgid "Task"
msgstr ""

#: riders/auth/forms.py:34 users/auth/forms.py:40
#, python-brace-format
msgid "{prefix}Recovery your password"
msgstr "{prefix}Recupere a sua senha"

#: riders/auth/serializers.py:42
msgid "OTP token not found"
msgstr ""

#: riders/auth/serializers.py:65
msgid "Invalid OTP token"
msgstr ""

#: riders/filters/warehouse.py:11
msgid "Bring warehouses where tracking options are set"
msgstr ""

#: riders/filters/warehouse.py:17
msgid "Bring warehouses that belong to a given city"
msgstr ""

#: riders/models/riders.py:42
msgid "Not sent"
msgstr ""

#: riders/models/riders.py:48
msgid "Set"
msgstr ""

#: riders/models/riders.py:54
msgid "Last time that notification was sent"
msgstr ""

#: riders/models/riders.py:56
msgid "Time where service was accepted by the rider"
msgstr ""

#: riders/models/riders.py:59
msgid "Flag to hide a service from pending services in "
msgstr ""

#: riders/models/riders.py:65
msgid ""
"Raw response from app, used to allow different responses for tasks or "
"whatever related FK"
msgstr ""

#: riders/models/riders.py:72
msgid ""
"Flag to know if this rider has submitted the inventory location on a given "
"warehouse"
msgstr ""

#: riders/models/riders.py:86
msgid "Team has no rider which it is mandatory to send push notifications"
msgstr ""

#: riders/models/riders.py:89
msgid "Service linked is mandatory to send push notifications"
msgstr ""

#: riders/models/riders.py:108
msgid "Not created"
msgstr ""

#: riders/models/riders.py:109
msgid "Enabled"
msgstr ""

#: riders/models/riders.py:110
msgid "Disabled"
msgstr ""

#: riders/models/riders.py:133
msgid "Manual flag to set when service account has the needed roles granted"
msgstr ""

#: riders/models/riders.py:230
#, python-brace-format
msgid "{prefix} Activate your account to get access in the Riders app"
msgstr "{prefix} Ativar conta en Box2box"

#: riders/serializers/riders.py:51
msgid "Warehouses which belong to this city"
msgstr ""

#: riders/serializers/riders.py:52
#, fuzzy
#| msgid "Warehouse location"
msgid "Warehouses with tracking options"
msgstr "Localização no armazém"

#: riders/views.py:32 riders/views.py:50
msgid "Deprecated once all riders use the v2.1.0 version of the app"
msgstr ""

#: templates/admin/searchable_dropdown_filter.html:31
#, python-format
msgid " By %(filter_title)s "
msgstr ""

#: templates/otp/login.html:38
msgid "Please correct the error below."
msgstr ""

#: templates/otp/login.html:38
msgid "Please correct the errors below."
msgstr ""

#: templates/otp/login.html:54
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: templates/otp/login.html:73
msgid "OTP Token:"
msgstr ""

#: templates/otp/login.html:78
msgid "Forgotten your password or username?"
msgstr ""

#: templates/otp/login.html:82
msgid "Log in"
msgstr ""

#: users/admin.py:26
msgid "Contract"
msgstr ""

#: users/admin.py:78
msgid "Customer area"
msgstr ""

#: users/admin.py:85
msgid "Hubspot"
msgstr ""

#: users/admin.py:86
msgid "Chargebee"
msgstr ""

#: users/admin.py:87
msgid "Moloni"
msgstr ""

#: users/admin.py:88
msgid "Personal area"
msgstr ""

#: users/admin.py:89
msgid "Inheritance"
msgstr ""

#: users/admin.py:134
msgid "Schedule activation account email for this user"
msgstr ""

#: users/admin.py:139
#, python-brace-format
msgid "Activation email scheduled to {user} successfully"
msgstr ""

#: users/admin.py:144
#, python-brace-format
msgid "Activation email could not be scheduled for the {user}"
msgstr ""

#: users/admin.py:163
msgid "Send activation account email"
msgstr ""

#: users/admin.py:168
#, python-brace-format
msgid "Activation email could not be sent to {user}"
msgstr ""

#: users/admin.py:174
#, python-brace-format
msgid "Activation email sent to {user} successfully"
msgstr ""

#: users/auth/custom_jwt/authentication.py:43
msgid "Given token not valid for any token type"
msgstr ""

#: users/auth/custom_jwt/authentication.py:55
msgid "Token contained no recognizable user identification"
msgstr ""

#: users/auth/custom_jwt/authentication.py:63
msgid "User is inactive"
msgstr ""

#: users/auth/custom_jwt/tokens.py:69
msgid "Invalid algorithm specified"
msgstr ""

#: users/auth/custom_jwt/tokens.py:71 users/auth/custom_jwt/tokens.py:97
msgid "Token is invalid or expired"
msgstr ""

#: users/auth/custom_jwt/tokens.py:82
msgid "Cannot create token with no type or lifetime"
msgstr ""

#: users/auth/serializers.py:74
msgid "Must include email and password"
msgstr ""

#: users/auth/serializers.py:91 users/auth/serializers.py:107
#: users/auth/serializers.py:117
msgid "Unable to log in with provided credentials."
msgstr ""

#: users/auth/serializers.py:102
msgid "User is not allowed to access to this site."
msgstr ""

#: users/auth/views.py:69
msgid "Error generating token for requested user"
msgstr ""

#: users/models.py:28
msgid "Date when activation account email was sent"
msgstr ""

#: users/models.py:39 users/models.py:200
#, python-brace-format
msgid "{prefix} Activate your account in Box2box"
msgstr "{prefix} Ativar conta en Box2box"

#: users/models.py:56
msgid "username"
msgstr ""

#: users/models.py:59
msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

#: users/models.py:63
msgid "A user with that username already exists."
msgstr ""

#: users/models.py:73
msgid "Used to enforce to user to change password and fill missing data"
msgstr ""

#: users/models.py:78
msgid "User identifier in external CRM"
msgstr ""

#: users/models.py:80
msgid "Chargebee identifier"
msgstr ""

#: users/models.py:82
msgid "active"
msgstr ""

#: users/models.py:85
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

#: users/models.py:88
msgid "Source domain from which user has signed in"
msgstr ""

#: users/models.py:91
msgid "Customer identifier in moloni software billing"
msgstr ""

#: users/models.py:99
msgid "Handle third-party software duplicity on emails"
msgstr ""

#: users/models.py:102
msgid "Customer preferred language"
msgstr ""

#: users/models.py:106
msgid "First name set by the customer"
msgstr ""

#: users/models.py:109
msgid "Last name set by the customer"
msgstr ""

#: users/models.py:118
msgid "date leave"
msgstr ""

#: users/serializers/user_serializers.py:205
msgid "Wrong password"
msgstr ""

#: users/serializers/user_serializers.py:210
msgid "An user with that email already exists."
msgstr ""

#: users/views.py:64
msgid "Email changed"
msgstr ""

#: webhooks/callbacks/hubspot_public_forms.py:199
msgid "Thank you for your request! We will contact you as soon as possible. 😀"
msgstr "Obrigado pela sua solicitação! Contactá-lo-emos em breve. 😀"

#: webhooks/callbacks/hubspot_public_forms.py:233
msgid "Congrats! You have a new collaborator"
msgstr ""

#: webhooks/callbacks/microservices.py:150
msgid "Your payment was successful!"
msgstr "Pagamento de sinal recebida"

#: webhooks/callbacks/mixins/transaction_handlers.py:159
#, python-brace-format
msgid ""
"[CHARGEBACK] Fee on the transaction {transaction} and invoice {invoice} due "
"to chargeback"
msgstr ""

#: webhooks/callbacks/mixins/transaction_handlers.py:266
#, python-brace-format
msgid ""
"[CHARGEBACK] Fee on the invoice {invoice} due to chargeback coming from card"
msgstr ""

#: webhooks/callbacks/ridersapp_ext/base.py:15
msgid "Quote is linked to a service with 'Especial' billing cycle"
msgstr ""

#: webhooks/callbacks/ridersapp_ext/base.py:18
msgid "Quote is linked to a service with 'Promo pack' billing cycle"
msgstr ""

#: webhooks/serializers/serializers.py:30
msgid "Internal id"
msgstr ""

#: webhooks/views.py:361
#, python-brace-format
msgid "Congrats! A B2B lead wants a quote - {deal_id}"
msgstr ""

#: webhooks/views.py:416
#, python-brace-format
msgid "New form submission from deal {deal_id}"
msgstr "Novo formulário de {deal_id}"

#: webhooks/views.py:502
#, python-brace-format
msgid "Budget request from {deal_id}"
msgstr "Pedido de orçamento de {deal_id}"

#: webhooks/views.py:524
#, python-brace-format
msgid "Booking data updated for deal {deal_id}"
msgstr "Dados de reserva actualizados para {deal_id}"
