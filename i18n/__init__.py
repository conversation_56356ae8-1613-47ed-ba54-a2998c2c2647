"""Class inspired in flask-babel 'Babel' implementation.

==================================
 Instructions to add translations
==================================

# Extract base messages
>> pybabel extract . -o locale/base.po

# Initialize languages (one per language)
>> pybabel init -l es_ES -i locale/base.po -d locale/

# Update locales using base.po
>> pybabel update -i locale/base.po -d locale/

# Compile languages once .po file has been filled with translations
>> pybabel compile -d locale/
"""

from babel import Locale, support

# Cached variable to avoid load in every request the same i18n language
cached_processor = {}
DEFAULT_LANGUAGE_LIST = ["es_ES", "fr_FR", "pt_PT", "it_IT", "en_US"]


class Babel:
    def __init__(self, request, i18_dir: str = "locale/"):
        self._i18_directory = i18_dir
        self._request = request
        self._language_list = DEFAULT_LANGUAGE_LIST

    @property
    def infer_language(self):
        return self._request.accept_languages.best_match(self._language_list) or "en_US"

    def get_translations(self):
        # Parse languages
        locale = Locale.parse(self.infer_language)

        # Init translator preprocessor
        translations = support.Translations()

        # Load the catalog
        catalog = support.Translations.load(self._i18_directory, locale, "messages")
        translations.merge(catalog)
        # FIXME: Workaround for merge() being really, really stupid. It
        # does not copy _info, plural(), or any other instance variables
        # populated by GNUTranslations. We probably want to stop using
        # `support.Translations.merge` entirely.
        if hasattr(catalog, "plural"):
            translations.plural = catalog.plural

        return translations

    def gettext(self, string):
        """Translate the message in a given locale"""

        translation_processor = self.get_translations()
        return translation_processor.ugettext(string)


def gettext(message, request):
    """Helper method to manage i18n for serverless backends"""
    babel = Babel(request)

    try:
        processor = cached_processor[babel.infer_language]
    except KeyError:
        cached_processor[babel.infer_language] = babel.get_translations()
        processor = cached_processor[babel.infer_language]

    return processor.gettext(message)
