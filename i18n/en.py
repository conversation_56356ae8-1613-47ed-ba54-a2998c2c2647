import datetime

translations = {
    "common": {
        "sys_year": datetime.datetime.now().year,
        "sys_home": "https://box2boxstorage.com/es-en/",
        "sys_href_legal": "https://box2boxstorage.com/es-en/legal",
        "sys_href_policy": "https://box2boxstorage.com/es-en/legal/privacy",
        "sys_href_faq": "https://box2boxstorage.com/es-en/faq",
        "sys_email": "<EMAIL>",
        "sys_thanks": "Thanks",
        "sys_team": "box2box Team",
        "sys_phone": "+***********",
        "sys_display_phone": "***********",
    },
    "account_activation_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_account_activation": "Activate your account in few seconds!",
        "sys_greeting": "Hi",
        "sys_welcome_pre": "Welcome to ",
        "sys_welcome_pre2": " personal area.",
        "sys_welcome_suffix": "As a customer, you have access to many features to allow maximum flexibility to manage your objects.",
        "sys_welcome_suffix2": "In just one click, you will be able to perform any kind of management, such as ",
        "sys_welcome_suffix21": "requesting an additional pickup ",
        "sys_welcome_suffix22": "to add items to your warehouse, see",
        "sys_welcome_suffix23": "your digital inventory",
        "sys_welcome_suffix24": ", or ",
        "sys_welcome_suffix25": "requesting the delivery",
        "sys_welcome_suffix26": " of any of your items.",
        "sys_welcome_suffix3": "It also allows you to view your ",
        "sys_welcome_suffix31": "invoices, ",
        "sys_welcome_suffix32": "documents ",
        "sys_welcome_suffix33": "and all your personal contract information.",
        "sys_welcome_suffix4": "To activate your account, click the button below and follow the steps.",
        "sys_cta": "Activate account",
        "sys_end_message": "We are waiting for you!",
        "sys_help": "If you have problems with the button above, copy and paste the following link into your web browser:",
    },
    "pick_up_order_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_pickup_order": "Application summary",
        "sys_contract": "Contract: ",
        "sys_date": "Date: ",
        "sys_address": "Address: ",
        "sys_comments": "Comments: ",
        "sys_items": "Items",
        "sys_id": "Units",
        "sys_description": "Description",
        "sys_floor": "Floor",
        "sys_access": "Access",
        "sys_box_packaging": "Box packaging",
        "sys_furniture_packaging": "Furniture packaging",
        "sys_furniture_disassemble": "Furniture disassembly",
        "sys_cta": "Go intranet",
    },
    "delivery_order_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_delivery_order": "Application summary",
        "sys_contract": "Contract: ",
        "sys_date": "Date: ",
        "sys_address": "Address: ",
        "sys_comments": "Comments: ",
        "sys_items": "Items",
        "sys_id": "Tag",
        "sys_description": "Description",
        "sys_floor": "Floor",
        "sys_access": "Access",
        "sys_furniture_assembly": "Furniture assembly",
        "sys_cta": "Go intranet",
    },
    "signed_contract_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_signed_contract": "Documentation related with the service",
        "sys_greeting": "Hi",
        "sys_body": "Attached to this email you have the service contract signed with the box2box team.",
    },
    "signed_contract_email_with_review": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_signed_contract": "Documentation related with the service",
        "sys_greeting": "Hi",
        "sys_body": "Attached to this email you have the service contract signed with the box2box team.",
        "sys_cta": "Write a review",
    },
    "password_reset_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_password_reset": "Reset the password",
        "sys_greeting": "Hi",
        "sys_pre": "You have requested to reset your password for your account.",
        "sys_post": "Use the button below to create a new password.",
        "sys_cta": "Recover your password",
        "sys_help": "If you have problems with the button above, copy and paste the following link into your web browser:",
    },
    "warehouse_inventory": {
        "sys_body": "Notification of movement in customer inventory.",
        "sys_additional_info": "Please find attached information.",
        "sys_greeting": "Greetings",
        "sys_company": "BOX2BOX SL",
        "sys_automatic_message": "This message has been generated automatically, please do not reply. If you need further information <NAME_EMAIL> or call ***********.",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_country": "Madrid, Spain",
    },
    "generic_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
    },
    "warehouse_excel_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_greeting": "Hi",
        "sys_body": "We attached the file for the ",
        "sys_pickup_date": "Pickup date: ",
    },
    "warehouse_excel_cancel_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_greeting": "Hola",
        "sys_message": "Finally, the delivery request is cancelled until further notice. We will let you know when it will be rescheduled.",
        "sys_apologies": "Sorry for the inconvenience.",
    },
    "welcome_email": {
        "sys_welcome_title": "We will contact you shortly!",
        "sys_feature_1": "Our service makes your life easier",
        "sys_body_1": "box2box is a storage service with home pick up and delivery.  With our system you have nothing to worry about, we take care of collecting, storing and delivering your things when and where you need them. We take care of all the hard work: packing objects in boxes, disassembly and protection of your furniture if you need it.",
        "sys_feature_2": "Your objects in the palm of your hand",
        "sys_body_2": "Through our digital inventory you can keep track of your belongings.  If you need to retrieve an item, you don't have to go to the storage room, in just one click we deliver what you need for FREE.",
        "sys_feature_3": "Our facilities protect your things",
        "sys_body_3": "Our facilities are equipped with a system of cameras, alarms and surveillance 24 hours a day. We also have anti-humidity measures for the correct conservation of all your belongings. For peace of mind, we have transport and storage insurance whose policy is up to 3.000€.",
        "sys_opinion_title": "Our customers say ...",
        "sys_opinion_subtitle_span": " ",
        "sys_opinion_subtitle_suffix": " ",
        "sys_customer_1_name": "Sophia Smith",
        "sys_customer_1_body": "Very good professionals: attentive, organized, efficient and very pleasant. I recommend 100% box2box!",
        "sys_customer_2_name": "Mason Brown",
        "sys_customer_2_body": "Excellent team, the service is fantastic and the prices are very economical, especially considering the quality.",
        "sys_final": "<span style='font-weight: 600'>box2box</span> team",
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
    "payment_success_email": {
        "sys_section_one": "Pick-up information:",
        "sys_address": "Address:",
        "sys_date": "Date:",
        "sys_time_slot": "Time slot:",
        "sys_section_two": "How to get prepared",
        "sys_section_two_body": "Make sure you have separated you personal belongings from those object designated for storage. Moreover, keep the objects that will be taken to storage at the entrance, easy to reach.",
        "sys_section_two_tips": "Please consider the following instructions:",
        "sys_section_two_1": "Boxes",
        "sys_section_two_1_list_1": "Always choose for strong and solid boxes.",
        "sys_section_two_1_list_2": "Respect the maximum weight it can hold.",
        "sys_section_two_1_list_3": "Close the boxes carefully with tape.",
        "sys_section_two_1_list_4": "Number or identify the boxes with a unique and representative description",
        "sys_section_two_1_list_5": "Instead of using plastic bags, always choose for boxes.",
        "sys_section_two_2": "Furniture",
        "sys_section_two_2_list_1": "Dismantle all furniture prior to the arrival of the removal team. (Unless requested as an additional service)",
        "sys_section_two_2_list_2": "All furniture has to be empty. In order to carry out a safe and correct transportation, furniture has to bee free of content ",
        "sys_section_two_2_list_3": "If there are any crystal pieces, please wrap them individually.",
        "sys_section_two_3": "Home appliances",
        "sys_section_two_3_list_1": "Disconnect all home appliances before the arrival of the team.",
        "sys_section_two_3_list_2": "For refrigerators and freezers, turn off the power at least 24 hours prior to service.",
        "sys_section_two_3_list_3": "Remove everything from home appliances for hygienic reasons.",
        "sys_payment_service_title": "SERVICE PAYMENT",
        "sys_payment_service_body": "On the day of pick-up, once the service is completed, we will proceed to charge automatically the first invoice (first monthly payment + pick-up + additional services in case you have selected them). The receipt will be emitted to the same card with which you have made the payment of the reservation.",
        "sys_warn": "Warning!",
        "sys_warn_prefix": "Pick-up schedule is from",
        "sys_warn_suffix": "The team will arrive in that time slot. The customer must be available at home, otherwise a 20€ penalty will be applied after the first 20 minutes of waiting. After 50 minutes, the penalty will be 50 €. In case the team is waiting and the customer does not answer the phone, a charge of 50€ will be made and the pick-up will be cancelled immediately.",
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
    "rider_account_activation_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_greeting": "Hi",
        "sys_message": "To activate your driver account and have access to the Box2box rider app, click on the button below and follow the steps. Once activated, you will find a link to the Google and Apple stores to download the app if you have not already done so.",
        "sys_cta": "Activate access",
        "sys_help": "If you have problems with the button above, copy and paste the following link into your web browser:",
    },
    "payment_reminder_email": {
        "sys_greeting": "Hi",
        "sys_message": "We remind you that your collection date is coming soon.",
        "sys_section_one": "Pick-up information",
        "sys_address": "Address: ",
        "sys_date": "Date: ",
        "sys_time_slot": "Time slot:",
        "sys_section_two": "Please note before pickup",
        "sys_section_two_1_list_1": "Organize all small items in boxes and disassemble the furniture. If you wish, we can help you with packing or disassembly, for which you must request it in advance by calling the phone number below in this email.",
        "sys_section_two_1_list_2": "Our team takes care of the rest! We pack the most fragile items in bubble wrap and move everything from your home's door to our facilities.",
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
    "password_reset_otp_email": {
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_password_reset": "Reset the password",
        "sys_greeting": "Hi",
        "sys_pre": "You have requested to reset your password for your account. Insert the one-time code below to complete the password change:",
    },
    "welcome_moving_email": {
        "sys_welcome_title": "Thank you for requesting your moving quote!",
        "sys_subtitle": "Soon, one of our experts will contact you to provide a personalized solution tailored to your needs.",
        "sys_feature_1": "Our moving service is unique",
        "sys_body_1": "At Box2box, we specialize in making your move a simple and stress-free process. Our professional team takes care of:",
        "sys_body_1_list_1": "Disassembling and protecting your furniture.",
        "sys_body_1_list_2": "Carefully packing your belongings.",
        "sys_body_1_list_3": "Transporting them to your new home.",
        "sys_feature_2": "Maximum peace of mind at every step",
        "sys_body_2": "We ensure your belongings arrive in perfect condition thanks to our expert staff, equipped vehicles, and transport insurance that covers your items.",
        "sys_feature_3": "Why choose us?",
        "sys_body_3": "More than <span style='font-weight: 600'>12000 clients</span> trust Box2Box for our commitment to quality, punctuality, and attention to detail.",
        "sys_final": "You will soon receive a call from our team to finalize the details of your move. If you have any questions in the meantime, don't hesitate to write or call us.",
        "sys_thanks": "Thank you for trusting Box2Box!",
        "sys_opinion_title": "What our customers say ...",
        "sys_opinion_subtitle_span": " ",
        "sys_opinion_subtitle_suffix": " ",
        "sys_customer_1_name": "Sophia Smith",
        "sys_customer_1_body": "Very good professionals: attentive, organized, efficient and very pleasant. I recommend 100% box2box!",
        "sys_customer_2_name": "Mason Brown",
        "sys_customer_2_body": "Excellent team, the service is fantastic and the prices are very economical, especially considering the quality.",
        "sys_legal": "Legal notice",
        "sys_policy": "Privacy policy",
        "sys_faq": "Frequently asked questions",
        "sys_cus_service": "Customer service hours",
        "sys_week_service": "Monday to Friday, 9am to 8pm",
        "sys_weekend_service": "Saturdays, 10am to 5pm",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
}
