import datetime

translations = {
    "common": {
        "sys_year": datetime.datetime.now().year,
        "sys_home": "https://box2boxstorage.com/pt/",
        "sys_href_legal": "https://box2boxstorage.com/pt/legal",
        "sys_href_policy": "https://box2boxstorage.com/pt/legal/privacy",
        "sys_href_faq": "https://box2boxstorage.com/pt/faq",
        "sys_email": "<EMAIL>",
        "sys_thanks": "<PERSON><PERSON><PERSON>",
        "sys_team": "Equipa box2box",
        "sys_phone": "+351*********",
        "sys_display_phone": "21 020 33 66",
    },
    "account_activation_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_account_activation": "Ativa a tua conta em poucos segundo!",
        "sys_greeting": "Olá",
        "sys_welcome_pre": "Bem-vindo a sua área de cliente de ",
        "sys_welcome_pre2": ".",
        "sys_welcome_suffix": "Como cliente, tem acesso a muitas funcionalidades que permitem a máxima flexibilidade na gestão dos seus objetos.",
        "sys_welcome_suffix2": "Num só click podes realizar qualquer gestão, como pedir uma ",
        "sys_welcome_suffix21": "recolha adicional ",
        "sys_welcome_suffix22": "para incluir mais coisas ao teu espaço, ver o ",
        "sys_welcome_suffix23": "teu inventário digital",
        "sys_welcome_suffix24": ", ou ",
        "sys_welcome_suffix25": "pedir uma entrega",
        "sys_welcome_suffix26": " de qualquer dos teus objetos.",
        "sys_welcome_suffix3": "Também permite ver as tuas ",
        "sys_welcome_suffix31": "faturas, ",
        "sys_welcome_suffix32": "documentos ",
        "sys_welcome_suffix33": "e toda a informação pessoal do teu contrato.",
        "sys_welcome_suffix4": "Para ativar a sua conta, carregue no botão inferior e siga os passos.",
        "sys_cta": "Ativar conta",
        "sys_end_message": "Estamos á tua espera!",
        "sys_help": "Se tens problemas com o botão, faz copy-paste do link no navegador:",
    },
    "pick_up_order_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_pickup_order": "Application summary",
        "sys_contract": "Contrato: ",
        "sys_date": "Data: ",
        "sys_address": "Endereço: ",
        "sys_comments": "Comentários: ",
        "sys_items": "Produtos",
        "sys_id": "Unidades",
        "sys_description": "Descrição",
        "sys_floor": "Andar",
        "sys_access": "Aceso",
        "sys_box_packaging": "Embalagem da caixas",
        "sys_furniture_packaging": "Embalagem de móveis",
        "sys_furniture_disassemble": "Desmontagem de móveis",
        "sys_cta": "Intranet",
    },
    "delivery_order_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_delivery_order": "Application summary",
        "sys_contract": "Contrato: ",
        "sys_date": "Data: ",
        "sys_address": "Endereço: ",
        "sys_comments": "Comentários: ",
        "sys_items": "Produtos",
        "sys_id": "Etiqueta",
        "sys_description": "Descrição",
        "sys_floor": "Andar",
        "sys_access": "Aceso",
        "sys_furniture_assembly": "Montagem de móveis",
        "sys_cta": "Intranet",
    },
    "signed_contract_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_signed_contract": "Documentação relacionada com o serviço",
        "sys_greeting": "Olá",
        "sys_body": "Anexado a este e-mail você tem o contrato de serviço assinado com a equipe box2box.",
    },
    "signed_contract_email_with_review": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_signed_contract": "Documentação relacionada com o serviço",
        "sys_greeting": "Olá",
        "sys_body": "Anexado a este e-mail você tem o contrato de serviço assinado com a equipe box2box.",
        "sys_cta": "Deixa a tua opinião",
    },
    "password_reset_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_password_reset": "Redefinir a senha",
        "sys_greeting": "Olá",
        "sys_pre": "Você pediu para redefinir sua senha para sua conta.",
        "sys_post": "Use o botão abaixo para criar uma nova senha.",
        "sys_cta": "Recuperar a sua senha.",
        "sys_help": "Se tens problemas com o botão, faz copy-paste do link no navegador:",
    },
    "inventory_email": {
        "sys_body": "Notificação de movimento em inventario de cliente.",
        "sys_additional_info": "Em anexo segue a informação.",
        "sys_greeting": "Cumprimentos,",
        "sys_company": "BOX2BOX PORTUGAL",
        "sys_automatic_message": " Esta mensagem foi gerada automaticamente, por favot não responda. Para mais informação entre em contacto atravé<NAME_EMAIL> o *********.",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_country": "Portugal",
    },
    "generic_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
    },
    "warehouse_excel_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_greeting": "Ola",
        "sys_body": "Enviamos o ficheiro de ",
        "sys_pickup_date": "Data de recolha: ",
    },
    "warehouse_excel_cancel_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_greeting": "Olá",
        "sys_message": "Anulamos a entrega solicitada. Assim que tivermos nova data voltamos a programar.",
        "sys_apologies": "Lamentamos o inconeniente.",
    },
    "welcome_email": {
        "sys_welcome_title": "Entraremos em contacto consigo em breve!",
        "sys_feature_1": "O nosso serviço torna a sua vida mais fácil",
        "sys_body_1": "box2box é um serviço de armazenamento com recolha e entrega ao domicílio.  Com o nosso sistema não tem de se preocupar com nada, nós tratamos de recolher, armazenar e entregar as suas coisas quando e onde precisar delas. Tratamos de todo o trabalho árduo: embalar objectos em caixas, desmontar e proteger o seu mobiliário, se precisar dele.",
        "sys_feature_2": "Os seus artigos na palma da sua mão",
        "sys_body_2": "Através do nosso inventário digital pode manter um registo dos seus pertences.  Se precisar de recuperar um item, não precisa de ir ao armazém, com apenas um clique entregamos o que precisa GRATUITAMENTE.",
        "sys_feature_3": "As nossas instalações protegem os seus pertences",
        "sys_body_3": "As nossas instalações estão equipadas com um sistema de câmaras, alarmes e vigilância 24 horas por dia. Temos também medidas anti-humidade para a correcta conservação de todos os seus pertences. Para maior paz de espírito, temos seguro de transporte e armazenamento com uma apólice de até 2.500 euros.",
        "sys_opinion_title": "O que dizem os nossos clientes ...",
        "sys_opinion_subtitle_span": " ",
        "sys_opinion_subtitle_suffix": " ",
        "sys_customer_1_name": "Carolina",
        "sys_customer_1_body": "Muito bons profissionais: atenciosos, organizados, eficientes e muito agradáveis. Recomendo 100% box2box!",
        "sys_customer_2_name": "Cristiano",
        "sys_customer_2_body": "Excelente equipa, o serviço é fantástico e os preços são muito económicos, especialmente tendo em conta a qualidade.",
        "sys_final": "Equipa <span style='font-weight: 600'>box2box</span>",
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
    "payment_success_email": {
        "sys_section_one": "Dados de recolha",
        "sys_address": "Morada:",
        "sys_date": "Data:",
        "sys_time_slot": "Rango de horas:",
        "sys_section_two": "Preparação",
        "sys_section_two_body": "É importante separar os objetos que vão ás instalções da Box2box e os que não, assim como ter facil acesso aos mesmos, sem coisas pelo meio.",
        "sys_section_two_tips": "Damos uns conselhos:",
        "sys_section_two_1": "Caixas",
        "sys_section_two_1_list_1": "Optar por caixas fortes e resistentes",
        "sys_section_two_1_list_2": "Não encher demasiado as caixas",
        "sys_section_two_1_list_3": "Fechar cada caixa com fita-cola",
        "sys_section_two_1_list_4": "Enumerar/identificar cada caixa com uma descrição unica e representativa ",
        "sys_section_two_1_list_5": "Evitar o uso de sacos de plástico, já que não permitem uma correta armazenagem, escolher semptre caixas.",
        "sys_section_two_2": "Móveis",
        "sys_section_two_2_list_1": "Salvo não esteja solicitado como suplemento, desmontar móveis e guardar parafusos num saco",
        "sys_section_two_2_list_2": "Esvaziar os móveis. Por segurança não se podem transportar móveis com objetos dentro",
        "sys_section_two_2_list_3": "Caso haja vidros, devem ser retirados e guardados separadamente, indicar sempre que é um objetos frágil ",
        "sys_section_two_3": "Eletrodomésticos ",
        "sys_section_two_3_list_1": "Desconectar todos os electrodomésticos antes que a equipa chegue",
        "sys_section_two_3_list_2": "Caso haja frigorificos e congeladores, desconectar pelos menos 24 horas antes do serviço",
        "sys_section_two_3_list_3": "Por higiene, retirar tudo o que houver dentro dos electrodomésticos ",
        "sys_payment_service_title": "PAGAMENTO DO SERVIÇO",
        "sys_payment_service_body": "No dia da recolha, uma vez terminado o serviço, procederemos à emissão do pagamento automático da primeira fatura (primeira mensalidade + recolha + serviços adicionais se contratados). A fatura será emitida para o mesmo cartão que utilizou para pagar a reserva.",
        "sys_warn": "Aviso!",
        "sys_warn_prefix": "Hora prevista de recolha",
        "sys_warn_suffix": "A equipa chegará nesse rango de horas. O cliente deve estar disponivel no domicílio, caso contrário, será aplicada uma penalização de 20€ passados os primeiros 20 minutos. A partir de 50 minutos, a penalização é de 50€. Caso a equipa tente contactar e o cliente não responder, procedemos a emitir uma penalização de 50€ e cancelação imediata do serviço.",
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
    "rider_account_activation_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_greeting": "Olá",
        "sys_message": "Para ativar a sua conta de condutor e ter acesso à aplicação Box2box rider, clique no botão abaixo e siga os passos. Uma vez activada, encontrará uma ligação para as lojas Google e Apple para descarregar a aplicação, caso ainda não o tenha feito.",
        "sys_cta": "Ativar o acesso",
        "sys_help": "Se tens problemas com o botão, faz copy-paste do link no navegador:",
    },
    "payment_reminder_email": {
        "sys_greeting": "Olá",
        "sys_message": "Recordamos que a data de recolha está cada vez mais próxima.",
        "sys_section_one": "Dados de recolha",
        "sys_address": "Morada: ",
        "sys_date": "Data: ",
        "sys_time_slot": "Horário: ",
        "sys_section_two": "Importante antes da recolha",
        "sys_section_two_1_list_1": "Organize todos os objetos pequenos em caixas e desmonte os móveis. Se desejar, podemos ajudá-lo com a embalagem ou desmontagem, para isso, você deve solicitar previamente ligando para o telefone que aparece abaixo neste e-mail.",
        "sys_section_two_1_list_2": "Nossa equipa encarrega-se do resto! Embalamos os objectos mais delicados em plástico bolha e transportamos tudo desde a sua porta até às nossas instalações.",
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "Segunda a Sexta das 9:00 às 20:00",
        "sys_weekend_service": "Sábados das 10:00 às 17:00",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
    "password_reset_otp_email": {
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Horário de atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
        "sys_password_reset": "Redefinir a senha",
        "sys_greeting": "Olá",
        "sys_pre": "Você pediu para redefinir sua senha para sua conta. Insira o seguinte código de verificação abaixo:",
    },
    "welcome_moving_email": {
        "sys_welcome_title": "Obrigado por solicitar o seu orçamento de mudança!",
        "sys_subtitle": "Em breve, um dos nossos especialistas entrará em contato para lhe oferecer uma solução personalizada e adaptada às suas necessidades.",
        "sys_feature_1": "Nosso serviço de mudanças é único",
        "sys_body_1": "Na Box2box, somos especializados em tornar sua mudança um processo simples e sem estresse. Nossa equipe profissional cuida de:",
        "sys_body_1_list_1": "Desmontar e proteger seus móveis.",
        "sys_body_1_list_2": "Embalar cuidadosamente seus pertences.",
        "sys_body_1_list_3": "Transportá-los até a sua nova casa.",
        "sys_feature_2": "Máxima tranquilidade em cada etapa",
        "sys_body_2": "Garantimos que seus pertences cheguem em perfeito estado graças à nossa equipe especializada, veículos equipados e um seguro de transporte que cobre seus itens.",
        "sys_feature_3": "Por que nos escolher?",
        "sys_body_3": "Mais de <span style='font-weight: 600'>12000 clientes</span> confiam na Box2Box pelo nosso compromisso com qualidade, pontualidade e cuidado em cada detalhe.",
        "sys_final": "Em breve, você receberá uma ligação da nossa equipe para concluir os detalhes da sua mudança. Se tiver alguma dúvida enquanto isso, não hesite em nos escrever ou ligar.",
        "sys_thanks": "Obrigado por confiar na Box2Box!",
        "sys_opinion_title": "A opinião dos nossos clientes ...",
        "sys_opinion_subtitle_span": " ",
        "sys_opinion_subtitle_suffix": " ",
        "sys_customer_1_name": "Carolina",
        "sys_customer_1_body": "Muito bons profissionais: atenciosos, organizados, eficientes e muito agradáveis. Recomendo 100% box2box!",
        "sys_customer_2_name": "Cristiano",
        "sys_customer_2_body": "Excelente equipa, o serviço é fantástico e os preços são muito económicos, especialmente tendo em conta a qualidade.",
        "sys_legal": "Aviso legal",
        "sys_policy": "Política de privacidade",
        "sys_faq": "Perguntas frequentes",
        "sys_cus_service": "Atendimento ao cliente",
        "sys_week_service": "De segunda a sexta-feira, das 08:00 às 17:00h",
        "sys_weekend_service": "",
        "sys_contact_email": "E-mail: ",
        "sys_contact_phone": "Tel: ",
    },
}
