from datetime import timedelta
from unittest.mock import patch

import pytest
from django.contrib.auth import get_user_model
from django.core import mail
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from pytest import mark
from rest_framework import status
from rest_framework.test import APIClient

from organizations.models import Membership, Organization, UserInvitation

pytestmark = mark.django_db
User = get_user_model()


@pytest.fixture
def client():
    client = APIClient()
    yield client


@pytest.fixture
def register_user():
    def register_user(client, email):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response = client.post(path="/auth/signup/", data={"email": email, "password": "cliente12345"})
        return response.json()

    return register_user


@pytest.fixture
def register_organization():
    def register_organization(client, access_token, name):
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path="/organizations/", data={"name": name, "type_organization": "business"})

        return response.json()

    return register_organization


@pytest.fixture
def organization_visibility():
    def organization_visibility(visibility):
        return Organization.objects.create(
            name="namyTest",
            type_organization="business",
            avatar=None,
            date_created="2021-03-01T12:07:01.068883Z",
            alias=None,
            visibility=visibility,
        )

    return organization_visibility


class TestOrganizationsViewSet:

    # Organization
    def test_public_profile_ok(self, client, organization_visibility):
        organization = organization_visibility(visibility=True)
        organization_id = str(organization.id)
        fake_response = {
            "id": organization_id,
            "name": "namyTest",
            "type_organization": "business",
            "avatar": None,
            "date_created": "2021-03-01T13:07:01.068883+01:00",
            "alias": None,
            "visibility": True,
        }
        response = client.get(path=f"/organizations/{organization_id}/public-profile/")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == fake_response

    def test_public_profile_ko(self, client, organization_visibility):
        organization_id = str(organization_visibility(visibility=False).id)
        fake_response = {"non_field_errors": _("This organization does not have a visible profile")}
        response = client.get(path=f"/organizations/{organization_id}/public-profile/")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == fake_response

    # Membership
    def test_create_organization_with_membership(self, client, register_user):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        # Organization
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path="/organizations/", data={"name": "namyTest", "type_organization": "business"})
        organization_id = response.json()["id"]
        assert response.status_code == status.HTTP_201_CREATED
        # Membership
        membership = Membership.objects.filter(organization=organization_id).first()
        membership_id = membership.id
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response_membership = client.get(path=f"/memberships/{membership_id}/")

        fake_response = {
            "id": str(membership_id),
            "organization": organization_id,
            "role": "owner",
            "user": str(user["user"]["pk"]),
        }
        assert response_membership.status_code == status.HTTP_200_OK
        assert response_membership.json() == fake_response


class TestUserInvitationsViewSet:

    # UserInvitation
    def test_send_invitation_not_user_exist_ok(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={
                    "email": "<EMAIL>",
                    "organization": organization_id,
                    "role_name": "admin",
                },
            )
            user_invitation = response.json()
            fake_reponse = {
                "id": user_invitation["id"],
                "sent": user_invitation["sent"],
                "role": "admin",
                "invited_by": None,
                "email": "<EMAIL>",
                "accepted": False,
                "organization": organization_id,
            }
            assert response.status_code == status.HTTP_201_CREATED
            assert response.json() == fake_reponse
            assert mail.outbox[1].template == "new_invitation"

    def test_send_invitation_not_user_exist_ko(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            fake_response = {"non_field_errors": "An invitation with that email already exists for this organization."}
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == fake_response

    def test_send_invitation_user_exist_ok(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            user_invitation = response.json()
            fake_reponse = {
                "id": user_invitation["id"],
                "sent": user_invitation["sent"],
                "role": "admin",
                "invited_by": None,
                "email": "<EMAIL>",
                "accepted": False,
                "organization": organization_id,
            }
            assert response.status_code == status.HTTP_201_CREATED
            assert response.json() == fake_reponse
            assert mail.outbox[1].template == "new_invitation"

    def test_send_invitation_user_exist_ko(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            fake_response = {"non_field_errors": "An invitation with that email already exists for this organization."}
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == fake_response

    def test_resend_invitation_ok(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            user_invitation = response.json()
            UserInvitation.objects.update(sent=timezone.now() - timedelta(seconds=90))
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path=f"/userinvitations/{user_invitation['id']}/resend/?organization_id={organization_id}"
            )
            fake_response = {"status": _("Invitation resent")}
            assert response.status_code == status.HTTP_200_OK
            assert response.json() == fake_response

    def test_resend_invitation_ko(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])
        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            user_invitation = response.json()
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path=f"/userinvitations/{user_invitation['id']}/resend/?organization_id={organization_id}",
                data={},
            )
            fake_response = {
                "non_field_errors": _("Please wait for at least 1 minute before requesting another invite.")
            }
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == fake_response

    def test_resend_invitation_accepted_ko(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            user_invitation = response.json()
            UserInvitation.objects.update(sent=timezone.now() - timedelta(seconds=90), accepted=True)
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path=f"/userinvitations/{user_invitation['id']}/resend/?organization_id={organization_id}",
                data={},
            )
            fake_response = {"non_field_errors": _("It is not possible to resend an already accepted invitation.")}
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == fake_response

    def test_link_invitation(self, client, register_user, register_organization):
        user = register_user(client, "<EMAIL>")
        other_user = register_user(client, "<EMAIL>")
        access_token = user["access_token"]
        organization = register_organization(client, access_token, "Test Organization")
        organization_id = str(organization["id"])

        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                path="/userinvitations/",
                data={"email": "<EMAIL>", "organization": organization_id, "role_name": "admin"},
            )
            user_invitation = response.json()
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.patch(
                path=f"/userinvitations/{user_invitation['id']}/link/?organization_id={organization_id}"
            )

            fake_response = {"details": _("User linked.")}
            assert response.status_code == status.HTTP_200_OK
            assert response.json() == fake_response
