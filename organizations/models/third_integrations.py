import uuid

from allauth.socialaccount.models import SocialAccount
from allauth.socialaccount.providers.slack.provider import <PERSON><PERSON><PERSON><PERSON><PERSON>ider
from django.db import models
from django.db.models.signals import post_delete

from organizations.models import Organization
from organizations.receivers import delete_social_account

ALLOWED_THIRD_PARTY_INTEGRATIONS = {SlackProvider.id}


class ThirdPartyIntegration(models.Model):
    """Model to relate a given social account with organization for third-party apps"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    social_account = models.OneToOneField(SocialAccount, on_delete=models.CASCADE)
    date_added = models.DateTimeField(auto_now_add=True)

    @property
    def extra_data(self):
        return self.social_account.extra_data


post_delete.connect(delete_social_account, sender=ThirdPartyIntegration)
