from datetime import timedelta

from django.conf import settings
from django.db import transaction
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from core.serializers import EmptySerializer, TokenSerializer
from core.utils import (encoding_payload_with_hmac,
                        generate_gcloud_access_token_on_demand,
                        get_openapi_error_response, get_openapi_response)
from organizations.models import (Membership, Organization,
                                  ThirdPartyIntegration, UserInvitation)
from organizations.serializers import (MembershipSerializer,
                                       MembershipUpdateSerializer,
                                       OrganizationConcreteSerializer,
                                       OrganizationPartialUpdateSerializer,
                                       OrganizationSerializer,
                                       ThirdPartyIntegrationSerializer,
                                       UserInvitationBodyEmptySerializer,
                                       UserInvitationPostSerializer,
                                       UserInvitationQuerySetSerializer,
                                       UserInvitationSerializer)


class ThirdPartyIntegrationsViewSet(viewsets.ModelViewSet):
    serializer_class = ThirdPartyIntegrationSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = ThirdPartyIntegration.objects.all()
    http_method_names = ["delete"]

    def get_queryset(self):
        queryset = super(ThirdPartyIntegrationsViewSet, self).get_queryset()

        filter_queryset_params = {}
        organization_id = self.request.query_params.get("organization", None)

        if organization_id:
            filter_queryset_params.update(**{"organization_id": organization_id})
        else:
            filter_queryset_params.update(**{"organization__membership__user": self.request.user})
        return queryset.filter(**filter_queryset_params)


@method_decorator(
    name="list",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="update",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
class OrganizationViewSet(viewsets.ModelViewSet):
    serializer_class = OrganizationSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Organization.objects.all()
    http_method_names = ["get", "post", "patch", "delete"]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return OrganizationConcreteSerializer
        elif self.action == "partial_update":
            return OrganizationPartialUpdateSerializer
        return self.serializer_class

    def get_queryset(self):
        queryset = super(OrganizationViewSet, self).get_queryset()
        return queryset.filter(membership__user=self.request.user)

    def destroy(self, request, *args, **kwargs):
        """Avoiding detroy organization if there are active active subscriptions linked to it"""
        organization = self.get_object()

        if organization.subscription_set.active_subscription():
            return Response(
                {
                    "non_field_errors": _(
                        "You can't remove organization because there are active subscriptions. Please, "
                        "cancel them first."
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return super(OrganizationViewSet, self).destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Read public profile of an Organization",
        responses={
            status.HTTP_200_OK: OrganizationSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(OrganizationSerializer, "Error"),
        },
    )
    @action(detail=True, methods=["get"], url_path="public-profile", permission_classes=(permissions.AllowAny,))
    def get_public_profile(self, request, pk=None):
        organization = self.get_object()
        if not organization.visibility:
            return Response(
                {"non_field_errors": _("This organization does not have a visible profile")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        organization_serializer = OrganizationSerializer(organization)
        return Response(organization_serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Generate an access token for google cloud services",
        operation_description=(
            "Generate a valid access token scoped to this organization in order to throttle the "
            "number of requests against remote service"
        ),
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: TokenSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(TokenSerializer, "Error"),
        },
    )
    @action(detail=True, methods=["post"], url_path="ai-auth", permission_classes=(permissions.IsAuthenticated,))
    def google_cloud_scoped_token(self, request, pk=None):
        """Generate a google cloud signed token to make requests against text-generation cloud function"""
        organization = self.get_object()

        google_token = generate_gcloud_access_token_on_demand(
            target_audience=settings.AI_TEXT_GENERATION_AUDIENCE,
        )

        encoded_payload = encoding_payload_with_hmac(
            payload={
                "organization_id": str(organization.pk),
                "user_id": str(self.request.user.pk),
                "environment": settings.ENVIRONMENT,
                "token": google_token,
            },
        )

        token_serializer = TokenSerializer({"payload": encoded_payload})
        return Response(token_serializer.data, status=status.HTTP_200_OK)


@method_decorator(
    name="create",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="list",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="update",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
class MembershipViewSet(viewsets.ModelViewSet):
    serializer_class = MembershipSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Membership.objects.all()
    http_method_names = ["get", "patch", "delete"]

    def get_serializer_class(self):
        if self.action == "partial_update":
            return MembershipUpdateSerializer
        return self.serializer_class


@method_decorator(
    name="retrieve",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="update",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="partial_update",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(name="list", decorator=swagger_auto_schema(query_serializer=UserInvitationQuerySetSerializer))
@method_decorator(name="create", decorator=swagger_auto_schema(query_serializer=UserInvitationQuerySetSerializer))
class UserInvitationViewSet(viewsets.ModelViewSet):
    serializer_class = UserInvitationSerializer
    queryset = UserInvitation.objects.all()
    permission_classes = (permissions.IsAuthenticated,)
    http_method_names = ["get", "post", "delete", "patch"]

    def get_serializer_class(self):
        if self.action == "create":
            return UserInvitationPostSerializer
        return self.serializer_class

    def get_queryset(self):
        queryset = super(UserInvitationViewSet, self).get_queryset()

        filter_queryset_params = {"accepted": False}
        organization_id = self.request.query_params.get("organization", None)

        if organization_id:
            filter_queryset_params.update(**{"organization_id": organization_id})
        else:
            filter_queryset_params.update(**{"organization__membership__user": self.request.user})
        return queryset.filter(**filter_queryset_params)

    @swagger_auto_schema(
        operation_summary="Resend invitation",
        responses={
            status.HTTP_200_OK: get_openapi_response("Invitation resent"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                UserInvitationSerializer, "Please wait for at least 1 minute before requesting another invite"
            ),
        },
    )
    @action(detail=True, methods=["post"], url_path="resend", permission_classes=(IsAuthenticated,))
    def resend_invitation(self, request, pk=None):
        resend_invitation = self.get_object()

        if (
            resend_invitation.sent
            and resend_invitation.sent
            + timedelta(seconds=settings.TIME_TO_WAIT_TO_RESEND_EMAIL_CONFIRMATION_IN_SECONDS)
            > timezone.now()
        ):
            return Response(
                {"non_field_errors": _("Please wait for at least 1 minute before requesting another invite.")},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if resend_invitation.accepted:
            return Response(
                {"non_field_errors": _("It is not possible to resend an already accepted invitation.")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        resend_invitation.send_email()
        return Response({"status": "Invitation resent"})

    @swagger_auto_schema(
        operation_summary="Delete Userinvitation",
        query_serializer=UserInvitationQuerySetSerializer,
        responses={
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                UserInvitationSerializer, "The user has already accepted the invitation"
            ),
        },
    )
    def destroy(self, request, *args, **kwargs):
        user_invitation = self.get_object()
        if user_invitation.accepted:
            return Response(
                {"non_field_errors": _("The user has already accepted the invitation")},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return super(UserInvitationViewSet, self).destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Register users already created",
        request_body=UserInvitationBodyEmptySerializer,
        query_serializer=UserInvitationQuerySetSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Registered user", "detail"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                UserInvitationBodyEmptySerializer, "Non-existent user"
            ),
        },
    )
    @action(detail=True, methods=["patch"], url_path="link", permission_classes=(IsAuthenticated,))
    def link_membership(self, request, pk=None):
        user_invitation = self.get_object()

        with transaction.atomic():
            user_invitation.accepted = True
            user_invitation.save(update_fields=["accepted"])
            Membership.objects.create(
                organization=user_invitation.organization, user=user_invitation.invited_user, role=user_invitation.role
            )

        return Response({"details": _("User linked.")}, status=status.HTTP_200_OK)
