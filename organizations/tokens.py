from datetime import date

from django.conf import settings
from django.utils.crypto import constant_time_compare, salted_hmac
from django.utils.http import base36_to_int, int_to_base36


class UserInvitationTokenGenerator:
    """
    Inspired by django.contrib.auth.tokens.PasswordResetTokenGenerator
    """

    key_salt = "adtuo_api.users.tokens.UserInvitationTokenGenerator"
    secret = settings.SECRET_KEY

    def make_token(self, user_pk, invited_email):
        """
        Return a token that can be used to register an invited user
        """
        ts = self._num_days(self._today())
        return self._make_token_with_timestamp(user_pk, invited_email, ts)

    def check_token(self, user_pk, invited_email, token):
        """
        Check that a token is correct for a given user and email.
        """
        if not (user_pk and invited_email and token):
            return False
        # Parse the token
        try:
            ts_b36, user_hash, email_hash = token.split("-")
        except ValueError:
            return False

        try:
            ts = base36_to_int(ts_b36)
        except ValueError:
            return False

        # Check that the timestamp/uid has not been tampered with
        if not constant_time_compare(self._make_token_with_timestamp(user_pk, invited_email, ts), token):
            return False

        return True

    def _make_token_with_timestamp(self, user_pk, invited_email, timestamp):
        # timestamp is number of days since 2001-1-1.  Converted to
        # base 36, this gives us a 3 digit string until about 2121
        ts_b36 = int_to_base36(timestamp)

        user_hash = salted_hmac(
            self.key_salt,
            self._make_hash_value(user_pk, timestamp),
            secret=self.secret,
        ).hexdigest()[::2]

        email_hash = salted_hmac(
            self.key_salt,
            self._make_hash_value(invited_email, timestamp),
            secret=self.secret,
        ).hexdigest()[::2]

        return "%s-%s-%s" % (ts_b36, user_hash, email_hash)

    def _make_hash_value(self, value, timestamp):
        return str(value) + str(timestamp)

    def _num_days(self, dt):
        return (dt - date(2001, 1, 1)).days

    def _today(self):
        # Used for mocking in tests
        return date.today()


user_invite_token_generator = UserInvitationTokenGenerator()
