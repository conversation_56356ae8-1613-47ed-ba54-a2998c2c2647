# Generated by Django 3.2 on 2021-04-27 18:53

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0016_alter_organization_alias"),
    ]

    operations = [
        migrations.RunSQL(
            sql="CREATE UNIQUE INDEX alias_case_insensitive_org_idx ON organizations_organization(lower(alias));",
            reverse_sql="DROP INDEX IF EXISTS alias_case_insensitive_org_idx",
        )
    ]
