# Generated by Django 3.1.5 on 2021-01-29 10:16

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("organizations", "0007_auto_20210125_1614"),
    ]

    operations = [
        migrations.RenameField(
            model_name="membership",
            old_name="organization_id",
            new_name="organization",
        ),
        migrations.RenameField(
            model_name="membership",
            old_name="user_id",
            new_name="user",
        ),
        migrations.AlterUniqueTogether(
            name="membership",
            unique_together={("organization", "user")},
        ),
        migrations.CreateModel(
            name="UserInvitation",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("email", models.EmailField(max_length=254)),
                ("accepted", models.BooleanField(default=False)),
                ("sent", models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True)),
                (
                    "invited_user",
                    models.OneToOneField(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "unique_together": {("organization", "invited_user")},
            },
        ),
    ]
