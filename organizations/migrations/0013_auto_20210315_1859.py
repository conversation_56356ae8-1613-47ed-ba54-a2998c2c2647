# Generated by Django 3.1.7 on 2021-03-15 17:59

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0012_auto_20210310_1206"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="organization",
            name="alias",
            field=models.CharField(
                max_length=200,
                null=True,
                unique=True,
                validators=[
                    django.core.validators.RegexValidator(
                        "^[a-zA-Z0-9]+[\\.\\-]?[a-zA-Z0-9]+$",
                        "Only alphanumeric characters or characters containing a period or hyphen are allowed.",
                    )
                ],
            ),
        ),
    ]
