export default [
  {
    path: "/",
    component: () => import("@/components/HelloWorld.vue"),
  },
  {
    path: "/voice",
    component: () => import("@/views/Voice.vue"),
  },
  {
    path: "/rtc",
    component: () => import("@/views/WebRTC.vue"),
  },
  {
    path: "/rtc-live",
    component: () => import("@/views/WebRTCLive.vue"),
  },
  {
    path: "/rtc-server",
    component: () => import("@/views/WebRTCWithRTC.vue"),
  },
  {
    path: "/rtc-server2",
    component: () => import("@/views/WebRTCMediaServer.vue"),
  }
];
