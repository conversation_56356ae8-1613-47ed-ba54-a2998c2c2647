<template>
  <div
    class="flex flex-col w-full justify-center items-center font-medium mt-8 space-y-4"
  >
    <p><PERSON>la WEBRTC</p>
    <!-- <video class="border-[1px] border-gray-400 rounded-md h-[720px] w-[1280px]" autoplay plasinline></video> -->
    <!-- <audio
      class="border-[1px] border-gray-400 rounded-md h-[720px] w-[1280px]"
      controls autoplay
    ></audio> -->
    <!-- <input type="file" accept="audio/*" capture id="recorder" />
    <audio id="player" controls></audio> -->
    <audio controls></audio>
    <div class="inline-flex space-x-2">
      <button
        class="p-3 bg-green-700 font-medium text-sm rounded-md hover:bg-green-500 text-white"
        :class="{
          'pointer-events-none opacity-40': recordings,
        }"
        @click="call"
      >
        Call!
      </button>
      <button
        class="p-3 bg-red-700 font-medium text-sm rounded-md hover:bg-red-500 text-white"
        @click="hangUp"
        :class="{
          'pointer-events-none opacity-40': !recordings,
        }"
      >
        Hang up
      </button>
    </div>
  </div>
</template>
<script>
export default {
  name: "WebRTC",
  mounted() {
    // const recorder = document.getElementById("recorder");
    // const player = document.getElementById("player");

    // recorder.addEventListener("change", function (e) {
    //   const file = e.target.files[0];
    //   const url = URL.createObjectURL(file);
    //   // Do something with the audio file.
    //   player.src = url;
    // });

    // Detect media devices available and ask the user for permission
    navigator.mediaDevices
      .getUserMedia(this.mediaStreamConstraints)
      .then(this.gotLocalMediaStream)
      .catch(this.handleLocalMediaStreamError);
  },
  data() {
    return {
      mediaStreamConstraints: {
        video: false,
        // video: {
        //   width: {
        //     min: 1280,
        //   },
        //   height: {
        //     min: 720,
        //   },
        // },
        audio: {
          deviceId: "default",
          sampleRate: 16000,
          sampleSize: 16,
          channelCount: 1,
        },
      },
      localStream: null,
      chunks: [],
      recordings: null,
      timeToRecord: 6000,
      mediaRecorder: null,
    };
  },
  methods: {
    gotLocalMediaStream(mediaStream) {
      // const player = document.getElementById("player");
      // if (window.URL) {
      //   player.srcObject = mediaStream;
      // } else {
      //   player.src = mediaStream;
      // }
      this.mediaRecorder = new MediaRecorder(mediaStream);

      this.mediaRecorder.addEventListener("dataavailable", (event) => {
        console.log("DATA!! ", event);
        if (event.data.size > 0) {
          this.chunks.push(event.data);
        }
      });

      // this.mediaRecorder.start();

      // // stop recording after 10 seconds
      // setTimeout(() => {
      //   console.log("PARADA!!!")
      //   this.mediaRecorder.stop();
      // }, 10000);
      // this.recordings = setInterval(() => {
      //   this.loopMediaRecorder();
      // }, this.timeToRecord);

      // this.localStream = mediaStream;

      // console.log("MEDIA ", this.localStream)

      // const localVideo = document.querySelector("video");
      // localVideo.srcObject = mediaStream;
    },
    // https://github.com/muaz-khan/RecordRTC/blob/master/simple-demos/audio-recording.html
    // https://github.com/muaz-khan/RecordRTC/blob/master/dev/StereoAudioRecorder.js#L7
    handleLocalMediaStreamError(error) {
      console.log("navigator.getUserMedia error: ", error);
    },
    loopMediaRecorder() {
      if (this.mediaRecorder.state === "recording") {
        this.mediaRecorder.stop();
      }

        console.log("AQUI!!!")
        const blobFile = new Blob(this.chunks, {
          type: this.mediaRecorder.mimeType,
        });
        const audioURL = window.URL.createObjectURL(blobFile);
        const audio = document.querySelector("audio");
        audio.src = audioURL;
        console.log("this.chunks ", blobFile);
        // Send chunks to server and clear the array

        // clear chunks
        // this.chunks = [];

      if (this.mediaRecorder.state === "inactive") {
        this.mediaRecorder.start();
      }
    },
    call() {
      if (this.recordings) {
        return null;
      }

      this.recordings = setInterval(() => {
        this.loopMediaRecorder();
      }, this.timeToRecord);
    },
    hangUp() {
      if (this.mediaRecorder.state === "recording") {
        this.mediaRecorder.stop();
      }

      clearInterval(this.recordings);
      this.recordings = null;
    },
  },
};
</script>