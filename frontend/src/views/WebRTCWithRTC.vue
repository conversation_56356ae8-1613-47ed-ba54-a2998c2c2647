<template>
  <div
    class="flex flex-col w-full justify-center items-center font-medium mt-8 space-y-4"
  >
    <p><PERSON><PERSON> WEBRTC - ToServer</p>
    <audio controls></audio>
  </div>
</template>
<script>
import RecordRTC from "recordrtc";
import axios from "axios";

export default {
  name: "WebRTC",
  mounted() {
    // Detect media devices available and ask the user for permission
    navigator.mediaDevices
      .getUserMedia(this.mediaStreamConstraints)
      .then(this.gotLocalMediaStream)
      .catch(this.handleLocalMediaStreamError);
  },
  data() {
    return {
      mediaStreamConstraints: {
        video: false,
        audio: true,
      },
      nope: false,
    };
  },
  methods: {
    async gotLocalMediaStream(mediaStream) {
      const recorder = RecordRTC(mediaStream, {
        type: "audio",
        numberOfAudioChannels: 1,
        // timeSlice: 3000,
        // ondataavailable: async (blob) => {

        //   const audioURL = window.URL.createObjectURL(blob);
        //   const audio = document.querySelector("audio");
        //   audio.src = audioURL;
        //   await this.sendBlob(blob);
        // },
      });
      recorder.startRecording();

      // setInterval(async () => {
      //   recorder.stopRecording(async () => {
      //     const blob = recorder.getBlob();
      //     console.log("BLOB", blob, recorder)
      //   });

      //   // const audioURL = window.URL.createObjectURL(blob);
      //   // const audio = document.querySelector("audio");
      //   // audio.src = audioURL;
      //   // console.log("AUDIO ", blob);

      //   // await this.sendBlob(blob);
      // }, 1500);

      // recorder.startRecording();

      const sleep = (m) => new Promise((r) => setTimeout(r, m));
      await sleep(3000);

      recorder.stopRecording(async () => {
        const blob = await recorder.getBlob();
        const audioURL = window.URL.createObjectURL(blob);
        const audio = document.querySelector("audio");
        audio.src = audioURL;
        console.log("AUDIO ", blob);

        await this.sendBlob(blob)
      });
    },
    async sendBlob(data) {
      const formData = new FormData();
      const config = {
        header: {
          "Content-Type": "multipart/form-data",
        },
      };

      // const audioFile = new File([data], 'audio.webm', { type: 'audio/webm' });
      formData.append("audio-blob", data);
      // formData.append("file", data, "audio.webm");
      // formData.append("title", "audio.webm")

      console.log(formData);

      return await axios.post("http://localhost:5000/chunk", formData, config);
    },
  },
};
</script>