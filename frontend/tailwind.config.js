module.exports = {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      spacing: {
        mobile: "96px",
        mobileL: "96px",
        tablet: "152px",
        desktop: "152px",
        desktopL: "152px",
      },
      gridTemplateColumns: {
        mobile: "repeat(4, minmax(0, 1fr))",
        mobileL: "repeat(8, minmax(0, 1fr))",
        tablet: "repeat(12, minmax(0, 1fr))",
        desktop: "repeat(12, minmax(0, 1fr))",
        desktopL: "repeat(12, minmax(0, 1fr))",
      },
      colors: {
        "electric-blue": "#4760ff",
        "electric-blue-50": "#EBEDFF",
        "electric-blue-100": "#DBE0FF",
        "electric-blue-200": "#B8C1FF",
        "electric-blue-300": "#8F9EFF",
        "electric-blue-400": "#6B7FFF",
        "electric-blue-500": "#4760FF",
        "electric-blue-600": "#0526FF",
        "electric-blue-700": "#001AC2",
        "electric-blue-800": "#001285",
        "electric-blue-900": "#000942",
        "soft-blue": "#e7edfa",
        "dark-blue": "#140c40",
      },
      backgroundImage: {
        "main-logo": "url('@/assets/backoffice_logo.svg')",
      },
      height: {
        content: "calc(100vh - 8.5rem)",
      },
      screens: {
        mobile: { max: "599px" },
        mobileL: { min: "600px", max: "904px" },
        tablet: { min: "905px", max: "1239px" },
        desktop: { min: "1240px", max: "1919px" },
        desktopL: { min: "1920px" },
      },
    },
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
      tablet: { min: "905px", max: "1239px" },
      desktop: { min: "1240px", max: "1919px" },
      desktopL: { min: "1920px" },
    },
  },
  variants: {
    extend: {
      backgroundColor: ["even", "odd"],
    },
  },
  plugins: [],
};
