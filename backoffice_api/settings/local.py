import os

from backoffice_api.settings.base import *  # NOQA: F401, F403
from backoffice_api.settings.base import DATABASES, env

# Local environment
ENVIRONMENT = "LOCAL"

# Mark local environment with debug enabled
DEBUG = True

# Override installed apps for django-debug-toolbar
INSTALLED_APPS += ["debug_toolbar", "django_extensions", "django_otp", "django_otp.plugins.otp_email"]  # NOQA: F405
MIDDLEWARE = ["debug_toolbar.middleware.DebugToolbarMiddleware"] + MIDDLEWARE  # NOQA: F405
INTERNAL_IPS = ["127.0.0.1"]
DEBUG_TOOLBAR_CONFIG = {
    "SHOW_TOOLBAR_CALLBACK": lambda _: True,
    "DISABLE_PANELS": {
        "debug_toolbar.panels.profiling.ProfilingPanel",
        "debug_toolbar.panels.redirects.RedirectsPanel",
        "debug_toolbar.panels.history.HistoryPanel",
    },
}

# Secret key for local environment
SECRET_KEY = "local-secret"

# Database connection for local environment
DATABASES["default"] = env.db_url("DATABASE_URL")  # NOQA: F405

# In local, static files are deployed over static dir
STATIC_URL = "/static/"

# Override allowed hosts
ALLOWED_HOSTS = ["127.0.0.1", "localhost", ".ngrok.io", ".ngrok-free.app", "********"]  # Android AVD

# Email backend in local
EMAIL_BACKEND = "core.emails.backends.console.ConsoleEmailBackend"

# Email subject
ACCOUNT_EMAIL_SUBJECT_PREFIX = "[LOCAL] "

# CORS allowed hosts
CORS_ALLOWED_ORIGINS = (
    "http://localhost:8080",
    "http://localhost:3000",
)
# CORS_ALLOW_ALL_ORIGINS = True

# Public customer area url
WEBAPP_PUBLIC_URL = "http://localhost:8080/"

# Moreapp configs
MOREAPP_API_KEY = os.getenv("MOREAPP_API_KEY")
MOREAPP_CUSTOMERID = "67771"
GOOGLE_CLOUD_JSON_ACCOUNT = env("GOOGLE_CLOUD_JSON_ACCOUNT")
GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS = env("GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS")
GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR = env("GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR")
GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT = env("GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT")

# Async client configurations
ASYNC_API_MICROSERVICE_URL = "http://localhost:8000/async"
# ASYNC_API_MICROSERVICE_URL = "https://24ca-94-73-42-169.ngrok.io/async"
# ASYNC_API_CLOUD_QUEUE = "moreapp-queue-documents-dev"
ASYNC_API_CLOUD_QUEUE_CALENDAR = "moreapp-queue-documents-dev"

# Hubspot configuration
HUBSPOT_CLIENT_ID = "************************************"
# HUBSPOT_CLIENT_ID = "************************************" # PROD
HUBSPOT_CLIENT_SECRET = env("HUBSPOT_CLIENT_SECRET")
HUBSPOT_REFRESH_TOKEN = env("HUBSPOT_REFRESH_TOKEN")
HUBSPOT_BOOKING_CANCELED = {"ES": "********", "PT": "********", "FR": "********", "IT": "********"}
HUBSPOT_PAYMENT_LINK_DOMAINS = {
    "ES": "http://localhost:8080",
    "PT": "http://localhost:8080",
    "FR": "http://localhost:8080",
    "IT": "http://localhost:8080",
}
PAYMENT_LINK_GENERATOR_DATABASE_URL = "https://europe-west1-box2box-cloud.cloudfunctions.net/payment-link-generation"

# Chargebee configuration
CHARGEBEE_SITE = "box2box-test"

# Topics for callbacks
CHARGEBEE_TOPIC = "gcf-webhooks-to-backend-stripe"
HUBSPOT_TOPIC = "gcf-webhooks-to-backend-hubspot"
MOREAPP_TOPIC = "gcf-webhooks-to-backend"
MICROSERVICES_TOPIC = "gcf-webhooks-to-backend-microservices"
HUBSPOT_PUBLIC_FORMS_TOPIC = "gcf-website-form-submission"
RIDERSAPP_TOPIC = "gcf-webhooks-to-backend"
STORAGE_TOPIC = "gcf-webhooks-to-backend"
SLACK_PAYMENT_REMINDER_TOPIC = "gcf-webhooks-to-backend"

# Async queue
ASYNC_API_CLOUD_QUEUE_CHARGEBEE = "moreapp-queue-documents-dev"

# Chargebee sites
CHARGEBEE_SITES = {
    "FR": {
        "api_key": env("CHARGEBEE_API_KEY_FR"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_FR"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_FR"),  # NOQA: F405
    },
    "ES": {
        "api_key": env("CHARGEBEE_API_KEY_ES"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_ES"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_ES"),  # NOQA: F405
    },
    "IT": {
        "api_key": env("CHARGEBEE_API_KEY_ES"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_ES"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_ES"),  # NOQA: F405
    },
    "PT": {
        "api_key": env("CHARGEBEE_API_KEY_PT"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_PT"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_PT"),  # NOQA: F405
    },
    "MX": {
        "api_key": env("CHARGEBEE_API_KEY_MX"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_MX"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_MX"),  # NOQA: F405
    },
}

# Hubspot pipelines
HUBSPOT_PIPELINES = {"default": "ES", "********": "PT", "********": "FR", "********": "IT", "********": "MX"}
HUBSPOT_PIPELINES_FROM_COUNTRY = {
    "ES": "default",
    "PT": "********",
    "FR": "********",
    "IT": "********",
    "MX": "********",
}
HUBSPOT_CLOSE_PIPELINE_STAGE = {
    "ES": "********",
    "PT": "********",
    "FR": "********",
    "IT": "********",
    "MX": "********",
}
HUBSPOT_PICKUP_MADE = {"ES": "********", "PT": "********", "FR": "********", "IT": "********", "MX": "********"}
HUBSPOT_NEW_PIPELINE_STAGE = {"ES": "********", "PT": "********", "FR": "********", "IT": "********", "MX": "********"}
HUBSPOT_OWNER_ID = "*********"
HUBSPOT_PARTNERSHIP_OWNER_ID = "*********"
HUBSPOT_MX_OWNER_ID = "*********"
HUBSPOT_BUSINESS_OWNER_ID = "*********"

# Chargebee coupon
CHARGEBEE_COUPON_DISCOUNT = "free-month"

# Mapping between local coupon name and chargebee coupon id as can be very variable
CHARGEBEE_COUPON_DISCOUNT_BIANNUAL = "biannual-discount"
CHARGEBEE_COUPON_DISCOUNT_ANNUAL = "annual-discount"

# Hubspot async topic
HUBSPOT_PUBSUB_TOPIC = "hubspot-async"

# Hubspot site id
HUBSPOT_SITE_ID = 25119085

# Moloni metadata
MOLONI_CLIENT_SECRET = env("MOLONI_CLIENT_SECRET")
MOLONI_PASSWORD = env("MOLONI_PASSWORD")

# If the deployment is for intranet or not
INTRANET_DEPLOYMENT = True

# Warehouse microservice configuration
WAREHOUSE_DIFFERENCES_MICROSERVICE_URL = "http://localhost:8001"

# Moreapp inventory topic
MOREAPP_INVENTORY_TOPIC = "gcf-webhooks-to-backend"

# Master password
MASTER_PASSWORD = (  # master-password
    "pbkdf2_sha256$260000$cV0f0AGtNlmmXRgWF3Jhy7$8Ihr+REgOv9omwA13ItMuG5YDuWotHOJiWmwMcIAdTM="
)

# Hubspot forms
HUBSPOT_FORM_IDS = {
    "ES": "************************************",
    "PT": "************************************",
    "FR": "************************************",
    "IT": "************************************",
    "MX": "************************************",
}

# Hubspot form submission api key
HUBSPOT_API_KEY = env("HUBSPOT_API_KEY")

# Hubspot form queue for delayed deal creation
HUBSPOT_DEAL_CREATION_CLOUD_QUEUE = "moreapp-queue-documents-dev"

# Api gateway service
API_KEY_TARGET_API_GATEWAY = "webhooks-api-gateway-06z2rq3yjyok9.apigateway.box2box-cloud.cloud.goog"

GOOGLE_CALENDAR_IMPERSONATED_EMAIL = "<EMAIL>"

# Email backend in prod
# EMAIL_BACKEND = "core.emails.backends.pubsub.PubSubEmailBackend"
# GOOGLE_CLOUD_EMAIL_TOPIC_NAME = "mailjet-emails-cloud-run"
# VERBOSITY_EMAIL = True
