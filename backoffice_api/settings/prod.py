import urllib.parse

import sentry_sdk

from core.signal_handlers import <PERSON><PERSON><PERSON><PERSON>

from .common import *  # NOQA: F401

# Local environment
ENVIRONMENT = "PROD"

# Secret key for local environment
SECRET_KEY = env("SECRET_KEY")  # NOQA: F405

# Documentation bucket name
DOCUMENTATION_BUCKET_NAME = "docs-box2box-prod"

# Email backend in prod
EMAIL_BACKEND = "core.emails.backends.pubsub.PubSubEmailBackend"

# In staging, static files are deployed over static dir
STATIC_URL = "https://storage.googleapis.com/api_resources_box2box/static/"

# Override allowed hosts
ALLOWED_HOSTS = ["api.box2box.es", "127.0.0.1", "localhost", "***********", "ui.box2box.es"]

# CORS allowed hosts
CORS_ALLOWED_ORIGINS = (
    "https://app.box2box.es",
    "https://app.box2box.net",
    "https://app.box2box.io",
    "https://app.box2boxstorage.com",
    "https://intranet.box2box.es",
    "https://portal.box2box.es",
    "https://portal.box2box.net",
    "https://portal.box2box.io",
    "https://portal.box2boxstorage.com",
    "https://www.box2boxstorage.com",
)

# Set sentry config for production environment
sentry_sdk.init(**SENTRY_CONFIGURATION, environment=ENVIRONMENT)  # NOQA: F405 # Will be prod

# Hubspot configuration
HUBSPOT_CLIENT_ID = "************************************"
HUBSPOT_CLIENT_SECRET = env("HUBSPOT_CLIENT_SECRET")  # NOQA: F405
HUBSPOT_REFRESH_TOKEN = env("HUBSPOT_REFRESH_TOKEN")  # NOQA: F405
HUBSPOT_PAYMENT_LINK_DOMAINS = {
    "ES": "https://www.box2boxstorage.com/es",
    "PT": "https://www.box2boxstorage.com/pt",
    "FR": "https://www.box2boxstorage.com/fr",
    "IT": "https://www.box2boxstorage.com/it",
    "MX": "https://www.box2boxstorage.com/mx",
}

# For kubernetes probes
ALLOWED_CIDR_NETS = ["10.28.0.0/14", "10.32.0.0/20"]
INSTALLED_APPS += [  # NOQA: F405
    # django otp
    "django_otp",
    "django_otp.plugins.otp_totp",
    "django_otp.plugins.otp_email",  # For OTP tokens used in riders app
]
MIDDLEWARE = (
    [
        "core.middleware.SignalingMiddleware",
        "allow_cidr.middleware.AllowCIDRMiddleware",
    ]
    + MIDDLEWARE  # NOQA: F405
    + ["intranet.middleware.OTPMiddleware"]
)

PAYMENT_LINK_GENERATOR_DATABASE_URL = (
    "https://europe-west1-box2box-cloud.cloudfunctions.net/payment-link-generation-prod"
)

# Async client configurations
ASYNC_API_MICROSERVICE_URL = "https://api.box2box.es/tasks/async"
ASYNC_API_CLOUD_QUEUE = "moreapp-queue-documents-prod"
ASYNC_API_CLOUD_QUEUE_CHARGEBEE = "chargebee-queue-documents-prod"
ASYNC_API_CLOUD_QUEUE_CALENDAR = "calendar-queue-prod"

# Topics for callbacks
CHARGEBEE_TOPIC = "gcf-webhooks-to-backend-chargebee-prod"
HUBSPOT_TOPIC = "gcf-webhooks-to-backend-hubspot-prod"
MOREAPP_TOPIC = "gcf-webhooks-to-backend-moreapp-prod"
MOREAPP_INVENTORY_TOPIC = "gcf-webhooks-to-backend-moreapp-inventory-prod"
MICROSERVICES_TOPIC = "gcf-webhooks-to-backend-microservices-prod"
HUBSPOT_PUBLIC_FORMS_TOPIC = "gcf-website-form-submission-prod"
RIDERSAPP_TOPIC = "gcf-webhooks-to-backend-riders-prod"
STORAGE_TOPIC = "gcf-webhooks-to-backend-storage-prod"
SLACK_PAYMENT_REMINDER_TOPIC = "gcf-webhooks-to-backend-payment-reminder-prod"

# Prefix API
PREFIX = env("PREFIX")  # NOQA: F405

# Multi domains for webapps
WEBAPP_PUBLIC_URLS = {
    "ES": "https://app.box2box.es/",
    "PT": "https://app.box2box.net/",
    "FR": "https://app.box2box.io/",
    "IT": "https://app.box2box.es/",  # TODO: fix it
    "MX": "https://app.box2box.es/",  # TODO: fix it
}

# Hubspot pipelines
HUBSPOT_PIPELINES = {"default": "ES", "13498082": "PT", "13488357": "FR", "23820737": "IT", "26720448": "MX"}
HUBSPOT_PIPELINES_FROM_COUNTRY = {
    "ES": "default",
    "PT": "13498082",
    "FR": "13488357",
    "IT": "23820737",
    "MX": "26720448",
}
HUBSPOT_CLOSE_PIPELINE_STAGE = {
    "ES": "40213701",
    "PT": "48319941",
    "FR": "48319943",
    "IT": "75653610",
    "MX": "173847018",
}
HUBSPOT_NEW_PIPELINE_STAGE = {
    "ES": "qualifiedtobuy",
    "PT": "46227677",
    "FR": "46357989",
    "IT": "75632847",
    "MX": "82494695",
}
HUBSPOT_PICKUP_MADE = {"ES": "47812593", "PT": "48319940", "FR": "48319942", "IT": "75632853", "MX": "173847017"}
HUBSPOT_BOOKING_CANCELED = {"ES": "contractsent", "PT": "46227681", "FR": "46357992", "IT": "75632851"}
HUBSPOT_OWNER_ID = "150886886"
HUBSPOT_PARTNERSHIP_OWNER_ID = "568742095"  # Enrique owner id
HUBSPOT_MX_OWNER_ID = "876573908"  # <EMAIL> id
HUBSPOT_BUSINESS_OWNER_ID = "511829843"  # <EMAIL> id

# Hubspot async topic
HUBSPOT_PUBSUB_TOPIC = "hubspot-async-prod"

# TEMPORAL FLAG
VERBOSITY_EMAIL = env("VERBOSITY_EMAIL")  # NOQA: F405

# Hubspot site id
HUBSPOT_SITE_ID = 25093590

# Intranet deployment
INTRANET_DEPLOYMENT = env("INTRANET_DEPLOYMENT")  # NOQA: F405

# Set SSL header
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Intranet URL
INTRANET_URL = "https://intranet.box2box.es"


# Microservices
WAREHOUSE_DIFFERENCES_MICROSERVICE_URL = "https://ms-warehouse-ycrbonqbfq-ew.a.run.app"
WAREHOUSE_DIFFERENCE_CLOUD_QUEUE = "warehouse-differences-ms-prod"

# Mongo URI for moreapp inventory callback
MONGODB_PASSWORD = env("MONGODB_PASSWORD")  # NOQA: F405
MONGODB_URI = f"mongodb://itsystem:{urllib.parse.quote_plus(MONGODB_PASSWORD)}@***********:27017/admin"

# Master password
MASTER_PASSWORD = env("MASTER_PASSWORD")  # NOQA: F405

# Hubspot form queue for delayed deal creation
HUBSPOT_DEAL_CREATION_CLOUD_QUEUE = "hubspot-form-submission-queue-prod"

# Hubspot forms
HUBSPOT_FORM_IDS = {
    "ES": "************************************",
    "PT": "da64b577-1de6-4da6-bcc2-963c627520b2",
    "FR": "8e659508-7591-4a46-ba75-fb31decbfb31",
    "IT": "23df6bd2-f995-4480-80db-7f68d097b790",
    "MX": "4e41b869-1ce8-4079-bdee-8794e8837d55",
}

# Hubspot form submission api key
HUBSPOT_API_KEY = env("HUBSPOT_API_KEY")  # NOQA: F405

# Firestore forms database
FIRESTORE_FORMS_DATABASE = "public_website_prod"

# Api gateway service
API_KEY_TARGET_API_GATEWAY = "gateway-prod-25opf9o4cwrn3.apigateway.box2box-cloud.cloud.goog"

###### SIGNAL HANDLING
signal_handler = SignalHandler()
