import sentry_sdk

from .common import *  # NOQA: F401

# Local environment
ENVIRONMENT = "STAGING"

# Needed for OTP tokens
INSTALLED_APPS += ["django_otp", "django_otp.plugins.otp_email"]  # NOQA: F405

# Secret key for local environment
SECRET_KEY = "local-secret"

# Email subject
ACCOUNT_EMAIL_SUBJECT_PREFIX = "[STAGING] "

# Email backend in prod
EMAIL_BACKEND = "core.emails.backends.pubsub.PubSubEmailBackend"

# In staging, static files are deployed over static dir
STATIC_URL = "/static/"

# Override allowed hosts
ALLOWED_HOSTS = ["staging.devbox2box.es", "127.0.0.1", "localhost", "*************"]

# CORS allowed hosts
CORS_ALLOWED_ORIGINS = (
    "https://devbox2box.es",
    "https://box2box-webapp-staging.web.app",
    "https://app.devbox2box.es",
    "https://app.devbox2boxbeta.es",
)

# Set sentry config for staging environment
sentry_sdk.init(**SENTRY_CONFIGURATION, environment=ENVIRONMENT)  # NOQA: F405 # Will be staging

# Support team (ATC) email address
SUPPORT_TEAM_EMAILS = {
    "ES": ["<EMAIL>"],
    "PT": ["<EMAIL>"],
    "FR": ["<EMAIL>"],
    "IT": ["<EMAIL>"],
    "MX": ["<EMAIL>"],
}

# Hubspot configuration
HUBSPOT_CLIENT_ID = "************************************"
HUBSPOT_CLIENT_SECRET = env("HUBSPOT_CLIENT_SECRET")  # NOQA: F405
HUBSPOT_REFRESH_TOKEN = env("HUBSPOT_REFRESH_TOKEN")  # NOQA: F405
HUBSPOT_PAYMENT_LINK_DOMAINS = {
    "ES": "https://app.devbox2box.es",
    "PT": "https://app.devbox2box.es",
    "FR": "https://app.devbox2box.es",
    "IT": "https://app.devbox2box.es",
}
PAYMENT_LINK_GENERATOR_DATABASE_URL = "https://europe-west1-box2box-cloud.cloudfunctions.net/payment-link-generation"

# Async client configurations
ASYNC_API_MICROSERVICE_URL = "https://staging.devbox2box.es/async"
ASYNC_API_CLOUD_QUEUE = "moreapp-queue-documents-dev"
ASYNC_API_CLOUD_QUEUE_CHARGEBEE = "moreapp-queue-documents-dev"
ASYNC_API_CLOUD_QUEUE_CALENDAR = "moreapp-queue-documents-dev"

# Topics for callbacks
CHARGEBEE_TOPIC = "gcf-webhooks-to-backend-stripe"
HUBSPOT_TOPIC = "gcf-webhooks-to-backend-hubspot"
MOREAPP_TOPIC = "gcf-webhooks-to-backend"
MOREAPP_INVENTORY_TOPIC = "gcf-webhooks-to-backend"
RIDERSAPP_TOPIC = "gcf-webhooks-to-backend"
STORAGE_TOPIC = "gcf-webhooks-to-backend"

# Multi domains for webapps
WEBAPP_PUBLIC_URLS = {
    "ES": "https://app.devbox2box.es/",
    "PT": "https://app.devbox2box.es/",
    "FR": "https://app.devbox2box.es/",
    "IT": "https://app.devbox2box.es/",
    "MX": "https://app.devbox2box.es/",
}

# Hubspot pipelines
HUBSPOT_PIPELINES = {"default": "ES", "13488364": "PT", "13498104": "FR", "32972479": "IT", "26720446": "MX"}
HUBSPOT_PIPELINES_FROM_COUNTRY = {
    "ES": "default",
    "PT": "13488364",
    "FR": "13498104",
    "IT": "32972479",
    "MX": "26720446",
}
HUBSPOT_CLOSE_PIPELINE_STAGE = {
    "ES": "49160151",
    "PT": "46358245",
    "FR": "46438374",
    "IT": "98260431",
    "MX": "82494672",
}
HUBSPOT_NEW_PIPELINE_STAGE = {"ES": "21292265", "PT": "46358239", "FR": "46438369", "IT": "98336463", "MX": "82494667"}
HUBSPOT_PICKUP_MADE = {"ES": "51004894", "PT": "51004896", "FR": "50957279", "IT": "98336468", "MX": "82494671"}
HUBSPOT_BOOKING_CANCELED = {"ES": "21292269", "PT": "46358242", "FR": "46438372", "IT": "98336466"}
HUBSPOT_OWNER_ID = "155168218"
HUBSPOT_PARTNERSHIP_OWNER_ID = "655452403"
HUBSPOT_MX_OWNER_ID = "655452403"
HUBSPOT_BUSINESS_OWNER_ID = "655452403"

# Hubspot async topic
HUBSPOT_PUBSUB_TOPIC = "hubspot-async"

# Hubspot site id
HUBSPOT_SITE_ID = 25119085

# Set verbosity email
VERBOSITY_EMAIL = True

# Exclude intranet deployment
INTRANET_DEPLOYMENT = False

# Master password
MASTER_PASSWORD = None

# Hubspot form queue for delayed deal creation
HUBSPOT_DEAL_CREATION_CLOUD_QUEUE = "moreapp-queue-documents-dev"

# Api gateway service
API_KEY_TARGET_API_GATEWAY = "webhooks-api-gateway-06z2rq3yjyok9.apigateway.box2box-cloud.cloud.goog"
