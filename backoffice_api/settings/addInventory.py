from contracts.models import Item

items = []
name = "B2BAPPDEL"
for i in range(500):
    items.append(Item(
        tag_id=f"{name}{i}",
        description=f"Descripción del objeto {i} que es super larga ademas para ver el comportamiento que tiene con overflows",
        service_id="0acb98c4-9915-4300-8510-087dad2363be",
        contract_id="77f51f81-b40d-44d1-a06c-00fd24e21517"
    ))

Item.objects.bulk_create(items)