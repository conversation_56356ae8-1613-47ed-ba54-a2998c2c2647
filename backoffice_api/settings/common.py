import logging

from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from .base import *  # NOQA: F401, F403
from .base import env

# Mark staging/production environment with debug disabled
DEBUG = False

# Tune logging
LOGGING["handlers"]["console"]["filters"] = []  # NOQA: F405 # Log regardless of DEBUG

sentry_logging = LoggingIntegration(
    level=logging.WARNING,  # Capture warning and above as breadcrumbs
    event_level=logging.WARNING,  # Send no events from log messages
)


def traces_sampler(sampling_context):
    """Set custom traces sampler to exclude kubernetes urls from transaction"""
    if "wsgi_environ" in sampling_context:
        url = sampling_context["wsgi_environ"].get("PATH_INFO", "") or ""
        if (
            url.startswith("/health/")
            or url.startswith("/healthz/")
            or url.startswith("/ihealth/")
            or url.startswith("/ihealthz/")
            or url.startswith("/tasks/health/")
            or url.startswith("/tasks/healthz/")
        ):
            return 0  # Don't trace any
    return 0.6  # Trace a subset of transactions


SENTRY_CONFIGURATION = {
    "dsn": env("SENTRY_SDK_DSN"),
    "integrations": [DjangoIntegration(), sentry_logging],
    "traces_sampler": traces_sampler,
    "send_default_pii": True,
}

# Email configuration
GOOGLE_CLOUD_EMAIL_TOPIC_NAME = "mailjet-emails"
# Temporary configuration - deployed over cloud run 13/06/2023 -- Replaced by sendgrid on 14/06/2023
# GOOGLE_CLOUD_EMAIL_TOPIC_NAME = "mailjet-emails-cloud-run"

# Set default cache
CACHES["default"]["LOCATION"] = env("REDIS_SERVER")  # NOQA: F405

DATABASES["default"] = env.db_url("DATABASE_URL")  # NOQA: F405
DATABASES["default"].update(**{  # NOQA: F405
    "ENGINE": "django_db_geventpool.backends.postgresql_psycopg2",
    "CONN_MAX_AGE": 0,
    "OPTIONS": {"MAX_CONNS": 4, "REUSE_CONNS": 2},
})

# Google cloud service accounts
GOOGLE_CLOUD_JSON_ACCOUNT = env("GOOGLE_CLOUD_JSON_ACCOUNT")
GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS = env("GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS")
GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR = env("GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR")
GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT = env("GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT")

# Moreapp configs
MOREAPP_API_KEY = env("MOREAPP_API_KEY")
MOREAPP_CUSTOMERID = "67771"

# Hubspot configuration
HUBSPOT_CLIENT_SECRET = env("HUBSPOT_CLIENT_SECRET")
HUBSPOT_REFRESH_TOKEN = env("HUBSPOT_REFRESH_TOKEN")

# Chargebee configuration
CHARGEBEE_COUPON_DISCOUNT = "free-month"

# Mapping between local coupon name and chargebee coupon id as can be very variable
CHARGEBEE_COUPON_DISCOUNT_BIANNUAL = "biannual-discount"
CHARGEBEE_COUPON_DISCOUNT_ANNUAL = "annual-discount"

# Chargebee sites
CHARGEBEE_SITES = {
    "FR": {
        "api_key": env("CHARGEBEE_API_KEY_FR"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_FR"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_FR"),  # NOQA: F405
    },
    "ES": {
        "api_key": env("CHARGEBEE_API_KEY_ES"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_ES"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_ES"),  # NOQA: F405
    },
    "IT": {
        "api_key": env("CHARGEBEE_API_KEY_ES"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_ES"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_ES"),  # NOQA: F405
    },
    "PT": {
        "api_key": env("CHARGEBEE_API_KEY_PT"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_PT"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_PT"),  # NOQA: F405
    },
    "MX": {
        "api_key": env("CHARGEBEE_API_KEY_MX"),  # NOQA: F405
        "site": env("CHARGEBEE_SITE_MX"),  # NOQA: F405
        "payment_gateway_id": env("ADYEN_GATEWAY_ACCOUNT_ID_MX"),  # NOQA: F405
    },
}


# Moloni metadata
MOLONI_CLIENT_SECRET = env("MOLONI_CLIENT_SECRET")
MOLONI_USERNAME = "<EMAIL>"
MOLONI_PASSWORD = env("MOLONI_PASSWORD")
