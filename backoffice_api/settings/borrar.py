import uuid
from urllib.parse import urlencode

print([
    uuid.uuid4().__str__() for _ in range(80)
])

print(''.join([f"&excluded_services={i}" for i in ['fa706738-5d4f-461b-b4bf-7a47a8d11499']*200]))

import requests
response = requests.get(
    "https://webhooks-api-gateway-d5ppcvke.ew.gateway.dev/services/",
    params={
        "key": "AIzaSyDBfniKT1k8QnoeHi42jt5iwJUI-XNn7ro",
        "excluded_services":['fa706738-5d4f-461b-b4bf-7a47a8d11499']*147,
    }
)
print(response.text)

params = {
    "key": "AIzaSyDBfniKT1k8QnoeHi42jt5iwJUI-XNn7ro",
    "excluded_services": ['fa706738-5d4f-461b-b4bf-7a47a8d11499']*147,
}

# Step 1: URL Encode the Query Parameters
# Note: urlenco<PERSON> converts the dictionary to a URL-encoded query string.
encoded_params = urlencode(params, doseq=True)

# Step 2: Calculate Byte Size
# The encoded string is converted to bytes, and then the size is measured.
bytes_size = len(encoded_params.encode('utf-8'))

print(f"Size of params in bytes: {bytes_size}")