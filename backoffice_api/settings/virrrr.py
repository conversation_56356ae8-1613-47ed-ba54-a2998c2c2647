from django.utils import timezone

from intranet.models import *

NUMBER_OF_MOVEMENTS = 5000000
BATCH_SIZE = 100000

# Process and insert in batches
for start_idx in range(0, NUMBER_OF_MOVEMENTS, BATCH_SIZE):
    end_idx = min(start_idx + BATCH_SIZE, NUMBER_OF_MOVEMENTS)

    # Create a batch of movements
    batch = [
        WarehouseMovement(
            location=f"[AUTOMATIC] Almacen {idx} con descripcion larga para comprobar de nuevo el flexible widget",
            pallet=idx + 1,
            floor="Alta" if idx % 2 == 0 else "Baja",
            sent_at=timezone.now(),
            internal_id=f"tracking-{idx}w",
            rider_id="73e0681d-eb6d-4828-8554-e976d6b0529a",
            contract_id="536d702c-b11d-46d4-bf1d-5a3b07461aca",
            warehouse_id="4b178ac4-18ac-4cdb-a892-bf0ad8daeac0",
            service_id="bf79510c-5d52-4020-8382-2e313bdfdd03",
            remote_dir="/16382608351/tracking/000ab19e-0acf-4ac0-a105-3194ebd666c6-73e0681d-eb6d-4828-8554-e976d6b0529a"
        )
        for idx in range(start_idx, end_idx)
    ]

    WarehouseMovement.objects.bulk_create(batch)

    print(f"Batch {start_idx} to {end_idx} processed")