from datetime import timed<PERSON>ta

from django.utils import timezone

from intranet.models import Task, Event
from riders.models import Team

tasks = []
events = []
teams = []

# Urgent
for i in range(15):
    task = Task(
        additional_info=f"Info adicional que estoy metiendo con index {i}",
        created_by_id="30529130-9c68-4e9c-8f6f-fcd9f7d8eedc"
    )

    event = Event(
        user_id="30529130-9c68-4e9c-8f6f-fcd9f7d8eedc",
        calendar_id="4c0c8c0e-9f88-4e9c-a7c8-9caa66f208ab",
        title=f"Titulo del evento #{i}",
        start_time="2023-12-30T18:00:00Z",
        end_time="2023-12-30T20:00:00Z",
        task=task
    )

    team = Team(
        rider_id="73e0681d-eb6d-4828-8554-e976d6b0529a",
        task=task
    )
    tasks.append(task)
    events.append(event)
    teams.append(team)

# Today
now = timezone.now().replace(hour=10, minute=0, second=0)
for i in range(6):
    task = Task(
        additional_info=f"Info adicional que estoy metiendo con index {i}",
        created_by_id="30529130-9c68-4e9c-8f6f-fcd9f7d8eedc"
    )

    event = Event(
        user_id="30529130-9c68-4e9c-8f6f-fcd9f7d8eedc",
        calendar_id="4c0c8c0e-9f88-4e9c-a7c8-9caa66f208ab",
        title=f"Titulo del evento #{i}",
        start_time=now,
        end_time=now + timedelta(hours=1),
        task=task
    )

    team = Team(
        rider_id="73e0681d-eb6d-4828-8554-e976d6b0529a",
        task=task
    )
    tasks.append(task)
    events.append(event)
    teams.append(team)

# Upcoming
now = timezone.now().replace(hour=18, minute=0, second=0)
for i in range(3):
    task = Task(
        additional_info=f"Info adicional que estoy metiendo con index {i}",
        created_by_id="30529130-9c68-4e9c-8f6f-fcd9f7d8eedc"
    )

    event = Event(
        user_id="30529130-9c68-4e9c-8f6f-fcd9f7d8eedc",
        calendar_id="4c0c8c0e-9f88-4e9c-a7c8-9caa66f208ab",
        title=f"Titulo del evento #{i}",
        start_time=now + timedelta(days=4),
        end_time=now + timedelta(days=4, hours=1),
        task=task
    )

    team = Team(
        rider_id="73e0681d-eb6d-4828-8554-e976d6b0529a",
        task=task
    )
    tasks.append(task)
    events.append(event)
    teams.append(team)

if tasks:
    Task.objects.bulk_create(tasks)

if events:
    Event.objects.bulk_create(events)

if teams:
    Team.objects.bulk_create(teams)


# Event.objects.filter(task__isnull=True, service__isnull=True).delete()