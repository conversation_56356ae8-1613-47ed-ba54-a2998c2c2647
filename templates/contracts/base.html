<html>
<head>
    <meta charset="utf-8">
    <script src="https://unpkg.com/twemoji@latest/dist/twemoji.min.js" crossorigin="anonymous"></script>
    <script>window.onload = function () { twemoji.parse(document.body, {ext: ".png"});}</script>
    <style>
        * {
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
            page-break-inside: avoid;
            page-break-after: avoid;
            page-break-before: avoid;
        }

        img.emoji {
            height: 16px;
            width: 16px;
            margin: 0 .05em 0 .1em;
            vertical-align: -0.1em;
        }

        body {
            font-size: 12px;
            font-weight: 400 !important;
            line-height: 1.5;
            color: #2b2a2b;
            -webkit-font-smoothing: antialiased;
            font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI,  Roboto, "Droid Sans", "Helvetica Neue", sans-serif;
            /*font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Ubuntu, Droid Sans, Helvetica Neue, sans-serif;*/
        }

        .block {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            flex-direction: column;
            padding-top: 24px;
            -webkit-flex-wrap: nowrap;
            flex-wrap: nowrap;
        }

        .block > .title {
            text-align: left;
            background-color: #140c40;
            color: #ffffff;
            -webkit-border-radius: 3px;
            border-radius: 3px;
            line-height: 30px;
            padding: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }

        .block > .content {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -webkit-flex-direction: row;
            flex-direction: row;
            width: 100%;
            font-size: 13px;
            padding-left: 2px;
        }

        .block > .content > .column {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            flex-direction: column;
            width: 50%
        }

        .plain {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            flex-direction: column;
            text-align: justify;
            margin-bottom: 14px;
        }

        .plain > p {
            text-align: justify;
        }

        .blank {
            margin-top: 24px;
        }
    </style>
</head>
<body>
<div style="display:-webkit-box;
  display:-webkit-flex;
  display:flex; -webkit-box-orient:vertical; -webkit-box-direction:normal; -webkit-flex-direction:column; flex-direction:column; text-align: center; -webkit-box-align: center; -webkit-align-items: center; align-items: center; text-transform: uppercase; width: 100%">
    <h1 style="width: 80%; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; -webkit-box-align: center; -webkit-align-items: center; align-items: center;">
        {{ main.title }}
    </h1>

    {% if main.subtitle %}
    <h3 style="font-size: 10px"> {{ main.subtitle }} </h3>
    {% endif %}
</div>
{% block content %}
{% endblock content %}
</body>
</html>
