{% extends "contracts/base.html" %}
{% load replace %}
{% load optimize_image %}

{% block content %}

<div class="block">
    <h2 class="title">{{ contract.title }}</h2>
    {% include "contracts/block.html" with key=contract.id.key value=contract.id.value %}
</div>
<div class="block">
    <h2 class="title">{{ personal.title }}</h2>
    {% include "contracts/block.html" with key=personal.firstname.key value=personal.firstname.value %}
    {% include "contracts/block.html" with key=personal.lastname.key value=personal.lastname.value %}
    {% include "contracts/block.html" with key=personal.national_id.key value=personal.national_id.value %}
</div>
<div class="block">
    <h2 class="title">{{ declaration.title }}</h2>
    {% include "contracts/block.html" with key=declaration.date.key value=declaration.date.value %}
    {% include "contracts/block.html" with key=declaration.address.key value=declaration.address.value %}
</div>
<div class="block">
    <h2 class="title">{{ boxes.title }}</h2>
    {% for box in boxes.boxes %}
    <div style="padding-left:2px; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; padding-top: 12px; -webkit-flex-wrap: nowrap; flex-wrap: nowrap;">
        <h2 style="text-transform: uppercase; font-weight: 800">{{ service.title }}</h2>
        {% for content in box.values %}
        <div style="display: -webkit-box;
display: -webkit-flex;
display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; padding-left:2px;">
            <div style="display: -webkit-box;
display: -webkit-flex;
display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 50%; {% if content.child %}padding-left: 8px;{% endif %}">
                <p> {{ content.key }} </p>
            </div>
            <div style="display: -webkit-box;
display: -webkit-flex;
display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 50%">
                <p> {{ content.value }} </p>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endfor %}
</div>
<div class="block">
    <h2 class="title">{{ additional_services.title }}</h2>
    {% for service in additional_services.services %}
    <div style="padding-left:2px; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; padding-top: 12px; -webkit-flex-wrap: nowrap; flex-wrap: nowrap;">
        <h2 style="text-transform: uppercase; font-weight: 800">{{ service.title }}</h2>
        {% for content in service.values %}
        <div style="display: -webkit-box;
display: -webkit-flex;
display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; padding-left:2px;">
            <div style="display: -webkit-box;
display: -webkit-flex;
display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 50%; {% if content.child %}padding-left: 8px;{% endif %}">
                <p> {{ content.key }} </p>
            </div>
            <div style="display: -webkit-box;
display: -webkit-flex;
display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 50%">
                <p> {{ content.value }} </p>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endfor %}
</div>
<div class="block" style="page-break-before: always">
    {% if comment.comment %}
    <h2 class="title">{{ comment.title }}</h2>
    <p style="padding-left:2px; padding-top: 8px; font-size: 14px; text-align: justify">{{comment.comment|replace:"\\n"|linebreaksbr|safe}}</p>
    {% endif %}
</div>
<div class="block">
    <h2 class="title">{{ signature.title }}</h2>
    <div style="display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; width: 100%; padding-top: 12px; -webkit-flex-wrap: nowrap; flex-wrap: nowrap; text-align: center">
        <div style="display: -webkit-box;
  display: -webkit-flex;
  display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 50%;">
            <p style="font-weight: 800; text-decoration: underline; font-size: 16px"> {{ signature.customer.title }} </p>
             <img src="{{signature.customer.signature | optimize_image:'576,60'}}" style="width: 50%; object-fit: cover;">
        </div>
        <div style="display: -webkit-box;
  display: -webkit-flex;
  display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 50%;">
            <p style="font-weight: 800; text-decoration: underline; font-size: 16px"> {{ signature.company.title }} </p>
            <img src="{{signature.company.signature | optimize_image:'576,60'}}" style="width: 50%; object-fit: cover;">
        </div>
    </div>
</div>

{% endblock content %}
