{% extends "admin/base_site.html" %}

{% load static %}

{% block extrastyle %}{{ block.super }}
<!-- Environment notice -->
<style type="text/css"{% if request.csp_nonce %} nonce="{{ request.csp_nonce }}"{% endif %}>
    body:before {
        display: block;
        line-height: 35px;
        text-align: center;
        font-weight: bold;
        text-transform: uppercase;
        color: white;
        content: "{{ ENVIRONMENT }} ENVIRONMENT";
        background-color: {{ ENVIRONMENT_COLOR }};
        {% if True %}
            position: sticky;
            top: 0;
            z-index: 1000;
        {% endif %}
    }
</style>
<!-- Django countries flags -->
<link rel="stylesheet" href="{% static 'flags/sprite.css' %}">
{% endblock %}

{% block extrahead %}
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <link rel="shortcut icon" type="image/png" href="{% static 'favicon.ico' %}"/>
    <style>
        #header {
            color: var(--body-loud-color);
            background-color: #fff;
        }

        .dashboard #header {
            border-bottom: 1px solid var(--breadcrumbs-bg);
        }

        #header a:visited, #header a:link{
            color: #4760ff;
            font-weight: bold;
        }
        #login-form .submit-row > input {
            width: 100%;
            padding: 1rem 1.5rem;
        }
        :root {
            --primary: #4760ff;
            --secondary: #2d43d5;
            --link-fg: #5069ff;
            --button-hover-bg: #4960e8;
        }
        #container > .main {
            margin-top: 1rem;
        }
  </style>
{% endblock %}

{% block branding %}
<a href="{% url 'admin:index' %}"><img src="https://storage.googleapis.com/api_resources_box2box/logo.png" width="175px" background-color="#fff"/></a>

{% endblock %}
