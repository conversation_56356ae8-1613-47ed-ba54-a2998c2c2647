from django import forms
from django.contrib.admin import site
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import UserChangeForm as DjangoUserChangeForm
from django.contrib.auth.forms import \
    UserCreationForm as DjangoUserCreationForm

from contracts.models import WarehouseConfiguration
from riders.models import Device, RiderConfiguration, Team
from riders.widgets import RiderRawIdWidget

User = get_user_model()


class BaseRiderForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["rider"].widget = RiderRawIdWidget(
            rel=self.Meta.model._meta.get_field("rider").remote_field, admin_site=site
        )


class DeviceForm(BaseRiderForm):
    class Meta:
        model = Device
        fields = "__all__"


class TeamForm(BaseRiderForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["service"].required = False
        self.fields["rider"].required = False
        self.fields["task"].required = False

    class Meta:
        model = Team
        fields = "__all__"


class RiderConfigurationForm(BaseRiderForm):
    class Meta:
        model = RiderConfiguration
        fields = "__all__"


class WarehouseConfigurationForm(BaseRiderForm):
    class Meta:
        model = WarehouseConfiguration
        fields = "__all__"


class RiderCreationForm(DjangoUserCreationForm):
    class Meta:
        model = User
        fields = ("email", "first_name", "last_name", "is_rider", "is_active", "registration_country")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["email"].required = False
        self.fields["first_name"].required = True
        self.fields["last_name"].required = True
        self.fields["registration_country"].required = True

    def clean_email(self):
        email = self.cleaned_data["email"]
        try:
            return email.lower()
        except AttributeError:
            return None

    def save(self, commit=True):
        user = super().save(commit=False)
        user.username = user.email
        user.customer_first_name = user.first_name
        user.customer_last_name = user.last_name
        user.is_rider = True
        user.save()

        return user


class RiderForm(DjangoUserChangeForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["security_key"].required = False
        self.fields["registration_country"].required = False
