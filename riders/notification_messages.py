from typing import Optional

from firebase_admin import messaging


class MessageType:
    SERVICE_CREATED = "service_created"
    SERVICE_UPDATED = "service_updated"
    SERVICE_CANCELED = "service_canceled"
    SERVICE_ACCEPTED = "service_started"
    SERVICE_DISCARDED = "service_rejected"
    SERVICE_PROCESSED = "service_processed"
    TASK_CREATED = "task_created"
    TASK_UPDATED = "task_updated"
    TASK_REMOVED = "task_removed"


class Message(messaging.Message):
    type: MessageType = None

    def build(self, body: str, token: str, **kwargs):
        if self.type is None:
            return None

        metadata = kwargs.get("metadata") or {}
        silent = kwargs.get("silent", False)

        return self._build(
            title=self.type,
            body=body,
            token=token,
            data=metadata,
            silent=silent,
        )

    def _build(
        self, title: str, body: str, token: str, data: dict = None, silent: bool = False
    ) -> Optional[messaging.Message]:
        if not self.type or not data:
            return None

        data.update(**{
            "type": self.type,
            "title": title,
            "silent": str(silent).lower(),
            "body": body,
        })

        return messaging.Message(
            data=data,
            token=token.strip(),
            android=messaging.AndroidConfig(
                # Required for background/terminated app state
                priority="high",
                ttl=3600,
                # notification=messaging.AndroidNotification(
                #     title=title,
                #     body=body,
                #     visibility="public",
                #     default_vibrate_timings=True,
                #     # notification_count=1,  # Request a badge count
                #     sound="default",  # Play default notification sound
                #     channel_id="high_importance_channel"
                # ),
            ),
            apns=messaging.APNSConfig(
                payload=messaging.APNSPayload(
                    aps=messaging.Aps(
                        # Looks like this is mandatory when the app is quited
                        # alert=messaging.ApsAlert(
                        #     title=title,
                        #     body=body,
                        # ),
                        # Required for background/terminated app state
                        content_available=True,
                        # badge=1,
                        sound="default",
                    )
                )
            ),
        )


class ServiceCreatedMessage(Message):
    type = MessageType.SERVICE_CREATED


class ServiceUpdatedMessage(Message):
    type = MessageType.SERVICE_UPDATED


class ServiceCanceledMessage(Message):
    type = MessageType.SERVICE_CANCELED


class ServiceAcceptedMessage(Message):
    type = MessageType.SERVICE_ACCEPTED


class ServiceDiscardedMessage(Message):
    type = MessageType.SERVICE_DISCARDED


class TaskMessage(Message):
    def build(self, body: str, token: str, **kwargs):
        if self.type is None:
            return None

        metadata = kwargs.get("metadata") or {}
        silent = kwargs.get("silent", False)

        return self._build(
            title=metadata.get("title"),
            body=body,
            token=token,
            data=metadata,
            silent=silent,
        )


class TaskCreatedMessage(TaskMessage):
    type = MessageType.TASK_CREATED


class TaskUpdatedMessage(TaskMessage):
    type = MessageType.TASK_UPDATED


class TaskRemovedMessage(TaskMessage):
    type = MessageType.TASK_REMOVED


class ServiceProcessedMessage(Message):
    type = MessageType.SERVICE_PROCESSED
