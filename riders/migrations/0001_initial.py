# Generated by Django 3.2.15 on 2023-02-07 08:43

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("contracts", "0042_service_calendar"),
        ("intranet", "0015_calendar"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Team",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[("not_sent", "Not sent"), ("Sent", "Sent")],
                        default="not_sent",
                        max_length=100,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "rider",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.service"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RiderToCalendar",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "calendar",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="intranet.calendar"),
                ),
                (
                    "rider",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Device",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("registration_id", models.TextField(blank=True, null=True)),
                (
                    "user",
                    models.OneToOneField(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
        ),
    ]
