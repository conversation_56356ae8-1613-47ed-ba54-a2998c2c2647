# Generated by Django 3.2.25 on 2025-06-05 09:03

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("riders", "0017_auto_20250605_0857"),
    ]

    operations = [
        migrations.AddField(
            model_name="device",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="device",
            name="last_used",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name="device",
            name="type",
            field=models.CharField(
                blank=True,
                choices=[("android", "Android"), ("ios", "iOS")],
                default="android",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="device",
            name="rider",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddConstraint(
            model_name="device",
            constraint=models.UniqueConstraint(fields=("rider", "registration_id"), name="unique_device"),
        ),
    ]
