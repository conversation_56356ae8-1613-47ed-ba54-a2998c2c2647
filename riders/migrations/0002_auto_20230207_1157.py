# Generated by Django 3.2.15 on 2023-02-07 10:57

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("riders", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="RiderConfiguration",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("google_api_key", models.TextField(blank=True, null=True)),
                (
                    "rider",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="RiderToCalendar",
        ),
    ]
