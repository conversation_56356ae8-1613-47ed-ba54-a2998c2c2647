from django.contrib import admin
from django.utils.translation import gettext_lazy as _


class IsServiceOrTaskFilter(admin.SimpleListFilter):
    # Human-readable title which will be displayed in the right admin sidebar just above the filter options.
    title = _("Linked")

    # Parameter for the filter that will be used in the URL query.
    parameter_name = "service_or_task"

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return [
            ("service", _("Service")),
            ("task", _("Task")),
        ]

    def queryset(self, request, queryset):
        if self.value() == "service":
            return queryset.filter(service__isnull=False).distinct()

        if self.value() == "task":
            return queryset.filter(task__isnull=False).distinct()

        return queryset.distinct()
