from django.db.models import Q
from django.utils import timezone
from firebase_admin.exceptions import FirebaseError
from firebase_admin.messaging import UnregisteredError

from riders.notification_messages import (MessageType, ServiceAcceptedMessage,
                                          ServiceCanceledMessage,
                                          ServiceCreatedMessage,
                                          ServiceDiscardedMessage,
                                          ServiceProcessedMessage,
                                          ServiceUpdatedMessage,
                                          TaskCreatedMessage,
                                          TaskRemovedMessage,
                                          TaskUpdatedMessage)
from webhooks.clients.firebase import fcm_client


def _notify_message(teams, message_class, body, metadata, silent=False):
    from riders.models import Device

    if not teams:
        return None

    devices = Device.objects.filter(rider__in=[team.rider.id for team in teams]).select_related("rider")
    if not devices:
        return None

    messages = []

    for device in devices:
        message = message_class().build(
            body=body,
            token=device.registration_id,
            metadata=metadata,
            silent=silent,
        )
        message.rider_id = device.rider.id

        messages.append(message)

    delete_filter = Q()

    for message in messages:
        try:
            fcm_client.send_message(message)
        except UnregisteredError:
            delete_filter |= Q(registration_id=message.token, rider_id=message.rider_id)
        except FirebaseError:
            pass

    if bool(delete_filter):
        Device.objects.filter(delete_filter).delete()

    return None


def notify_service_created(instance):
    metadata = {
        "id": str(instance.service.id),
        "address": str(instance.service.address),
        "contract_id": str(instance.service.contract_id),
        "service_type": "today",
        "created_at": str(instance.service.created_at),
        "sent_at": str(timezone.now()),
    }
    from riders.models import Team

    team = Team.objects.filter(service=instance.service)
    _notify_message(team, ServiceCreatedMessage, MessageType.SERVICE_CREATED, metadata)


def notify_service_updated(instance):
    metadata = {
        "id": str(instance.service.id),
        "address": str(instance.service.address),
        "contract_id": str(instance.service.contract_id),
        "service_type": "today",
        "created_at": str(instance.service.created_at),
        "sent_at": str(timezone.now()),
    }
    from riders.models import Team

    team = Team.objects.filter(service=instance.service)
    _notify_message(team, ServiceUpdatedMessage, MessageType.SERVICE_UPDATED, metadata)


def notify_service_canceled(instance):
    metadata = {
        "id": str(instance.service.id),
        "address": str(instance.service.address),
        "contract_id": str(instance.service.contract_id),
        "is_upcoming": str(instance.start_time > timezone.now()).lower(),
        "sent_at": str(timezone.now()),
    }
    from riders.models import Team

    team = Team.objects.filter(service=instance.service)
    _notify_message(team, ServiceCanceledMessage, MessageType.SERVICE_CANCELED, metadata)


def notify_service_accepted(instance, rider):
    metadata = {
        "id": str(instance.service.id),
        "address": str(instance.service.address),
        "contract_id": str(instance.service.contract_id),
        "rider_name": str(rider.get_full_name() if rider else ""),
        "sent_at": str(timezone.now()),
    }
    from riders.models import Team

    team = Team.objects.filter(service=instance.service).exclude(rider=rider)
    _notify_message(team, ServiceAcceptedMessage, MessageType.SERVICE_ACCEPTED, metadata)


def notify_service_discarded(instance, rider):
    metadata = {
        "id": str(instance.service.id),
        "address": str(instance.service.address),
        "contract_id": str(instance.service.contract_id),
        "rider_name": str(rider.get_full_name() if rider else ""),
        "sent_at": str(timezone.now()),
    }

    from riders.models import Team

    team = Team.objects.filter(service=instance.service).exclude(rider=rider)
    _notify_message(team, ServiceDiscardedMessage, MessageType.SERVICE_DISCARDED, metadata)


def notify_task_created(instance):
    metadata = {
        "id": str(instance.task.id),
        "title": str(instance.title),
        "is_upcoming": str(instance.start_time > timezone.now()).lower(),
        "sent_at": str(timezone.now()),
    }

    from riders.models import Team

    team = Team.objects.filter(task=instance.task)
    _notify_message(team, TaskCreatedMessage, MessageType.TASK_CREATED, metadata)


def notify_task_updated(instance):
    metadata = {
        "id": str(instance.task.id),
        "title": str(instance.title),
        "is_upcoming": str(instance.start_time > timezone.now()).lower(),
        "sent_at": str(timezone.now()),
    }

    from riders.models import Team

    team = Team.objects.filter(task=instance.task)
    _notify_message(team, TaskUpdatedMessage, MessageType.TASK_UPDATED, metadata)


def notify_task_removed(instance):
    metadata = {
        "id": str(instance.task.id),
        "title": str(instance.title),
        "is_upcoming": str(instance.start_time > timezone.now()).lower(),
        "sent_at": str(timezone.now()),
    }

    from riders.models import Team

    team = Team.objects.filter(task=instance.task)
    _notify_message(team, TaskRemovedMessage, MessageType.TASK_REMOVED, metadata)


def notify_service_processed(instance):
    metadata = {
        "id": str(instance.service.id),
        "contract_id": str(instance.service.contract_id),
    }

    from riders.models import Team

    team = Team.objects.filter(service=instance.service)
    _notify_message(team, ServiceProcessedMessage, MessageType.SERVICE_PROCESSED, metadata, silent=True)
