import base64
import uuid

from allauth.account.models import EmailAddress
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models.signals import post_save
from django.utils import timezone
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _

from core.api_keys.google import APIKeyForAPIGatewayWrapper
from core.emails.message import EmailMessage
from core.encoders import CustomDjangoJSONEncoder
from core.service_accounts.google import ServiceAccountWrapper
from riders.exceptions import RemoteResourcesError
from riders.managers import RiderManager
from riders.receivers import (create_configuration_entries,
                              send_activation_email_to_rider)

User = get_user_model()


class Device(models.Model):
    ANDROID = "android"
    IOS = "ios"

    DEVICE_CHOICES = (
        (ANDROID, _("Android")),
        (IOS, _("iOS")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    registration_id = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=100, choices=DEVICE_CHOICES, default=ANDROID, null=True, blank=True)

    # Internals
    created_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(default=timezone.now)

    # Foreign key
    rider = models.ForeignKey("users.user", null=True, on_delete=models.CASCADE)

    class Meta:
        constraints = [models.UniqueConstraint(fields=["rider", "registration_id"], name="unique_device")]


class Team(models.Model):
    NOT_SENT = "not_sent"
    SENT = "Sent"

    STATUS_CHOICES = (
        (NOT_SENT, _("Not sent")),
        (SENT, _("Sent")),
    )

    SET = "set"
    DISCARDED = "discarded"
    TRACKING_STATUS_CHOICES = ((SET, _("Set")), (DISCARDED, _("Discarded")))

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default=NOT_SENT, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True, help_text=_("Last time that notification was sent"))
    accepted_at = models.DateTimeField(
        null=True, blank=True, help_text=_("Time where service was accepted by the rider")
    )
    discarded_at = models.DateTimeField(
        null=True, blank=True, help_text=_("Flag to hide a service from pending services in ")
    )
    raw_data = models.JSONField(
        encoder=CustomDjangoJSONEncoder,
        null=True,
        blank=True,
        help_text=_("Raw response from app, used to allow different responses for tasks or whatever related FK"),
    )
    warehouse_movement_status = models.CharField(
        max_length=100,
        choices=TRACKING_STATUS_CHOICES,
        null=True,
        blank=True,
        help_text=_("Flag to know if this rider has submitted the inventory location on a given warehouse"),
    )

    rider = models.ForeignKey("users.User", null=True, on_delete=models.CASCADE)
    service = models.ForeignKey("contracts.Service", null=True, on_delete=models.CASCADE)
    task = models.ForeignKey("intranet.Task", null=True, on_delete=models.CASCADE)

    class Meta:
        unique_together = [["rider", "service"]]

    def send_push_notification(self):
        """Send a FCM push notification"""

        if not self.rider:
            raise ValidationError(_("Team has no rider which it is mandatory to send push notifications"))

        if not self.service:
            raise ValidationError(_("Service linked is mandatory to send push notifications"))

        from core.notifications import SimpleFCMNotification

        notification_service = SimpleFCMNotification()
        notification_service.notify(self.service)

        # Update this team accordingly
        self.status = self.SENT
        self.sent_at = timezone.now()
        self.save(update_fields=["status", "sent_at"])

    @property
    def elapsed_time(self):
        if self.accepted_at and self.sent_at:
            return (self.sent_at - self.accepted_at).total_seconds()
        return None


class RiderConfiguration(models.Model):
    NOT_CREATED = "not_created"
    ENABLED = "enabled"
    DISABLED = "disabled"

    STATUS_CHOICES = (
        (NOT_CREATED, _("Not created")),
        (ENABLED, _("Enabled")),
        (DISABLED, _("Disabled")),
    )

    # Rider configuration as a whole status
    READY = "ready"
    MANUAL = "manual"
    PENDING = "pending"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    google_api_key = models.TextField(blank=True, null=True)
    google_api_key_uid = models.TextField(blank=True, null=True)

    # Service accounts
    service_account_email = models.CharField(max_length=200, null=True, blank=True)
    service_account_key = models.JSONField(null=True, blank=True, encoder=CustomDjangoJSONEncoder)
    service_account_key_name = models.TextField(blank=True, null=True)

    service_account_status = models.CharField(
        max_length=100, choices=STATUS_CHOICES, default=NOT_CREATED, null=True, blank=True
    )

    setup_finished = models.BooleanField(
        default=False, help_text=_("Manual flag to set when service account has the needed roles granted")
    )

    # Primary keys
    rider = models.OneToOneField("users.User", null=True, on_delete=models.CASCADE)

    @property
    def service_account_name(self):
        """
        Encode pk as base64 to avoid hitting the requirement of Google for service account names <= 30 characters.
        Be careful: this method **is not deterministic**, so every call to this method will return a new name.
        """
        unique_key = uuid.uuid4().bytes
        encoded_id = base64.b32encode(unique_key).decode("utf-8").rstrip("=")
        return f"{settings.ENVIRONMENT[0]}-{encoded_id}"

    @property
    def status(self):
        if self.google_api_key and self.google_api_key_uid and self.service_account_status == self.ENABLED:
            if self.setup_finished:
                return self.READY
            else:
                return self.MANUAL

        return self.PENDING

    @property
    def api_key_configured(self):
        return bool(self.google_api_key and self.google_api_key_uid)

    def delete_remote_keys(self):
        """Remove remote api key and service account when destroying the rider thru api endpoint"""

        api_key_wrapper = APIKeyForAPIGatewayWrapper()
        service_account_wrapper = ServiceAccountWrapper()

        if self.google_api_key:
            try:
                api_key_wrapper.delete(self.google_api_key_uid, timeout=10)

                self.google_api_key = self.google_api_key_uid = None
                self.save(update_fields=["google_api_key", "google_api_key_uid"])
            except Exception as err:
                raise RemoteResourcesError("Error deleting the api key") from err

        if self.service_account_email:
            try:
                service_account_wrapper.delete_service_account(self.service_account_email)

                self.service_account_key = None
                self.service_account_email = None
                self.service_account_key_name = None
                self.setup_finished = False
                self.service_account_status = RiderConfiguration.NOT_CREATED
                self.save(
                    update_fields=[
                        "service_account_key",
                        "service_account_email",
                        "service_account_key_name",
                        "setup_finished",
                        "service_account_status",
                    ]
                )
            except Exception as err:
                raise RemoteResourcesError("Error deleting the service account") from err


class RiderProxy(User):
    """Proxy class to add new methods only for intranet app"""

    ACCOUNT_ACTIVE = "active"
    ACCOUNT_DISABLED = "disabled"
    ACCOUNT_PENDING = "pending"

    class Meta:
        proxy = True
        verbose_name = _("User")

    objects = RiderManager()

    @property
    def activate_account_url(self):
        # Encode url
        encoded_id = urlsafe_base64_encode(force_bytes(self.pk))
        token = default_token_generator.make_token(self)

        return f"{settings.INTRANET_URL}/app/activate/{encoded_id}/{token}/?hl={self.email_domain}"

    def send_activation_email(self):
        """Send the activation email to users"""

        # Create the email address to check if is verified
        EmailAddress.objects.get_or_create(user=self, email__iexact=self.email, defaults={"email": self.email})

        # Activate locale from user
        activate(self.email_domain)
        message = EmailMessage(
            subject=_("{prefix} Activate your account to get access in the Riders app").format(
                prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX,
            ),
            to=[
                self.email,
            ],
            bcc=[
                "<EMAIL>",
            ],
            sender=self.email_domain,
            template="rider_account_activation_email",
            context={"user_name": self.first_name, "url": self.activate_account_url},
        )

        # Send email
        return message.send() > 0

    @property
    def is_account_verified(self):
        email_address_set = "emailaddress_set"
        if hasattr(self, "emailaddress_attr"):
            email_address_set = "emailaddress_attr"

        email_address_set = getattr(self, email_address_set)

        try:
            return email_address_set[0].verified
        except IndexError:
            return False
        except TypeError:
            return email_address_set.is_verified(self.email)

    @property
    def status(self):
        if not self.is_active:
            if not self.is_account_verified:
                return self.ACCOUNT_PENDING
            return self.ACCOUNT_DISABLED
        return self.ACCOUNT_ACTIVE

    @property
    def has_finished_service_or_task(self):
        from contracts.models import Service

        return (
            Service.objects.filter(submitted_by_id=self.id, status=Service.FINISHED).exists()
            or Team.objects.filter(rider_id=self.id, sent_at__isnull=False, task__isnull=False).exists()
        )


post_save.connect(send_activation_email_to_rider, sender=RiderProxy)
post_save.connect(create_configuration_entries, sender=RiderProxy)
