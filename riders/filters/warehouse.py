from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from contracts.models import Warehouse


class WarehouseFilter(filters.FilterSet):
    by_tracking_options = filters.BooleanFilter(
        field_name="by_tracking_options",
        method="filter_by_tracking_options",
        help_text=_("Bring warehouses where tracking options are set"),
    )

    by_city = filters.CharFilter(
        field_name="by_city",
        method="filter_by_city",
        help_text=_("Bring warehouses that belong to a given city"),
    )

    class Meta:
        model = Warehouse
        fields = ("by_tracking_options", "by_city")

    def filter_by_tracking_options(self, queryset, name, value):
        filters = {
            True: {"tracking_inventory__len__gt": 0},
            False: {
                "tracking_inventory__len": 0,
            },
        }

        # Ensure the value is a valid boolean value before filtering
        if isinstance(value, bool):
            queryset = queryset.filter(**filters[value])

        return queryset.distinct()

    def filter_by_city(self, queryset, name, value):
        if value:
            queryset = queryset.filter(city_id=value)

        return queryset.distinct()
