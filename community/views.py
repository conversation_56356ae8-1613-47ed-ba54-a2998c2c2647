from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets

from community.pagination import CommunityCampaignsPagination
from community.serializers import (CommentsSerializer,
                                   CommunityQuerySetSerializer,
                                   PublicCampaignCommentsSerializer,
                                   PublicCampaignConcreteSerializer,
                                   PublicCampaignPartialUpdateSerializer,
                                   PublicCampaignSerializer)
from core.models import CampaignConfiguration
from core.serializers import (CommentBaseSerializer, CommentDeleteSerializer,
                              CommentMutationSerializer,
                              CommentRetrieveParamsSerializer,
                              CommentSerializer)
from core.utils import (get_openapi_cache_header, get_openapi_error_response,
                        get_openapi_response)
from core.views import CommentsView


class CampaignConfigurationViewSet(viewsets.ModelViewSet):
    """Endpoints to be consumed by public interface (i.e. community)"""

    serializer_class = PublicCampaignSerializer
    permission_classes = (permissions.AllowAny,)
    queryset = CampaignConfiguration.objects.all()
    pagination_class = CommunityCampaignsPagination
    http_method_names = ["get", "patch", "delete"]

    def get_queryset(self):
        """Filter queryset by user"""
        queryset = super(CampaignConfigurationViewSet, self).get_queryset()

        user_id = self.request.query_params.get("user_id", None)

        # For PATCH/DELETE, must be authenticated session
        if self.request.method in ["PATCH", "DELETE"]:
            user_id = self.request.user

        return queryset.filter(user_id=user_id)

    def get_serializer_class(self):
        if self.action == "retrieve":
            return PublicCampaignConcreteSerializer
        elif self.action == "partial_update":
            return PublicCampaignPartialUpdateSerializer
        return self.serializer_class

    @swagger_auto_schema(
        operation_summary="List campaigns",
        operation_description="Get list of public campaigns.",
        query_serializer=CommunityQuerySetSerializer,
        security=[],
    )
    def list(self, request, *args, **kwargs):
        return super(CampaignConfigurationViewSet, self).list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Read campaign [C]",
        operation_description=(
            "Get public data from campaigns. "
            "This is a cached endpoint for **category** response with 6 months of cache. "
        ),
        query_serializer=CommunityQuerySetSerializer,
        security=[],
        manual_parameters=[
            get_openapi_cache_header(
                "X-API-cache-eviction",
                description="""
        Evict cached data from redis.
        """,
            )
        ],
    )
    def retrieve(self, request, *args, **kwargs):
        return super(CampaignConfigurationViewSet, self).retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Update campaign",
        operation_description=(
            "Update the campaign public configuration. This endpoint is intended to be called "
            "through private panel, so is **mandatory** to send an authenticated request."
        ),
        permission_classes=(permissions.IsAuthenticated,),
    )
    def partial_update(self, request, *args, **kwargs):
        return super(CampaignConfigurationViewSet, self).partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Delete campaign from public view",
        operation_description=(
            "Remove campaign from public view. This endpoint is intended to be called "
            "through private panel, so is **mandatory** to send an authenticated request."
        ),
        permission_classes=(permissions.IsAuthenticated,),
    )
    def destroy(self, request, *args, **kwargs):
        return super(CampaignConfigurationViewSet, self).destroy(request, *args, **kwargs)


class PublicCommentsView(CommentsView):
    """Endpoints for public comments"""

    permissions_classes_by_action = {
        "GET": (permissions.AllowAny,),
        "POST": (permissions.IsAuthenticated,),
        "PATCH": (permissions.IsAuthenticated,),
        "DELETE": (permissions.IsAuthenticated,),
    }

    http_method_names = ["get", "patch", "post", "delete"]

    def get_permissions(self):
        return [permission() for permission in self.permissions_classes_by_action[self.request.method]]

    @swagger_auto_schema(
        operation_summary="List comments",
        operation_description=(
            "Read a comment for a campaign. This is a paginated response and, by default, "
            "will fetch **20 comments** per batch at most."
        ),
        query_serializer=CommentRetrieveParamsSerializer,
        security=[],
        responses={
            status.HTTP_200_OK: PublicCampaignCommentsSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                CommentRetrieveParamsSerializer, "Error reading comment"
            ),
        },
    )
    def get(self, request, format=None):
        """Override base comments client and adding public argument to reference to the right database"""
        return super(PublicCommentsView, self).get(request, format, public=True)

    @swagger_auto_schema(
        operation_summary="Create comment",
        operation_description="Create a comment for a campaign",
        request_body=CommentSerializer,
        security=[],
        responses={
            status.HTTP_200_OK: CommentsSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(CommentSerializer, "Error posting comment"),
        },
    )
    def post(self, request, format=None):
        return super(PublicCommentsView, self).post(request, format, public=True)

    @swagger_auto_schema(
        operation_summary="Update comment",
        operation_description="Update a comment for a given campaign, adset or ad",
        request_body=CommentMutationSerializer,
        security=[],
        responses={
            status.HTTP_200_OK: CommentsSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(CommentBaseSerializer, "Error updating comment"),
        },
    )
    def patch(self, request, format=None):
        return super(PublicCommentsView, self).patch(request, format, public=True)

    @swagger_auto_schema(
        operation_summary="Delete comment",
        operation_description="Delete a comment for a given campaign, adset or ad",
        request_body=CommentDeleteSerializer,
        security=[],
        responses={
            status.HTTP_204_NO_CONTENT: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error deleting comment"),
        },
    )
    def delete(self, request, format=None):
        return super(PublicCommentsView, self).delete(request, format, public=True)
