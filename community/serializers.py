from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from campaigns.serializers.facebook_campaign_serializers import (
    OverviewChartSerializer, OverviewTableSerializer)
from core.mixins import StringErrorsMixin
from core.models import CampaignConfiguration

User = get_user_model()


class UserProfileSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get user data"""

    full_name = serializers.CharField(source="get_full_name", read_only=True)

    class Meta:
        model = User
        fields = ("id", "full_name", "alias", "avatar")


class CommentsSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to comments response"""

    _id = serializers.CharField(read_only=True, allow_null=True)
    date_created = serializers.DateTimeField(read_only=True, allow_null=True)
    user = serializers.SerializerMethodField(read_only=True)
    message = serializers.CharField(read_only=True, allow_null=True)

    @swagger_serializer_method(UserProfileSerializer)
    def get_user(self, obj):
        return UserProfileSerializer(User.objects.filter(pk=obj["user_id"]).first()).data


class PublicCampaignCommentsSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer for campaign public comments"""

    batch_size = serializers.IntegerField(read_only=True, allow_null=True)
    next_page = serializers.CharField(read_only=True, allow_null=True)
    prev_page = serializers.CharField(read_only=True, allow_null=True)
    response = CommentsSerializer(read_only=True, allow_null=True, many=True)


class PublicCampaignListMetricsSerializer(StringErrorsMixin, serializers.Serializer):
    """Campaign listview serializer to get some kpis from hiperion and send to frontend"""

    chart = serializers.JSONField(
        read_only=True,
        help_text=_("Compliant format with highcharts. For more information, please, visit hiperion api doc."),
    )
    total_comments = serializers.IntegerField(default=0, read_only=True)
    total_spend = serializers.FloatField(default=0, read_only=True)
    currency = serializers.CharField(read_only=True)


class PublicCampaignSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get the full list of campaigns"""

    name = serializers.CharField(source="campaign.name", allow_null=True, allow_blank=True, read_only=True)
    objective = serializers.CharField(source="campaign.objective", allow_blank=True, allow_null=True, read_only=True)
    created_at = serializers.CharField(source="campaign.created_at", allow_null=True, allow_blank=True, read_only=True)
    metrics = PublicCampaignListMetricsSerializer(source="overview_metrics", allow_null=True, read_only=True)

    class Meta:
        model = CampaignConfiguration
        fields = (
            "id",
            "campaign",
            "description",
            "name",
            "objective",
            "created_at",
            "metrics",
        )


class PublicCampaignConcreteSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Basic serializer to get concrete campaign from database"""

    business_name = serializers.CharField(read_only=True, allow_null=True, allow_blank=True)
    campaign_name = serializers.CharField(read_only=True, allow_null=True, allow_blank=True)
    objective = serializers.CharField(read_only=True, allow_null=True, source="campaign.objective")
    comments = PublicCampaignCommentsSerializer(read_only=True, allow_null=True)
    overview_table = OverviewTableSerializer(read_only=True, allow_null=True)
    overview_chart = OverviewChartSerializer(read_only=True, allow_null=True)
    category = serializers.CharField(read_only=True, help_text=_("Facebook page category"))
    campaign_id = serializers.CharField(read_only=True, source="campaign.id")
    total_comments = serializers.IntegerField(read_only=True, default=0)

    class Meta:
        model = CampaignConfiguration
        fields = (
            "id",
            "business_name",
            "campaign_name",
            "comments",
            "overview_table",
            "overview_chart",
            "kpis",
            "comments_allow",
            "description",
            "category",
            "objective",
            "campaign_id",
            "total_comments",
        )
        extra_kwargs = {"kpis": {"help_text": _("Basic kpis to display in detailed view")}}


class PublicCampaignPartialUpdateSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Basic serializer to update campaign configuration"""

    class Meta:
        model = CampaignConfiguration
        fields = (
            "comments_allow",
            "anonymize_business_name",
            "anonymize_campaign_name",
            "show_campaign_hierarchy",
            "show_campaign_chart",
            "description",
            "kpis",
        )
        extra_kwargs = {"kpis": {"source": "basic_kpis"}}


class CommunityQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check user_id type and return empty queryset if is not valid"""

    user_id = serializers.UUIDField(
        required=True,
        allow_null=False,
        help_text=_("If is not provided, won't return anything"),
    )
