from datetime import datetime
from unittest.mock import MagicMock, PropertyMock, patch

import pytest
import pytz
from allauth.socialaccount.models import SocialApp, SocialToken
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.test import override_settings
from django.utils.translation import gettext_lazy as _
from facebook_business.exceptions import FacebookRequestError
from pytest import mark

from business.models import Business, BusinessSocialAccount, Organization
from campaigns.models import (CampaignMedia, FacebookAd, FacebookAdset,
                              FacebookBusinessAccount, FacebookCampaign,
                              FacebookLeadForm)
from users.auth import SocialAccount

pytestmark = mark.django_db
User = get_user_model()


@pytest.fixture
def user():
    yield User.objects.create_user(
        first_name="calimero", last_name="molon", email="<EMAIL>", password="passy"
    )


@pytest.fixture(autouse=True)
def organization():
    yield Organization.objects.create(
        name="Test Organization",
        type_organization="business",
    )


@pytest.fixture
def business(organization):
    yield Business.objects.create(
        name="Test business",
        organization=organization,
    )


@pytest.fixture(autouse=True)
def facebook_business_account(business):
    yield FacebookBusinessAccount.objects.create(page_id=*********, ad_account_id="act_*********", business=business)


@pytest.fixture(autouse=True)
def facebook_social_account(user):
    yield SocialAccount.objects.create(user=user, provider="facebook", uid=**********)


@pytest.fixture(autouse=True)
def social_app_facebook():
    yield SocialApp.objects.create(provider="facebook", name="test app", secret="123")


@pytest.fixture(autouse=True)
def social_token_facebook(social_app_facebook, facebook_social_account):
    yield SocialToken.objects.create(app=social_app_facebook, account=facebook_social_account, token="123")


@pytest.fixture(autouse=True)
def business_social_account_facebook(business, user, facebook_social_account):
    yield BusinessSocialAccount.objects.create(
        business=business, user=user, social_account=facebook_social_account, uid=facebook_social_account.uid
    )


@pytest.fixture
def campaign(facebook_business_account):
    yield FacebookCampaign.objects.create(
        name="FacebookCampaign",
        objective=FacebookCampaign.CONVERSIONS,
        facebook_business_account=facebook_business_account,
        conversion_event_type=FacebookCampaign.PURCHASE,
        pixel_id="********",
        budget=10000,
        ad_account_id="act_4555889756",
    )


@pytest.fixture
def adset():
    def _adset(**params):
        return FacebookAdset.objects.create(**params)

    return _adset


@pytest.fixture
def ad():
    def _ad(**params):
        return FacebookAd.objects.create(**params)

    return _ad


@pytest.fixture(autouse=True)
def media_image(business):
    yield CampaignMedia.objects.create(
        provider=CampaignMedia.FACEBOOK,
        media_url="https://fake.url",
        business=business,
    )


@pytest.fixture(autouse=True)
def media_video(business):
    yield CampaignMedia.objects.create(
        provider=CampaignMedia.FACEBOOK, media_url="https://fake.url", business=business, video_id="*************"
    )


@pytest.fixture
def facebook_request_error():
    yield FacebookRequestError(
        message="error",
        request_context={},
        http_status="200",
        http_headers={},
        body={"error": {"error_data": {"blame_field_specs": ""}, "error_user_msg": "error message"}},
    )


class TestFacebookLeadForm:
    def test_save_without_name_right_counter(self, facebook_business_account):
        lead_form = FacebookLeadForm.objects.create(
            date_added=datetime(2021, 3, 3), facebook_business_account=facebook_business_account
        )

        assert lead_form.name == "ADTUO-leads-210303 form"

        lead_form = FacebookLeadForm.objects.create(
            date_added=datetime(2021, 3, 3), facebook_business_account=facebook_business_account
        )

        assert lead_form.name == "ADTUO-leads-210303 form-1"

    def test_save_with_name_no_counter(self, facebook_business_account):
        lead_form = FacebookLeadForm.objects.create(
            name="LeadForm", facebook_business_account=facebook_business_account
        )

        assert lead_form.name == "LeadForm"

        lead_form = FacebookLeadForm.objects.create(
            name="LeadForm", facebook_business_account=facebook_business_account
        )

        assert lead_form.name == "LeadForm"


class TestFacebookCampaign:
    def test_save_imported_campaign(self, facebook_business_account):
        with patch(
            "core.async_tasks.sync_imported_facebook_campaigns.sync_imported_facebook_campaigns_asynchronously"
        ) as mock_sync:
            with patch(
                "campaigns.models.receivers.post_refresh_campaign_lifetime_insights_in_data_warehouse_asynchronously"
            ) as mock_lifetime_refresh:
                with patch(
                    "campaigns.models.receivers.post_refresh_campaign_daily_insights_in_data_warehouse_asynchronously"
                ) as mock_daily_refresh:
                    with patch(
                        "campaigns.models.receivers.post_breakdowns_insights_in_data_warehouse_asynchronously"
                    ) as mock_breakdowns:
                        with patch(
                            "campaigns.models.receivers.post_send_campaign_to_ai_service_asynchronously"
                        ) as mock_ai:
                            campaign = FacebookCampaign.objects.create(
                                objective=FacebookCampaign.POST_ENGAGEMENT,
                                facebook_business_account=facebook_business_account,
                                date_added=datetime(2021, 3, 3),
                                remote_id=123456,
                                imported=True,
                            )

                            mock_sync.assert_called_once_with(
                                "123",
                                str(campaign.pk),
                                "123456",
                                "act_*********",
                                str(campaign.facebook_business_account.business.pk),
                            )

                            mock_lifetime_refresh.assert_called_once_with(
                                access_token="123",
                                campaign_id=123456,
                                objective="POST_ENGAGEMENT",
                                conversion_event_type=None,
                                custom_conversion_event_id=None,
                                business_id=str(campaign.facebook_business_account.business.pk),
                                api_campaign_id=str(campaign.pk),
                                ad_account_id="act_*********",
                                websocket_id=None,
                            )

                            mock_daily_refresh.assert_called_once_with(
                                access_token="123",
                                campaign_id=123456,
                                objective="POST_ENGAGEMENT",
                                conversion_event_type=None,
                                custom_conversion_event_id=None,
                                business_id=str(campaign.facebook_business_account.business.pk),
                                api_campaign_id=str(campaign.pk),
                                ad_account_id="act_*********",
                                websocket_id=None,
                            )

                            mock_breakdowns.assert_called_once_with(
                                access_token="123",
                                campaign_id=123456,
                                objective="POST_ENGAGEMENT",
                                business_id=str(campaign.facebook_business_account.business.pk),
                                api_campaign_id=str(campaign.pk),
                                ad_account_id="act_*********",
                            )

                            mock_ai.assert_called_once_with(
                                str(campaign.facebook_business_account.business.pk),
                                123456,
                                str(campaign.pk),
                                "POST_ENGAGEMENT",
                                None,
                            )

    def test_save_imported_campaign_no_call_signals(self, facebook_business_account):
        with patch(
            "core.async_tasks.sync_imported_facebook_campaigns.sync_imported_facebook_campaigns_asynchronously"
        ) as mock_sync:
            with patch("campaigns.models.receivers.post_send_campaign_to_ai_service_asynchronously") as mock_ai:
                with patch(
                    "business.models.business.FacebookBusinessAccount.tokens", new_callable=PropertyMock
                ) as mock_tokens:
                    with patch("campaigns.models.receivers.logger") as mock_logger:
                        mock_tokens.return_value = BusinessSocialAccount.objects.none()

                        campaign = FacebookCampaign.objects.create(
                            objective=FacebookCampaign.POST_ENGAGEMENT,
                            facebook_business_account=facebook_business_account,
                            date_added=datetime(2021, 3, 3),
                            remote_id=123456,
                            imported=True,
                        )
                        campaign.refresh_from_db()

                        assert campaign.has_errors is True
                        assert campaign.errors == {"error_msg": _("No access token found linked to business ")}
                        assert not mock_sync.called
                        mock_ai.assert_called_once_with(
                            str(campaign.facebook_business_account.business.pk),
                            123456,
                            str(campaign.pk),
                            "POST_ENGAGEMENT",
                            None,
                        )
                        mock_logger.warning.assert_called_once_with(
                            "Error getting access token when getting campaign details from facebook"
                        )

    def test_save_without_name_default(self, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            objective=FacebookCampaign.POST_ENGAGEMENT,
            facebook_business_account=facebook_business_account,
            date_added=datetime(2021, 3, 3),
        )

        assert campaign.name == "ADTUO-Test business-ES-es-POST_ENGAGEMENT-210303 00:00"

    def test_save_with_name(self, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.POST_ENGAGEMENT,
            facebook_business_account=facebook_business_account,
            date_added=datetime(2021, 3, 3),
        )

        assert campaign.name == "FacebookCampaign"

    def test_discard_draft_changes_no_draft(self, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.POST_ENGAGEMENT,
            facebook_business_account=facebook_business_account,
        )

        restored_campaign = campaign.discard_draft_changes()

        assert campaign == restored_campaign

    def test_discard_draft_changes_ok(self, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.POST_ENGAGEMENT,
            facebook_business_account=facebook_business_account,
            budget=100,
        )

        campaign.draft = campaign.draft_changes
        campaign.budget = 200
        campaign.save()

        # Get restored campaign
        restored_campaign = FacebookCampaign.objects.get(id=campaign.pk).discard_draft_changes()

        assert campaign.draft != restored_campaign.draft
        assert campaign.budget != restored_campaign.budget
        assert restored_campaign.budget == 100

    def test_pomegranate_message_ok(self, campaign, adset, ad):
        # This is a CREATE adset
        adset_no_published = adset(
            **{
                "included_locations": [{"key": "ES", "name": "Spain", "type": "country", "country_name": "Spain"}],
                "genders": FacebookAdset.FEMALE,
                "age_min": 18,
                "age_max": 34,
                "optimization_goal": FacebookAdset.OFFSITE_CONVERSIONS,
                "billing_event": FacebookAdset.IMPRESSIONS,
                "custom_audiences": [{"id": "6198765675590", "name": "visitantes 90 días con exclusión (24/09/2020)"}],
                "start_time": datetime(2020, 1, 1, 10, 23).replace(tzinfo=pytz.utc),
                "name": "AdSet No Published",
                "campaign": campaign,
            }
        )

        # This is an UPDATE adset
        adset_published = adset(
            **{
                "included_locations": [{"key": "ES", "name": "Spain", "type": "country", "country_name": "Spain"}],
                "genders": FacebookAdset.BOTH,
                "age_min": 18,
                "age_max": 34,
                "optimization_goal": FacebookAdset.OFFSITE_CONVERSIONS,
                "billing_event": FacebookAdset.IMPRESSIONS,
                "custom_audiences": [{"id": "6198765675590", "name": "visitantes 90 días con exclusión (24/09/2020)"}],
                "start_time": datetime(2020, 1, 1, 10, 23).replace(tzinfo=pytz.utc),
                "name": "AdSet Published",
                "remote_id": "*********",
                "campaign": campaign,
            }
        )

        adset_published.draft = adset_published.draft_changes
        adset_published.age_min = 26
        adset_published.save()

        # This is a CREATE ad
        ad_image_no_published = ad(
            **{
                "name": "Ad Image No Published",
                "message": "Ad message",
                "title": "Ad title",
                "link": "https://fake.url",
                "utm_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                "adset": adset_no_published,
            }
        )

        ad_image_no_published.media.add(
            *CampaignMedia.objects.filter(video_id__isnull=True).values_list("pk", flat=True)
        )

        ad_video_no_published = ad(
            **{
                "name": "Ad Video No Published",
                "message": "Ad message",
                "title": "Ad title",
                "link": "https://fake.url",
                "utm_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                "adset": adset_no_published,
            }
        )

        ad_video_no_published.media.add(
            *CampaignMedia.objects.filter(video_id__isnull=False).values_list("pk", flat=True)
        )

        ad_carousel_no_published = ad(
            **{
                "name": "Ad Carousel No Published",
                "message": "Ad message",
                "title": "Ad title",
                "link": "https://fake.url",
                "utm_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                "adset": adset_no_published,
            }
        )

        ad_carousel_no_published.media.add(*CampaignMedia.objects.all().values_list("pk", flat=True))

        # This is an UPDATE message
        ad_image_published = ad(
            **{
                "name": "Ad Image Published",
                "message": "Ad message",
                "title": "Ad title",
                "link": "https://fake.url",
                "utm_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                "remote_id": "4915165198",
                "adset": adset_published,
            }
        )

        ad_image_published.media.add(*CampaignMedia.objects.filter(video_id__isnull=True).values_list("pk", flat=True))
        ad_image_published.draft = ad_image_published.draft_changes
        ad_image_published.title = "Ad title changed"
        ad_image_published.save()

        with patch("campaigns.models.facebook.uuid.uuid4", spec=MagicMock) as mock_uuid4:
            mock_uuid4.return_value = PropertyMock(hex="123aa5")
            assert campaign.pomegranate_message == {
                "adsets": {
                    "CREATE": [
                        {
                            str(adset_no_published.pk): {
                                "ads": {
                                    "CREATE": [
                                        {
                                            str(ad_image_no_published.pk): {
                                                "creative": {
                                                    "name": "Ad Image No Published AdCreative",
                                                    "url_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                                                },
                                                "internal_id": str(ad_image_no_published.pk),
                                                "link_data": {
                                                    "call_to_action": {"cta": "LEARN_MORE"},
                                                    "link": "https://fake.url",
                                                    "message": "Ad message",
                                                    "name": "Ad title",
                                                    "picture": "https://fake.url",
                                                    "type": "image",
                                                },
                                                "name": "Ad Image No Published",
                                                "pixel_id": "********",
                                            }
                                        },
                                        {
                                            str(ad_video_no_published.pk): {
                                                "creative": {
                                                    "name": "Ad Video No Published AdCreative",
                                                    "url_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                                                },
                                                "internal_id": str(ad_video_no_published.pk),
                                                "link_data": {
                                                    "call_to_action": {"cta": "LEARN_MORE"},
                                                    "link": "https://fake.url",
                                                    "message": "Ad message",
                                                    "name": "Ad title",
                                                    "picture": "https://fake.url",
                                                    "type": "image",
                                                },
                                                "name": "Ad Video No Published",
                                                "pixel_id": "********",
                                            }
                                        },
                                        {
                                            str(ad_carousel_no_published.pk): {
                                                "creative": {
                                                    "name": "Ad Carousel No Published AdCreative",
                                                    "url_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                                                },
                                                "internal_id": str(ad_carousel_no_published.pk),
                                                "link_data": {
                                                    "call_to_action": {"cta": "LEARN_MORE"},
                                                    "child_attachments": [
                                                        {
                                                            "call_to_action": {
                                                                "app_destination": None,
                                                                "cta": "LEARN_MORE",
                                                            },
                                                            "description": None,
                                                            "link": "https://fake.url",
                                                            "name": "Ad title",
                                                            "picture": "https://fake.url",
                                                            "type": "image",
                                                        },
                                                        {
                                                            "call_to_action": {
                                                                "app_destination": None,
                                                                "cta": "LEARN_MORE",
                                                            },
                                                            "description": None,
                                                            "link": "https://fake.url",
                                                            "name": "Ad title",
                                                            "picture": "https://fake.url",
                                                            "type": "image",
                                                        },
                                                    ],
                                                    "link": "https://fake.url",
                                                },
                                                "name": "Ad Carousel No Published",
                                                "pixel_id": "********",
                                            }
                                        },
                                    ]
                                },
                                "billing_event": "IMPRESSIONS",
                                "conversion_event_type": "PURCHASE",
                                "name": "AdSet No Published",
                                "optimization_goal": "OFFSITE_CONVERSIONS",
                                "page_id": *********,
                                "pixel_id": "********",
                                "start_time": "2020-01-01T10:23:00+00:00",
                                "targeting": {
                                    "age_max": 34,
                                    "age_min": 18,
                                    "custom_audiences": [
                                        {"id": "6198765675590", "name": "visitantes 90 días con exclusión (24/09/2020)"}
                                    ],
                                    "genders": [2],
                                    "geo_locations": {"countries": ["ES"]},
                                },
                            }
                        }
                    ],
                    "NONE": [],
                    "UPDATE": [
                        {
                            str(adset_published.pk): {
                                "ads": {
                                    "UPDATE": [
                                        {
                                            str(ad_image_published.pk): {
                                                "creative": {
                                                    "name": "Ad Image Published AdCreative",
                                                    "url_tags": "utm_source=facebook&utm_medium=paid&utm_campaign=",
                                                },
                                                "id": "4915165198",
                                                "internal_id": str(ad_image_published.pk),
                                                "link_data": {
                                                    "call_to_action": {"cta": "LEARN_MORE"},
                                                    "link": "https://fake.url",
                                                    "message": "Ad message",
                                                    "name": "Ad title changed",
                                                    "picture": "https://fake.url",
                                                    "type": "image",
                                                },
                                                "name": "Ad Image Published",
                                                "pixel_id": "********",
                                            }
                                        }
                                    ]
                                },
                                "id": "*********",
                                "internal_id": str(adset_published.pk),
                                "name": "AdSet Published",
                                "page_id": *********,
                                "targeting": {
                                    "age_max": 34,
                                    "age_min": 26,
                                    "custom_audiences": [
                                        {"id": "6198765675590", "name": "visitantes 90 días con exclusión (24/09/2020)"}
                                    ],
                                    "genders": [1, 2],
                                    "geo_locations": {"countries": ["ES"]},
                                },
                            }
                        }
                    ],
                },
                "campaign": {
                    "budget": 10000,
                    "id": None,
                    "internal_id": str(campaign.pk),
                    "is_daily": False,
                    "method": "CREATE",
                    "name": "FacebookCampaign",
                    "objective": "CONVERSIONS",
                    "page_id": *********,
                },
                "metadata": {
                    "ad_account": 1,
                    "ads": 4,
                    "adsets": 2,
                    "campaign": 1,
                    "campaign_id": str(campaign.pk),
                    "global_identifier": "123aa5",
                    "instagram": 1,
                },
            }

    def test_create_campaign_bad_objective(self, user, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.REACH,
            facebook_business_account=facebook_business_account,
            budget=100,
        )

        with pytest.raises(ValidationError) as error:
            campaign.create_campaign(user)

        assert str(error.value.message) == _("Objective REACH not supported in current version")

    def test_create_campaign_conversions_no_pixel_id(self, user, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.CONVERSIONS,
            facebook_business_account=facebook_business_account,
            budget=100,
        )

        with pytest.raises(ValidationError) as error:
            campaign.create_campaign(user)

        assert str(error.value.message) == _("Conversions campaigns requires a valid pixel")

    def test_create_campaign_conversions_no_event_type(self, user, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.CONVERSIONS,
            facebook_business_account=facebook_business_account,
            budget=100,
            pixel_id="***********",
        )

        with pytest.raises(ValidationError) as error:
            campaign.create_campaign(user)

        assert str(error.value.message) == _("Conversions campaigns requires a conversion event type")

    def test_create_campaign_leadgen_no_tos(self, user, facebook_business_account):
        with patch(
            "business.models.business.FacebookBusinessAccount.leadgen_tos_accepted", new_callable=PropertyMock
        ) as mock_tos:
            mock_tos.return_value = False

            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.LEAD_GENERATION,
                facebook_business_account=facebook_business_account,
                budget=100,
                pixel_id="***********",
            )

            with pytest.raises(ValidationError) as error:
                campaign.create_campaign(user)

            assert str(error.value.message) == _("Lead Generation campaigns requires leadgen tos accepted")

    def test_create_campaign_no_account_id(self, user, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.POST_ENGAGEMENT,
            facebook_business_account=facebook_business_account,
            budget=100,
        )

        with pytest.raises(ValidationError) as error:
            campaign.create_campaign(user)

        assert str(error.value.message) == _("A valid ad account is needed to create a campaign")

    def test_create_campaign_no_access_token(self, user, facebook_business_account):
        with patch("business.models.business.FacebookBusinessAccount.tokens", new_callable=PropertyMock) as mock_tokens:
            mock_tokens.return_value = BusinessSocialAccount.objects.none()
            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.POST_ENGAGEMENT,
                facebook_business_account=facebook_business_account,
                budget=100,
                ad_account_id="act_123342425",
            )

            with pytest.raises(ValidationError) as error:
                campaign.create_campaign(user)

            assert str(error.value.message) == _("Your user has not linked an access token to business")

    def test_create_campaign_invalid_access_token(self, user, facebook_business_account):
        with patch("campaigns.models.facebook.FacebookClient") as mock_client:
            mock_client.return_value.debug_token.return_value = False
            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.POST_ENGAGEMENT,
                facebook_business_account=facebook_business_account,
                budget=100,
                ad_account_id="act_123342425",
            )

            with pytest.raises(ValidationError) as error:
                campaign.create_campaign(user)

            assert str(error.value.message) == _("Access token is not valid or has not enough permissions")

    def test_create_campaign_invalid_page_token(self, user, facebook_business_account):
        with patch("campaigns.models.facebook.FacebookClient") as mock_client:
            mock_client.return_value.debug_token.return_value = True
            mock_client.return_value.get_page_token.return_value = False

            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.POST_ENGAGEMENT,
                facebook_business_account=facebook_business_account,
                budget=100,
                ad_account_id="act_123342425",
            )

            with pytest.raises(ValidationError) as error:
                campaign.create_campaign(user)

            assert str(error.value.message) == _("Impossible to get a valid page token from Facebook")

    def test_create_campaign_no_pomegranate_message(self, user, facebook_business_account):
        with patch("campaigns.models.facebook.FacebookClient") as mock_client:
            with patch(
                "campaigns.models.facebook.FacebookCampaign.pomegranate_message", new_callable=PropertyMock
            ) as mock_pm:
                mock_client.return_value.debug_token.return_value = True
                mock_client.return_value.get_page_token.return_value = True
                mock_pm.return_value = None

                campaign = FacebookCampaign.objects.create(
                    name="FacebookCampaign",
                    objective=FacebookCampaign.POST_ENGAGEMENT,
                    facebook_business_account=facebook_business_account,
                    budget=100,
                    ad_account_id="act_123342425",
                )

                with pytest.raises(ValidationError) as error:
                    campaign.create_campaign(user)

                assert str(error.value.message) == _("Nothing to send to pomegranate")

    def test_create_campaign_error_calling_pomegranate_service(self, user, facebook_business_account):
        with patch("campaigns.models.facebook.FacebookClient") as mock_client:
            with patch("campaigns.models.facebook.PomegranateClient") as mock_pc:
                mock_client.return_value.debug_token.return_value = True
                mock_client.return_value.get_page_token.return_value = True
                mock_pc.return_value.create_or_update_campaign.return_value = False

                campaign = FacebookCampaign.objects.create(
                    name="FacebookCampaign",
                    objective=FacebookCampaign.POST_ENGAGEMENT,
                    facebook_business_account=facebook_business_account,
                    budget=100,
                    ad_account_id="act_123342425",
                )

                with pytest.raises(ValidationError) as error:
                    campaign.create_campaign(user)

                assert str(error.value.message) == _("Error creating/updating campaign in Facebook")

    def test_create_campaign_ok(self, user, facebook_business_account):
        with patch("campaigns.models.facebook.FacebookClient") as mock_client:
            with patch("campaigns.models.facebook.PomegranateClient") as mock_pc:
                mock_client.return_value.debug_token.return_value = True
                mock_client.return_value.get_page_token.return_value = True
                mock_pc.return_value.create_or_update_campaign.return_value = True

                campaign = FacebookCampaign.objects.create(
                    name="FacebookCampaign",
                    objective=FacebookCampaign.POST_ENGAGEMENT,
                    facebook_business_account=facebook_business_account,
                    budget=100,
                    ad_account_id="act_123342425",
                )

                assert campaign.creation_status != FacebookCampaign.IN_PROCESS
                campaign.create_campaign(user)
                assert campaign.creation_status == FacebookCampaign.IN_PROCESS

    def test_mutate_campaign_no_remote_id(self, user, facebook_business_account):
        campaign = FacebookCampaign.objects.create(
            name="FacebookCampaign",
            objective=FacebookCampaign.POST_ENGAGEMENT,
            facebook_business_account=facebook_business_account,
            budget=100,
            ad_account_id="act_123342425",
        )

        with pytest.raises(ValidationError) as error:
            campaign.pause_campaign(user)

        assert str(error.value.message) == _("Campaign cannot be pause because doesn't have valid id")

    def test_mutate_campaign_no_access_token(self, user, facebook_business_account):
        with override_settings(ENVIRONMENT="PROD"):
            with patch(
                "business.models.business.FacebookBusinessAccount.tokens", new_callable=PropertyMock
            ) as mock_tokens:
                mock_tokens.return_value = BusinessSocialAccount.objects.none()
                campaign = FacebookCampaign.objects.create(
                    name="FacebookCampaign",
                    objective=FacebookCampaign.POST_ENGAGEMENT,
                    facebook_business_account=facebook_business_account,
                    budget=100,
                    ad_account_id="act_123342425",
                    remote_id=123345,
                )

                with pytest.raises(ValidationError) as error:
                    campaign.pause_campaign(user)

                assert str(error.value.message) == _("Your user has not linked an access token to business")

    def test_mutate_campaign_wrong_debug_token(self, user, facebook_business_account):
        with override_settings(ENVIRONMENT="PROD"):
            with patch("campaigns.mixins.FacebookClient") as mock_client:
                mock_client.return_value.debug_token.return_value = False
                campaign = FacebookCampaign.objects.create(
                    name="FacebookCampaign",
                    objective=FacebookCampaign.POST_ENGAGEMENT,
                    facebook_business_account=facebook_business_account,
                    budget=100,
                    ad_account_id="act_123342425",
                    remote_id=123345,
                )

                with pytest.raises(ValidationError) as error:
                    campaign.pause_campaign(user)

                assert str(error.value.message) == _("Access token is not valid or has not enough permissions")
                mock_client.return_value.debug_token.assert_called_once_with(settings.APP_TOKEN, "123")

    def test_mutate_campaign_facebook_error(self, user, facebook_business_account, facebook_request_error):
        with patch("campaigns.mixins.CampaignManagementMixin._pause_campaign") as mock_client:
            mock_client.side_effect = facebook_request_error
            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.POST_ENGAGEMENT,
                facebook_business_account=facebook_business_account,
                budget=100,
                ad_account_id="act_123342425",
                remote_id=123345,
            )

            with pytest.raises(ValidationError) as error:
                campaign.pause_campaign(user)

            assert str(error.value.message) == _("Error doing pause operation over Facebook campaign")

    def test_pause_campaign_ok(self, user, facebook_business_account, facebook_request_error):
        with patch("campaigns.models.facebook.FacebookCampaign.mutate_campaign") as mock_mutate:
            mock_mutate.return_value = FacebookCampaign.PAUSED
            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.POST_ENGAGEMENT,
                facebook_business_account=facebook_business_account,
                budget=100,
                ad_account_id="act_123342425",
                remote_id=123345,
            )

            assert campaign.status == FacebookCampaign.NONE
            campaign.pause_campaign(user)
            assert campaign.status == FacebookCampaign.PAUSED
            mock_mutate.assert_called_once_with("campaign", user, "pause")

    def test_active_campaign_ok(self, user, facebook_business_account, facebook_request_error):
        with patch("campaigns.models.facebook.FacebookCampaign.mutate_campaign") as mock_mutate:
            mock_mutate.return_value = FacebookCampaign.ACTIVE
            campaign = FacebookCampaign.objects.create(
                name="FacebookCampaign",
                objective=FacebookCampaign.POST_ENGAGEMENT,
                facebook_business_account=facebook_business_account,
                budget=100,
                ad_account_id="act_123342425",
                remote_id=123345,
            )

            assert campaign.status == FacebookCampaign.NONE
            campaign.activate_campaign(user)
            assert campaign.status == FacebookCampaign.ACTIVE
            mock_mutate.assert_called_once_with("campaign", user, "activate")
