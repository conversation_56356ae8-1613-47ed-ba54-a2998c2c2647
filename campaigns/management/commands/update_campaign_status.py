from django.conf import settings
from django.core.management import BaseCommand

from campaigns.models import FacebookCampaign, GoogleCampaign
from campaigns.utils.defaults import STATUSES
from core.external_services.facebook.client import FacebookClient
from core.external_services.google.client import GoogleAdsClient


def update_facebook_campaigns():
    """Update facebook campaign statuses"""
    for facebook_campaign in FacebookCampaign.objects.exclude(
        status__in=[FacebookCampaign.NONE, FacebookCampaign.REMOVED]
    ):

        access_token = facebook_campaign.facebook_business_account.tokens.first()
        if not access_token:
            # Skip this and pass to the next one
            continue

        access_token = access_token["facebook"]

        facebook_client = FacebookClient(sandbox_or_production=settings.ENVIRONMENT == "PROD")
        campaign_metadata = (
            facebook_client.get_campaign_metadata(access_token=access_token, campaign_id=facebook_campaign.remote_id)
            or {}
        )

        facebook_campaign.status = STATUSES.get(campaign_metadata.get("effective_status"), facebook_campaign.status)
        facebook_campaign.stop_time = campaign_metadata.get("stop_time") or facebook_campaign.stop_time
        facebook_campaign.save(update_fields=["status", "stop_time"])


def update_google_campaigns():
    """Update google campaign statuses"""
    for google_campaign in GoogleCampaign.objects.exclude(status__in=[GoogleCampaign.NONE, GoogleCampaign.REMOVED]):

        refresh_token = google_campaign.business_account.tokens.first()
        if not refresh_token:
            # Skip this and pass to the next one
            continue

        refresh_token = refresh_token["google"]

        google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")
        campaign_metadata = (
            google_client.get_campaign_metadata(
                refresh_token=refresh_token,
                customer_id=google_campaign.customer_id,
                campaign_id=google_campaign.remote_id,
            )
            or {}
        )

        google_campaign.status = STATUSES.get(campaign_metadata.get("status"), "paused")
        google_campaign.stop_time = campaign_metadata.get("stop_time") or google_campaign.stop_time
        google_campaign.save(update_fields=["status", "stop_time"])


class Command(BaseCommand):
    """Update campaign local status from remote """

    def handle(self, *args, **options):
        update_facebook_campaigns()
        update_google_campaigns()
