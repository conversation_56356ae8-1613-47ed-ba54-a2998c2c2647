from datetime import timedelta

from django.conf import settings
from django.core.management import BaseCommand
from django.db.models import Case, DateTimeField, F, Q, Value, When
from django.utils import timezone

from campaigns.models import GoogleCampaign
from core.async_tasks.hiperion import (post_aggregated_asynchronously,
                                       post_metrics_asynchronously)
from core.external_services.google.client import GoogleAdsClient


class Command(BaseCommand):
    """Update campaign metrics"""

    def handle(self, *args, **options):
        today = timezone.now()

        batch_size = 0
        next_tick = 0

        # Lookup for campaigns whose stop_time with respect to today is 30 days
        wrapper = Case(
            When(Q(stop_time__isnull=False), then=F("stop_time") + timedelta(days=30)),
            default=Value(today),
            output_field=DateTimeField(),
        )

        google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")

        for google_campaign in (
            GoogleCampaign.objects.exclude(
                status__in=[GoogleCampaign.NONE, GoogleCampaign.REMOVED],
                importing_metrics_status=GoogleCampaign.IN_PROCESS,
            )
            .annotate(duration=wrapper)
            .filter(Q(duration__gte=today))
        ):

            refresh_token = google_campaign.business_account.tokens.first()
            if not refresh_token:
                # Skip this and pass to the next one
                continue

            refresh_token = refresh_token["google"]

            # Get stop time if campaign is paused and no stop time is set
            if google_campaign.status == GoogleCampaign.PAUSED and not google_campaign.stop_time:
                campaign_metadata = (
                    google_client.get_campaign_metadata(
                        refresh_token=refresh_token,
                        customer_id=google_campaign.customer_id,
                        campaign_id=google_campaign.remote_id,
                    )
                    or {}
                )
                stop_time = campaign_metadata.get("stop_time") or today

                google_campaign.stop_time = stop_time
                google_campaign.save(update_fields=["stop_time"])

            if batch_size == 15:
                next_tick += 5
                batch_size = 0  # reset batch

            # Get every part of facebook campaign using Hiperion
            post_metrics_asynchronously(
                params={
                    "refresh_token": refresh_token,
                    "user_id": str(google_campaign.pk),
                    "objective": google_campaign.advertising_channel_type,
                    "update": True,
                    "campaign_id": google_campaign.remote_id,
                    "date_start": google_campaign.created_at.date(),
                    "date_end": (google_campaign.stop_time or today).date(),
                    "level": "campaign",
                    "customer_id": google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                },
                integrity_key=google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                base_url="google/",
                in_seconds=next_tick,
            )

            post_metrics_asynchronously(
                params={
                    "refresh_token": refresh_token,
                    "user_id": str(google_campaign.pk),
                    "objective": google_campaign.advertising_channel_type,
                    "update": True,
                    "campaign_id": google_campaign.remote_id,
                    "date_start": google_campaign.created_at.date(),
                    "date_end": (google_campaign.stop_time or today).date(),
                    "level": "adset",
                    "customer_id": google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                },
                integrity_key=google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                base_url="google/",
                in_seconds=next_tick,
            )

            post_metrics_asynchronously(
                params={
                    "refresh_token": refresh_token,
                    "user_id": str(google_campaign.pk),
                    "objective": google_campaign.advertising_channel_type,
                    "update": True,
                    "campaign_id": google_campaign.remote_id,
                    "date_start": google_campaign.created_at.date(),
                    "date_end": (google_campaign.stop_time or today).date(),
                    "level": "ad",
                    "customer_id": google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                },
                integrity_key=google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                base_url="google/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "refresh_token": refresh_token,
                    "user_id": str(google_campaign.pk),
                    "objective": google_campaign.advertising_channel_type,
                    "campaign_id": google_campaign.remote_id,
                    "level": "campaign",
                    "customer_id": google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                },
                integrity_key=google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                base_url="google/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "refresh_token": refresh_token,
                    "user_id": str(google_campaign.pk),
                    "objective": google_campaign.advertising_channel_type,
                    "campaign_id": google_campaign.remote_id,
                    "level": "adset",
                    "customer_id": google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                },
                integrity_key=google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                base_url="google/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "refresh_token": refresh_token,
                    "user_id": str(google_campaign.pk),
                    "objective": google_campaign.advertising_channel_type,
                    "campaign_id": google_campaign.remote_id,
                    "level": "ad",
                    "customer_id": google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                },
                integrity_key=google_campaign.customer_id or google_campaign.google_business_account.customer_id,
                base_url="google/",
                in_seconds=next_tick,
            )
