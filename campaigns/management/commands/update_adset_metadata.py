from datetime import timedelta

from django.core.management import BaseCommand
from django.db.models import Case, DateTimeField, F, Q, Value, When
from django.utils import timezone

from campaigns.models import FacebookAdset, FacebookCampaign
from core.async_tasks.hiperion import post_adset_asynchronously


class Command(BaseCommand):
    """Update campaign metrics"""

    def handle(self, *args, **options):
        today = timezone.now()

        batch_size = 0
        next_tick = 0

        # Lookup for campaigns whose stop_time with respect to today is 30 days
        wrapper = Case(
            When(Q(campaign__stop_time__isnull=False), then=F("campaign__stop_time") + timedelta(days=30)),
            default=Value(today),
            output_field=DateTimeField(),
        )

        for facebook_adset in (
            FacebookAdset.objects.exclude(campaign__status=[FacebookCampaign.NONE, FacebookCampaign.REMOVED])
            .select_related("campaign")
            .annotate(duration=wrapper)
            .filter(Q(duration__gte=today))
        ):

            access_token = facebook_adset.campaign.facebook_business_account.tokens.first()
            if not access_token:
                # Skip this and pass to the next one
                continue

            access_token = access_token["facebook"]

            if batch_size == 15:
                next_tick += 5
                batch_size = 0  # reset batch

            post_adset_asynchronously(
                access_token=access_token,
                user_id=str(facebook_adset.pk),
                adset_id=facebook_adset.remote_id,
                ad_account_id=facebook_adset.campaign.ad_account_id
                or facebook_adset.campaign.facebook_business_account.ad_account_id,
                in_seconds=next_tick,
            )
