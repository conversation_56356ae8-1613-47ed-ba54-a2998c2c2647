from datetime import timedelta

from django.conf import settings
from django.core.management import BaseCommand
from django.db.models import Case, DateTimeField, F, Q, Value, When
from django.utils import timezone

from campaigns.models import FacebookCampaign
from core.async_tasks.hiperion import (post_aggregated_asynchronously,
                                       post_breakdowns_asynchronously,
                                       post_metrics_asynchronously)
from core.external_services.facebook.client import FacebookClient


class Command(BaseCommand):
    """Update campaign metrics"""

    def handle(self, *args, **options):
        today = timezone.now()

        batch_size = 0
        next_tick = 0

        # Lookup for campaigns whose stop_time with respect to today is 30 days
        wrapper = Case(
            When(Q(stop_time__isnull=False), then=F("stop_time") + timedelta(days=30)),
            default=Value(today),
            output_field=DateTimeField(),
        )

        facebook_client = FacebookClient(sandbox_or_production=settings.ENVIRONMENT == "PROD")

        for facebook_campaign in (
            FacebookCampaign.objects.exclude(
                status__in=[FacebookCampaign.NONE, FacebookCampaign.REMOVED],
                importing_metrics_status=FacebookCampaign.IN_PROCESS,
            )
            .annotate(duration=wrapper)
            .filter(Q(duration__gte=today))
        ):

            access_token = facebook_campaign.facebook_business_account.tokens.first()
            if not access_token:
                # Skip this and pass to the next one
                continue

            access_token = access_token["facebook"]

            # Get stop time if campaign is paused and no stop time is set
            if facebook_campaign.status == FacebookCampaign.PAUSED and not facebook_campaign.stop_time:
                campaign_metadata = (
                    facebook_client.get_campaign_metadata(
                        access_token=access_token, campaign_id=facebook_campaign.remote_id
                    )
                    or {}
                )
                stop_time = campaign_metadata.get("stop_time") or today

                facebook_campaign.stop_time = stop_time
                facebook_campaign.save(update_fields=["stop_time"])

            if batch_size == 15:
                next_tick += 5
                batch_size = 0  # reset batch

            # Get every part of facebook campaign using Hiperion
            post_metrics_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "update": True,
                    "campaign_id": facebook_campaign.remote_id,
                    "date_start": facebook_campaign.created_at.date(),
                    "date_end": (facebook_campaign.stop_time or today).date(),
                    "level": "campaign",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick,
            )

            post_metrics_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "update": True,
                    "campaign_id": facebook_campaign.remote_id,
                    "date_start": facebook_campaign.created_at.date(),
                    "date_end": (facebook_campaign.stop_time or today).date(),
                    "level": "adset",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick,
            )

            post_metrics_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "update": True,
                    "campaign_id": facebook_campaign.remote_id,
                    "date_start": facebook_campaign.created_at.date(),
                    "date_end": (facebook_campaign.stop_time or today).date(),
                    "level": "ad",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "mode": "insights",
                    "breakdowns": "all",
                    "campaign_id": facebook_campaign.remote_id,
                    "level": "campaign",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "mode": "insights",
                    "breakdowns": "all",
                    "campaign_id": facebook_campaign.remote_id,
                    "level": "adset",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "mode": "insights",
                    "breakdowns": "all",
                    "campaign_id": facebook_campaign.remote_id,
                    "level": "ad",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick,
            )

            post_aggregated_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "mode": "breakdowns",
                    "breakdowns": "all",
                    "campaign_id": facebook_campaign.remote_id,
                    "level": "campaign",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick + 5,
            )

            post_aggregated_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "mode": "breakdowns",
                    "breakdowns": "all",
                    "campaign_id": facebook_campaign.remote_id,
                    "level": "adset",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick + 5,
            )

            post_aggregated_asynchronously(
                params={
                    "access_token": access_token,
                    "user_id": str(facebook_campaign.pk),
                    "objective": facebook_campaign.objective,
                    "mode": "breakdowns",
                    "breakdowns": "all",
                    "campaign_id": facebook_campaign.remote_id,
                    "level": "ad",
                    "conversion_event_type": facebook_campaign.conversion_event_type,
                },
                integrity_key=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                base_url="facebook/",
                in_seconds=next_tick + 5,
            )

            post_breakdowns_asynchronously(
                access_token=access_token,
                user_id=str(facebook_campaign.pk),
                objective=facebook_campaign.objective,
                update=True,
                campaign_id=facebook_campaign.remote_id,
                date_start=facebook_campaign.created_at.date(),
                date_end=(facebook_campaign.stop_time or today).date(),
                level="campaign",
                conversion_event_type=facebook_campaign.conversion_event_type,
                ad_account_id=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                breakdowns="all",
                in_seconds=next_tick + 10,
            )

            post_breakdowns_asynchronously(
                access_token=access_token,
                user_id=str(facebook_campaign.pk),
                objective=facebook_campaign.objective,
                update=True,
                campaign_id=facebook_campaign.remote_id,
                date_start=facebook_campaign.created_at.date(),
                date_end=(facebook_campaign.stop_time or today).date(),
                level="adset",
                conversion_event_type=facebook_campaign.conversion_event_type,
                ad_account_id=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                breakdowns="all",
                in_seconds=next_tick + 10,
            )

            post_breakdowns_asynchronously(
                access_token=access_token,
                user_id=str(facebook_campaign.pk),
                objective=facebook_campaign.objective,
                update=True,
                campaign_id=facebook_campaign.remote_id,
                date_start=facebook_campaign.created_at.date(),
                date_end=(facebook_campaign.stop_time or today).date(),
                level="ad",
                conversion_event_type=facebook_campaign.conversion_event_type,
                ad_account_id=facebook_campaign.ad_account_id
                or facebook_campaign.facebook_business_account.ad_account_id,
                breakdowns="all",
                in_seconds=next_tick + 10,
            )
