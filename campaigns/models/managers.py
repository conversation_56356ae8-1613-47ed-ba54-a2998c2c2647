from django.db import models
from django.db.models import F, TextField
from django.db.models.functions import Cast


class BaseManager(models.Manager):
    """Custom manager to get model hierarchy keys"""

    def get_name_from_instance_type(self, instance_name: str):
        model_name = self.model._meta.model_name
        return model_name.split(instance_name)[0].lower()

    def hierarchy(self, pk, only_first=False):
        try:
            return self._hierarchy(pk, only_first)
        except TypeError:
            return {}

    def _hierarchy(self, pk, first):
        raise NotImplementedError("Not implemented yet!")


class CampaignManager(BaseManager):
    def _hierarchy(self, pk, first):
        campaign_type = self.get_name_from_instance_type("campaign")

        queryset = (
            self.filter(pk=pk)
            .annotate(
                **{
                    "business_id": F(f"{campaign_type}_business_account__business__pk"),
                    "organization_id": F(f"{campaign_type}_business_account__business__organization__pk"),
                    "campaign_name": F("name"),
                }
            )
            .annotate(
                campaign_id=Cast("pk", output_field=TextField()),
                business_id=Cast("business_id", output_field=TextField()),
                organization_id=Cast("organization_id", output_field=TextField()),
            )
            .values("campaign_id", "business_id", "organization_id", "campaign_name")
        )

        if first:
            queryset = queryset.first()

        return queryset


class AdsetManager(BaseManager):
    def _hierarchy(self, pk, first):
        campaign_type = self.get_name_from_instance_type("adset")

        queryset = (
            self.filter(pk=pk)
            .annotate(
                **{
                    "business_id": F(f"campaign__{campaign_type}_business_account__business__pk"),
                    "organization_id": F(f"campaign__{campaign_type}_business_account__business__organization__pk"),
                    "campaign_name": F("campaign__name"),
                }
            )
            .annotate(
                adset_id=Cast("pk", output_field=TextField()),
                business_id=Cast("business_id", output_field=TextField()),
                organization_id=Cast("organization_id", output_field=TextField()),
            )
            .values("adset_id", "campaign_id", "business_id", "organization_id", "campaign_name")
        )

        if first:
            queryset = queryset.first()
            queryset["campaign_id"] = str(queryset["campaign_id"])

        return queryset


class AdManager(BaseManager):
    def _hierarchy(self, pk, first):
        campaign_type = self.get_name_from_instance_type("ad")

        queryset = (
            self.filter(pk=pk)
            .annotate(
                **{
                    "campaign_id": F("adset__campaign__pk"),
                    "business_id": F(f"adset__campaign__{campaign_type}_business_account__business__pk"),
                    "organization_id": F(
                        f"adset__campaign__{campaign_type}_business_account__business__organization__pk"
                    ),
                    "campaign_name": F("adset__campaign__name"),
                }
            )
            .annotate(
                ad_id=Cast("pk", output_field=TextField()),
                campaign_id=Cast("campaign_id", output_field=TextField()),
                business_id=Cast("business_id", output_field=TextField()),
                organization_id=Cast("organization_id", output_field=TextField()),
            )
            .values(
                "ad_id",
                "adset_id",
                "campaign_id",
                "business_id",
                "organization_id",
                "campaign_name",
            )
        )

        if first:
            queryset = queryset.first()
            queryset["adset_id"] = str(queryset["adset_id"])

        return queryset
