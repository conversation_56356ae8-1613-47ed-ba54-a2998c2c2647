import uuid
from functools import cached_property

from dateutil import parser
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core import validators
from django.core.exceptions import ValidationError
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.db.models.signals import post_save, pre_delete
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from business.models import GoogleBusinessAccount
from campaigns.models import (AdManager, AdsetManager, Campaign,
                              CampaignManager,
                              fetch_and_create_every_campaign_part,
                              set_overview_kpis)
from campaigns.models.mixins import (CampaignInsightsMixin,
                                     GoogleManagementMixin,
                                     SynchronizeRemoteCampaignMixin)
from campaigns.models.receivers import (
    get_campaign_details, remove_budgets_if_needed,
    remove_cache_entry_for_imported_campaigns_on_creation, send_to_ai_service)
from core.async_tasks.hiperion import (post_aggregated_asynchronously,
                                       post_metrics_asynchronously)
from core.external_services.google.client import GoogleAdsClient
from core.external_services.google.helpers import (STATUSES,
                                                   safe_attribute_getter)
from core.validators import URLWithoutShemeValidator


class GoogleCampaignBudget(models.Model):
    """Google campaign budget metadata"""

    UNSPECIFIED = "UNSPECIFIED"
    UNKNOWN = "UNKNOWN"
    ENABLED = "ENABLED"
    REMOVED = "REMOVED"

    BUDGET_STATUS_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (ENABLED, _("Enabled")),
        (REMOVED, _("Removed")),
    )

    STANDARD = "STANDARD"
    ACCELERATED = "ACCELERATED"

    BUDGET_DELIVERY_METHOD_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (STANDARD, _("Standard")),
        (ACCELERATED, _("Accelerated")),
    )

    DAILY = "DAILY"
    CUSTOM_PERIOD = "CUSTOM_PERIOD"

    BUDGET_PERIOD_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (DAILY, _("Daily")),
        (CUSTOM_PERIOD, _("Custom period")),
    )

    HOTEL_ADS_COMMISSION = "HOTEL_ADS_COMMISSION"
    FIXED_CPA = "FIXED_CPA"
    SMART_CAMPAIGN = "SMART_CAMPAIGN"

    BUDGET_TYPE_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (STANDARD, _("Standard")),
        (HOTEL_ADS_COMMISSION, _("Hotel ads commission")),
        (FIXED_CPA, _("Fixed CPA")),
        (SMART_CAMPAIGN, _("Smart Campaign")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Remote identifiers
    remote_id = models.CharField(max_length=100, null=True, help_text=_("Google campaign budget remote id"))
    resource_name = models.CharField(max_length=400, null=True, help_text=_("Google budget resource name"))

    status = models.CharField(max_length=200, choices=BUDGET_STATUS_CHOICES, null=True)
    delivery_method = models.CharField(max_length=200, choices=BUDGET_DELIVERY_METHOD_CHOICES, default=STANDARD)
    period = models.CharField(max_length=200, choices=BUDGET_PERIOD_CHOICES, default=DAILY)
    type = models.CharField(max_length=200, choices=BUDGET_TYPE_CHOICES, null=True)
    name = models.CharField(max_length=300, null=False, blank=False)
    explicitly_shared = models.BooleanField(default=False)


class GoogleCampaign(GoogleManagementMixin, SynchronizeRemoteCampaignMixin, CampaignInsightsMixin, Campaign):
    """Google campaign model with concrete attributes"""

    __social_network__ = "google"

    SEARCH = "SEARCH"
    DISPLAY = "DISPLAY"
    SHOPPING = "SHOPPING"
    LOCAL = "LOCAL"
    MULTI_CHANNEL = "MULTI_CHANNEL"

    ADVERTISING_CHANNEL_TYPES_CHOICES = (
        (SEARCH, _("Search")),
        (DISPLAY, _("Display")),
        (SHOPPING, _("Shopping")),
        (LOCAL, _("Local")),
        (MULTI_CHANNEL, _("Multichannel")),
    )

    UNSPECIFIED = "UNSPECIFIED"
    UNKNOWN = "UNKNOWN"
    SHOPPING_SMART_ADS = "SHOPPING_SMART_ADS"
    DISPLAY_SMART_CAMPAIGN = "DISPLAY_SMART_CAMPAIGN"
    APP_CAMPAIGN = "APP_CAMPAIGN"
    APP_CAMPAIGN_FOR_ENGAGEMENT = "APP_CAMPAIGN_FOR_ENGAGEMENT"

    ADVERTISING_CHANNEL_SUBTYPES_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (SHOPPING_SMART_ADS, _("Search")),
        (DISPLAY_SMART_CAMPAIGN, _("Display")),
        (APP_CAMPAIGN, _("Shopping")),
        (APP_CAMPAIGN_FOR_ENGAGEMENT, _("Local")),
    )

    OPTIMIZE = "OPTIMIZE"
    CONVERSION_OPTIMIZE = "CONVERSION_OPTIMIZE"
    ROTATE = "ROTATE"
    ROTATE_INDEFINITELY = "ROTATE_INDEFINITELY"
    UNAVAILABLE = "UNAVAILABLE"

    AD_SERVING_OPTIMIZATION_STATUS_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (OPTIMIZE, _("Optimize")),
        (CONVERSION_OPTIMIZE, _("Conversion optimize")),
        (ROTATE, _("Rotate")),
        (ROTATE_INDEFINITELY, _("Rotate indefinitely")),
        (UNAVAILABLE, _("Unavailable")),
    )

    CALL_CLICKS = "CALL_CLICKS"
    DRIVING_DIRECTIONS = "DRIVING_DIRECTIONS"

    OPTIMIZATION_GOAL_TYPE_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (CALL_CLICKS, _("Call clicks")),
        (DRIVING_DIRECTIONS, _("Driving directions")),
    )

    CLICKS = "CLICKS"
    CONVERSION_VALUE = "CONVERSION_VALUE"
    CONVERSIONS = "CONVERSIONS"
    GUEST_STAY = "GUEST_STAY"

    PAYMENT_MODE_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (CLICKS, _("Clicks")),
        (CONVERSION_VALUE, _("Conversion value")),
        (CONVERSIONS, _("Conversions")),
        (GUEST_STAY, _("Guest stay")),
    )

    HEADLINE = "HEADLINE"
    DESCRIPTION = "DESCRIPTION"
    MANDATORY_AD_TEXT = "MANDATORY_AD_TEXT"
    MARKETING_IMAGE = "MARKETING_IMAGE"
    MEDIA_BUNDLE = "MEDIA_BUNDLE"
    YOUTUBE_VIDEO = "YOUTUBE_VIDEO"
    BOOK_ON_GOOGLE = "BOOK_ON_GOOGLE"
    LEAD_FORM = "LEAD_FORM"
    PROMOTION = "PROMOTION"
    CALLOUT = "CALLOUT"
    STRUCTURED_SNIPPET = "STRUCTURED_SNIPPET"
    SITELINK = "SITELINK"

    ASSET_FIELD_TYPES_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (HEADLINE, _("Headline")),
        (DESCRIPTION, _("Description")),
        (MANDATORY_AD_TEXT, _("Mandatory ad text")),
        (MARKETING_IMAGE, _("Marketing image")),
        (MEDIA_BUNDLE, _("Media bundle")),
        (YOUTUBE_VIDEO, _("Youtube video")),
        (BOOK_ON_GOOGLE, _("Book on google")),
        (LEAD_FORM, _("Lead form")),
        (PROMOTION, _("Promotion")),
        (CALLOUT, _("Callout")),
        (STRUCTURED_SNIPPET, _("Structured snippet")),
        (SITELINK, _("Site linke")),
    )

    COMMON_INSIGHTS = ["cost", "impressions", "interactions", "ctr"]
    DEFAULT_INSIGHTS = {
        SEARCH: COMMON_INSIGHTS,
    }

    google_business_account = models.ForeignKey(GoogleBusinessAccount, on_delete=models.CASCADE, null=True)

    # Linked customer_id to this campaign
    customer_id = models.CharField(max_length=200, blank=True, null=True)

    # Type of campaign
    advertising_channel_type = models.CharField(
        max_length=200, choices=ADVERTISING_CHANNEL_TYPES_CHOICES, null=True, blank=True
    )
    advertising_channel_sub_type = models.CharField(
        max_length=200, choices=ADVERTISING_CHANNEL_SUBTYPES_CHOICES, null=True, blank=True
    )

    # Network settings options
    target_google_search = models.BooleanField(default=False)
    target_search_network = models.BooleanField(default=False)
    target_content_network = models.BooleanField(default=False)
    target_partner_search_network = models.BooleanField(default=False)

    # Budget metadata
    budget_metadata = models.ForeignKey(
        GoogleCampaignBudget,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        help_text=_("Budget may be shared across several campaigns"),
    )

    # Ad serving optimization status
    ad_serving_optimization_status = models.CharField(
        max_length=200,
        choices=AD_SERVING_OPTIMIZATION_STATUS_CHOICES,
        null=True,
        help_text=_("The ad serving optimization status of the campaign"),
    )

    # Dynamic search ads setting
    domain_name = models.TextField(blank=True, null=True, validators=[URLWithoutShemeValidator()])
    language_code = models.CharField(max_length=10, null=True)
    feeds = ArrayField(models.CharField(max_length=200), blank=True, null=True)
    use_supplied_urls_only = models.BooleanField(default=False)

    # Targeting settings
    targeting_restrictions = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )

    # Frequency caps
    frequency_caps = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )

    # Selective optimization
    conversion_actions = ArrayField(models.CharField(max_length=200), blank=True, null=True)

    # Optimization goal settings
    optimization_goal_types = ArrayField(
        models.CharField(max_length=200, choices=OPTIMIZATION_GOAL_TYPE_CHOICES), blank=True, null=True
    )

    # Payment mode
    payment_mode = models.CharField(max_length=200, choices=PAYMENT_MODE_CHOICES, blank=True, null=True)

    # Excluded parent assets
    excluded_parent_asset_field_types = ArrayField(
        models.CharField(max_length=200, choices=ASSET_FIELD_TYPES_CHOICES), blank=True, null=True
    )

    # Remote resource name
    resource_name = models.CharField(max_length=400, null=True, help_text=_("Google bidding strategy resource name"))

    objects = CampaignManager()

    class Meta(Campaign.Meta):
        unique_together = ("remote_id", "google_business_account")

    def __str__(self):
        return f"GoogleCampaign:: {self.name}"

    @property
    def is_published(self):
        return self.remote_id

    @cached_property
    def business_account(self):
        return self.google_business_account

    @property
    def objective(self):
        """Needed for ai-service async call"""
        return self.advertising_channel_type

    @property
    def budget_in_cents(self):
        return (self.budget / 1e4) if self.budget is not None else self.budget

    def pause_campaign(self, **kwargs):
        """Pause a campaign using sync approach making a call against Facebook"""
        self.status = self.mutate_campaign(
            "campaign",
            "pause",
            customer_id=self.customer_id or self.google_business_account.customer_id,
            campaign_id=self.remote_id,
        )
        self.stop_time = timezone.now()

        # Update internal effective status
        self.save(update_fields=["status", "stop_time"])

    def activate_campaign(self, **kwargs):
        """Activate a campaign using sync approach making a call against Facebook"""
        self.status = self.mutate_campaign(
            "campaign",
            "activate",
            customer_id=self.customer_id or self.google_business_account.customer_id,
            campaign_id=self.remote_id,
        )
        self.stop_time = None

        # Update internal effective status
        self.save(update_fields=["status", "stop_time"])

    def import_campaign(self, tokens, update=False):
        """Wrap google import business logic on post_save dispatch signal"""

        from core.async_tasks.sync_imported_google_campaigns import \
            sync_imported_google_campaigns_asynchronously

        refresh_token = tokens["google"]

        google_campaign_internal_id = str(self.pk)
        google_campaign_remote_id = str(self.remote_id)
        customer_id = self.customer_id
        business_id = str(self.google_business_account.business.pk)

        sync_imported_google_campaigns_asynchronously(
            refresh_token,
            google_campaign_internal_id,
            google_campaign_remote_id,
            customer_id,
            business_id,
            update=update,
        )

    def get_campaign_details(self):
        """Wrap google campaign metrics fetching business logic on post_save dispatch signal"""

        google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")
        campaign_metadata = (
            google_client.get_campaign_metadata(
                refresh_token=self._proxy_token, customer_id=self.customer_id, campaign_id=self.remote_id
            )
            or {}
        )

        # Update internal status and importing_metric_status for production campaigns
        self.status = STATUSES.get(campaign_metadata.get("status"), "paused")
        self.importing_metrics_status = self.IN_PROCESS
        self.save(update_fields=["status", "importing_metrics_status"])

        stop_time = campaign_metadata.get("end_date")
        if stop_time:
            stop_time = parser.parse(stop_time).date()

        # Sync key for metrics completion
        sync_key = timezone.now().isoformat()

        # Get every part of facebook campaign using Hiperion
        post_metrics_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "update": False,
                "campaign_id": self.remote_id,
                "date_start": self.created_at.date(),
                "date_end": stop_time or timezone.now().date(),
                "level": "campaign",
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            in_seconds=0,
            base_url="google/",
            sync_key=sync_key,
        )

        post_metrics_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "update": False,
                "campaign_id": self.remote_id,
                "date_start": self.created_at.date(),
                "date_end": stop_time or timezone.now().date(),
                "level": "adset",
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            in_seconds=30,
            base_url="google/",
            sync_key=sync_key,
        )

        post_metrics_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "update": False,
                "campaign_id": self.remote_id,
                "date_start": self.created_at.date(),
                "date_end": stop_time or timezone.now().date(),
                "level": "ad",
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            in_seconds=120,
            base_url="google/",
            sync_key=sync_key,
        )

        post_aggregated_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "campaign_id": self.remote_id,
                "level": "campaign",
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            base_url="google/",
            in_seconds=300,
        )

        post_aggregated_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "campaign_id": self.remote_id,
                "level": "adset",
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            base_url="google/",
            in_seconds=340,
        )

        post_aggregated_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "campaign_id": self.remote_id,
                "level": "ad",
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            base_url="google/",
            in_seconds=360,
        )

        if settings.ENVIRONMENT in ["STAGING", "LOCAL"]:
            # Call directly internal method to send notification
            from webhooks.hiperion.metrics_finish import metrics_finish

            metrics_finish(campaign={"campaign_id": str(self.pk), "campaign_type": self.__social_network__})

    def update_insights(self, level):
        """Bring fresh insights from remote on campaign structure update"""

        google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")
        campaign_metadata = (
            google_client.get_campaign_metadata(
                refresh_token=self._proxy_token, customer_id=self.customer_id, campaign_id=self.remote_id
            )
            or {}
        )

        stop_time = campaign_metadata.get("end_date")
        if stop_time:
            stop_time = parser.parse(stop_time).date()

        # Sync key for metrics completion
        sync_key = timezone.now().isoformat()

        # Get every part of facebook campaign using Hiperion
        post_metrics_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "update": True,
                "campaign_id": self.remote_id,
                "date_start": self.created_at.date(),
                "date_end": stop_time or timezone.now().date(),
                "level": level,
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            in_seconds=0,
            base_url="google/",
            sync_key=sync_key,
        )

        post_aggregated_asynchronously(
            params={
                "refresh_token": self._proxy_token,
                "user_id": str(self.pk),
                "objective": self.advertising_channel_type,
                "campaign_id": self.remote_id,
                "level": level,
                "customer_id": self.customer_id or self.google_business_account.customer_id,
            },
            integrity_key=self.customer_id or self.google_business_account.customer_id,
            base_url="google/",
            in_seconds=60,
        )

    def get_campaign_parts(self):
        """Get campaign parts from google through user request"""

        # First of all, check if this campaign exists in facebook
        google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")
        campaign_metadata = google_client.get_campaign_metadata(
            refresh_token=self._proxy_token,
            customer_id=self.customer_id or self.business_account.customer_id,
            campaign_id=self.remote_id,
            reraise=True,
        )

        account_metadata = google_client.get_account_metadata_from_resource(
            refresh_token=self._proxy_token,
            resource_id=self.business_account.customer_id,
            reraise=True,
        )

        status = STATUSES.get(campaign_metadata.get("effective_status"), self.status)

        # Reuse signal method to call the business logic
        fetch_and_create_every_campaign_part(sender=GoogleCampaign, instance=self, created=True, update=True)

        # Update last synced metadata
        self.last_manual_sync_from_remote = timezone.now()
        self.deleted_from_remote_source = False
        self.stop_time = campaign_metadata.get("end_date") or self.stop_time
        self.currency = safe_attribute_getter(account_metadata, "currency_code") or self.currency
        self.draft = None
        self.errors = None
        self.has_errors = False
        self.status = status
        self.save(
            update_fields=[
                "last_manual_sync_from_remote",
                "deleted_from_remote_source",
                "stop_time",
                "currency",
                "draft",
                "errors",
                "has_errors",
                "status",
                "draft",
            ]
        )


post_save.connect(set_overview_kpis, sender=GoogleCampaign)
post_save.connect(fetch_and_create_every_campaign_part, sender=GoogleCampaign)
post_save.connect(remove_cache_entry_for_imported_campaigns_on_creation, sender=GoogleCampaign)
post_save.connect(get_campaign_details, sender=GoogleCampaign)
post_save.connect(send_to_ai_service, sender=GoogleCampaign)
pre_delete.connect(remove_budgets_if_needed, sender=GoogleCampaign)


class GoogleBiddingStrategy(models.Model):
    """Google campaign bidding strategies"""

    UNSPECIFIED = "UNSPECIFIED"
    UNKNOWN = "UNKNOWN"
    ANYWHERE_ON_PAGE = "ANYWHERE_ON_PAGE"
    TOP_OF_PAGE = "TOP_OF_PAGE"
    ABSOLUTE_TOP_OF_PAGE = "ABSOLUTE_TOP_OF_PAGE"

    LOCATION_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (ANYWHERE_ON_PAGE, _("Anywhere on page")),
        (TOP_OF_PAGE, _("Top of page")),
        (ABSOLUTE_TOP_OF_PAGE, _("Absolute top of page")),
    )

    COMMISSION = "COMMISSION"
    ENHANCED_CPC = "ENHANCED_CPC"
    MANUAL_CPC = "MANUAL_CPC"
    MANUAL_CPM = "MANUAL_CPM"
    MANUAL_CPV = "MANUAL_CPV"
    MAXIMIZE_CONVERSIONS = "MAXIMIZE_CONVERSIONS"
    MAXIMIZE_CONVERSION_VALUE = "MAXIMIZE_CONVERSION_VALUE"
    PERCENT_CPC = "PERCENT_CPC"
    TARGET_CPA = "TARGET_CPA"
    TARGET_CPM = "TARGET_CPM"
    TARGET_IMPRESSION_SHARE = "TARGET_IMPRESSION_SHARE"
    TARGET_ROAS = "TARGET_ROAS"
    TARGET_SPEND = "TARGET_SPEND"

    BIDDING_STRATEGIES_CHOICES = (
        (COMMISSION, _("Commission")),
        (ENHANCED_CPC, _("Enhanced cpc")),
        (MANUAL_CPC, _("Manual cpc")),
        (MANUAL_CPM, _("Manual cpm")),
        (MANUAL_CPV, _("Manual cpv")),
        (MAXIMIZE_CONVERSIONS, _("Maximize conversions")),
        (MAXIMIZE_CONVERSION_VALUE, _("Maximize conversion value")),
        (PERCENT_CPC, _("Percent cpc")),
        (TARGET_CPA, _("Target cpa")),
        (TARGET_CPM, _("Target cpm")),
        (TARGET_IMPRESSION_SHARE, _("Target impression share")),
        (TARGET_ROAS, _("Target roas")),
        (TARGET_SPEND, _("Target spend")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Remote identifiers
    remote_id = models.CharField(max_length=100, null=True, help_text=_("Google bidding strategy remote id"))
    resource_name = models.CharField(max_length=400, null=True, help_text=_("Google bidding strategy resource name"))

    commission_rate_micros = models.IntegerField(help_text=_("Commission. Works only with Hotel campaigns."), null=True)
    enhanced_cpc = models.BooleanField(default=False, help_text=_("This not requires extra params, only itself"))
    enhanced_cpc_enabled = models.BooleanField(default=False, help_text=_("Manual CPC"))
    manual_cpv = models.BooleanField(default=False, help_text=_("This not requires extra params, only itself"))
    target_cpa = models.IntegerField(help_text=_("Maximize Conversions"), null=True)
    target_roas = models.FloatField(help_text=_("Maximize Conversion Value, Target Roas"), null=True)
    cpc_bid_ceiling_micros = models.IntegerField(
        help_text=_("Percent CPC, Target CPA, Target Impression Share, Target Roas, Target Spend"), null=True
    )
    target_cpa_micros = models.IntegerField(help_text=_("Target CPA"), null=True)
    cpc_bid_floor_micros = models.IntegerField(help_text=_("Target CPA, Target Roas"), null=True)
    target_cpm = models.BooleanField(default=False, help_text=_("Target CPM"), null=True)
    location = models.CharField(
        max_length=200, choices=LOCATION_CHOICES, null=True, help_text=_("Target Impression Share")
    )
    location_fraction_micros = models.IntegerField(help_text=_("Target Impression Share"), null=True)
    type = models.CharField(
        max_length=200, choices=BIDDING_STRATEGIES_CHOICES, null=True, help_text=_("Bidding strategy type")
    )

    # Bidding strategy
    campaign = models.OneToOneField(GoogleCampaign, null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        verbose_name_plural = "Google bidding strategies"


class GoogleAdset(GoogleManagementMixin, SynchronizeRemoteCampaignMixin, models.Model):
    __social_network__ = "google"

    NONE = "none"
    ACTIVE = "active"
    PAUSED = "paused"
    REMOVED = "removed"

    STATUS_CHOICES = (
        (NONE, _("None")),
        (ACTIVE, _("Active")),
        (PAUSED, _("Paused")),
        (REMOVED, _("Removed")),
    )

    UNSPECIFIED = "UNSPECIFIED"
    UNKNOWN = "UNKNOWN"
    SEARCH_STANDARD = "SEARCH_STANDARD"
    DISPLAY_STANDARD = "DISPLAY_STANDARD"
    SHOPPING_PRODUCT_ADS = "SHOPPING_PRODUCT_ADS"
    SHOPPING_SMART_ADS = "SHOPPING_SMART_ADS"
    SEARCH_DYNAMIC_ADS = "SEARCH_DYNAMIC_ADS"
    SHOPPING_COMPARISON_LISTING_ADS = "SHOPPING_COMPARISON_LISTING_ADS"
    PROMOTED_HOTEL_ADS = "PROMOTED_HOTEL_ADS"
    SMART_CAMPAIGN_ADS = "SMART_CAMPAIGN_ADS"

    TYPE_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (SEARCH_STANDARD, _("Search standard")),
        (DISPLAY_STANDARD, _("Display standard")),
        (SHOPPING_PRODUCT_ADS, _("Shopping product ads")),
        (SHOPPING_SMART_ADS, _("Shopping smart ads")),
        (SEARCH_DYNAMIC_ADS, _("Search dynamic ads")),
        (SHOPPING_COMPARISON_LISTING_ADS, _("Shopping comparison listing ads")),
        (PROMOTED_HOTEL_ADS, _("Promoted hotel ads")),
        (SMART_CAMPAIGN_ADS, _("Smart campaign ads")),
    )

    OPTIMIZE = "OPTIMIZE"
    ROTATE_FOREVER = "ROTATE_FOREVER"

    AD_ROTATION_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (OPTIMIZE, _("Optimize")),
        (ROTATE_FOREVER, _("Rotate forever")),
    )

    AUDIENCE = "AUDIENCE"
    TOPIC = "TOPIC"
    GENDER = "GENDER"
    AGE_RANGE = "AGE_RANGE"
    PLACEMENT = "PLACEMENT"
    PARENTAL_STATUS = "PARENTAL_STATUS"
    INCOME_RANGE = "INCOME_RANGE"

    TARGETING_DIMENSION_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (AUDIENCE, _("Audience")),
        (TOPIC, _("Topic")),
        (GENDER, _("Gender")),
        (AGE_RANGE, _("Age range")),
        (PLACEMENT, _("Placement")),
        (PARENTAL_STATUS, _("Parental status")),
        (INCOME_RANGE, _("Income ")),
        (SMART_CAMPAIGN_ADS, _("Smart campaign ads")),
    )

    HEADLINE = "HEADLINE"
    DESCRIPTION = "DESCRIPTION"
    MANDATORY_AD_TEXT = "MANDATORY_AD_TEXT"
    MARKETING_IMAGE = "MARKETING_IMAGE"
    MEDIA_BUNDLE = "MEDIA_BUNDLE"
    YOUTUBE_VIDEO = "YOUTUBE_VIDEO"
    BOOK_ON_GOOGLE = "BOOK_ON_GOOGLE"
    LEAD_FORM = "LEAD_FORM"
    PROMOTION = "PROMOTION"
    CALLOUT = "CALLOUT"
    STRUCTURED_SNIPPET = "STRUCTURED_SNIPPET"
    SITELINK = "SITELINK"

    ASSET_FIELD_TYPES_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (HEADLINE, _("Headline")),
        (DESCRIPTION, _("Description")),
        (MANDATORY_AD_TEXT, _("Mandatory ad text")),
        (MARKETING_IMAGE, _("Marketing image")),
        (MEDIA_BUNDLE, _("Media bundle")),
        (YOUTUBE_VIDEO, _("Youtube video")),
        (BOOK_ON_GOOGLE, _("Book on google")),
        (LEAD_FORM, _("Lead form")),
        (PROMOTION, _("Promotion")),
        (CALLOUT, _("Callout")),
        (STRUCTURED_SNIPPET, _("Structured snippet")),
        (SITELINK, _("Site linke")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, null=True, blank=True)
    remote_id = models.CharField(max_length=100, null=True, blank=True)
    campaign = models.ForeignKey(GoogleCampaign, null=True, on_delete=models.CASCADE)

    status = models.CharField(
        max_length=200,
        choices=STATUS_CHOICES,
        help_text=_("Remote status in social network"),
        default=NONE,
    )

    type = models.CharField(
        max_length=200,
        choices=TYPE_CHOICES,
        help_text=_("The type of the ad group"),
        default=UNKNOWN,
    )
    ad_rotation = models.CharField(
        max_length=200,
        choices=AD_ROTATION_CHOICES,
        help_text=_("The ad rotation mode of the ad group"),
        default=UNKNOWN,
    )
    url_custom_parameters = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )
    explorer_auto_optimizer_setting = models.BooleanField(default=False)
    targeting_dimension = models.CharField(
        max_length=200,
        choices=TARGETING_DIMENSION_CHOICES,
        help_text=_("The ad rotation mode of the ad group"),
        default=UNKNOWN,
    )

    # Excluded parent assets
    excluded_parent_asset_field_types = ArrayField(
        models.CharField(max_length=200, choices=ASSET_FIELD_TYPES_CHOICES), blank=True, null=True
    )

    # Targeting settings
    targeting_restrictions = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )
    tracking_url_template = models.TextField(null=True, blank=True)
    cpc_bid_micros = models.PositiveIntegerField(null=True)
    cpm_bid_micros = models.PositiveIntegerField(null=True)
    target_cpa_micros = models.PositiveIntegerField(null=True)
    target_cpm_micros = models.PositiveIntegerField(null=True)
    target_roas = models.FloatField(null=True)
    percent_cpc_bid_micros = models.PositiveIntegerField(null=True)
    final_url_suffix = models.TextField(null=True, blank=True)

    # Remote resource name
    resource_name = models.CharField(max_length=400, null=True, help_text=_("Google adgroup resource name"))

    # When this adgroup has been added
    date_added = models.DateTimeField(default=timezone.now)

    # Date to delete this adgroup
    mark_to_delete_at = models.DateTimeField(null=True, blank=True)
    revoke_auto_deletion = models.BooleanField(default=False, help_text=_("Cancel automatic deletion by user"))

    # Draft metadata
    draft = models.JSONField(blank=True, null=True, encoder=DjangoJSONEncoder)

    # Has been validated
    validated = models.BooleanField(default=False)

    # Errors when creating in social network
    errors = models.JSONField(blank=True, null=True, encoder=DjangoJSONEncoder)

    objects = AdsetManager()

    def __str__(self):
        return f"GoogleAdgroup:: {self.name}"

    @property
    def is_published(self):
        return self.remote_id

    @cached_property
    def business_account(self):
        return self.campaign.business_account

    def pause_adset(self, **kwargs):
        """Pause a campaign using sync approach making a call against Facebook"""
        self.status = self.mutate_campaign(
            "adset",
            "pause",
            customer_id=self.campaign.customer_id or self.business_account.customer_id,
            adset_id=self.remote_id,
        )

        # Update internal effective status
        self.save(update_fields=["status"])

    def activate_adset(self, **kwargs):
        """Activate a campaign using sync approach making a call against Facebook"""
        self.status = self.mutate_campaign(
            "adset",
            "activate",
            customer_id=self.campaign.customer_id or self.business_account.customer_id,
            adset_id=self.remote_id,
        )

        # Update internal effective status
        self.save(update_fields=["status"])


class GoogleAd(GoogleManagementMixin, SynchronizeRemoteCampaignMixin, models.Model):
    __social_network__ = "google"

    NONE = "none"
    ACTIVE = "active"
    PAUSED = "paused"
    REMOVED = "removed"

    STATUS_CHOICES = (
        (NONE, _("None")),
        (ACTIVE, _("Active")),
        (PAUSED, _("Paused")),
        (REMOVED, _("Removed")),
    )

    UNSPECIFIED = "UNSPECIFIED"
    UNKNOWN = "UNKNOWN"
    MOBILE = "MOBILE"
    TABLET = "TABLET"
    DESKTOP = "DESKTOP"
    CONNECTED_TV = "CONNECTED_TV"
    OTHER = "OTHER"

    DEVICE_CHOICES = (
        (UNSPECIFIED, _("Unspecified")),
        (UNKNOWN, _("Unknown")),
        (MOBILE, _("Mobile")),
        (TABLET, _("Tablet")),
        (DESKTOP, _("Desktop")),
        (CONNECTED_TV, _("Connected tv")),
        (OTHER, _("Other")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, null=True, blank=True)
    remote_id = models.CharField(max_length=100, null=True, blank=True)
    adset = models.ForeignKey(GoogleAdset, null=True, on_delete=models.CASCADE)

    status = models.CharField(
        max_length=200,
        choices=STATUS_CHOICES,
        help_text=_("Remote status in social network"),
        default=NONE,
    )

    final_urls = ArrayField(
        models.TextField(null=True, blank=True, validators=[validators.URLValidator()]), blank=True, null=True
    )
    final_app_urls = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )
    final_mobile_urls = ArrayField(
        models.TextField(null=True, blank=True, validators=[validators.URLValidator()]), blank=True, null=True
    )
    url_custom_parameters = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )
    device_preference = models.CharField(
        max_length=200, choices=DEVICE_CHOICES, help_text=_("The ad rotation mode of the ad group"), default=UNKNOWN
    )
    url_collections = models.JSONField(
        blank=True,
        null=True,
        help_text=_("The content of this field **must** be managed through serializers"),
        encoder=DjangoJSONEncoder,
    )
    tracking_url_template = models.TextField(null=True, blank=True)
    final_url_suffix = models.TextField(null=True, blank=True)
    display_url = models.TextField(null=True, blank=True, validators=[validators.URLValidator()])

    # Remote resource name
    resource_name = models.CharField(max_length=400, null=True, help_text=_("Google ad resource name"))

    # Specific type of data (text_ads, expanded_text_ads, etc)
    content_type = models.ForeignKey(ContentType, null=True, blank=True, on_delete=models.CASCADE)
    object_id = models.UUIDField(default=uuid.uuid4, editable=False)
    content = GenericForeignKey("content_type", "object_id")

    # When this adgroup has been added
    date_added = models.DateTimeField(default=timezone.now)

    # Date to delete this adgroup
    mark_to_delete_at = models.DateTimeField(null=True, blank=True)
    revoke_auto_deletion = models.BooleanField(default=False, help_text=_("Cancel automatic deletion by user"))

    # Draft metadata
    draft = models.JSONField(blank=True, null=True, encoder=DjangoJSONEncoder)

    # Has been validated
    validated = models.BooleanField(default=False)

    # Errors when creating in social network
    errors = models.JSONField(blank=True, null=True, encoder=DjangoJSONEncoder)

    objects = AdManager()

    def clean(self):
        """This model is intended to be used with an instance of AbstractContentAd, nothing else"""
        from campaigns.models import AbstractContentAd

        super(GoogleAd, self).clean()
        if not any(
            [
                parent._meta.model_name == AbstractContentAd._meta.model_name
                for parent in self.content_type.model_class().__bases__
                if hasattr(parent, "_meta")
            ]
        ):
            raise ValidationError(_("GoogleAd model expects an AbstractContentAd instance"))

    @property
    def is_published(self):
        return self.remote_id

    @cached_property
    def business_account(self):
        return self.adset.business_account

    def pause_ad(self, **kwargs):
        """Pause an ad using sync approach making a call against Google"""
        self.status = self.mutate_campaign(
            "ad",
            "pause",
            customer_id=self.adset.campaign.customer_id or self.business_account.customer_id,
            adset_id=self.adset.remote_id,
            ad_id=self.remote_id,
        )

        # Update internal effective status
        self.save(update_fields=["status"])

    def activate_ad(self, **kwargs):
        """Activate an ad using sync approach making a call against Google"""
        self.status = self.mutate_campaign(
            "ad",
            "activate",
            customer_id=self.adset.campaign.customer_id or self.business_account.customer_id,
            adset_id=self.adset.remote_id,
            ad_id=self.remote_id,
        )

        # Update internal effective status
        self.save(update_fields=["status"])
