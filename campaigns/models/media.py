import uuid

from django.core import validators
from django.db import models
from django.utils.translation import gettext_lazy as _


class CampaignMedia(models.Model):
    """Main abstraction to save campaign creatives media"""

    FACEBOOK = "facebook"
    INSTAGRAM = "instagram"
    GOOGLE = "google"

    PLATFORMS_CHOICES = (
        (FACEBOOK, _("Facebook")),
        (INSTAGRAM, _("Instagram")),
        (GOOGLE, _("Google")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    is_video = models.BooleanField(default=False)
    provider = models.Char<PERSON>ield(max_length=200, null=True, blank=True, choices=PLATFORMS_CHOICES)
    media_url = models.TextField(blank=True, null=True, validators=[validators.URLValidator()])
    business = models.ForeignKey("business.Business", null=True, blank=True, on_delete=models.CASCADE)
    video_id = models.Char<PERSON>ield(max_length=400, null=True, blank=True)

    def __str__(self):
        return f'{self.provider} {"video" if self.is_video else "image"} - {self.business}'
