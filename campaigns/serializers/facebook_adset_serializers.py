from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from campaigns.models import FacebookAdset
from core.mixins import (BulkDeleteListSerializer, PartialUpdateMixin,
                         StringErrorsMixin)


class FacebookAdSetSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get default campaign metadata on simple GET """

    class Meta:
        model = FacebookAdset
        fields = "__all__"
        extra_kwargs = {
            "lifetime_budget": {"help_text": _("Amount in cents")},
            "daily_budget": {"help_text": _("Amount in cents")},
        }


class FacebookAdSetLocationSerializer(StringErrorsMixin, serializers.Serializer):
    """Adset location serializer to format data"""

    name = serializers.CharField(required=False, allow_blank=False)
    distance_unit = serializers.CharField(default="kilometer", allow_null=True, allow_blank=True)
    radius = serializers.IntegerField(required=False, min_value=17, max_value=80, allow_null=True)
    key = serializers.CharField(required=True, allow_blank=False)
    region_id = serializers.IntegerField(required=False, allow_null=True)
    country_code = serializers.CharField(required=False, allow_blank=True)
    primary_city_id = serializers.IntegerField(required=False, allow_null=True)
    type = serializers.ChoiceField(
        choices=[
            "city",
            "country",
            "region",
            "zip",
            "neighborhood",
            "subcity",
            "place",
            "metro_area",
            "custom_location",
            "electoral_district",
            "geo_market",
            "large_geo_area",
            "medium_geo_area",
            "small_geo_area",
            "subneighborhood",
        ],
        required=True,
        allow_blank=False,
    )
    region = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    country_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    def validate(self, data):
        radius = data.get("radius", None)
        region = data.get("region", None)

        if radius is None:
            data.pop("distance_unit", None)
            data.pop("radius", None)

        if region is None:
            data.pop("region", None)

        return data


class AdSetLocaleSerializer(StringErrorsMixin, serializers.Serializer):
    """Adset locale serializer to format data"""

    name = serializers.CharField(required=True, allow_blank=False)
    key = serializers.IntegerField(required=True, allow_null=False)


class AdSetScheduleSerializer(StringErrorsMixin, serializers.Serializer):
    """Adset schedule serializer to format data"""

    start_minute = serializers.IntegerField(required=True, min_value=0)
    end_minute = serializers.IntegerField(required=True, min_value=0)
    days = serializers.ListField(child=serializers.IntegerField(min_value=0, max_value=6), required=True)
    timezone_type = serializers.ChoiceField(choices=["USER", "ADVERTISER"], required=False)


class FrequencyControlSpecs(StringErrorsMixin, serializers.Serializer):
    event = serializers.CharField(default=FacebookAdset.IMPRESSIONS, allow_blank=False, allow_null=False)
    max_frequency = serializers.IntegerField(min_value=0, allow_null=False, default=1)
    interval_days = serializers.IntegerField(min_value=0, allow_null=False, default=7)


class FacebookAdSetBulkCreateSerializer(StringErrorsMixin, serializers.ModelSerializer):
    age_min = serializers.IntegerField(required=True, min_value=13, max_value=65)
    age_max = serializers.IntegerField(required=True, min_value=13, max_value=65)
    included_locations = FacebookAdSetLocationSerializer(many=True, required=False, default=None, allow_null=True)
    excluded_locations = FacebookAdSetLocationSerializer(many=True, required=False, default=None, allow_null=True)
    locales = AdSetLocaleSerializer(many=True, required=False, default=None, allow_null=True)
    location_types = serializers.ListSerializer(child=serializers.CharField(max_length=200), default=["home"])
    adset_schedule = AdSetScheduleSerializer(required=False, allow_null=True, default=None)
    start_time = serializers.DateTimeField(required=True, allow_null=True)
    frequency_control_specs = FrequencyControlSpecs(required=False, allow_null=True)

    class Meta:
        model = FacebookAdset
        fields = (
            "id",
            "name",
            "campaign",
            "lifetime_budget",
            "daily_budget",
            "start_time",
            "stop_time",
            "adset_schedule",
            "location_types",
            "included_locations",
            "excluded_locations",
            "locales",
            "genders",
            "age_min",
            "age_max",
            "relationship_statuses",
            "flexible_inclusions",
            "flexible_exclusions",
            "targeting_optimization",
            "connections",
            "excluded_connections",
            "friends_of_connections",
            "publisher_platforms",
            "device_platforms",
            "facebook_positions",
            "audience_network_positions",
            "instagram_positions",
            "messenger_positions",
            "optimization_goal",
            "billing_event",
            "conversion_event_location",
            "frequency_control_specs",
            "bid_amount",
            "bid_strategy",
        )
        extra_kwargs = {
            "campaign": {"required": True, "allow_null": False, "write_only": True},
            "id": {"read_only": True},
            "genders": {"default": "both"},
            "optimization_goal": {"required": True, "allow_null": True, "allow_blank": False},
            "billing_event": {"required": True, "allow_null": True, "allow_blank": False},
            "lifetime_budget": {"help_text": _("Amount in cents")},
            "daily_budget": {"help_text": _("Amount in cents")},
            "bid_strategy": {"help_text": _("Only needed if budget is set in campaign")},
            "bid_amount": {"help_text": _("Amount in cents")},
        }

    def validate_location_types(self, location_types):
        if "travel_in" in location_types and len(location_types) != 1:
            raise serializers.ValidationError({"location_types": _('You cannot use "travel_in" with other values.')})
        return location_types

    def validate(self, data):  # NOQA: C901
        publisher_platforms = data.get("publisher_platforms", [])
        facebook_positions = data.get("facebook_positions", [])
        device_platforms = data.get("device_platforms", [])
        audience_network_positions = data.get("audience_network_positions", [])
        instagram_positions = data.get("instagram_positions", [])
        messenger_positions = data.get("messenger_positions", [])
        targeting_optimization = data.get("targeting_optimization", None)
        age_min = data.get("age_min")
        age_max = data.get("age_max")
        campaign = data.get("campaign")
        optimization_goal = data.get("optimization_goal")
        billing_event = data.get("billing_event")
        start_time = data.get("start_time")
        stop_time = data.get("stop_time")
        lifetime_budget = data.get("lifetime_budget")
        daily_budget = data.get("daily_budget")
        frequency_control_specs = data.get("frequency_control_specs", None)
        bid_amount = data.get("bid_amount", None)
        bid_strategy = data.get("bid_strategy", None)

        if lifetime_budget and daily_budget:
            raise serializers.ValidationError(
                {"lifetime_budget": _("You cannot set lifetime_budget and daily_budget at once")}
            )

        if (lifetime_budget or daily_budget) and campaign.budget:
            raise serializers.ValidationError(
                {"non_field_errors": _("You can only set an ad set budget or a campaign budget, but not both.")}
            )

        if facebook_positions and "facebook" not in publisher_platforms:
            raise serializers.ValidationError(
                {"publisher_platforms": _("Facebook is a must when provide publisher platforms")}
            )

        if (
            facebook_positions
            and "instant_article" in facebook_positions
            and ("feed" not in facebook_positions or "mobile" not in device_platforms)
        ):
            raise serializers.ValidationError(
                {"facebook_positions": _("Instant article position requires feed position and mobile platform")}
            )

        if (
            audience_network_positions
            and "instream_video" in audience_network_positions
            and campaign.objective == "CONVERSIONS"
        ):
            raise serializers.ValidationError(
                {
                    "audience_network_positions": _(
                        "Instream video is not a valid audience network position with CONVERSIONS objective"
                    )
                }
            )

        if (
            facebook_positions
            and "story" in facebook_positions
            and not ("feed" in facebook_positions and "story" in instagram_positions)
            and "mobile" not in device_platforms
        ):
            raise serializers.ValidationError(
                {"facebook_positions": _("Story needs facebook feed or instagram story and mobile as device platform")}
            )

        if (
            facebook_positions
            and ("marketplace" in facebook_positions or "search" in facebook_positions)
            and "feed" not in facebook_positions
        ):
            raise serializers.ValidationError(
                {"facebook_positions": _("Marketplace/search needs facebook feed as well")}
            )

        if (
            messenger_positions
            and "messenger_home" in messenger_positions
            and "facebook" not in publisher_platforms
            and "feed" not in facebook_positions
        ):
            raise serializers.ValidationError(
                {
                    "messenger_positions": _(
                        "Messenger home needs facebook as publisher platform and feed as facebook position"
                    )
                }
            )

        if (
            messenger_positions
            and "story" in messenger_positions
            and "story" not in instagram_positions
            and "feed" not in facebook_positions
        ):
            raise serializers.ValidationError(
                {
                    "messenger_positions": _(
                        "Messenger story needs story as instagram platform and feed as facebook position"
                    )
                }
            )

        if age_min > age_max:
            raise serializers.ValidationError({"age_min": _("Min age cannot be greater than max age")})

        if targeting_optimization == FacebookAdset.EXPANSION_ALL and campaign.objective not in [
            "LEAD_GENERATION",
            "LINK_CLICKS",
            "POST_ENGAGEMENT",
            "CONVERSIONS",
        ]:
            raise serializers.ValidationError(
                {"targeting_optimization": _("Only lead_generation, link_clicks, post_engagement and web conversions")}
            )

        # TODO: Set default optimization goal to avoid 400 error on adset creation
        if optimization_goal not in FacebookAdset.OPTIMIZATION_GOAL_DEFAULTS.get(campaign.objective, []):
            data["optimization_goal"] = FacebookAdset.OPTIMIZATION_GOAL_DEFAULTS.get(campaign.objective)[0]
            # raise serializers.ValidationError(
            #     {
            #         "optimization_goal": _("Invalid optimization goal for {objective} objective").format(
            #             objective=campaign.objective
            #         )
            #     }
            # )

        if (
            optimization_goal in FacebookAdset.BILLING_EVENT_ALLOWED_CHOICES_PER_OPTIMIZATION_GOAL
            and billing_event
            not in FacebookAdset.BILLING_EVENT_ALLOWED_CHOICES_PER_OPTIMIZATION_GOAL.get(optimization_goal, [])
        ):
            raise serializers.ValidationError(
                {"billing_event": _("Invalid billing event for {og} optimization goal").format(og=optimization_goal)}
            )

        if not daily_budget and not lifetime_budget and not campaign.budget:
            raise serializers.ValidationError(
                {"non_field_errors": _("Neither campaign nor adset has budget. Set one to continue.")}
            )

        if (
            not daily_budget
            and (lifetime_budget or (campaign.budget and not campaign.is_daily_budget))
            and not stop_time
        ):
            raise serializers.ValidationError(
                {
                    "stop_time": _(
                        "Adsets with lifetime_budget needs a stop time."
                        " This time must be greater than 24h since start_time"
                    )
                }
            )

        if stop_time and start_time > stop_time:
            raise serializers.ValidationError({"start_time": _("Start time cannot not be greater than stop time")})

        if (
            campaign.objective == "BRAND_AWARENESS"
            and optimization_goal != FacebookAdset.AD_RECALL_LIFT
            and billing_event != FacebookAdset.IMPRESSIONS
        ):
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "BRAND_AWARENESS objective requires AD_RECALL_LIFT optimization goal and "
                        "IMPRESSIONS billing event"
                    )
                }
            )

        if campaign.objective != "REACH" and frequency_control_specs:
            raise serializers.ValidationError(
                {"frequency_control_specs": _("Frequency limits are only allowed for REACH campaigns")}
            )

        if campaign.objective == "BRAND_AWARENESS" and bid_amount:
            raise serializers.ValidationError({"bid_amount": _("BRAND_AWARENESS campaigns not requires bid_amount")})

        if optimization_goal == FacebookAdset.LANDING_PAGE_VIEWS and not campaign.pixel_id:
            raise serializers.ValidationError(
                {
                    "pixel_id": _(
                        "LANDING_PAGE_VIEWS optimization goal requires a valid pixel with PageView tracking event set"
                    )
                }
            )

        if campaign.bid_strategy == "LOWEST_COST_WITHOUT_CAP" and bid_amount:
            raise serializers.ValidationError(
                {"bid_amount": _("Campaigns with lowest cost strategy cannot set the bid amount")}
            )

        if not campaign.bid_strategy and not bid_strategy:
            raise serializers.ValidationError({"bid_strategy": _("Either campaign or adset needs a bid strategy")})

        if campaign.bid_strategy and (daily_budget or lifetime_budget):
            data["bid_strategy"] = campaign.bid_strategy

        return data


class FacebookAdSetPartialUpdateSerializer(StringErrorsMixin, PartialUpdateMixin, serializers.ModelSerializer):
    adset_schedule = AdSetScheduleSerializer(required=False, allow_null=True, default=None)
    age_min = serializers.IntegerField(required=False, min_value=13, max_value=65)
    age_max = serializers.IntegerField(required=False, min_value=13, max_value=65)
    included_locations = FacebookAdSetLocationSerializer(many=True, required=False, default=None, allow_null=True)
    excluded_locations = FacebookAdSetLocationSerializer(many=True, required=False, default=None, allow_null=True)
    locales = AdSetLocaleSerializer(many=True, required=False, default=None, allow_null=True)
    location_types = serializers.ListSerializer(child=serializers.CharField(max_length=200), default=["home"])
    start_time = serializers.DateTimeField(required=True, allow_null=True)

    class Meta:
        model = FacebookAdset
        fields = (
            "id",
            "name",
            "lifetime_budget",
            "daily_budget",
            "start_time",
            "stop_time",
            "adset_schedule",
            "location_types",
            "included_locations",
            "excluded_locations",
            "locales",
            "genders",
            "age_min",
            "age_max",
            "relationship_statuses",
            "flexible_inclusions",
            "flexible_exclusions",
            "targeting_optimization",
            "connections",
            "excluded_connections",
            "friends_of_connections",
            "publisher_platforms",
            "device_platforms",
            "facebook_positions",
            "audience_network_positions",
            "instagram_positions",
            "messenger_positions",
            "optimization_goal",
            "billing_event",
            "conversion_event_location",
            "draft",
        )
        extra_kwargs = {
            "id": {"read_only": True},
            "genders": {"required": False},
            "lifetime_budget": {"help_text": _("Amount in cents")},
            "daily_budget": {"help_text": _("Amount in cents")},
            "draft": {"read_only": True},
            "optimization_goal": {"allow_null": False, "allow_blank": False},
            "billing_event": {"allow_null": True, "allow_blank": False},
        }

    def validate(self, attrs):
        start_time = attrs.get("start_time")
        publisher_platforms = attrs.get("publisher_platforms", [])
        facebook_positions = attrs.get("facebook_positions", [])
        device_platforms = attrs.get("device_platforms", [])
        audience_network_positions = attrs.get("audience_network_positions", [])
        instagram_positions = attrs.get("instagram_positions", [])
        messenger_positions = attrs.get("messenger_positions", [])
        targeting_optimization = attrs.get("targeting_optimization", None)
        age_min = attrs.get("age_min")
        age_max = attrs.get("age_max")
        optimization_goal = attrs.get("optimization_goal")
        stop_time = attrs.get("stop_time")
        lifetime_budget = attrs.get("lifetime_budget")
        daily_budget = attrs.get("daily_budget")

        if self.instance.campaign_running_in_pomegranate:
            raise serializers.ValidationError(
                {"non_field_errors": _("In creation/updating process, you cannot edit the adset")}
            )

        if start_time and self.instance.is_published and start_time != self.instance.start_time:
            raise serializers.ValidationError({"start_time": _("In published adsets, you cannot edit the start time")})

        if stop_time and start_time and start_time > stop_time:
            raise serializers.ValidationError({"end_time": _("Start time could not be greater than end time")})

        if lifetime_budget and daily_budget:
            raise serializers.ValidationError(
                {"lifetime_budget": _("You cannot set lifetime_budget and daily_budget at once")}
            )

        if not daily_budget and not lifetime_budget and not self.instance.campaign.budget:
            raise serializers.ValidationError(
                {"non_field_errors": _("Neither campaign nor adset has budget. Set one to continue.")}
            )

        if (
            not daily_budget
            and (lifetime_budget or (self.instance.campaign.budget and not self.instance.campaign.is_daily_budget))
            and (not stop_time and not self.instance.stop_time)
        ):
            raise serializers.ValidationError(
                {
                    "stop_time": _(
                        "Adsets with lifetime_budget needs a stop time."
                        " This time must be greater than 24h since start_time"
                    )
                }
            )

        if (lifetime_budget or daily_budget) and self.instance.campaign.budget:
            raise serializers.ValidationError(
                {"non_field_errors": _("You can only set an ad set budget or a campaign budget, but not both.")}
            )

        if facebook_positions and "facebook" not in publisher_platforms:
            raise serializers.ValidationError(
                {"publisher_platforms": _("Facebook is a must when provide facebook positions")}
            )

        if (
            facebook_positions
            and "instant_article" in facebook_positions
            and ("feed" not in facebook_positions or "mobile" not in device_platforms)
        ):
            raise serializers.ValidationError(
                {"facebook_positions": _("Instant article position requires feed position and mobile platform")}
            )

        if (
            audience_network_positions
            and "instream_video" in audience_network_positions
            and self.instance.campaign_objective == "CONVERSIONS"
        ):
            raise serializers.ValidationError(
                {
                    "audience_network_positions": _(
                        "Instream video is not a valid audience network position with CONVERSIONS objective"
                    )
                }
            )

        if (
            facebook_positions
            and "story" in facebook_positions
            and not ("feed" in facebook_positions and "story" in instagram_positions)
            and "mobile" not in device_platforms
        ):
            raise serializers.ValidationError(
                {"facebook_positions": _("Story needs facebook feed or instagram story and mobile as device platform")}
            )

        if (
            facebook_positions
            and ("marketplace" in facebook_positions or "search" in facebook_positions)
            and "feed" not in facebook_positions
        ):
            raise serializers.ValidationError(
                {"facebook_positions": _("Marketplace/search needs facebook feed as well")}
            )

        if (
            messenger_positions
            and "messenger_home" in messenger_positions
            and "facebook" not in publisher_platforms
            and "feed" not in facebook_positions
        ):
            raise serializers.ValidationError(
                {
                    "messenger_positions": _(
                        "Messenger home needs facebook as publisher platform and feed as facebook position"
                    )
                }
            )

        if (
            messenger_positions
            and "story" in messenger_positions
            and "story" not in instagram_positions
            and "feed" not in facebook_positions
        ):
            raise serializers.ValidationError(
                {
                    "messenger_positions": _(
                        "Messenger story needs story as instagram platform and feed as facebook position"
                    )
                }
            )

        if age_min > age_max:
            raise serializers.ValidationError({"age_min": _("Min age cannot be greater than max age")})

        if targeting_optimization == FacebookAdset.EXPANSION_ALL and self.instance.campaign_objective not in [
            "LEAD_GENERATION",
            "LINK_CLICKS",
            "POST_ENGAGEMENT",
            "CONVERSIONS",
        ]:
            raise serializers.ValidationError(
                {"targeting_optimization": _("Only lead_generation, link_clicks, post_engagement and web conversions")}
            )

        if optimization_goal and optimization_goal not in FacebookAdset.OPTIMIZATION_GOAL_DEFAULTS.get(
            self.instance.campaign_objective, []
        ):
            raise serializers.ValidationError(
                {
                    "optimization_goal": _("Invalid optimization goal for {objective} objective").format(
                        objective=self.instance.campaign_objective
                    )
                }
            )

        return attrs

    def update(self, instance, validated_data):
        # Only update draft iff we don't have anything yet saved
        if instance.is_published and not instance.draft:
            validated_data["draft"] = instance.draft_changes

        return super(FacebookAdSetPartialUpdateSerializer, self).update(instance, validated_data)


class FacebookAdSetBulkDestroySerializer(serializers.ModelSerializer):
    """Bulk delete with list serializer"""

    id = serializers.PrimaryKeyRelatedField(
        queryset=FacebookAdset.objects.all(), help_text=_("A UUID string identifying this facebook adset.")
    )

    class Meta:
        model = FacebookAdset
        fields = ("id",)
        list_serializer_class = BulkDeleteListSerializer

    def validate(self, attrs):
        facebook_adset = attrs.get("id")
        if facebook_adset.is_published:
            raise serializers.ValidationError({"non_field_errors": _("Published ad sets cannot be deleted")})
        return attrs
