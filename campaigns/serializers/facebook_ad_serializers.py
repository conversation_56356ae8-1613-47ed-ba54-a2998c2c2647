from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_countries.serializer_fields import Country<PERSON>ield
from django_countries.serializers import CountryFieldMixin
from rest_framework import serializers

from campaigns.models import CampaignMedia, FacebookAd, FacebookCampaign
from core.mixins import (BulkCreateListSerializer, BulkDeleteListSerializer,
                         BulkUpdateListSerializer,
                         ManyToManyGetOrCreateOrUpdateSerializerMixin,
                         PartialUpdateMixin, StringErrorsMixin)
from core.utils import parse_phone, validate_phone


class FacebookAdSerializer(StringErrorsMixin, CountryFieldMixin, serializers.ModelSerializer):
    """Serializer to get default ad metadata on simple GET """

    phone = serializers.CharField(read_only=True, allow_null=True)
    phone_country_code = CountryField(read_only=True, allow_null=True)
    direction = serializers.CharField(read_only=True, allow_null=True)

    class Meta:
        model = FacebookAd
        fields = "__all__"

    def to_representation(self, instance):
        representation = super(FacebookAdSerializer, self).to_representation(instance)

        # Parse call_to_action_value accordingly
        call_to_action_value = representation.get("call_to_action_value", None)
        if call_to_action_value:
            # Try parse phone
            phone_country_code, phone = parse_phone(call_to_action_value)

            representation.update(
                **{
                    "phone": phone,
                    "phone_country_code": phone_country_code,
                    "direction": call_to_action_value if not phone else None,
                }
            )

        return representation


class CampaignMediaSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get multimedia data for ad"""

    class Meta:
        model = CampaignMedia
        fields = ("id", "provider", "media_url", "business", "is_video", "video_id")
        extra_kwargs = {
            "id": {"read_only": True},
            "provider": {"required": True},
            "media_url": {"required": True},
            "business": {"required": True},
            "is_video": {"required": False},
            "video_id": {"required": False},
        }

    def validate(self, attrs):
        provider = attrs["provider"]
        is_video = attrs.get("is_video")
        video_id = attrs.get("video_id")

        if provider == "facebook" and is_video and not video_id:
            raise serializers.ValidationError(
                {"non_field_errors": _("Facebook videos must be uploaded in Facebook Ad Account")}
            )
        return attrs


class FacebookAdBulkCreateSerializer(
    StringErrorsMixin, ManyToManyGetOrCreateOrUpdateSerializerMixin, CountryFieldMixin, serializers.ModelSerializer
):
    """Serializer to create ads in bulk fashion"""

    media = CampaignMediaSerializer(many=True, required=False)
    phone = serializers.CharField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if CTA is CALL_NOW")
    )
    phone_country_code = CountryField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if CTA is CALL_NOW")
    )
    direction = serializers.CharField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if CTA is GET_DIRECTIONS")
    )
    lead_form_id = serializers.CharField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if objective is LEAD_GENERATION")
    )

    class Meta:
        model = FacebookAd
        fields = (
            "id",
            "name",
            "adset",
            "message",
            "title",
            "link",
            "description",
            "caption",
            "media",
            "call_to_action",
            "utm_tags",
            "facebook_post",
            "instagram_post",
            "phone",
            "phone_country_code",
            "direction",
            "lead_form_id",
            "conversion_domain",
        )
        extra_kwargs = {
            "adset": {"required": True, "allow_null": False, "write_only": True},
            "id": {"read_only": True},
            "utm_tags": {
                "help_text": _("utm_source=XX&utm_medium=YY&utm_campaign=ZZ"),
                "default": "utm_source=ADTUO&utm_medium=AD&utm_campaign={utm_campaign}",
            },
        }
        list_serializer_class = BulkCreateListSerializer

    m2m_related_fields = ("media",)  # ManyToManyMixin use only

    def validate(self, attrs):
        utm_tags = attrs.get("utm_tags")
        lead_form_id = attrs.get("lead_form_id")
        adset = attrs.get("adset")
        call_to_action = attrs.get("call_to_action")
        direction = attrs.pop("direction", None)
        phone = attrs.pop("phone", None)
        phone_country_code = attrs.pop("phone_country_code", None)
        instagram_post = attrs.get("instagram_post")
        facebook_post = attrs.get("facebook_post")
        media = attrs.get("media")
        conversion_domain = attrs.get("conversion_domain")
        link = attrs.get("link")

        if adset.campaign_objective == FacebookCampaign.LEAD_GENERATION:
            if not lead_form_id:
                raise serializers.ValidationError(
                    {"lead_form_id": _("Lead form is needed to create an ad for lead_generation campaign objective")}
                )

        if (
            call_to_action
            and adset.campaign_objective in ["CONVERSIONS", "POST_ENGAGEMENT"]
            and call_to_action not in FacebookAd.CALL_TO_ACTION_DEFAULTS[adset.campaign_objective]
        ):
            raise serializers.ValidationError(
                {
                    "call_to_action": _("Invalid call to action for {objective} objective").format(
                        objective=adset.campaign_objective
                    )
                }
            )

        if call_to_action == FacebookAd.GET_DIRECTIONS:
            if not direction or (direction and not direction.startswith("fbgeo://")):
                raise serializers.ValidationError(
                    {"direction": _("When call to action is get_directions, you must specify valid coordinates")}
                )

            attrs["call_to_action_value"] = direction

        if adset.campaign_objective == "POST_ENGAGEMENT" and not instagram_post and not facebook_post:
            raise serializers.ValidationError(
                {"non_field_errors": _("Campaigns with post engagement objective expect facebook or instagram posts")}
            )

        if adset.campaign_objective != FacebookCampaign.POST_ENGAGEMENT and not media:
            raise serializers.ValidationError({"media": _("Non post engagement campaigns need a media")})

        if call_to_action == FacebookAd.CALL_NOW:
            if not phone_country_code:
                raise serializers.ValidationError(
                    {"phone_country_code": _("CALL_NOW call to action requires a valid phone country code")}
                )

            validated_phone = validate_phone(phone, phone_country_code)

            if not validated_phone:
                raise serializers.ValidationError({"phone": _("Phone is not valid in selected country")})

            attrs["call_to_action_value"] = validated_phone

        if adset.campaign_objective == FacebookCampaign.VIDEO_VIEWS and len(media) > 1:
            raise serializers.ValidationError(
                {"media": _("Campaigns with VIDEO_VIEWS objective requires exactly one video")}
            )

        if adset.campaign_objective == FacebookCampaign.VIDEO_VIEWS and not media.is_video:
            raise serializers.ValidationError({"media": _("Campaign with VIDEO_VIEWS objective requires a video")})

        # Set default call to action to like page if none is provided
        if adset.campaign_objective == FacebookCampaign.PAGE_LIKES and not call_to_action:
            attrs["call_to_action"] = FacebookAd.LIKE_PAGE

        if adset.campaign_objective == FacebookCampaign.CONVERSIONS and not conversion_domain and not link:
            raise serializers.ValidationError(
                {"conversion_domain": _("Campaigns with conversions objective needs a valid conversion domain or link")}
            )

        if adset.campaign_objective == FacebookCampaign.CONVERSIONS and not conversion_domain and link:
            attrs["conversion_domain"] = link.split("://")[-1]

        attrs["utm_tags"] = utm_tags.format(
            utm_campaign=f'{adset.campaign.objective}-{adset.campaign.date_added.strftime("%y%m%d")}'
        )

        return attrs


class FacebookAdPartialUpdateSerializer(
    StringErrorsMixin, ManyToManyGetOrCreateOrUpdateSerializerMixin, PartialUpdateMixin, serializers.ModelSerializer
):
    media = CampaignMediaSerializer(many=True, required=False)
    phone = serializers.CharField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if CTA is CALL_NOW")
    )
    phone_country_code = CountryField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if CTA is CALL_NOW")
    )
    direction = serializers.CharField(
        required=False, allow_blank=False, allow_null=True, help_text=_("Required if CTA is GET_DIRECTIONS")
    )
    lead_form_id = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    class Meta:
        model = FacebookAd
        fields = (
            "id",
            "name",
            "message",
            "title",
            "link",
            "description",
            "caption",
            "media",
            "call_to_action",
            "utm_tags",
            "facebook_post",
            "instagram_post",
            "draft",
            "phone",
            "phone_country_code",
            "direction",
            "lead_form_id",
            "conversion_domain",
            "call_to_action_value",
        )
        extra_kwargs = {"id": {"read_only": True}, "draft": {"read_only": True}}

    m2m_related_fields = ("media",)  # ManyToManyMixin use only

    def validate(self, attrs):
        media = attrs.get("media", None)
        call_to_action = attrs.get("call_to_action")
        direction = attrs.pop("direction", None)
        phone = attrs.pop("phone", None)
        phone_country_code = attrs.pop("phone_country_code", None)
        conversion_domain = attrs.get("conversion_domain")

        if self.instance.campaign_running_in_pomegranate:
            raise serializers.ValidationError(
                {"non_field_errors": _("In creation/updating process, you cannot edit the ad")}
            )

        if self.instance.adset.campaign.objective != FacebookCampaign.POST_ENGAGEMENT and (
            media is not None and not media
        ):
            raise serializers.ValidationError(
                {
                    "media": _("Media cannot be empty for this campaign objective: {objective}").format(
                        objective=self.instance.adset.campaign.objective
                    )
                }
            )

        if call_to_action == FacebookAd.GET_DIRECTIONS:
            if not direction or (direction and not direction.startswith("fbgeo://")):
                raise serializers.ValidationError(
                    {
                        "call_to_action_value": _(
                            "When call to action is get_directions, you must specify valid coordinates"
                        )
                    }
                )

            attrs["call_to_action_value"] = direction

        if call_to_action == FacebookAd.CALL_NOW:
            if not phone_country_code:
                raise serializers.ValidationError(
                    {"phone_country_code": _("CALL_NOW call to action requires a valid phone country code")}
                )

            validated_phone = validate_phone(phone, phone_country_code)

            if not validated_phone:
                raise serializers.ValidationError({"phone": _("Phone is not valid in selected country")})

            attrs["call_to_action_value"] = validated_phone

        if (
            self.instance.adset.campaign_objective == FacebookCampaign.CONVERSIONS
            and not conversion_domain
            and not self.instance.conversion_domain
        ):
            raise serializers.ValidationError(
                {"conversion_domain": _("Campaigns with conversions objective needs a valid conversion domain")}
            )

        return attrs

    def exclude_fields_in_many_to_many(self, instance, attribute):
        """Override this method from M2MMixin to exclude fields from relationship when updating instance"""
        if attribute == "media":
            excluded_medias = instance.draft.get("media", []) if instance.draft else []
            return [Q(id__in=excluded_medias)]

    def update(self, instance, validated_data):
        if instance.is_published and not instance.draft:
            validated_data["draft"] = instance.draft_changes

        # Update this ad
        return super(FacebookAdPartialUpdateSerializer, self).update(instance, validated_data)


class FacebookAdBulkPartialUpdateSerializer(FacebookAdPartialUpdateSerializer):
    """Bulk partial update with list serializer"""

    id = serializers.UUIDField()

    class Meta(FacebookAdPartialUpdateSerializer.Meta):
        list_serializer_class = BulkUpdateListSerializer


class FacebookAdBulkDestroySerializer(serializers.ModelSerializer):
    """Bulk delete with list serializer"""

    id = serializers.PrimaryKeyRelatedField(
        queryset=FacebookAd.objects.all(), help_text=_("A UUID string identifying this facebook ad.")
    )

    class Meta:
        model = FacebookAd
        fields = ("id",)
        list_serializer_class = BulkDeleteListSerializer

    def validate(self, attrs):
        facebook_ad = attrs.get("id")
        if facebook_ad.is_published:
            raise serializers.ValidationError({"non_field_errors": _("Published ads cannot be deleted")})
        return attrs


class FacebookAdPreviewQuerySerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Parse options to generate the preview for given ad"""

    AD_PREVIEW_FORMATS = (
        ("RIGHT_COLUMN_STANDARD", "right_column_standard"),
        ("DESKTOP_FEED_STANDARD", "desktop_feed_standard"),
        ("MOBILE_FEED_STANDARD", "mobile_feed_standard"),
        ("MOBILE_FEED_BASIC", "mobile_feed_basic"),
        ("MOBILE_INTERSTITIAL", "mobile_interstitial"),
        ("MOBILE_BANNER", "mobile_banner"),
        ("MOBILE_MEDIUM_RECTANGLE", "mobile_medium_rectangle"),
        ("MOBILE_FULLWIDTH", "mobile_fullwidth"),
        ("MOBILE_NATIVE", "mobile_native"),
        ("INSTAGRAM_STANDARD", "instagram_standard"),
        ("AUDIENCE_NETWORK_OUTSTREAM_VIDEO", "audience_network_outstream_video"),
        ("INSTANT_ARTICLE_STANDARD", "instant_article_standard"),
        ("INSTREAM_VIDEO_DESKTOP", "instream_video_desktop"),
        ("INSTREAM_VIDEO_MOBILE", "instream_video_mobile"),
        ("SUGGESTED_VIDEO_DESKTOP", "suggested_video_desktop"),
        ("SUGGESTED_VIDEO_MOBILE", "suggested_video_mobile"),
    )

    instagram_user_id = serializers.CharField(
        max_length=200,
        required=False,
        allow_null=False,
        allow_blank=False,
        help_text=_("Instagram user_id required to show instagram post previews"),
    )
    ad_format = serializers.ChoiceField(choices=AD_PREVIEW_FORMATS, required=False, default="MOBILE_FEED_STANDARD")

    class Meta:
        model = FacebookAd
        fields = ("instagram_user_id", "ad_format")

    def validate(self, attrs):
        instagram_user_id = attrs.get("instagram_user_id", None)
        if (
            self.instance.adset.campaign.objective == FacebookCampaign.POST_ENGAGEMENT
            and self.instance.instagram_post
            and not instagram_user_id
        ):
            raise serializers.ValidationError(
                {"instagram_user_id": _("For instagram ads, is needed the instagram_user_id")}
            )
        return attrs


class FacebookAdPreviewResponseSerializer(StringErrorsMixin, serializers.Serializer):
    """Parse options to generate the preview for given ad"""

    body = serializers.CharField(
        read_only=True, allow_null=True, allow_blank=True, help_text=_("iframe with the preview")
    )


class FacebookAdDiscardChangesSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to retrieve a copy of an ad when discarding changes"""

    media = CampaignMediaSerializer(many=True, read_only=True)

    class Meta:
        model = FacebookAd
        fields = (
            "id",
            "name",
            "adset",
            "message",
            "title",
            "link",
            "description",
            "caption",
            "media",
            "call_to_action",
            "utm_tags",
            "facebook_post",
            "instagram_post",
            "draft",
        )
