from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_countries.serializer_fields import Country<PERSON>ield
from django_countries.serializers import CountryFieldMixin
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from campaigns.models import (CampaignMedia, FacebookAd, FacebookAdset,
                              FacebookCampaign)
from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.utils import parse_phone


class FacebookCampaignMediaInAdsSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get multimedia data for ad"""

    class Meta:
        model = CampaignMedia
        fields = ("id", "provider", "media_url", "is_video", "video_id", "business")


class FacebookAdSetAdsSerializer(StringErrorsMixin, CountryFieldMixin, serializers.ModelSerializer):
    """Serializer to encapsulate ads in adsets in campaign serialized response"""

    media = FacebookCampaignMediaInAdsSerializer(many=True, read_only=True)
    phone = serializers.CharField(read_only=True, allow_null=True)
    phone_country_code = CountryField(read_only=True, allow_null=True)
    direction = serializers.CharField(read_only=True, allow_null=True)

    class Meta:
        model = FacebookAd
        exclude = ("adset",)

    def to_representation(self, instance):
        representation = super(FacebookAdSetAdsSerializer, self).to_representation(instance)

        # Parse call_to_action_value accordingly
        call_to_action_value = representation.get("call_to_action_value", None)
        if call_to_action_value:
            # Try parse phone
            phone_country_code, phone = parse_phone(call_to_action_value)

            representation.update(
                **{
                    "phone": phone,
                    "phone_country_code": phone_country_code,
                    "direction": call_to_action_value if not phone else None,
                }
            )

        return representation


class FacebookCampaignAdSetsSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get every adset metadata from campaign"""

    ads = FacebookAdSetAdsSerializer(many=True, source="facebookad_set", read_only=True)

    class Meta:
        model = FacebookAdset
        exclude = ("campaign",)


class DataSerializer(serializers.ListField):
    data = serializers.BooleanField()


class LabelSerializer(serializers.ListField):
    data = serializers.DateField()


class DatasetsSerializer(serializers.Serializer):
    data = DataSerializer()
    name = serializers.CharField(max_length=200)
    total = serializers.BooleanField()


class OverviewDataSerializer(serializers.Serializer):
    datasets = DatasetsSerializer(many=True)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    label = LabelSerializer()


class CommonTreeSerializer(serializers.Serializer):
    """Common fields for overview table serializer"""

    api_id = serializers.CharField(max_length=200, read_only=True)
    id = serializers.CharField(max_length=200, read_only=True)
    name = serializers.CharField(max_length=200, read_only=True)
    has_comment = serializers.BooleanField(read_only=True)


class AdSerializer(CommonTreeSerializer):
    """Ad structure in the tree for common fields"""

    pass


class AdsetSerializer(CommonTreeSerializer):
    """Adset structure in the tree for common fields"""

    ads = AdSerializer(many=True, read_only=True)


class CampaignTreeSerializer(CommonTreeSerializer):
    """Campaign structure in the tree for common fields"""

    adsets = AdsetSerializer(many=True, read_only=True)


class OverviewTableSerializer(serializers.Serializer):
    """Full tree structure"""

    campaign = CampaignTreeSerializer(read_only=True)
    start_date = serializers.DateField(read_only=True)
    end_date = serializers.DateField(read_only=True)


class OverviewChartSerializer(serializers.Serializer):
    """Chart view"""

    datasets = serializers.JSONField(
        read_only=True,
        help_text=_("Compliant format with highcharts. For more information, please, visit hiperion api doc."),
    )
    end_time = serializers.DateField(read_only=True)
    start_time = serializers.DateField(read_only=True)
    label = serializers.ListField(child=serializers.DateField(), read_only=True)
    comments = serializers.ListField(child=serializers.IntegerField(), read_only=True)


class CampaignRecommendationsStructureSerializer(serializers.Serializer):
    """Inner recommendations array structure"""

    date = serializers.DateTimeField(read_only=True)
    expected = serializers.FloatField(read_only=True)
    name = serializers.CharField(read_only=True)
    real = serializers.FloatField(read_only=True)


class CampaignRecommendationsSerializer(serializers.Serializer):
    """Campaign recommendations serializer for redoc"""

    recommendations = CampaignRecommendationsStructureSerializer(many=True, read_only=True)
    traffic_light_status = serializers.IntegerField(min_value=-1, max_value=1, allow_null=True, read_only=True)


class CampaignRetrieveQueryParametersSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse extra query parameters to get reports"""

    since = serializers.DateField(allow_null=True, required=False, help_text=_("Allows null"))
    until = serializers.DateField(allow_null=True, required=False, help_text=_("Allows null"))
    aggregation_mode = serializers.ChoiceField(
        required=False, default="auto", choices=["auto", "days", "weeks", "months"]
    )


class FacebookCampaignSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to fetch data from campaigns"""

    is_published = serializers.BooleanField(read_only=True)
    adsets = FacebookCampaignAdSetsSerializer(many=True, source="facebookadset_set", read_only=True)

    overview_data = serializers.SerializerMethodField(allow_null=True, read_only=True)
    overview_table = serializers.SerializerMethodField(allow_null=True, read_only=True)
    recommendations = serializers.SerializerMethodField(allow_null=True, read_only=True)
    social_network = serializers.CharField(source="__social_network__", read_only=True)

    class Meta:
        model = FacebookCampaign
        exclude = (
            "start_utc_time",
            "end_utc_time",
            "draft",
            "has_errors",
            "facebook_business_account",
        )
        extra_kwargs = {
            "creation_status": {
                "help_text": _(
                    """
                - none: Unused status.
                - in_process: Campaign is being deployed in Facebook using Pomegranate Engine.
                - draft: Campaign exists in local database, but is not deployed yet.
                - finished: Campaign is already published in Facebook.
                """
                )
            }
        }

    def _parse_date_params(self, obj):
        query_params = self.context["request"].query_params
        since = query_params.get("since", None)
        until = query_params.get("until", None)
        aggregation_mode = query_params.get("aggregation_mode", None)

        if since is None:
            since = obj.created_at.date()

        if until is None:
            until = timezone.now().date()

        return since, until, aggregation_mode

    @swagger_serializer_method(serializer_or_field=CampaignRecommendationsSerializer(allow_null=True))
    def get_recommendations(self, obj):
        """Chart view from campaign with a set of daily metrics"""
        since, until, _ = self._parse_date_params(obj)
        return obj.get_recommendations(since, until)

    @swagger_serializer_method(serializer_or_field=OverviewChartSerializer(allow_null=True))
    def get_overview_data(self, obj):
        """Chart view from campaign with a set of daily metrics"""
        since, until, aggregation_mode = self._parse_date_params(obj)
        return obj.get_overview_kpis(since, until, aggregation_mode)

    @swagger_serializer_method(serializer_or_field=OverviewTableSerializer(allow_null=True))
    def get_overview_table(self, obj):
        """Tree view from campaign with a set of metrics"""
        since, until, _ = self._parse_date_params(obj)
        return obj.get_overview_table(since, until)


class FacebookCampaignListSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to retrieve basic campaign metadata information"""

    business = serializers.PrimaryKeyRelatedField(
        source="facebook_business_account.business", read_only=True, allow_null=True
    )
    traffic_light_status = serializers.IntegerField(read_only=True, allow_null=True)
    social_network = serializers.CharField(source="__social_network__", read_only=True)

    class Meta:
        model = FacebookCampaign
        fields = (
            "id",
            "name",
            "objective",
            "business",
            "imported",
            "traffic_light_status",
            "social_network",
            "date_added",
        )


class FacebookCampaignBulkCreateSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to create a lot of campaigns in bulk fashion"""

    currency = serializers.CharField(required=False, default="EUR")
    is_published = serializers.BooleanField(read_only=True)

    class Meta:
        model = FacebookCampaign
        fields = (
            "id",
            "name",
            "facebook_business_account",
            "pixel_id",
            "conversion_event_type",
            "custom_conversion_event_id",
            "currency",
            "budget",
            "objective",
            "imported",
            "remote_id",
            "is_published",
            "creation_status",
            "ad_account_id",
            "created_at",
            "bid_strategy",
            "is_daily_budget",
            "special_ad_categories",
            "spend_cap",
            "stop_time",
        )
        extra_kwargs = {
            "facebook_business_account": {"required": True, "allow_null": False, "write_only": True},
            "budget": {"help_text": _("Amount in cents")},
            "creation_status": {
                "read_only": True,
                "help_text": _(
                    """
                - none: Unused status.
                - in_process: Campaign is being deployed in Facebook using Pomegranate Engine.
                - draft: Campaign exists in local database, but is not deployed yet.
                - finished: Campaign is already published in Facebook.
                """
                ),
            },
            "imported": {
                "help_text": _(
                    "Internal use only when importing campaigns. Do not use in creation process under any circumstances"
                )
            },
            "id": {"read_only": True},
            "remote_id": {
                "required": True,
                "allow_null": True,
                "help_text": _("For non imported campaigns, set this field as null"),
            },
            "ad_account_id": {"help_text": _("Ad account id where deploy the campaign")},
            "conversion_event_type": {"required": False, "allow_null": True, "allow_blank": True},
            "custom_conversion_event_id": {
                "required": False,
                "allow_null": True,
                "allow_blank": True,
                "help_text": _(
                    "Set custom conversion id if conversion event is OTHER. "
                    "Can be useful in order to track specific events in case of pixel has lots of custom events."
                ),
            },
            "created_at": {"help_text": _("For non imported campaigns, set this field as null")},
            "bid_strategy": {"help_text": _("Only needed if budget is set in campaign")},
            "pixel_id": {
                "required": False,
                "allow_null": True,
                "allow_blank": True,
                "help_text": _("Only required for CONVERSIONS objective campaigns."),
            },
            "stop_time": {
                "required": False,
                "allow_null": True,
                "help_text": _(
                    "Internal use only when importing campaigns. Do not use in creation process under any circumstances"
                ),
            },
        }
        validators = [
            serializers.UniqueTogetherValidator(
                queryset=FacebookCampaign.objects.all(),
                fields=("remote_id", "facebook_business_account"),
                message=_("You have a similar campaign in this business."),
            )
        ]

    def validate(self, attrs):
        budget = attrs.get("budget", None)
        is_daily_budget = attrs.get("is_daily_budget", False)
        objective = attrs.get("objective", None)
        conversion_event_type = attrs.get("conversion_event_type", None)
        custom_conversion_id = attrs.get("custom_conversion_id", None)
        pixel_id = attrs.get("pixel_id", None)
        imported = attrs.get("imported")
        created_at = attrs.get("created_at", None)
        ad_account_id = attrs.get("ad_account_id", None)
        facebook_business_account = attrs.get("facebook_business_account")
        bid_strategy = attrs.get("bid_strategy", None)

        if objective is None or objective not in [
            FacebookCampaign.BRAND_AWARENESS,
            FacebookCampaign.CONVERSIONS,
            FacebookCampaign.EVENT_RESPONSES,
            FacebookCampaign.LEAD_GENERATION,
            FacebookCampaign.LINK_CLICKS,
            FacebookCampaign.MESSAGES,
            FacebookCampaign.PAGE_LIKES,
            FacebookCampaign.POST_ENGAGEMENT,
            FacebookCampaign.PRODUCT_CATALOG_SALES,
            FacebookCampaign.REACH,
            FacebookCampaign.VIDEO_VIEWS,
        ]:
            raise serializers.ValidationError(
                {"objective": _("Invalid objective {objective}").format(objective=objective)}
            )

        if objective == FacebookCampaign.CONVERSIONS:
            valid_conversion_events = [event[0] for event in FacebookCampaign.CONVERSION_TYPES_CHOICES]
            if not conversion_event_type or conversion_event_type not in valid_conversion_events:
                raise serializers.ValidationError(
                    {
                        "conversion_event_type": _(
                            "Campaigns with objective CONVERSIONS, is needed a valid conversion event type"
                        )
                    }
                )

            if conversion_event_type == FacebookCampaign.OTHER and not custom_conversion_id:
                raise serializers.ValidationError(
                    {"custom_conversion_id": _("Custom conversion event needs a valid custom conversion event id")}
                )

        if objective == FacebookCampaign.CONVERSIONS and not pixel_id:
            raise serializers.ValidationError(
                {"pixel_id": _("Campaigns with objective {objective} needs a valid pixel").format(objective=objective)}
            )

        if not imported and is_daily_budget and not budget:
            raise serializers.ValidationError(
                {"is_daily_budget": _("Budget cannot be empty when daily budget is selected")}
            )

        if budget and not bid_strategy:
            raise serializers.ValidationError({"bid_strategy": _("Bid strategy is needed when budget is set")})

        # Set default attributes
        attrs.update(
            **{
                "creation_status": FacebookCampaign.IN_PROCESS if imported else FacebookCampaign.DRAFT,
                "status": FacebookCampaign.NONE,
                "start_utc_time": timezone.now(),
                "created_at": created_at or timezone.now(),
                "ad_account_id": ad_account_id or facebook_business_account.ad_account.ad_account_id,
            }
        )

        return attrs


class FacebookCampaignPartialUpdateSerializer(StringErrorsMixin, PartialUpdateMixin, serializers.ModelSerializer):
    is_published = serializers.BooleanField(read_only=True)

    class Meta:
        model = FacebookCampaign
        fields = (
            "budget",
            "objective",
            "conversion_event_type",
            "custom_conversion_event_id",
            "pixel_id",
            "is_daily_budget",
            "is_published",
            "creation_status",
            "draft",
            "overview_kpis",
            "campaign_kpis",
            "bid_strategy",
            "name",
        )
        extra_kwargs = {
            "creation_status": {
                "read_only": True,
                "help_text": _(
                    """
                - none: Unused status.
                - in_process: Campaign is being deployed in Facebook using Pomegranate Engine.
                - draft: Campaign exists in local database, but is not deployed yet.
                - finished: Campaign is already published in Facebook.
                """
                ),
            },
            "budget": {"help_text": _("Amount in cents")},
            "custom_conversion_event_id": {
                "help_text": _(
                    "Set custom conversion id if conversion event is OTHER. "
                    "Can be useful in order to track specific events in case of pixel has lots of custom events."
                )
            },
            "draft": {"read_only": True},
        }

    def validate(self, attrs):
        budget = attrs.get("budget", None)
        objective = attrs.get("objective", None)
        is_daily_budget = attrs.get("is_daily_budget", None)

        if self.instance.creation_status == FacebookCampaign.IN_PROCESS:
            raise serializers.ValidationError(
                {"creation_status": _("In creation/updating process, you cannot edit the campaign")}
            )

        if objective and self.instance.is_published:
            raise serializers.ValidationError(
                {"objective": _("In published campaigns, you cannot update the objective")}
            )

        if self.instance.is_published and (
            is_daily_budget is not None and self.instance.is_daily_budget != is_daily_budget
        ):
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "Changing from a lifetime budget to a daily budget or vice versa is not allowed for campaigns."
                    )
                }
            )

        if is_daily_budget is not None and (not budget and not self.instance.budget):
            raise serializers.ValidationError(
                {"is_daily_budget": _("Budget cannot be empty when daily budget is selected")}
            )

        return attrs

    def update(self, instance, validated_data):
        overview_kpis = validated_data.get("overview_kpis", None)
        campaign_kpis = validated_data.get("campaign_kpis", None)

        has_more_fields = (
            (overview_kpis and campaign_kpis and len(validated_data) > 2)
            or ((overview_kpis or campaign_kpis) and len(validated_data) > 1)
            or (not overview_kpis and not campaign_kpis and len(validated_data) > 0)
        )

        # Only update draft iff we don't have anything yet saved and we have more attributes than _kpis
        if instance.is_published and not instance.draft and has_more_fields:
            validated_data["draft"] = instance.draft_changes

        return super(FacebookCampaignPartialUpdateSerializer, self).update(instance, validated_data)


class FacebookCampaignDiscardChangesSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to retrieve a copy of a campaign when discarding changes"""

    is_published = serializers.BooleanField(read_only=True)

    class Meta:
        model = FacebookCampaign
        exclude = (
            "start_utc_time",
            "end_utc_time",
            "draft",
            "has_errors",
            "facebook_business_account",
        )
