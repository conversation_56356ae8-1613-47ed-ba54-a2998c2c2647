from campaigns.views import (FacebookAdSetViewSet, FacebookAdViewSet,
                             FacebookCampaignViewSet, GoogleAdSetViewSet,
                             GoogleAdViewSet, GoogleCampaignViewSet)
from core.router import BulkRouter

router = BulkRouter()
router.register("facebook-campaigns", FacebookCampaignViewSet, basename="facebook-campaigns")
router.register("facebook-adsets", FacebookAdSetViewSet, basename="facebook-adsets")
router.register("facebook-ads", FacebookAdViewSet, basename="facebook-ads")

# Google campaign routes
router.register("google-campaigns", GoogleCampaignViewSet, basename="google-campaigns")
router.register("google-adsets", GoogleAdSetViewSet, basename="google-adsets")
router.register("google-ads", GoogleAdViewSet, basename="google-ads")

urlpatterns = [] + router.urls
app_name = "campaigns"
