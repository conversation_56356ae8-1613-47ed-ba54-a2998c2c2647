# Generated by Django 3.2 on 2021-06-02 11:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0021_auto_20210507_1917"),
    ]

    operations = [
        migrations.AddField(
            model_name="facebookadset",
            name="is_dynamic_creative",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="facebookadset",
            name="billing_event",
            field=models.CharField(
                blank=True,
                choices=[
                    ("CLICKS", "Clicks"),
                    ("IMPRESSIONS", "Impressions"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("NONE", "None"),
                    ("OFFER_CLAIMS", "Offer claims"),
                    ("PAGE_LIKES", "Page likes"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("THRUPLAY", "Thruplay"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="facebookadset",
            name="optimization_goal",
            field=models.<PERSON>r<PERSON><PERSON>(
                blank=True,
                choices=[
                    ("APP_DOWNLOADS", "App downloads"),
                    ("APP_INSTALLS", "App installs"),
                    ("AD_RECALL_LIFT", "Ad recall lift"),
                    ("BRAND_AWARENESS", "Brand awareness"),
                    ("CLICKS", "Clicks"),
                    ("DERIVED_EVENTS", "Derived events"),
                    ("ENGAGED_USERS", "Engaged users"),
                    ("EVENT_RESPONSES", "Event responses"),
                    ("IMPRESSIONS", "Impressions"),
                    ("LANDING_PAGE_VIEWS", "Landing page views"),
                    ("LEAD_GENERATION", "Lead generation"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("NONE", "None"),
                    ("OFFER_CLAIMS", "Offer claims"),
                    ("OFFSITE_CONVERSIONS", "Offsite conversions"),
                    ("PAGE_ENGAGEMENT", "Page engagement"),
                    ("PAGE_LIKES", "Page likes"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("QUALITY_CALL", "Quality call"),
                    ("QUALITY_LEAD", "Quality lead"),
                    ("REACH", "Reach"),
                    ("REPLIES", "Replies"),
                    ("SOCIAL_IMPRESSIONS", "Social impressions"),
                    ("THRUPLAY", "Thruplay"),
                    ("TWO_SECOND_CONTINUOUS_VIDEO_VIEWS", "Two second continuous video views"),
                    ("VALUE", "Value"),
                    ("VISIT_INSTAGRAM_PROFILE", "Visit instagram profile"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookcampaign",
            name="objective",
            field=models.CharField(
                choices=[
                    ("BRAND_AWARENESS", "Brand awareness"),
                    ("CONVERSIONS", "Conversions"),
                    ("EVENT_RESPONSES", "Event responses"),
                    ("LEAD_GENERATION", "Lead generation"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("MESSAGES", "Messages"),
                    ("PAGE_LIKES", "Page likes"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("PRODUCT_CATALOG_SALES", "Product catalog sales"),
                    ("REACH", "Reach"),
                    ("VIDEO_VIEWS", "Video views"),
                ],
                max_length=200,
            ),
        ),
    ]
