# Generated by Django 3.1.7 on 2021-04-07 10:00

from django.db import migrations


def setting_default_kpis(apps, schema_editor):
    FacebookCampaign = apps.get_model("campaigns", "FacebookCampaign")
    FacebookCampaign.objects.filter(overview_kpis__isnull=True).update(
        overview_kpis=["results", "cost", "spend", "total_clicks"]
    )
    FacebookCampaign.objects.filter(campaign_kpis__isnull=True).update(
        campaign_kpis=["results", "cost", "spend", "total_clicks"]
    )


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0016_merge_20210324_1154"),
    ]

    operations = [
        migrations.RunPython(setting_default_kpis, reverse_code=migrations.RunPython.noop),
    ]
