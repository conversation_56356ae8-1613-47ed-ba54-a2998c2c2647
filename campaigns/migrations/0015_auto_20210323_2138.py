# Generated by Django 3.1.7 on 2021-03-23 20:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0014_auto_20210323_1609"),
    ]

    operations = [
        migrations.AddField(
            model_name="facebookadset",
            name="bid_strategy",
            field=models.CharField(
                choices=[
                    ("LOWEST_COST_WITHOUT_CAP", "Lowest cost without bid cap"),
                    ("LOWEST_COST_WITH_BID_CAP", "Lowest cost with bid cap"),
                    ("COST_CAP", "Cost cap"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookadset",
            name="optimization_goal",
            field=models.CharField(
                blank=True,
                choices=[
                    ("AD_RECALL_LIFT", "Ad recall lift"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("STORE_VISITS", "Store visits"),
                    ("LEAD_GENERATION", "Lead generation"),
                    ("CONVERSIONS", "Conversions"),
                    ("PAGE_LIKES", "Page likes"),
                    ("LANDING_PAGE_VIEWS", "Landing page views"),
                    ("IMPRESSIONS", "Impressions"),
                    ("REACH", "Reach"),
                    ("SOCIAL_IMPRESSIONS", "Social impressions"),
                    ("VALUE", "Value"),
                    ("REPLIES", "Replies"),
                    ("OFFSITE_CONVERSIONS", "Offsite conversions"),
                    ("OFFSITE_CONVERSIONS_REACH", "Offsite conversions reach"),
                    ("TWO_SECOND_CONTINUOUS_VIDEO_VIEWS", "Two seconds continuous video views"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookcampaign",
            name="bid_strategy",
            field=models.CharField(
                choices=[
                    ("LOWEST_COST_WITHOUT_CAP", "Lowest cost without bid cap"),
                    ("LOWEST_COST_WITH_BID_CAP", "Lowest cost with bid cap"),
                    ("COST_CAP", "Cost cap"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookcampaign",
            name="objective",
            field=models.CharField(
                choices=[
                    ("BRAND_AWARENESS", "Brand awareness"),
                    ("CONVERSIONS", "Conversions"),
                    ("EVENT_RESPONSES", "Event responses"),
                    ("LEAD_GENERATION", "Lead generation"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("MESSAGES", "Messages"),
                    ("PAGE_LIKES", "Page likes"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("PRODUCT_CATALOG_SALES", "Product catalog sales"),
                    ("REACH", "Reach"),
                    ("VIDEO_VIEWS", "Video views"),
                ],
                max_length=200,
            ),
        ),
    ]
