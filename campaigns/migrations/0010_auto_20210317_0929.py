# Generated by Django 3.1.7 on 2021-03-17 08:29

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0009_auto_20210309_1847"),
    ]

    operations = [
        migrations.AddField(
            model_name="facebookcampaign",
            name="campaign_kpis",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=200), blank=True, null=True, size=None
            ),
        ),
        migrations.AddField(
            model_name="facebookcampaign",
            name="overview_kpis",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=200), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name="facebookadset",
            name="billing_event",
            field=models.CharField(
                blank=True,
                choices=[
                    ("CLICKS", "Clicks"),
                    ("IMPRESSIONS", "Impressions"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("none", "None"),
                    ("OFFER_CLAIMS", "Offer claims"),
                    ("PAGE_LIKES", "Page likes"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("THRUPLAY", "Thruplay"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookadset",
            name="conversion_event_location",
            field=models.CharField(
                blank=True,
                choices=[("WEBSITE", "Website"), ("MESSENGER", "Messenger"), ("WHATSAPP", "Whatsapp")],
                help_text="App destination in Facebook",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookadset",
            name="optimization_goal",
            field=models.CharField(
                blank=True,
                choices=[
                    ("AD_RECALL_LIFT", "Ad recall lift"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("STORE_VISITS", "Store visits"),
                    ("LEAD_GENERATION", "Lead generation"),
                    ("CONVERSIONS", "Conversions"),
                    ("PAGE_LIKES", "Page likes"),
                    ("LANDING_PAGE_VIEWS", "Landing page views"),
                    ("IMPRESSIONS", "Impressions"),
                    ("REACH", "Reach"),
                    ("SOCIAL_IMPRESSIONS", "Social impressions"),
                    ("VALUE", "Value"),
                    ("REPLIES", "Replies"),
                    ("OFFSITE_CONVERSIONS", "Offsite conversions"),
                    ("OFFSITE_CONVERSIONS_REACH", "Offsite conversions reach"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookcampaign",
            name="conversion_event_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ADD_PAYMENT_INFO", "Add payment info"),
                    ("ADD_TO_CART", "Add to cart"),
                    ("ADD_TO_WISHLIST", "Add to wishlist"),
                    ("COMPLETE_REGISTRATION", "Complete registration"),
                    ("CONTACT", "Contact"),
                    ("CONTENT_VIEW", "Content view"),
                    ("CUSTOMIZE_PRODUCT", "Customize product"),
                    ("DONATE", "Donate"),
                    ("FIND_LOCATION", "Find location"),
                    ("INITIATED_CHECKOUT", "Initiated checkout"),
                    ("LEAD", "Lead"),
                    ("OTHER", "Other"),
                    ("PURCHASE", "Purchase"),
                    ("SCHEDULE", "Schedule"),
                    ("SEARCH", "Search"),
                    ("START_TRIAL", "Start trial"),
                    ("SUBMIT_APPLICATION", "Submit application"),
                    ("SUBSCRIBE", "Subscribe"),
                ],
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="facebookcampaign",
            name="objective",
            field=models.CharField(
                choices=[
                    ("BRAND_AWARENESS", "Brand awareness"),
                    ("CONVERSIONS", "Conversions"),
                    ("EVENT_RESPONSES", "Event responses"),
                    ("LEAD_GENERATION", "Lead generation"),
                    ("LINK_CLICKS", "Link clicks"),
                    ("MESSAGES", "Messages"),
                    ("PAGE_LIKES", "Page likes"),
                    ("POST_ENGAGEMENT", "Post engagement"),
                    ("PRODUCT_CATALOG_SALES", "Product catalog sales"),
                    ("REACH", "Reach"),
                ],
                max_length=200,
            ),
        ),
    ]
