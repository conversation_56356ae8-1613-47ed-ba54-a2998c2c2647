API_KEY = "40020defb66cfe5b5ae23bb6f3b1faaa8d7905297514cd6b1be3507d0ffe7aa08b90be3c"
URL = "https://gexik.api-us1.com/api/3/contacts/5"

import requests

# querystring = {"orders[email]": "ASC", "id": "5"}
querystring = {}
headers = {"Accept": "application/json", "Api-Token": API_KEY}

response = requests.request("GET", URL, headers=headers, params=querystring)

print(response.status_code)
print(response.text)

URL = "https://gexik.api-us1.com/api/3/contacts/5"
headers = {"Accept": "application/json", "Api-Token": API_KEY}
response = requests.request("PUT", URL, headers=headers, params={
    "contact": {
        "email": "<EMAIL>",
        "fieldValues": [
            {
                "field": "1",
                "value": "84"
            }
        ]
    }
})


print(response.status_code)
print(response.text)
