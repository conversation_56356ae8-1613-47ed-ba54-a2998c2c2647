import ffmpeg
from io import BytesIO
import numpy as np

with open("/home/<USER>/Documents/audio2.web", "rb") as fp:
    fpb = BytesIO(fp.read())

output_fpb = BytesIO()
process = ffmpeg.input("pipe:").output("-", format="s16le", acodec="pcm_s16le", ac=1,
                                       ar=16000).run_async(pipe_stdin=True)

process.communicate(input=fpb.getbuffer())

array = np.frombuffer(fpb.getbuffer(), np.int16).flatten().astype(np.float32) / 32768.0

# ffmpeg.input("pipe:").output("/home/<USER>/Documents/audio3.web", format="s16le", acodec="pcm_s16le", ac=1,
#                              ar=16000).run(cmd=["ffmpeg", "-nostdin"], capture_stdout=True, capture_stderr=True,
#                                            input=fpb.getbuffer())
