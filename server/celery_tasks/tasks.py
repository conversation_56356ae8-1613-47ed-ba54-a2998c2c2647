import traceback
from io import BytesIO

import numpy as np
import whisperx
from celery import Task
from werkzeug.datastructures import FileStorage

from server.openai_monkey_patch import audio


class WhisperTask(Task):
    HF_TOKEN = "*************************************"
    model_size = "large-v2"

    # def __init__(self):
    #     super().__init__()
    #     self.model = whisperx.load_model(self.model_size, device="cpu", compute_type="int8", language="es")

    def __call__(self, audio_wave, who, *args, **kwargs):
        self.model = whisperx.load_model(self.model_size, device="cpu", compute_type="int8", language="es")
        data = np.frombuffer(audio_wave, np.int16).flatten().astype(np.float32) / 32768.0
        print("HOLAAAAA!!!", self.model, self.model.transcribe)
        try:
            result = self.model.transcribe(data, batch_size=16)
            print("RESUSLT ", result)
        except Exception as err:
            print("ERROR ", err)
            return
        return result
        # data = np.frombuffer(audio_wave, np.int16).flatten().astype(np.float32) / 32768.0
        # result = None
        # try:
        #     result = self.model.transcribe(data, batch_size=16)
        #     print(f">>>>> [{who or 'computer'}]: {result}")
        # except Exception:
        #     print(traceback.print_exc(chain=False))
        #
        # return result


class WhisperOpenAITask(Task):

    def __call__(self, speech_file, who, *args, **kwargs):
        speech_file = BytesIO(speech_file)
        speech_file.filename = "output.wav"

        # speech_file = FileStorage(
        #     speech_file,
        #     filename="output.wav",
        #     content_type="audio/wav"
        # )
        result = audio.transcribe("whisper-1", speech_file, api_key="***************************************************", language="es")
        print(f">>>>> [{who or 'computer'}]: {result.get('text').encode('utf-8').decode('utf-8')}")

        return result
