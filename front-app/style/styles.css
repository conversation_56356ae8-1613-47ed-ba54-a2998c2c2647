/*
Author: <PERSON><PERSON>, Septiember 2021
#Email: <EMAIL>
#Github @bagalasamuele
*/

body{
  background-color: #140c40;
}

#app {
  width: 400px;
  height: 400px;
  margin: 0 auto;
  position: relative;
}

.marker {
  position: absolute;
  width: 60px;
  left: 172px;
  top: -20px;
  z-index: 2;
}

.wheel {
  width: 100%;
  height: 100%;
}

button {
  background-color: #4760ff;
  border-radius: 25px;
  display: inline-block;
  cursor: pointer;
  color: #ffffff;
  font-family: 'Roboto', sans-serif;
  font-style: normal;
  font-size: 17px;
  padding: 16px 31px;
  text-decoration: none;
  text-shadow: 0px 1px 0px white;
}

  button:hover {
      opacity: 0.6;
  }

button:active {
position: relative;
background-color: #140c40;
top: 1px;
}

.blur {
  animation: blur 10s;
}

@keyframes blur {
  0% {
    filter: blur(1.5px);
  }
  80% {
    filter: blur(1.5px);
  }
  100% {
    filter: blur(0px);
  }
}
