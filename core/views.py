from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.utils.translation import gettext_lazy as _
from django_countries import countries
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from drf_yasg.views import get_schema_view
from rest_framework import authentication, permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

from core.models import TemporaryDeletionEntries
from core.serializers import (ListCountriesSerializer,
                              TemporaryDeletionEntriesSerializer)
from core.swagger.schema_generators import BoxtoBoxOpenAPISchemaGenerator
from core.utils import get_openapi_error_response, get_openapi_response

API_DESCRIPTION = f"""
- Listing always returns the objects user has access to. i.e: superuser always gets a list of all users.
- PUT verbs are for updating the entire object. They'll validate all fields and required fields must be sent.
- PATCH verbs are for updating part of the object (one or more fields).
 They'll validate only the sent fields and required fields doesn't have to be sent.
- Create endpoints return errors per field as string. e.g:
```
{{
    "field1": "field1 is required",
    "field2": "Invalid format",
    "password": "Password too short.\\nPassword can't be all numbers."
    "{settings.REST_FRAMEWORK['NON_FIELD_ERRORS_KEY']}": "Stripe error"
}}
```
- Some endpoints use cache in order to improve the performance. These endpoints are marked with a **[C]** in the name and
a brief explanation is given in the description. In short, you can evict the cached response setting using this header:
```
X-API-cache-eviction=true
```
"""

schema_view = get_schema_view(
    openapi.Info(
        title="Box2box Backoffice API",
        default_version="v1",
        description=API_DESCRIPTION,
        contact=openapi.Contact(email="<EMAIL>"),
    ),
    # validators=["flex", "ssv"], # Can slow down the server according to docs
    public=True,
    authentication_classes=(authentication.BasicAuthentication,),
    permission_classes=(permissions.IsAdminUser,),
    generator_class=BoxtoBoxOpenAPISchemaGenerator,
)


class ListCountriesView(APIView):
    """Endpoint to get a list of valid countries"""

    permission_classes = (permissions.AllowAny,)

    @swagger_auto_schema(
        operation_summary="Get a list of countries",
        operation_description="Get a list of codes from all valid countries",
        responses={
            status.HTTP_200_OK: ListCountriesSerializer(many=True),
        },
    )
    def get(self, request, format=None):
        """Return a list of all countries"""
        countries_list = [
            {
                "code": country[0],
                "name": country[1],
            }
            for country in countries
        ]
        return Response(ListCountriesSerializer(countries_list, many=True).data)


class ChunkAPIMixin:
    @swagger_auto_schema(
        operation_summary="Perform a chunk 'upload' to later handle the deletion",
        operation_description="""
        Perform a chunk 'upload' to later handle the deletion.
        You must send the token and the list of ids to be deleted.
        You can also include the tracking by setting the include_tracking flag to True.
        """,
        responses={
            status.HTTP_200_OK: get_openapi_response("All chunks processed successfully"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="chunks", detail=False)
    def chunks(self, request, pk=None):
        # Ensure the view is a subclass of ModelViewSet
        if not isinstance(self, viewsets.ModelViewSet):
            raise ImproperlyConfigured(
                f"The {self.__class__.__name__} class must inherit from ModelViewSet to use ChunkAPIMixin."
            )

        total_chunks = int(request.headers.get("X-Total-Chunks", -1))
        chunk_index = int(request.headers.get("X-Chunk-Index", -1))

        if chunk_index < 0 or total_chunks < 0:
            return Response(
                {"non_field_errors": _("X-Chunk-Index and X-Total-Chunks are required")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        qp_serializer = TemporaryDeletionEntriesSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        token = qp_serializer.validated_data.get("token")
        ids = qp_serializer.validated_data.get("ids", [])
        include_recursive = qp_serializer.validated_data.get("include_recursive")
        mode = qp_serializer.validated_data.get("mode")

        if mode == "update" and not hasattr(self, "bulk_update"):
            raise ImproperlyConfigured(f"The {self.__class__.__name__} class must implement bulk_update method.")

        if not token and chunk_index > 1:
            return Response(
                {"token": _("Token is required on subsequent requests")}, status=status.HTTP_400_BAD_REQUEST
            )

        if len(ids) > 2000:
            return Response({"non_field_errors": "Payload too large"}, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)

        is_last_chunk = chunk_index == total_chunks

        if not ids and not is_last_chunk:
            return Response({"non_field_errors": _("No ids provided")}, status=status.HTTP_400_BAD_REQUEST)

        if not token:
            token = TemporaryDeletionEntries.generate_token()

        TemporaryDeletionEntries.objects.bulk_create([
            TemporaryDeletionEntries(token=token, related_id=id, table_name=self.queryset.model._meta.db_table)
            for id in ids
        ])

        if is_last_chunk:
            if mode == "update":
                # Call to parent class bulk_update method to perform the real update and after that, delete the deletion entries
                self.action = "bulk_update"
                self.bulk_update(request, pk, token=token)
            else:
                TemporaryDeletionEntries.objects.bulk_delete(
                    token=token, model_class=self.queryset.model, include_recursive=include_recursive
                )
            return Response({"token": None, "msg": _("All chunks processed successfully")}, status=status.HTTP_200_OK)

        return Response({"token": token, "msg": _("Chunk processed")}, status=status.HTTP_200_OK)
