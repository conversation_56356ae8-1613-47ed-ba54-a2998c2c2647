import base64
import json

from django.conf import settings
from google.oauth2 import service_account
from googleapiclient.discovery import build


class NotFoundError(Exception):
    pass


class BaseWrapper:
    def __init__(self, *args, **kwargs):
        self._client = None

    @property
    def client(self):
        """Initialize client if needed"""
        if self._client is None:
            self._client = self.lazy_init_client()
        return self._client

    def lazy_init_client(self):
        raise NotImplementedError("Not implemented yet!")


class PolicyMemberMixin:
    def add_member(self, policy, role_list, member):
        """Adds a new member to a role binding."""

        for binding in policy["bindings"]:
            role = binding["role"]

            # Ensure the role is in role list to add the member there
            if role in role_list and "members" in binding and member not in binding["members"]:
                binding["members"].append(member)

        return policy

    def remove_member(self, policy, role_list, member):
        """Removes a  member from a role binding."""

        for binding in policy["bindings"]:
            role = binding["role"]

            # Ensure the role is in role list to add the member there
            if role in role_list and "members" in binding and member in binding["members"]:
                binding["members"].remove(member)

        return policy


class PolicyRoleMixin:
    def add_roles(self, policy, role_list, member):
        """Adds a new role binding to a policy."""

        for role in role_list:
            binding = {"role": role, "members": [f"serviceAccount:{member}"]}
            policy["bindings"].append(binding)

        return policy


class ServiceAccountPolicyWrapper(BaseWrapper, PolicyMemberMixin, PolicyRoleMixin):
    project_id = settings.GOOGLE_CLOUD_PROJECT_NAME
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]

    def lazy_init_client(self):
        """Lazy init for clients, delaying it upto first time is used"""
        try:
            credentials = service_account.Credentials.from_service_account_file(
                settings.GOOGLE_CLOUD_JSON_ACCOUNT, scopes=self.scopes
            )
            service = build("cloudresourcemanager", "v1", credentials=credentials)
        except (FileNotFoundError, TypeError):
            raise NotFoundError(
                f"Wrong `{settings.GOOGLE_CLOUD_JSON_ACCOUNT}` credentials to init the google sheets client"
            )
        return service.projects()

    def get_policy(self, version=1):
        return self.client.getIamPolicy(
            resource=settings.GOOGLE_CLOUD_PROJECT_NAME, body={"options": {"requestedPolicyVersion": version}}
        ).execute()

    def set_policy(self, policy):
        self.client.setIamPolicy(resource=settings.GOOGLE_CLOUD_PROJECT_NAME, body={"policy": policy}).execute()


class ServiceAccountKeyMixin:
    def create_key(self, service_account_email):
        response = (
            self.client.keys()
            .create(
                name=f"projects/{settings.GOOGLE_CLOUD_PROJECT_NAME}/serviceAccounts/{service_account_email}",
            )
            .execute()
        )

        return response["name"], json.loads(base64.b64decode(response["privateKeyData"]).decode("utf-8"))

    def delete_key(self, full_key_name):
        return self.client.keys().delete(name=full_key_name).execute()


class ServiceAccountWrapper(BaseWrapper, ServiceAccountKeyMixin):
    project_id = settings.GOOGLE_CLOUD_PROJECT_NAME
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]

    def lazy_init_client(self):
        """Lazy init for clients, delaying it upto first time is used"""
        try:
            credentials = service_account.Credentials.from_service_account_file(
                settings.GOOGLE_CLOUD_JSON_ACCOUNT, scopes=self.scopes
            )
            service = build("iam", "v1", credentials=credentials)
        except (FileNotFoundError, TypeError):
            raise NotFoundError(
                f"Wrong `{settings.GOOGLE_CLOUD_JSON_ACCOUNT}` credentials to init the google sheets client"
            )
        return service.projects().serviceAccounts()

    def create_service_account(self, name, display_name, description):
        """Creates a service account"""

        return self.client.create(
            name=f"projects/{settings.GOOGLE_CLOUD_PROJECT_NAME}",
            body={
                "accountId": name,
                "serviceAccount": {
                    "description": description,
                    "displayName": display_name,
                },
            },
        ).execute()

    def delete_service_account(self, email):
        """Deletes a service account"""

        return self.client.delete(
            name=f"projects/{settings.GOOGLE_CLOUD_PROJECT_NAME}/serviceAccounts/{email}",
        ).execute()

    def enable_service_account(self, email):
        """Enables a service account"""

        return self.client.enable(
            name=f"projects/{settings.GOOGLE_CLOUD_PROJECT_NAME}/serviceAccounts/{email}",
        ).execute()

    def disable_service_account(self, email):
        """Enables a service account"""

        return self.client.disable(
            name=f"projects/{settings.GOOGLE_CLOUD_PROJECT_NAME}/serviceAccounts/{email}",
        ).execute()


class ServiceAcountDisabledWrapper(BaseWrapper):
    """Wrapper to disable Service account handling in non production environments"""

    def lazy_init_client(self):
        return None


SERVICE_ACCOUNT_WRAPPER = {
    "LOCAL": ServiceAccountWrapper,#ServiceAcountDisabledWrapper,
    "STAGING": ServiceAcountDisabledWrapper,
    "PROD": ServiceAccountWrapper,
}

# Too risky as policies are overwritten instead of appended. It is preferred to grant/revoke roles manually thru
# Google Cloud Console instead. DO NOT USE!
SERVICE_ACCOUNT_POLICY_WRAPPER = {
    "LOCAL": ServiceAcountDisabledWrapper,
    "STAGING": ServiceAcountDisabledWrapper,
    # "PROD": ServiceAccountPolicyWrapper,
    "PROD": ServiceAcountDisabledWrapper,
}
