import base64
import hashlib
import hmac
import json
from datetime import timed<PERSON><PERSON>
from functools import wraps

import google
import phonenumbers
import proto
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from django_object_actions import DjangoObjectActions
from google.auth.transport import requests
from google.oauth2 import service_account
from google.protobuf.message import Message as NativeMessageType
from phonenumbers import region_code_for_country_code
from rest_framework_simplejwt.tokens import RefreshToken


class BaseIterator:
    def __init__(self, iterable, max_number_of_results):
        self._iterable = iterable
        self._max_number_of_results = max_number_of_results

    @property
    def is_full(self):
        if self._max_number_of_results is not None:
            if self._max_number_of_results <= 0:
                return True

            self._max_number_of_results -= 1

        return False

    def __iter__(self):
        return self

    def __getitem__(self, item):
        return self._iterable[item]

    def __next__(self):
        if not self._iterable:
            raise StopIteration()

        return self._iterable


def cached_model_property(cache_time_in_seconds=None, is_property=True, attr=None):
    """Cache model property in redis"""

    def cached_model_property_decorator(model_property):
        @wraps(model_property)
        def _wrapped_model_property(instance, *args, **kwargs):
            prefix_key = instance.id
            if attr is not None:
                prefix_key = getattr(instance, attr)

            cache_key = f"{prefix_key}-{model_property.__name__}-{args}-{kwargs}"
            cached_ret = cache.get(cache_key)
            if cached_ret is not None:
                return cached_ret

            ret = model_property(instance, *args, **kwargs)

            # Only cache not null data
            if ret is not None:
                cache.set(cache_key, ret, cache_time_in_seconds)
            return ret

        return property(_wrapped_model_property) if is_property else _wrapped_model_property

    return cached_model_property_decorator


def errors_dict_to_string(errors):
    """Flatten Django Rest Framework errors into simple dict with key:value"""
    for key, value in errors.items():
        if isinstance(value, list) or isinstance(value, tuple):
            if isinstance(value[0], dict):
                for i, item in enumerate(value):
                    value[i] = errors_dict_to_string(item)
            else:
                if isinstance(value, str) or isinstance(value, list):
                    errors[key] = "\n".join(value)
                else:
                    errors[key] = value
        elif isinstance(value, dict):
            errors[key] = errors_dict_to_string(value)
    return errors


def get_openapi_tokens_response(refresh=False):
    """Helper method to add openapi response for JWT tokens in auth endpoints"""
    from drf_yasg import openapi

    properties = {"access": openapi.Schema(type="string", description="Access JWT")}

    if refresh:
        properties["refresh"] = openapi.Schema(type="string", description="Refresh JWT")
    return openapi.Response(description="Tokens", schema=openapi.Schema(type="object", properties=properties))


def get_openapi_response(status, status_key="status"):
    """Helper method to add a generic openapi response"""
    from drf_yasg import openapi

    return openapi.Response(
        description=status,
        examples={
            "application/json": {
                status_key: status,
            }
        },
    )


def get_openapi_error_response(serializer=None, non_field_error=None):
    """Helper method to add a generic openapi response for errors"""
    fields_error_examples = {}

    if serializer:
        for field in serializer().fields.keys():
            fields_error_examples[field] = "Invalid Format"

    if non_field_error:
        fields_error_examples[settings.REST_FRAMEWORK["NON_FIELD_ERRORS_KEY"]] = non_field_error
    else:
        fields_error_examples[settings.REST_FRAMEWORK["NON_FIELD_ERRORS_KEY"]] = "non_field_error"

    from drf_yasg import openapi

    return openapi.Response(description="Field specific errors", examples={"application/json": fields_error_examples})


def get_openapi_cache_header(header_name, description=None):
    """Helper method to add an opeanapi parameter for header meta information"""
    from drf_yasg import openapi

    return openapi.Parameter(
        header_name,
        openapi.IN_HEADER,
        description=description or "Evict cached data from redis cache",
        type=openapi.TYPE_BOOLEAN,
        required=False,
        enum=[True, False],
    )


def validate_phone(value, country):
    """Helper method to validate if a given phone number is valid"""
    try:
        phone_parsed = phonenumbers.parse(value, country)
        if phone_parsed.country_code != phonenumbers.country_code_for_region(country):
            return False
    except phonenumbers.phonenumberutil.NumberParseException:
        return False

    if not phonenumbers.is_valid_number(phone_parsed):
        return False

    return phonenumbers.format_number(phone_parsed, phonenumbers.PhoneNumberFormat.E164)


def parse_phone(phone_number):
    """Helper method to parse a full phone number into phone_number and country_code"""
    try:
        phone_parsed = phonenumbers.parse(phone_number)
        return region_code_for_country_code(phone_parsed.country_code), phone_parsed.national_number
    except phonenumbers.phonenumberutil.NumberParseException:
        return None, None


def generate_token_on_demand(user_email="<EMAIL>", minutes=45):
    """Make a JWT token to satellite microservices with exp time set to 45 minutes by default"""
    User = get_user_model()

    def _create_short_time_token(user):
        token = RefreshToken.for_user(user)
        token = token.access_token

        token["exp"] = timezone.now() + timedelta(minutes=minutes)
        token["email"] = user.email

        return token

    try:
        user = User.objects.get(email=user_email)
        payload = _create_short_time_token(user)
        return str(payload)

    except User.DoesNotExist:
        pass

    return None


class DictWithoutNones(dict):
    """Helper class to build dicts without nulls"""

    def __init__(self, seq=None, **kwargs):
        if seq:
            seq = self._drop_nullable_values(seq)
        super(DictWithoutNones, self).__init__(seq, **kwargs)

    def _drop_nullable_values(self, items):
        clean = {}
        for k, v in items.items():
            if isinstance(v, dict):
                nested = self._drop_nullable_values(v)
                if len(nested.keys()) > 0:
                    clean[k] = nested
            elif v:
                clean[k] = v
        return clean

    def __setitem__(self, key, value):
        if value:
            return super(DictWithoutNones, self).__setitem__(key, value)


def use_facebook_token(ad_account_id, access_token):
    """Helper method to choose right token depending on enviornment and if access token is needed or not"""
    if ad_account_id == settings.SANDBOX_AD_ACCOUNT:
        access_token = settings.SANDBOX_TOKEN
    return access_token


def subset_of_dict(dict_data: dict, keys: list = None, return_excluded_keys=False):
    """Helper method to get a subset of keys from dict"""
    if keys is None or not dict_data:
        return {}

    included_keys = {}
    excluded_keys = {}

    for key in dict_data:
        if key in keys:
            included_keys[key] = dict_data[key]
        else:
            excluded_keys[key] = dict_data[key]

    if return_excluded_keys:
        return included_keys, excluded_keys
    return included_keys


class DjangoAdminActions(DjangoObjectActions):
    """Subclass of DjangoObjectActions to fill change_actions from actions list and copys func description to label."""

    def __init__(self, *args, **kwargs):
        self.change_actions = self.actions

        for action in self.actions:
            func, action, description = self.get_action(action)
            func.label = description
        super().__init__(*args, **kwargs)


# def generate_gcloud_access_token_on_demand(target_audience):
#     """Helper method to fetch an access token from google cloud. Target audience must match with remote url."""
#
#     # TODO: there's a bug with credentials.signer => AttributeError
#
#     SCOPES = (
#         "https://www.googleapis.com/auth/cloud-platform",
#         "https://www.googleapis.com/auth/cloud-platform.read-only",
#         "https://www.googleapis.com/auth/cloud-identity.groups.readonly",
#     )
#     request = requests.Request()
#     credentials, _ = google.auth.default(scopes=SCOPES, request=request)
#     credentials = service_account.IDTokenCredentials(
#         signer=credentials.signer,
#         service_account_email=credentials.signer_email,
#         token_uri="https://oauth2.googleapis.com/token",
#         target_audience=target_audience,
#     )
#     credentials.refresh(request)
#     return credentials.token


def generate_gcloud_access_token_on_demand(target_audience):
    """Helper method to generate a signed JSON Web Token using a Google API Service Account. Target audience must
    match with remote url."""

    SCOPES = (
        "https://www.googleapis.com/auth/cloud-platform",
        "https://www.googleapis.com/auth/cloud-platform.read-only",
        "https://www.googleapis.com/auth/cloud-identity.groups.readonly",
    )
    request = requests.Request()

    # signer = google.auth.crypt.RSASigner.from_service_account_file(settings.GOOGLE_CLOUD_SERVICE_ACCOUNT)
    credentials, _ = google.auth.load_credentials_from_file(
        settings.GOOGLE_CLOUD_SERVICE_ACCOUNT, scopes=SCOPES, request=request
    )

    credentials = service_account.IDTokenCredentials(
        signer=credentials.signer,
        service_account_email=credentials.signer_email,
        token_uri="https://oauth2.googleapis.com/token",
        target_audience=target_audience,
    )
    credentials.refresh(request)
    return credentials.token


def helpscout_hmac_generator(email):
    """Helpscout hmac beacon generator given an user email"""
    message = bytes(email, "utf-8")
    secret = bytes(settings.SECRET_KEY_FROM_BEACON_CONFIG, "utf-8")

    return hmac.new(secret, message, digestmod=hashlib.sha256).hexdigest()


def encoding_payload_with_hmac(payload):
    """Generate hmac to verify the integrity of payload"""
    secret = bytes(settings.ENCODING_KEY, "utf-8")

    message = payload
    if isinstance(payload, dict):
        message = json.dumps(payload).encode("utf-8")

    elif isinstance(payload, str):
        message = bytes(payload, "utf-8")

    encoded_payload = hmac.new(secret, message, digestmod=hashlib.sha256).hexdigest()
    return base64.b64encode(json.dumps({"payload": payload, "key": encoded_payload}).encode("utf-8")).decode("utf-8")


def google_protobuf_copy_from(destination, origin):
    """A convenience method that wraps native CopyFrom methods.
    This method consolidates the CopyFrom logic of native and proto-plus
    wrapped protobuf messages into a single helper method.
    Args:
        destination: The protobuf message where changes are being copied.
        origin: The protobuf message where changes are being copied from.
    """
    is_dest_wrapped = isinstance(destination, proto.Message)
    is_orig_wrapped = isinstance(origin, proto.Message)
    is_dest_native = isinstance(destination, NativeMessageType)
    is_orig_native = isinstance(origin, NativeMessageType)

    if is_dest_wrapped and is_orig_wrapped:
        proto.Message.copy_from(destination, origin)
    elif is_dest_native and is_orig_native:
        destination.CopyFrom(origin)
    elif is_dest_wrapped and is_orig_native:
        proto.Message.copy_from(destination, type(destination)(origin))
    elif is_dest_native and is_orig_wrapped:
        destination.CopyFrom(type(origin).pb(origin))
    else:
        raise ValueError(
            "Only protobuf message instances can be used for copying. "
            f"A {type(destination)} and a {type(origin)} were given."
        )
