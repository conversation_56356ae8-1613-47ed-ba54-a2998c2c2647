import datetime
import decimal
import re
import unicodedata
import uuid
from dataclasses import dataclass
from datetime import timedelta
from functools import wraps
from typing import Callable, Union

import google
import pytz
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from google.auth.transport import requests
from google.oauth2 import service_account
from hashids import Hashids
from network_clients.chargebee.client import ChargebeeClient
from phonenumber_field.phonenumber import PhoneNumber
from rest_framework_simplejwt.tokens import RefreshToken


def get_openapi_error_response(serializer=None, non_field_error=None):
    """Helper method to add a generic openapi response for errors"""
    from drf_yasg import openapi

    fields_error_examples = {}

    if serializer:
        for field in serializer().fields.keys():
            fields_error_examples[field] = "Invalid Format"

    if non_field_error:
        fields_error_examples[settings.REST_FRAMEWORK["NON_FIELD_ERRORS_KEY"]] = non_field_error
    else:
        fields_error_examples[settings.REST_FRAMEWORK["NON_FIELD_ERRORS_KEY"]] = "non_field_error"

    return openapi.Response(description="Field specific errors", examples={"application/json": fields_error_examples})


def get_openapi_tokens_response(refresh=False):
    """Helper method to add openapi response for JWT tokens in auth endpoints"""
    from drf_yasg import openapi

    properties = {"access": openapi.Schema(type="string", description="Access JWT")}

    if refresh:
        properties["refresh"] = openapi.Schema(type="string", description="Refresh JWT")
    return openapi.Response(description="Tokens", schema=openapi.Schema(type="object", properties=properties))


def get_openapi_response(status, status_key="status"):
    """Helper method to add a generic openapi response"""
    from drf_yasg import openapi

    return openapi.Response(
        description=status,
        examples={
            "application/json": {
                status_key: status,
            }
        },
    )


def get_openapi_cache_header(header_name, description=None):
    """Helper method to add an opeanapi parameter for header meta information"""
    from drf_yasg import openapi

    return openapi.Parameter(
        header_name,
        openapi.IN_HEADER,
        description=description or "Evict cached data from redis cache",
        type=openapi.TYPE_BOOLEAN,
        required=False,
        enum=[True, False],
    )


def errors_dict_to_string(errors):
    """Flatten Django Rest Framework errors into simple dict with key:value"""
    for key, value in errors.items():
        if isinstance(value, (list, tuple)):
            if isinstance(value[0], dict):
                for i, item in enumerate(value):
                    value[i] = errors_dict_to_string(item)

            else:
                if isinstance(value, str):
                    errors[key] = "\n".join(value)
                elif isinstance(value, list):
                    flat_values = []
                    for item in value:
                        if isinstance(item, list):
                            flat_values.extend(item)
                        else:
                            flat_values.append(item)

                    # Convert to strings and join
                    str_values = [item.string if hasattr(item, "string") else str(item) for item in flat_values]
                    errors[key] = "\n".join(str_values)
                else:
                    errors[key] = value
        elif isinstance(value, dict):
            errors[key] = errors_dict_to_string(value)
    return errors


def generate_token_on_demand(user_email="<EMAIL>", minutes=45):
    """Make a JWT token to satellite microservices with exp time set to 45 minutes by default"""
    User = get_user_model()

    def _create_short_time_token(user):
        token = RefreshToken.for_user(user)
        token = token.access_token

        token["exp"] = timezone.now() + timedelta(minutes=minutes)
        token["email"] = user.safe_email

        return token

    try:
        user = User.objects.get(email=user_email)
        payload = _create_short_time_token(user)
        return str(payload)

    except User.DoesNotExist:
        pass

    return None


def get_minutes_range(date_time: datetime.datetime, interval=10):
    """Get range of dates in intervals of interval minutes"""

    start_date = (
        date_time
        - timedelta(minutes=date_time.minute % interval, seconds=date_time.second, microseconds=date_time.microsecond)
    ).replace(second=0, microsecond=0)

    end_date = start_date + timedelta(minutes=interval - 1, seconds=59)

    return start_date, end_date


def sanitize_email(email):
    """Helper method to sanitize email accounts and pass either good mails or none"""
    try:
        email = email.lower().strip()
        validate_email(email)
    except ValidationError:
        return None
    except Exception:  # smell code
        return None

    return email


def sanitize_phone(phone):
    """Helper method to sanitize phone numbers, returning None if is not valid"""
    try:
        if PhoneNumber.from_string(phone).is_valid():
            return phone
    except Exception:  # smell code
        pass

    return None


def cached_model_property(cache_time_in_seconds=None, is_property=True, attr=None):
    """Cache model property in redis"""

    def cached_model_property_decorator(model_property):
        @wraps(model_property)
        def _wrapped_model_property(instance, *args, **kwargs):
            prefix_key = instance.id
            if attr is not None:
                prefix_key = getattr(instance, attr)

            cache_key = f"{prefix_key}-{model_property.__name__}-{args}-{kwargs}"
            cached_ret = cache.get(cache_key)

            if cached_ret is not None:
                return cached_ret

            ret = model_property(instance, *args, **kwargs)

            # Only cache not null data
            if ret is not None:
                cache.set(cache_key, ret, cache_time_in_seconds)
            return ret

        return property(_wrapped_model_property) if is_property else _wrapped_model_property

    return cached_model_property_decorator


class Unique:
    hashids_instance = Hashids(salt="unique_ids")

    @classmethod
    def generate_id(cls, number: int):
        return cls.hashids_instance.encode(number)

    @classmethod
    def decode_id(cls, encoded_id: str):
        try:
            return cls.hashids_instance.decode(encoded_id)[0]
        except (ValueError, IndexError):
            return None

    @classmethod
    def to_hex(cls, data):
        return "%032x" % data


def generate_gcloud_access_token_on_demand(target_audience):
    """Helper method to generate a signed JSON Web Token using a Google API Service Account. Target audience must
    match with remote url."""

    SCOPES = (
        "https://www.googleapis.com/auth/cloud-platform",
        "https://www.googleapis.com/auth/cloud-platform.read-only",
        "https://www.googleapis.com/auth/cloud-identity.groups.readonly",
    )
    request = requests.Request()

    # signer = google.auth.crypt.RSASigner.from_service_account_file(settings.GOOGLE_CLOUD_SERVICE_ACCOUNT)
    credentials, _ = google.auth.load_credentials_from_file(
        settings.GOOGLE_CLOUD_JSON_ACCOUNT, scopes=SCOPES, request=request
    )

    credentials = service_account.IDTokenCredentials(
        signer=credentials.signer,
        service_account_email=credentials.signer_email,
        token_uri="https://oauth2.googleapis.com/token",
        target_audience=target_audience,
    )
    credentials.refresh(request)
    return credentials.token


def get_chargebee_instance(country, chargebee_metadata=None):
    """Helper method to get the right chargebee instance given a country"""
    chargebee_metadata = chargebee_metadata or settings.CHARGEBEE_SITES.get(country)
    if chargebee_metadata:
        return ChargebeeClient(api_key=chargebee_metadata.get("api_key"), site=chargebee_metadata.get("site"))

    # By default, None
    return None


def get_chargebee_instance_with_payment_gateway(country):
    """Helper method to retrieve chargebee instance and payment gateway linked to this instance"""
    chargebee_metadata = settings.CHARGEBEE_SITES.get(country)
    return get_chargebee_instance(country, chargebee_metadata=chargebee_metadata), chargebee_metadata.get(
        "payment_gateway_id"
    )


def format_float_to_specified_decimals(float_number: float, precision=3) -> Union[float, str]:
    """Helper method to format float numbers into Strings with 3 decimal positions"""
    try:
        return f"{float_number:.{precision}g}"
    except (TypeError, ValueError):
        return float_number


class PlanEstimator:
    PRODUCT_MAPPING_INTERVAL = {
        1: 2,
        2: 3,
        3: 4,
        4: 5,
        5: 6,
        6: 7.5,
        7.5: 9,
        9: 12,
        12: 15,
        15: 20,
        20: 25,
        25: 30,
        30: 35,
        35: 40,
        40: 45,
        45: 50,
        50: 51,
    }

    HUBSPOT_PRODUCT_MAPPING_KEYS = {
        1: 1,
        2: 2,
        3: 3,
        4: 4,
        5: 5,
        6: 6,
        7.5: 7.5,
        9: 9,
        12: 12,
        15: 15,
        20: 20,
        25: 25,
        30: 30,
        35: 35,
        40: 40,
        45: 45,
        50: 50,
        51: "gt50",
    }

    @classmethod
    def round_the_nearest_plan(cls, warehouse_space: decimal.Decimal):
        """Round this warehouse space to the nearest space, up or down"""
        nearest_plan = min(
            cls.PRODUCT_MAPPING_INTERVAL.items(), key=lambda x: abs(warehouse_space - decimal.Decimal(x[0]))
        )

        if nearest_plan[0] >= warehouse_space:
            return nearest_plan[0]

        return nearest_plan[1]

    @classmethod
    def get_hubspot_key_from_plan(cls, plan_space):
        """Get hubspot mapping key from plan space"""
        return cls.HUBSPOT_PRODUCT_MAPPING_KEYS.get(plan_space) or 0

    @classmethod
    def sanitize_space(cls, warehouse_space: decimal.Decimal, is_proxy=False) -> decimal.Decimal:
        """
        Take the nearest preset space for the warehouse_space or use warehouse_space if it is bigger than the greatest space

        Args:
            warehouse_space (decimal.Decimal): The original warehouse space to be sanitized.
            is_proxy (bool, optional): A flag indicating if the warehouse space should be returned as is. Defaults to False.

        Returns:
            decimal.Decimal: The sanitized warehouse space based on the defined rules.
        """

        if is_proxy:
            return warehouse_space

        nearest_space = cls.round_the_nearest_plan(warehouse_space)
        sanitized_space = decimal.Decimal(max(nearest_space, warehouse_space)).quantize(
            decimal.Decimal("1.0"), rounding=decimal.ROUND_UP
        )

        # Check if sanitized space is an integer to drop .0 decimal
        if sanitized_space.normalize().as_tuple()[-1] >= 0:
            sanitized_space = sanitized_space.normalize().to_integral()

        # Only allows 7.5 as space with decimals. Otherwise, round up to the nearest integer
        is_preset_space = cls.PRODUCT_MAPPING_INTERVAL.get(sanitized_space)
        if not is_preset_space:
            sanitized_space = sanitized_space.quantize(decimal.Decimal("1"), rounding=decimal.ROUND_UP)

        return sanitized_space


@dataclass
class BaseTemplate:
    template_file: str


@dataclass
class MoreappTemplate(BaseTemplate):
    first_row: int
    additional_services: bool = False


@dataclass
class DeliveryRequestTemplate(BaseTemplate):
    processor: Callable = lambda _: _


def sanitize(input_string):
    """Remove non-printable chars in string"""
    try:
        return "".join(c for c in input_string if c.isprintable())
    except TypeError:
        return input_string


TRANSLATES = {
    True: _("Yes"),
    False: _("No"),
}


def cached_method(cache_time_in_seconds=None, name=None):
    """Cache model property in redis"""

    def cached_method_decorator(method):
        @wraps(method)
        def _wrapped_method(*args, **kwargs):
            cache_key = f"{name}-{args}-{kwargs}"
            cached_ret = cache.get(cache_key)
            if cached_ret is not None:
                return cached_ret

            ret = method(*args, **kwargs)

            # Only cache not null data
            if ret is not None:
                cache.set(cache_key, ret, cache_time_in_seconds)
            return ret

        return _wrapped_method

    return cached_method_decorator


def is_valid_uuid(value):
    try:
        uuid.UUID(value)
    except (AttributeError, ValueError, TypeError):
        return False

    return True


def convert_naive_datetime_to_utc_datetime(datetime: datetime, timezone: str) -> datetime:
    local = pytz.timezone(timezone)
    local_datetime = local.localize(datetime, is_dst=None)
    return local_datetime.astimezone(pytz.utc)


def normalize_string(value: str, replacer: str = "") -> str:
    normalized_string = unicodedata.normalize("NFKD", value.lower()).encode("ASCII", "ignore").decode().strip()
    if replacer:
        return re.sub(r"\s+", replacer, normalized_string)

    return normalized_string
