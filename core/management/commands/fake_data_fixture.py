import datetime
import logging

from django.contrib.auth import get_user_model
from django.core.management import BaseCommand
from django.db import transaction

from contracts.models import Annex, Contract, Item
from core.deprecated import deprecated

logger = logging.getLogger(__name__)

User = get_user_model()


class Command(BaseCommand):
    help = "Populate database with fake data"

    @deprecated("Needs to update the base of contracts to a valid city first")
    def handle(self, *args, **options):
        logger.info("Populating database with fake data")

        with transaction.atomic():
            # First create a couple of users
            user_one = User.objects.create_user(
                first_name="<PERSON>",
                last_name="<PERSON><PERSON><PERSON><PERSON>",
                email="<EMAIL>",
                password="12345678a",
            )
            user_two = User.objects.create_user(
                first_name="<PERSON>",
                last_name="<PERSON> SinContraseña",
                email="<EMAIL>",
                is_active=False,
            )

            # Create a couple of contracts
            contract_one = Contract.objects.create(
                contract_document_id=1234,
                space=10,
                base=Contract.MADRID,
                country="ES",
                warehouse_id="1001",
                user=user_one,
            )

            # Create the items
            items = [
                Item(
                    tag_id=inventory["objetocodigo"],
                    description=inventory["objetodescripcion"],
                    pick_up_date=datetime.datetime(2021, 10, 14),
                    warehouse_id=1001,
                    contract=contract_one,
                )
                for inventory in [
                    {"objetofotos": [], "objetocodigo": "B2B2000", "objetodescripcion": "Nevera"},
                    {"objetofotos": [], "objetocodigo": "B2B2001", "objetodescripcion": "Zapatillas"},
                    {"objetofotos": [], "objetocodigo": "B2B2003", "objetodescripcion": "Sofá"},
                ]
                if inventory["objetocodigo"] is not None
            ]

            if items:
                Item.objects.bulk_create(items)

            # Set to Lisboa the city
            contract_two = Contract.objects.create(
                contract_document_id=45567, base=Contract.LISBOA, country="PT", warehouse_id="2003", user=user_two
            )

            # Create the items
            items = [
                Item(
                    tag_id=inventory["objetocodigo"],
                    description=inventory["objetodescripcion"],
                    pick_up_date=datetime.datetime(2021, 10, 14),
                    warehouse_id=2003,
                    contract=contract_two,
                )
                for inventory in [
                    {"objetocodigo": "B2BJ5048", "objetodescripcion": "Caja transparente 1", "objetofotos": []},
                    {"objetocodigo": "B2BJ5049", "objetodescripcion": "Caja transparente 2", "objetofotos": []},
                    {"objetocodigo": "B2BJ5050", "objetodescripcion": "Caja transparente 3", "objetofotos": []},
                    {"objetocodigo": "B2BJ5051", "objetodescripcion": "Caja transparente 4", "objetofotos": []},
                    {"objetocodigo": "B2BJ5052", "objetodescripcion": "Caja transparente 5", "objetofotos": []},
                    {"objetocodigo": "B2BJ5053", "objetodescripcion": "Caja transparente 6", "objetofotos": []},
                    {"objetocodigo": "B2BJ5055", "objetodescripcion": "Bolsa de cojines sillas", "objetofotos": []},
                    {"objetocodigo": "B2BJ5056", "objetodescripcion": "1 silla", "objetofotos": []},
                    {"objetocodigo": "B2BJ5059", "objetodescripcion": "2 sillas", "objetofotos": []},
                    {"objetocodigo": "B2BJ5058", "objetodescripcion": "2 sillas", "objetofotos": []},
                    {"objetocodigo": "B2BJ5057", "objetodescripcion": "Banqueta roja redonda", "objetofotos": []},
                    {"objetocodigo": "B2BJ5060", "objetodescripcion": "3 alfombras", "objetofotos": []},
                    {"objetocodigo": "B2BJ5061", "objetodescripcion": "Bolsa con almohadas", "objetofotos": []},
                    {"objetocodigo": "B2BJ5063", "objetodescripcion": "Caja toallas y bolsos", "objetofotos": []},
                    {"objetocodigo": "B2BJ5064", "objetodescripcion": "Caja varios invierno", "objetofotos": []},
                    {
                        "objetocodigo": "B2BJ5082",
                        "objetodescripcion": "Caja bolsas nieve cos y perchas",
                        "objetofotos": [],
                    },
                    {
                        "objetocodigo": "B2BJ5079",
                        "objetodescripcion": "Bolsa ropa azul asas marrones ep",
                        "objetofotos": [],
                    },
                    {"objetocodigo": "B2BJ5078", "objetodescripcion": "Bolsa negra ropa ep", "objetofotos": []},
                    {
                        "objetocodigo": "B2BJ5080",
                        "objetodescripcion": "Bolsa grande azul de ropa ep",
                        "objetofotos": [],
                    },
                    {"objetocodigo": "B2BJ5077", "objetodescripcion": "Bolsa roja ropa ep", "objetofotos": []},
                    {"objetocodigo": "B2BJ5081", "objetodescripcion": "Escalera plastico gris", "objetofotos": []},
                    {"objetocodigo": "B2BJ5073", "objetodescripcion": "Cristal vintage", "objetofotos": []},
                    {"objetocodigo": "B2BJ5074", "objetodescripcion": "Caja cocina 2", "objetofotos": []},
                    {"objetocodigo": "B2BJ5075", "objetodescripcion": "CAja cristal", "objetofotos": []},
                    {"objetocodigo": "B2BJ5076", "objetodescripcion": "Caja cocina platos", "objetofotos": []},
                    {"objetocodigo": "B2BJ5072", "objetodescripcion": "Caja cocina", "objetofotos": []},
                    {"objetocodigo": "B2BJ5071", "objetodescripcion": "Caja libros", "objetofotos": []},
                    {"objetocodigo": "B2BJ5070", "objetodescripcion": "Caja libros", "objetofotos": []},
                    {"objetocodigo": "B2BJ5069", "objetodescripcion": "Caja cocina", "objetofotos": []},
                    {"objetocodigo": "B2BJ5068", "objetodescripcion": "Caja bolsos", "objetofotos": []},
                    {"objetocodigo": "B2BJ5067", "objetodescripcion": "Caja toallas", "objetofotos": []},
                    {"objetocodigo": "B2BJ5066", "objetodescripcion": "Caja baño", "objetofotos": []},
                    {"objetocodigo": "B2BJ5065", "objetodescripcion": "Caja zapatos verano", "objetofotos": []},
                    {"objetocodigo": "B2BJ5084", "objetodescripcion": "Caja marron", "objetofotos": []},
                    {"objetocodigo": "B2BJ5085", "objetodescripcion": "Caja sabanas", "objetofotos": []},
                    {"objetocodigo": "B2BJ5087", "objetodescripcion": "Cama 1 de 3 colchon", "objetofotos": []},
                    {"objetocodigo": "B2BJ5083", "objetodescripcion": "Cama 2 de 3 canape entero", "objetofotos": []},
                    {"objetocodigo": "B2BJ5086", "objetodescripcion": "Cama 3 de 3 tapa canape", "objetofotos": []},
                    {"objetocodigo": "B2BJ5088", "objetodescripcion": "Tabla plancha", "objetofotos": []},
                ]
                if inventory["objetocodigo"] is not None
            ]

            if items:
                Item.objects.bulk_create(items)

            # Create the annex
            _ = Annex.objects.create(**{
                "delivery_space": 6,
                "pickup_space": 0,
                "signed_date": datetime.datetime(2021, 10, 26),
                "country": "PT",
                "warehouse_id": "2003",
                "contract": contract_two,
                "base": Annex.LISBOA,
                "internal_id": "internal_id",
            })

            # Get delivery items to update them
            _ = Item.objects.filter(
                tag_id__in=[
                    inventory.get("objetocodigoentrega") or inventory.get("objetocodigoentrega2")
                    for inventory in [
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ5083",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ5087",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ5065",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0558",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0577",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0586",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0557",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0556",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0562",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0561",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0580",
                            "objetodescripcionentrega2": None,
                        },
                        {
                            "objetocodigoentrega": None,
                            "objetocodigoentrega2": "B2BJ0560",
                            "objetodescripcionentrega2": None,
                        },
                    ]
                    if any([inventory.get("objetocodigoentrega"), inventory.get("objetocodigoentrega2")])
                ]
            ).update(delivery_date=datetime.datetime(2021, 10, 18), warehouse_id="2003")
