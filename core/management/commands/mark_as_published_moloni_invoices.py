import logging
import time

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand
from django.utils import timezone
from network_clients.base.base import ClientError
from network_clients.moloni.client import MoloniClient

logger = logging.getLogger(__name__)

User = get_user_model()


class Command(BaseCommand):
    help = "Get pending invoices from Moloni to update the status"
    company_id = 161640
    now = timezone.now()
    moloni_client = MoloniClient(
        client_id=settings.MOLONI_CLIENT_ID,
        client_secret=settings.MOLONI_CLIENT_SECRET,
        username=settings.MOLONI_USERNAME,
        password=settings.MOLONI_PASSWORD,
    )

    def get_pending_invoices(self):
        """Get pending invoices from moloni"""
        return [invoice_batch for invoice_batch in self.moloni_client.get_invoices(self.company_id, status=0)]

    def get_pending_credit_notes(self):
        """Get pending credit notes from moloni"""
        return [
            credit_note_batch for credit_note_batch in self.moloni_client.get_credit_notes(self.company_id, status=0)
        ]

    def process_moloni_pending_objects(self, item_name):
        pending_objects = getattr(self, f"get_pending_{item_name}s")()
        for item in pending_objects:
            document_id = item.get("document_id")

            try:
                getattr(self.moloni_client, f"update_{item_name}")(self.company_id, document_id, status=1)

                # Sleep during a while to avoid hit the rate limit (429)
                time.sleep(1)
            except ClientError as err:
                logger.error(f"Error updating {item_name} {document_id}: {err}")

    def handle(self, *args, **options):
        # Handle invoices first
        self.process_moloni_pending_objects(item_name="invoice")

        # Handle credit notest later
        self.process_moloni_pending_objects(item_name="credit_note")
