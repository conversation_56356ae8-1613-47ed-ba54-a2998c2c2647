import csv
import logging
from io import BytesIO, String<PERSON>

from dateutil import parser
from django.conf import settings
from django.core.management import BaseCommand
from django.db import transaction
from google.cloud import storage

from payments.models import CreditNote

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Fixing invoices from May in order to handle chargebacks accordingly."
    bucket = "box2box-migration-files"

    def download_from_remote_storage(self, country):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob(f"parsed_data_notes_{country}.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("utf-8"))

    def parse_credit_notes(self, data):
        data = csv.DictReader(data, delimiter=",")
        credit_notes_to_create = []

        for row in data:
            total = round(float(row.get("Amount Refunded") or 0), 3)
            tax = round(float(row.get("Tax Total") or 0), 3)
            tax_base = round(float(row.get("Tax Base") or 0), 3)

            invoice = CreditNote(**{
                "remote_id": row.get("Credit Note Number"),
                "reference": row.get("Credit Note Number"),
                "date": parser.parse(row.get("dates")).date(),
                "currency": row.get("Currency"),
                "total": -total,
                "tax_base": -tax_base,
                "tax": -tax,
                "chargebee_site": row.get("site"),
                "contract_id": row.get("pk"),
                "user_id": row.get("user_pk"),
            })

            credit_notes_to_create.append(invoice)

        return credit_notes_to_create

    def handle(self, *args, **options):
        logger.info("Populating database with chargebee credit notes...")

        countries = ["es", "pt", "fr"]

        credit_notes_to_create = []

        with transaction.atomic():
            for country in countries:
                logger.info(f"Processing country... {country}")
                partial_create = self.parse_credit_notes(self.download_from_remote_storage(country))
                credit_notes_to_create.extend(partial_create)

            if credit_notes_to_create:
                CreditNote.objects.bulk_create(credit_notes_to_create)

        logger.info("Done!")
