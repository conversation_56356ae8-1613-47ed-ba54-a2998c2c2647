import logging
import operator
from datetime import datetime
from functools import reduce
from io import BytesIO, StringIO

import pandas as pd
import pytz
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from google.cloud import storage

from core.models import Holiday
from core.utils import normalize_string
from intranet.models import ISO3166_2, BlankSlot, Calendar, Event

User = get_user_model()

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Add spanish holidays to the database"
    bucket = "box2box-migration-files"
    now = timezone.now()

    translated_states = {
        "bizkaia": "vizcaya",
        "araba/álava": "alava",
        "guipuzkoa": "gipuzcoa",
        "a coruña": "la coruna",
    }

    translated_cities = {
        "pamplona/iruna": "pamplona",
        "coruna_(a)": "a_coruna",
        "castellon_de_la_plana/castello_de_la_pla": "castellon_de_la_plana",
        "donostia-san_sebastian": "san_sebastian",
        "alicante/alacant": "alicante",
    }

    spanish_capitals_or_notable_cities = [
        normalize_string(city, replacer="_")
        for city in {
            # Provincial capitals
            "Almería",
            "Cádiz",
            "Córdoba",
            "Granada",
            "Huelva",
            "Jaén",
            "Málaga",
            "Sevilla",
            "Huesca",
            "Teruel",
            "Zaragoza",
            "Oviedo",
            "Palma de Mallorca",
            "Las Palmas de Gran Canaria",
            "Santa Cruz de Tenerife",
            "Santander",
            "Albacete",
            "Ciudad Real",
            "Cuenca",
            "Guadalajara",
            "Toledo",
            "Ávila",
            "Burgos",
            "León",
            "Palencia",
            "Salamanca",
            "Segovia",
            "Soria",
            "Valladolid",
            "Zamora",
            "Barcelona",
            "Girona",
            "Lleida",
            "Tarragona",
            "Badajoz",
            "Cáceres",
            "A Coruña",
            "Lugo",
            "Ourense",
            "Pontevedra",
            "Logroño",
            "Madrid",
            "Murcia",
            "Pamplona",
            "Vitoria-Gasteiz",
            "Bilbao",
            "San Sebastián",
            "Valencia",
            "Alicante",
            "Castellón de la Plana",
            "Ceuta",
            "Melilla",
            # Island capitals (some are not provincial)
            "Eivissa",
            "Mao",
            "Formentera",
            "Arrecife",
            "Puerto del Rosario",
            "San Sebastían de la Gomera",
            "Valverde",
            "Santa Cruz de La Palma",
        }
    ]

    types = {
        "local": Holiday.LOCAL,
        "nacional": Holiday.NATIONAL,
        "autonomico": Holiday.REGIONAL,
    }

    province_city_mapping = {
        "a_coruna": ["a_coruna"],
        "araba/alava": ["vitoria-gasteiz"],
        "asturias": ["oviedo"],
        "vizcaya": ["bilbao"],
        "cantabria": ["santander"],
        "castellon": ["castellon_de_la_plana"],
        "gipuzkoa": ["san_sebastian"],
        "illes_balears": ["eivissa", "mao", "formentera", "palma_de_mallorca"],
        "la_rioja": ["logrono"],
        "las_palmas": ["arrecife", "puerto_del_rosario", "las_palmas_de_gran_canaria"],
        "navarra": ["pamplona"],
        "santa_cruz_de_tenerife": [
            "santa_cruz_de_tenerife",
            "santa_cruz_de_la_palma",
            "valverde",
            "san_sebastian_de_la_gomera",
        ],
    }

    translated_states_iso3166_2 = {
        "bizkaia": "biscay",
        "araba/alava": "alava",
        "guipuzkoa": "gipuzcoa",
        "a coruña": "la coruna",
        "sevilla": "seville",
        "illes_balears": "balearic islands",
    }

    translated_cities_iso3166_2 = {
        "a_coruna": "a coruna",
        "albacete": "albacete",
        "alicante": "alicante",
        "almeria": "almeria",
        "avila": "avila",
        "badajoz": "badajoz",
        "barcelona": "barcelona",
        "bilbao": "biscay",
        "burgos": "burgos",
        "caceres": "caceres",
        "cadiz": "cadiz",
        "castellon_de_la_plana": "castellon",
        "ceuta": "ceuta",
        "ciudad_real": "ciudad real",
        "cordoba": "cordoba",
        "cuenca": "cuenca",
        "girona": "girona",
        "granada": "granada",
        "guadalajara": "guadalajara",
        "huelva": "huelva",
        "huesca": "huesca",
        "jaen": "jaen",
        "las_palmas_de_gran_canaria": "las palmas",
        "leon": "leon",
        "lleida": "lleida",
        "logrono": "la rioja",
        "lugo": "lugo",
        "madrid": "madrid",
        "malaga": "malaga",
        "melilla": "melilla",
        "murcia": "murcia",
        "ourense": "ourense",
        "oviedo": "asturias",
        "palencia": "palencia",
        "palma_de_mallorca": "balearic islands",
        "pamplona": "navarre",
        "pontevedra": "pontevedra",
        "salamanca": "salamanca",
        "san_sebastian": "gipuzkoa",
        "santa_cruz_de_tenerife": "santa cruz de tenerife",
        "santander": "cantabria",
        "segovia": "segovia",
        "sevilla": "seville",
        "soria": "soria",
        "tarragona": "tarragona",
        "teruel": "teruel",
        "toledo": "toledo",
        "valencia": "valencia",
        "valladolid": "valladolid",
        "vitoria-gasteiz": "alava",
        "zamora": "zamora",
        "zaragoza": "zaragoza",
    }

    time_zone = pytz.timezone(pytz.utc.zone)
    country = "ES"

    def download_data_from_remote_storage(self, file_name):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob(f"{file_name}.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("latin-1"))

    def handle(self, *args, **options):
        logger.info("Updating database with spanish holidays data...")

        # Download contract file from remote source
        data = self.download_data_from_remote_storage("calendarioES")

        df = pd.read_csv(data, sep=",")
        df.replace(pd.NA, "", inplace=True)
        df["FECHA"] = pd.to_datetime(df["FECHA"], format="%d-%m-%Y")
        year = df["FECHA"].iloc[0].year

        df["FECHA"] = df["FECHA"].dt.strftime("%Y-%m-%d")

        calendar_countries = {}
        calendar_states = {}
        calendar_cities = {}
        calendar_colors = {}
        for calendar in Calendar.objects.all().select_related("city"):
            if self.country not in calendar_countries:
                calendar_countries[calendar.city.country] = []

            if calendar.city.normalized_name not in calendar_cities:
                calendar_cities[calendar.city.normalized_name] = []

            if calendar.city.area_level_2 not in calendar_states:
                calendar_states[calendar.city.area_level_2] = []

            if calendar.city.country == self.country:
                calendar_countries[calendar.city.country].append(calendar.id)

            calendar_cities[calendar.city.normalized_name].append(calendar.id)
            calendar_states[calendar.city.area_level_2].append(calendar.id)
            calendar_colors[calendar.id] = calendar.color

        holidays = []
        events = []
        normalized_list = [normalize_string(value) for value in df.sort_values("PROVINCIA")["PROVINCIA"].unique()]

        # Build a Q object that ORs together icontains for each normalized value
        query = reduce(
            operator.or_, (Q(admin_level_2__icontains=value) for value in normalized_list), Q()  # initial empty Q
        )
        iso_3166_2_metadata = ISO3166_2.objects.filter(Q(country=self.country), query)
        iso_3166_2_metadata = {data.admin_level_2: data.iso_code for data in iso_3166_2_metadata}
        used_calendars = []

        blank_slot = BlankSlot.objects.filter(is_for_holiday=True).first()
        for _, row in df.sort_values("PROVINCIA").iterrows():
            normalized_province = normalize_string(row["PROVINCIA"], replacer="_").strip()
            province = self.translated_states.get(normalized_province, normalized_province)
            normalized_city = normalize_string(row["LOCALIDAD"], replacer="_").strip()
            city = self.translated_cities.get(normalized_city, normalized_city)
            date = self.time_zone.localize(datetime.strptime(row["FECHA"], "%Y-%m-%d"))
            type = self.types.get(normalize_string(row["TIPO"]), Holiday.NATIONAL)
            description = f"🏖️ {row['DESCRIPCION']}"

            # Needed for iso_3166_2 metadata lookup
            province_compliant = self.translated_states_iso3166_2.get(normalized_province, normalized_province).replace(
                "_", " "
            )

            if city in self.spanish_capitals_or_notable_cities:
                city_compliant = self.translated_cities_iso3166_2.get(city, city).replace("_", " ")
                holidays.append(
                    Holiday(
                        state=province,
                        type=type,
                        description=description,
                        date=date,
                        city=city,
                        iso_code=iso_3166_2_metadata.get(city_compliant) or iso_3166_2_metadata.get(province_compliant),
                        country=self.country,
                    )
                )

            if type == Holiday.NATIONAL:
                holidays.append(
                    Holiday(
                        state=province, type=type, description=description, date=date, city=None, country=self.country
                    )
                )
                used_calendars.extend(calendar_countries.get(self.country, []))
                events.extend([
                    Event(
                        title=description,
                        start_time=date,
                        end_time=date,
                        is_all_day=True,
                        calendar_id=calendar_id,
                        is_holiday=True,
                        blank_slot=blank_slot,
                        color=calendar_colors.get(calendar_id),
                    )
                    for calendar_id in calendar_countries.get(self.country, [])
                ])

            if type == Holiday.REGIONAL:
                holidays.append(
                    Holiday(
                        state=province,
                        type=type,
                        description=description,
                        date=date,
                        city=None,
                        iso_code=iso_3166_2_metadata.get(province_compliant),
                        country=self.country,
                    )
                )
                used_calendars.extend(calendar_cities.get(province, []))
                events.extend([
                    Event(
                        title=description,
                        start_time=date,
                        end_time=date,
                        is_all_day=True,
                        calendar_id=calendar_id,
                        is_holiday=True,
                        blank_slot=blank_slot,
                        color=calendar_colors.get(calendar_id),
                    )
                    for calendar_id in calendar_cities.get(province, [])
                ])

            if type == Holiday.LOCAL:
                used_calendars.extend(calendar_cities.get(city, []))
                events.extend([
                    Event(
                        title=description,
                        start_time=date,
                        end_time=date,
                        is_all_day=True,
                        calendar_id=calendar_id,
                        is_holiday=True,
                        blank_slot=blank_slot,
                        color=calendar_colors.get(calendar_id),
                    )
                    for calendar_id in calendar_cities.get(city, [])
                ])

            used_calendars = list(set(used_calendars))

        if holidays:
            with transaction.atomic():
                Holiday.objects.filter(country=self.country).delete()
                Holiday.objects.bulk_create(holidays)

                if events:
                    Event.objects.filter(
                        is_holiday=True, start_time__year=year, calendar_id__in=used_calendars
                    ).delete()
                    Event.objects.bulk_create(events)

        logger.info("Done!")
