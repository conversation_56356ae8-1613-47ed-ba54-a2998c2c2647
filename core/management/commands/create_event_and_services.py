import datetime
import json
import logging
from html.parser import <PERSON><PERSON><PERSON>arser
from io import BytesIO, StringIO

import pandas as pd
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand
from django.db import transaction
from google.cloud import storage

from contracts.models import Item, Service, Warehouse, WarehouseConfiguration
from contracts.utils import COUNTRY_BASES
from core.deprecated import deprecated
from intranet.models import Calendar, Event
from riders.models import RiderProxy, Team

logger = logging.getLogger(__name__)

User = get_user_model()


CALENDAR_COLORS = {
    # "<EMAIL>": "#8E24AA",
    # "<EMAIL>": "#A79B8E",
    # "<EMAIL>": "#EF6C00",
    "<EMAIL>": "#AD1457",
    # "<EMAIL>": "#E4C441",
    "<EMAIL>": "#C0CA33",
    "<EMAIL>": "#F6BF26",
    "<EMAIL>": "#D81B60",
    "<EMAIL>": "#A79B8E",
    # "<EMAIL>": "#F4511E",
    "<EMAIL>": "#039BE5",
    "<EMAIL>": "#7986CB",
    "<EMAIL>": "#3F51B5",
    "<EMAIL>": "#9E69AF",
    "<EMAIL>": "#616161",
    "<EMAIL>": "#B39DDB",
    "<EMAIL>": "#7CB342",
}


class HTMLFilter(HTMLParser):
    """
    A simple no dependency HTML -> TEXT converter.
    Usage:
          str_output = HTMLFilter.convert_html_to_text(html_input)
    """

    def __init__(self, *args, **kwargs):
        self.text = ""
        self.in_block = False
        super().__init__(*args, **kwargs)

    def handle_starttag(self, tag: str, attrs):
        if tag.lower() in ["br", "li"]:
            self.in_block = True

    def handle_data(self, data):
        if self.in_block:
            self.text += "\n"
            self.in_block = False

        self.text += data

    @classmethod
    def convert_html_to_text(cls, html: str) -> str:
        f = cls()
        f.feed(html)
        return f.text.strip()


def parse_html(html):
    html_parser = HTMLFilter()

    return html_parser.convert_html_to_text(html)


class Command(BaseCommand):
    help = "Create events and services from Google Calendar"
    bucket = "box2box-migration-files"

    def download_products_from_remote_storage(self):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob("calendar_events.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("utf-8"))

    def clear_phone_number(self, phone_number):
        try:
            return phone_number.split("/")[0].strip().replace("-", "").replace(" ", "")
        except:  # NOQA: E722, B001
            return None

    def clear_space(self, space):
        try:
            return f"{float(space[0]):.2f}".rstrip("0").rstrip(".")
        except (IndexError, TypeError):
            return None

    def build_additional_info(self, item):
        times = json.loads(item["times"].replace("'", '"'))

        additional_info = item["additional_info"]
        partial_delivery = (
            f"Entrega parcial?: {item['partial_delivery']}"
            if item["service_type"] in [Service.PARTIAL_DELIVERY, Service.FINAL_DELIVERY]
            else ""
        )
        extra = json.loads(item["extra"].replace("'", '"'))
        message = times.get("message") or ""
        original_description = item["description"]

        metadata = [additional_info, partial_delivery, message]
        metadata.extend(extra)
        metadata.append(original_description)

        return "\n".join([item for item in metadata])

    def clear_hired_space(self, hired_space):
        try:
            return f"{float(hired_space):.2f}".rstrip("0").rstrip(".")
        except (TypeError, ValueError):
            return None

    def parse_simple_inventory(self, inventory):
        return "\n".join(f"{item.get('code')} {item.get('value')}" for item in inventory) or None

    def parse_delivery_inventory(self, full_inventory, inventory, contract_id):
        codes = [item.get("code") for item in inventory]
        contract_id = str(contract_id)
        return full_inventory[
            (full_inventory.tag_id.isin(codes)) & (full_inventory["contract__contract_document_id"] == contract_id)
        ]

    def clear_contract_id(self, contract_id):
        try:
            return int(contract_id)
        except:  # NOQA: E722, B001
            return None

    def fix_type(self, service_type):
        if service_type == "additional_pickup":
            return Service.EXTRA_PICKUP
        return service_type

    def parse_time_slot(self, time_slot):
        if time_slot:
            return datetime.datetime.strptime(time_slot, "%H:%M").strftime("%H:%M")

        return None

    def parse_attendees(self, attendees):
        if attendees:
            return [attendee.get("email") for attendee in attendees]

        return []

    @deprecated("Needs to update the base of contracts to a valid city first")
    def handle(self, *args, **options):
        logger.info("Adding google calendar imported events...")

        # Download file from remote source
        file = self.download_products_from_remote_storage()
        calendars = Calendar.objects.all().values("id", "calendar_id", "city")
        user_id = User.objects.filter(email="<EMAIL>").values("id")
        warehouses = Warehouse.objects.all().values("id", "warehouse_city")
        items = (
            Item.objects.select_related("contract")
            .all()
            .values("id", "tag_id", "description", "contract__contract_document_id")
        )
        warehouse_configuration = WarehouseConfiguration.objects.all().values("warehouse_id", "rider_id", "default")
        riders = RiderProxy.objects.all().values("id", "email")

        df = pd.read_csv(file, sep=",").fillna("")
        df_calendars = pd.DataFrame(calendars).fillna("")
        df_warehouses = pd.DataFrame(warehouses).fillna("")
        df_items = pd.DataFrame(items).fillna("")
        df_warehouse_configuration = pd.DataFrame(warehouse_configuration).fillna("")
        df_riders = pd.DataFrame(riders).fillna("")

        service_list = []
        event_list = []
        item_list = []
        team_list = []

        for _index, item in df.iterrows():
            service_type = self.fix_type(item["service_type"])
            calendar_metadata = df_calendars[df_calendars.calendar_id == item["calendar_id"]]
            warehouse_id = df_warehouses[df_warehouses.warehouse_city == calendar_metadata["city"].values[0]][
                "id"
            ].values[0]
            calendar_id = calendar_metadata["id"].values[0]
            calendar_city = calendar_metadata["city"].values[0]
            times = json.loads(item["times"].replace("'", '"'))
            inventory = json.loads((item["inventory"] or "[]").replace("'", '"'))
            contract_id = self.clear_contract_id(item["id"])
            attendees = self.parse_attendees(
                json.loads((item["attendees"] or "[]").replace("'", '"').replace("True", "true"))
            )

            # Service fields
            service_data = {
                "contract_id": contract_id,
                "address": item["address"],
                "type": service_type,
                "first_name": item["first_name"],
                "last_name": item["last_name"],
                "space": self.clear_space(json.loads((item["space"] or "[]").replace("'", '"'))),
                "phone": self.clear_phone_number(item["phone_number"]),
                "start_time_slot": self.parse_time_slot(times["start"]),
                "end_time_slot": self.parse_time_slot(times["end"]),
                "additional_info": parse_html(item["description"]),
                "country": COUNTRY_BASES.get(calendar_city) or "ES",
                "created_by_id": user_id,
            }

            if service_type in [Service.INITIAL_PICKUP, Service.EXTRA_PICKUP, Service.BOX, Service.MOVING]:
                service_data["inventory_list"] = self.parse_simple_inventory(inventory)

            service = Service(**service_data)

            if service_type in [Service.PARTIAL_DELIVERY, Service.FINAL_DELIVERY]:
                service.warehouse_location = item["location_ai"]
                service.warehouse_id = warehouse_id
                service.hired_space = self.clear_hired_space(item["hired_space"])

                inventory_data = self.parse_delivery_inventory(df_items, inventory, contract_id)
                if not inventory_data.empty:
                    item_list.extend(
                        [Item(id=item_id, service=service) for item_id in inventory_data["id"].values.tolist()]
                    )
                    service.inventory_list = "\n".join(
                        [f"<b>{item[0]}</b>: {item[1]}" for item in inventory_data[["tag_id", "description"]].values]
                    )

            service_list.append(service)

            # Linked riders to the service
            if attendees and calendar_city == "ES-M":
                attendees = [df_riders[df_riders.email == attendee]["id"].values.tolist()[0] for attendee in attendees]
            else:
                attendees = df_warehouse_configuration[
                    (df_warehouse_configuration.warehouse_id == warehouse_id) & df_warehouse_configuration.default
                ]["rider_id"].values.tolist()

            # Create the team
            team_list.extend([Team(rider_id=member, service=service) for member in attendees])

            # Event fields
            event_data = {
                "title": item["summary"],
                "start_time": item["start"],
                "end_time": item["end"],
                "calendar_event_id": item["event_id"],
                "user_id": user_id,
                "calendar_id": calendar_id,
                "color": CALENDAR_COLORS.get(calendar_id),
                "service_id": service.id,  # Create first the service
            }

            event = Event(**event_data)
            event_list.append(event)

        with transaction.atomic():
            if service_list:
                Service.objects.bulk_create(service_list)

            if event_list:
                Event.objects.bulk_create(event_list)

            if item_list:
                Item.objects.bulk_update(item_list, fields=["service"])

            if team_list:
                Team.objects.bulk_create(team_list)

        logger.info("Done!")
