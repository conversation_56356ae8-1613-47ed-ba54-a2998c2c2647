import csv
import io
import itertools
import logging
from io import Bytes<PERSON>, String<PERSON>

from dateutil import parser
from dateutil.parser import ParserError
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.management import BaseCommand
from django.db import transaction
from django.db.models import CharField, F, OuterRef, Q, Subquery, Value
from django.db.models.functions import Concat
from django.utils import timezone
from django.utils.timezone import make_aware
from google.cloud import storage
from phonenumber_field.validators import validate_international_phonenumber

from contracts.models import Annex, Contract, Item
from core.deprecated import deprecated

User = get_user_model()

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Update database data with older contracts/items for intranet portal"
    bucket = "box2box-migration-files"
    now = timezone.now()

    def download_data_from_remote_storage(self, file_name):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob(f"{file_name}.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("utf-8"))

    def write_data_in_memory(self, csv_data):
        """Write data in memory to upload into storage"""
        fp = io.StringIO()

        writer = csv.DictWriter(fp, list(csv_data[0].keys()), delimiter=",")
        writer.writeheader()
        writer.writerows(csv_data)
        return fp

    def upload_data_to_remote_storage(self, file_name, data):
        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob(f"{file_name}.csv")

        data = self.write_data_in_memory(data)

        blob.upload_from_string(data.getvalue(), content_type="text/csv")

    def is_valid_phone(self, phone):
        try:
            validate_international_phonenumber(phone)
        except ValidationError:
            return False

        return True

    def safe_date_conversion(self, date, aware=True, **kwargs):
        try:
            date = parser.parse(date, **kwargs)
            if aware:
                date = make_aware(date)
            return date
        except ParserError:
            pass
        return None

    def _create_and_update_missing_users(self, data):
        """Create missing users in db as first step before creating the contracts"""

        created_users = []
        updated_users = []
        dict_copy = []
        visited_users = {}
        for row in data:
            pk = row.get("user_pk")
            phone = row.get("phone")
            date = row.get("date")
            chargebee_customer_id = row.get("customers.id")
            crm_internal_id = row.get("contact_id")
            country = row.get("country")
            created_in_chargebee = row.get("cb_country") is not None

            if crm_internal_id not in visited_users:
                user = User(
                    email=row.get("email") or None,
                    phone=phone if self.is_valid_phone(phone) else None,
                    crm_internal_id=crm_internal_id if crm_internal_id != "None" else None,
                    date_joined_in_crm=self.safe_date_conversion(date) if date else self.now,
                    chargebee_customer_id=chargebee_customer_id,
                    registration_country=country,
                    created_in_chargebee=created_in_chargebee,
                )

                if pk:
                    user.pk = pk
                    updated_users.append(user)
                else:
                    created_users.append(user)

                visited_users[crm_internal_id] = user.pk

            row["user_pk"] = visited_users[crm_internal_id]
            dict_copy.append(row)

        if created_users:
            User.objects.bulk_create(created_users)

        if updated_users:
            User.objects.bulk_update(updated_users, fields=["crm_internal_id", "phone"])

        return dict_copy

    def _create_and_update_missing_contracts(self, data):
        """Create missing contracts in db as first step before creating the items"""

        created_contracts = []
        updated_contracts = []
        dict_copy = []
        visited_contracts = {}
        for row in data:
            pk = row.get("contract_pk")
            contract_document_id = row.get("deal_id")
            created_at = row.get("in_date")
            base = row.get("base")
            space = row.get("space") or 0
            country = row.get("country")
            signed_user_name = row.get("full_name")
            odoo_number = row.get("odoo")
            warehouse_id = row.get("warehouse")
            user_id = row.get("user_pk")

            if contract_document_id not in visited_contracts:
                contract = Contract(
                    created_at=self.safe_date_conversion(created_at, dayfirst=True),
                    base=base,
                    warehouse_id=warehouse_id,
                    space=space,
                    country=country,
                    signed_user_name=signed_user_name,
                    contract_document_id=contract_document_id,
                    odoo_number=odoo_number,
                    user_id=user_id,
                )

                if pk:
                    contract.pk = pk
                    updated_contracts.append(contract)
                else:
                    created_contracts.append(contract)

                visited_contracts[contract_document_id] = contract.pk

            row["contract_pk"] = visited_contracts[contract_document_id]
            dict_copy.append(row)

        if created_contracts:
            Contract.objects.bulk_create(created_contracts)

        if updated_contracts:
            Contract.objects.bulk_update(
                updated_contracts,
                fields=["odoo_number", "signed_user_name", "base", "warehouse_id", "country", "created_at"],
            )

        return dict_copy

    def _create_and_update_missing_items(self, data):
        """Create missing contracts in db as first step before creating the items"""

        created_items = []
        dict_copy = []
        for row in data:
            pk = row.get("item_pk")
            description = row.get("description")
            contract_id = row.get("contract_pk")
            pick_up_date = row.get("in_date")
            delivery_date = row.get("out_date")
            warehouse_id = row.get("warehouse")
            tag_id = row.get("tag_id")

            item = Item(
                description=description,
                tag_id=tag_id,
                delivery_date=self.safe_date_conversion(delivery_date),
                pick_up_date=self.safe_date_conversion(pick_up_date),
                warehouse_id=warehouse_id,
                contract_id=contract_id,
            )

            if not pk:
                created_items.append(item)

            row["item_pk"] = pk or item.pk
            dict_copy.append(row)

        if created_items:
            # Item.objects.bulk_create(created_items)
            while True:
                batch = list(itertools.islice(created_items, 10000))
                if not batch:
                    break
                batch_size = len(batch)
                logger.info(f"Batch of {batch_size} finished...")
                Item.objects.bulk_create(batch, 10000)
                created_items = created_items[batch_size:]

        return dict_copy

    @deprecated("Needs to update the base of contracts to a valid city first")
    def handle(self, *args, **options):
        logger.info("Updating database with contracts/items for intranet portal...")

        # Download contract file from remote source
        users = self.download_data_from_remote_storage("inventories/processed_output")

        with transaction.atomic():
            parsed_data = self._create_and_update_missing_users(csv.DictReader(users, delimiter=","))

        self.upload_data_to_remote_storage("inventories/processed_output_migrated", parsed_data)

        logger.info("Users done!")

        # Download migrated file and create the contracts
        contracts = self.download_data_from_remote_storage("inventories/processed_output_migrated")

        with transaction.atomic():
            parsed_data = self._create_and_update_missing_contracts(csv.DictReader(contracts, delimiter=","))

        self.upload_data_to_remote_storage("inventories/processed_output_migrated", parsed_data)

        logger.info("Contracts done!")

        # Download migrated file and create the items
        items = self.download_data_from_remote_storage("inventories/processed_output_migrated")

        with transaction.atomic():
            parsed_data = self._create_and_update_missing_items(csv.DictReader(items, delimiter=","))

        self.upload_data_to_remote_storage("inventories/processed_output_migrated", parsed_data)

        logger.info("Items done!")

        # Final update to existing contracts
        annex_query = (
            Annex.objects.filter(annex_type=Annex.INITIAL_PICKUP, contract=OuterRef("pk"))
            .annotate(full_name=Concat(F("first_name"), Value(" "), F("last_name"), output_field=CharField()))
            .values("full_name")
        )
        Contract.objects.filter(Q(signed_user_name__isnull=True) | Q(signed_user_name__exact="")).update(
            signed_user_name=Subquery(annex_query[:1])
        )

        logger.info("Done!")
