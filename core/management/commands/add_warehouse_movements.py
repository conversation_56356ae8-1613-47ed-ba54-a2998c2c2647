import datetime
import logging
import time
import uuid
from hashlib import sha256
from io import BytesIO, StringIO

import pandas as pd
import pytz
from django.conf import settings
from django.core.management import BaseCommand
from django.utils import timezone
from google.cloud import storage

from contracts.models import Contract, Warehouse
from intranet.models import WarehouseMovement

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Add inventory location entries in WarehouseMovement model"
    bucket = "box2box-migration-files"
    now = timezone.now()

    def download_from_remote_storage(self):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob("inventory_location_tracking.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("utf-8"))

    def generate_internal_id(self, id, service_id, user_id):
        timestamp = str(int(datetime.datetime.now(pytz.UTC).timestamp() * 1000))

        return sha256("".join([timestamp, id, service_id, user_id]).encode("utf-8")).hexdigest()

    def handle(self, *args, **options):
        logger.info("Populating database with warehouse movements...")

        file = self.download_from_remote_storage()
        df = pd.read_csv(file, sep=",", header=2)  # .fillna("")
        df = df.iloc[1:]
        df = df.loc[:, ~df.columns.str.contains("^Unnamed")]
        df.columns = ["contract_id", "warehouse_name", "city", "location", "pallets", "floor", "comment", "importable"]
        df["city"] = df["city"].str.strip().str.lower()
        df.dropna(subset=["contract_id"], inplace=True)
        df = df[df["importable"]]
        df[["contract_id", "pallets"]] = df[["contract_id", "pallets"]].astype("Int64")
        df.replace({float("nan"): None}, inplace=True)

        # Get warehouse metadata
        warehouses = Warehouse.objects.filter(canonical_name__in=df["warehouse_name"].unique().tolist()).values(
            "canonical_name", "id"
        )
        contracts = Contract.objects.filter(contract_document_id__in=df["contract_id"].unique().tolist()).values(
            "contract_document_id", "id"
        )

        # Convert queryset into dict for fast lookups
        warehouses = {warehouse["canonical_name"]: warehouse["id"] for warehouse in warehouses}
        contracts = {int(contract["contract_document_id"]): contract["id"] for contract in contracts}

        warehouse_movements = []
        for _, item in df.iterrows():
            contract_id = contracts.get(item["contract_id"])

            if not contract_id:
                continue

            warehouse_movements.append(
                WarehouseMovement(
                    location=item["location"],
                    pallet=item["pallets"],
                    floor=item["floor"],
                    sent_at=self.now,
                    internal_id=self.generate_internal_id(str(uuid.uuid4()), str(contract_id), ""),
                    contract_id=contract_id,
                    warehouse_id=warehouses.get(item["warehouse_name"]),
                    imported=True,
                )
            )

            # Ensure different timestamps between iterations
            time.sleep(0.001)

        if warehouse_movements:
            WarehouseMovement.objects.bulk_create(warehouse_movements)
