import csv
import logging
from datetime import datetime
from io import BytesIO, StringIO

import pytz
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand
from django.db import transaction
from django.db.models import F
from django.utils.timezone import make_aware
from google.cloud import storage

from contracts.models import Contract
from payments.models import SepaMandate, Subscription

logger = logging.getLogger(__name__)

User = get_user_model()


class Command(BaseCommand):
    help = "Populate database with legacy data from Odoo"
    bucket = "box2box-migration-files"

    def download_legacy_data_from_remote_storage(self):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob("legacy_data.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("utf-8"))

    def handle(self, *args, **options):
        logger.info("Populate database with legacy data from Odoo...")

        # Download file from remote source
        file = self.download_legacy_data_from_remote_storage()

        users = []
        contracts = []
        subscriptions = []
        payment_methods = []
        users_id = {}

        # Preprocess
        User.objects.filter(is_staff=False).update(chargebee_customer_id=None)

        with transaction.atomic():
            # Get users
            for row in csv.DictReader(file, delimiter=";"):
                user_id = row["hs_contact_id"]
                if user_id not in users_id:
                    user, _ = User.objects.update_or_create(
                        crm_internal_id=user_id,
                        defaults={
                            "chargebee_customer_id": row["customer[id]"],
                            "crm_internal_id": user_id,
                            "registration_country": row["country"],
                            "email": row["customer[email]"],
                            "username": row["customer[email]"],
                        },
                    )

                    users_id[user_id] = user
                    users.append(user)

                    sepa_mandate_created_at = row.get("created_at")
                    payment_method = SepaMandate(
                        created=(
                            make_aware(datetime.fromtimestamp(int(sepa_mandate_created_at)), timezone=pytz.utc).date()
                            if sepa_mandate_created_at
                            else None
                        ),
                        last4=row.get("last4"),
                        remote_id=row.get("payment_source_id"),
                        user_id=user.pk,
                    )

                    payment_methods.append(payment_method)

                contract, _ = Contract.objects.update_or_create(
                    contract_document_id=row["subscription[id]"],
                    defaults={
                        "contract_document_id": row["subscription[id]"],
                        "imported_legacy": True,
                        "user_id": users_id[user_id].pk,
                    },
                )

                subscription = Subscription(
                    recurring_interval=row["free_qty_counter"],
                    imported_legacy=True,
                    free_month_allowed=not row["exclude_free_month"],
                    exclude_next_free_month=row["coupon_ids[0]"],
                    contract_id=contract.pk,
                    status=Subscription.ACTIVE,
                    chargebee_remote_id=row["subscription[id]"],
                )

                # Add to partial lists
                contracts.append(contract)
                subscriptions.append(subscription)

        if payment_methods:
            SepaMandate.objects.bulk_create(payment_methods)

        if subscriptions:
            Subscription.objects.bulk_create(subscriptions)

        # Preprocess
        User.objects.filter(is_staff=False, chargebee_customer_id__isnull=True).update(
            chargebee_customer_id=F("crm_internal_id")
        )

        logger.info("Done!")
