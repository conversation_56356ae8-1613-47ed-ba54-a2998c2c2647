from dataclasses import dataclass
from typing import List

from django.utils.translation import gettext_lazy as _
from firebase_admin import messaging
from firebase_admin.messaging import SendResponse

from contracts.models import Service
from riders.models import Device
from webhooks.clients.firebase import fcm_client


@dataclass
class FCMResponse:
    response: SendResponse
    configuration: dict


class NotificationException(Exception):
    pass


class BaseNotification:
    def notify(self, *args, **kwargs):
        raise NotImplementedError("Not implemented yet!")


class PushNotification(BaseNotification):
    method = None
    client = fcm_client
    message = None
    key = None
    limit = None
    query = (
        "SELECT riders_device.id, riders_device.registration_id, riders_team.id AS team_id "
        "FROM riders_team "
        "LEFT OUTER JOIN riders_device ON (riders_team.rider_id = riders_device.rider_id) "
        "WHERE (riders_team.service_id = %s::uuid AND riders_device.registration_id IS NOT NULL)"
    )

    def format_message(self, tokens: List, service: Service, msg_data):
        if not msg_data:
            msg_data = {}

        return self.message(
            **{self.key: tokens[0] if self.limit == 1 else tokens},
            data={"id": str(service.pk), "contract_id": service.contract_id, **msg_data},
            notification=messaging.Notification(
                title=_("Service {contract_id}").format(contract_id=service.contract_id),
            ),
        )

    def notify(self, service: Service, msg_data=None):
        configuration = Device.objects.raw(self.query, [service.id])

        # Get data from query
        configuration = {
            str(rider_device.team_id): rider_device.registration_id
            for rider_device in configuration
            if rider_device.registration_id
        }
        registration_tokens = list(configuration.values())

        if not registration_tokens:
            raise NotificationException("No devices found to send the notification")

        message = self.format_message(registration_tokens, service, msg_data)
        response = getattr(self.client, self.method)(message)
        return FCMResponse(response=response, configuration=configuration)


class SimpleFCMNotification(PushNotification):
    method = "send_message"
    message = messaging.Message
    key = "token"
    limit = 1


class MulticastFCMNotification(PushNotification):
    method = "send_multicast_message"
    message = messaging.MulticastMessage
    key = "tokens"
