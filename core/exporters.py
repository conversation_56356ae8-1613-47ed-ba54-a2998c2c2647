from typing import List, Optional

from django.db.models import QuerySet


class BaseExporter:
    """
    Base class for exporting data from a Django QuerySet.

    Attributes:
    queryset : QuerySet
        The Django QuerySet to export.
    columns : Optional[List[str]]
        The columns to export. If not provided, all fields of the model will be exported.

    Methods:
    export() -> bytes
        Exports the queryset as bytes. Raises ValueError if the queryset calls values or values_list method.
    _export()
        Abstract method to be implemented by subclasses.
    """

    def __init__(self, queryset: QuerySet, columns: Optional[List[str]] = None, chunk_size: Optional[int] = 10000):
        """
        Initializes the BaseExporter with a queryset and optional columns.

        Parameters:
        queryset : QuerySet
            The Django QuerySet to export. This is the data source that will be exported.
            It should be a QuerySet instance from the Django ORM.

        columns : Optional[List[str]]
            The columns to export. If not provided, all fields of the model will be exported.
            This parameter should be a list of strings, where each string represents a field name.

        chunk_size : Optional[int]
            The number of rows to process at a time when exporting the queryset.
            This parameter is optional and defaults to 10000. It can be used to optimize memory usage
            when dealing with large querysets.

        """
        self.queryset = queryset
        self.columns = columns
        self.chunk_size = chunk_size

    def export(self) -> bytes:
        """
        Exports the queryset as bytes. Raises ValueError if the queryset calls values or values_list method.

        Returns:
        bytes
            The exported data as bytes.
        """
        if self.queryset.query.values_select:
            raise ValueError(
                "QuerySet must not call to values or values_list method. Please, pass the values as columns instead."
            )

        if not self.columns:
            self.columns = [field.name for field in self.queryset.model._meta.fields]

        self.queryset = self.queryset.values_list(*self.columns).iterator(chunk_size=self.chunk_size)
        return self._export()

    def _export(self):
        """
        Abstract method to be implemented by subclasses.
        """
        raise NotImplementedError("Not implemented yet!")


class CSVExporter(BaseExporter):
    class Echo:
        """
        A simple class to simulate a file-like object for writing CSV data.

        Methods:
        write(value)
            Writes the given value to the buffer.
        """

        def write(self, value):
            return value

    def _export(self):
        """
        Exports the queryset as a CSV file.

        This method uses a generator expression to write each row in the queryset
        to a CSV file. This approach is memory-efficient, as each row is written
        as needed, rather than all at once.

        Returns:
        generator
            A generator expression that yields CSV rows.
        """
        import csv

        echo_buffer = self.Echo()
        csv_writer = csv.writer(echo_buffer)

        def stream_rows():
            # Set header as first row if columns are set
            yield csv_writer.writerow(self.columns)

            for row in self.queryset:
                yield csv_writer.writerow(row)

        return stream_rows()
