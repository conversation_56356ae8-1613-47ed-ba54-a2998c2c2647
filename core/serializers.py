from django.utils.translation import gettext_lazy as _
from django_countries.serializer_fields import <PERSON><PERSON><PERSON>
from rest_framework import serializers

from core.mixins import StringErrorsMixin, SubsetOfFieldsSerializerMixin


class ListCountriesSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to format country fields"""

    code = CountryField(required=False)
    name = serializers.CharField(required=False)


class EmptySerializer(StringErrorsMixin, serializers.Serializer):
    """For redoc purposes"""

    pass


class APIGatewayQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check contract_id type and return empty queryset if is not valid"""

    key = serializers.CharField(
        required=False,
        allow_null=False,
        help_text=_("Required to get the access thru the API Gateway in production environment"),
    )


class TemporaryDeletionEntriesSerializer(StringErrorsMixin, serializers.Serializer):
    token = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    ids = serializers.ListSerializer(child=serializers.UUIDField(allow_null=False), allow_null=False, allow_empty=False)
    include_recursive = serializers.BooleanField(default=False, allow_null=False)
    mode = serializers.ChoiceField(choices=["delete", "update"], default="delete")


class ModelSerializer(StringErrorsMixin, SubsetOfFieldsSerializerMixin, serializers.ModelSerializer):
    """Base class to replace every serializers.ModelSerializer to inject the mixin by default"""

    pass
