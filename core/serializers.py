from django.utils.translation import gettext_lazy as _
from django_countries.serializer_fields import Country<PERSON><PERSON>
from drf_yasg import openapi
from rest_framework import serializers

from business.models import Business, FacebookBusinessAccount, Report
from campaigns.models import (FacebookAd, FacebookAdset, FacebookCampaign,
                              GoogleAd, GoogleAdset, GoogleCampaign)
from core.fields import ModelRepresentationField
from core.mixins import StringErrorsMixin
from core.serializer_fields import Base64VideoField
from organizations.models import Organization
from users.models import User


class EmptySerializer(StringErrorsMixin, serializers.Serializer):
    pass


class FacebookGraphProxySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to format query params for facebook graph proxy calls"""

    path = serializers.CharField(required=True, help_text=_("List of fields to pass to Facebook Graph API"))
    token = serializers.ChoiceField(
        required=True,
        choices=["user", "owner", "from-social-account", "sandbox"],
        help_text="""
    - user: Use token from user who is related with given business (business_id).
    - owner: Use token from user who is **owner** from given business (business_id).
    - from-social-account: Use token from concrete social account (social_account_id).
    - sandbox: Use sandbox token (e.g. getting data from sandbox adaccount).
        """,
    )
    business = serializers.CharField(
        required=False, help_text=_("Business pk from specific user. This is mandatory if token=user or owner.")
    )
    social_account_id = serializers.CharField(
        required=False,
        help_text=_("Social account pk from specific user. This is mandatory if token=from-social-account."),
    )
    page_token = serializers.BooleanField(
        default=False, help_text=_("If a page token must be used instead of user related token")
    )
    fields = serializers.ListField(
        required=False,
        child=serializers.CharField(max_length=200),
        allow_null=True,
        allow_empty=True,
        help_text=_("Dynamic fields to pass into Facebook Request"),
    )
    limit = serializers.IntegerField(required=False, min_value=1, help_text=_("Limit the query size"), allow_null=True)
    params = serializers.DictField(
        required=False,
        allow_null=True,
        help_text=_("Dynamic params to pass into Facebook Request, like pagination pointers."),
    )
    method = serializers.ChoiceField(
        required=False,
        choices=["GET", "POST", "DELETE"],
        default="GET",
        help_text=_("HTTP method to perform to Facebook"),
    )
    video = serializers.ListField(
        child=Base64VideoField(required=False, encoded="source"),
        required=False,
        help_text=_("Upload a video as base64 encoded format passed as list"),
    )

    def validate(self, attrs):
        business = attrs.get("business", None)
        social_account_id = attrs.get("social_account_id", None)
        token = attrs.get("token", None)

        if token in ["user", "owner"] and not business:
            raise serializers.ValidationError(
                {"non_field_errors": _("{token} type requires a business").format(token=token)}
            )

        if token == "from-social-account" and not social_account_id:
            raise serializers.ValidationError(
                {"non_field_errors": _("{token} type requires a social_account_id").format(token=token)}
            )

        return attrs


class FacebookTargetingSearchSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse request params to send to targeting endpoint in Facebook"""

    business = serializers.CharField(
        required=True, help_text=_("Needed to take a valid user token and/or ad_account_id")
    )
    ad_account_id = serializers.CharField(required=True, help_text=_("Ad account to search against"))
    q = serializers.CharField(required=True)
    whitelisted_types = serializers.ListField(child=serializers.CharField(), default=[])
    limit = serializers.IntegerField(default=75)


class FacebookTargetingSearchResponseSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse the search from facebook"""

    id = serializers.CharField(read_only=True)
    name = serializers.CharField(read_only=True)
    type = serializers.CharField(read_only=True)
    path = serializers.JSONField(read_only=True)


class FacebookTargetingSuggestionTargetingListSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse list of dict required in suggestions"""

    id = serializers.CharField(required=True)
    type = serializers.CharField(required=True)


class FacebookTargetingSuggestionsQuerySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse params to send to Facebook"""

    business = serializers.CharField(
        required=True, help_text=_("Needed to take a valid user token and/or ad_account_id")
    )
    ad_account_id = serializers.CharField(required=True, help_text=_("Ad account to search against"))
    targeting_list = FacebookTargetingSuggestionTargetingListSerializer(many=True)
    limit = serializers.IntegerField(default=25)
    locale = serializers.CharField(required=False, help_text=_("Locale to fetch responses from facebook"))


class FacebookTargetingSuggestionsResponseSerializer(FacebookTargetingSearchResponseSerializer):
    """Serializer to parse targeting suggestions response"""

    audience_size = serializers.IntegerField(read_only=True)


class LocationsSearchSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse query params to send to facebook endpoint to make a lookup for locations"""

    business = serializers.CharField(
        required=True, help_text=_("Needed to take a valid user token and/or ad_account_id")
    )
    location_type = serializers.ChoiceField(
        choices=["city", "region", "all"],
        help_text="`region` to get a list of provinces. `city` to get a list of local locations and postal codes",
    )
    q = serializers.CharField()
    country_code = CountryField(required=False)


class FacebookLocationSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse location search response from facebook"""

    country_code = serializers.CharField(max_length=2)
    country_name = serializers.CharField()
    key = serializers.CharField()
    name = serializers.CharField()
    region = serializers.CharField(required=False)
    region_id = serializers.CharField(required=False)
    supports_city = serializers.BooleanField()
    supports_region = serializers.BooleanField()
    type = serializers.ChoiceField(choices=["country", "city", "region", "zip"])
    primary_city = serializers.CharField(required=False)
    primary_city_id = serializers.CharField(required=False)


class ListCountriesSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to format country fields"""

    code = CountryField(required=False)
    name = serializers.CharField(required=False)


class FacebookAvatarSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to fetch facebook avatar to return it in filtered response"""

    class Meta:
        model = FacebookBusinessAccount
        fields = ("facebook_page_data",)

    def to_representation(self, instance):
        representation = super(FacebookAvatarSerializer, self).to_representation(instance)
        return representation.get("facebook_page_data", {}).get("picture", None)


class BusinessFilterSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer for businesses in filtered response"""

    avatar = FacebookAvatarSerializer(source="facebook_business_account", read_only=True, context={"flatten": False})

    class Meta:
        model = Business
        fields = ("id", "name", "avatar")
        swagger_schema_fields = {
            "properties": {
                "id": openapi.Schema(
                    type=openapi.TYPE_STRING,
                ),
                "name": openapi.Schema(
                    type=openapi.TYPE_STRING,
                ),
                "avatar": openapi.Schema(
                    title="Facebook page picture",
                    type=openapi.TYPE_STRING,
                ),
            }
        }


class FacebookCampaignFilterSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer for facebook campaigns in filtered response"""

    business = serializers.UUIDField(source="facebook_business_account.business.pk", read_only=True)

    class Meta:
        model = FacebookCampaign
        fields = ("id", "name", "business", "is_draft")


class GoogleCampaignFilterSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer for facebook campaigns in filtered response"""

    business = serializers.UUIDField(source="google_business_account.business.pk", read_only=True)

    class Meta:
        model = GoogleCampaign
        fields = ("id", "name", "business", "is_draft")


class ReportFilterSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer for reports in filtered response"""

    class Meta:
        model = Report
        fields = ("id", "name", "business")


class RedocResultsFilterSerializer(StringErrorsMixin, serializers.Serializer):
    businesses = BusinessFilterSerializer(many=True, read_only=True)
    facebook_campaigns = FacebookCampaignFilterSerializer(many=True, read_only=True)
    reports = ReportFilterSerializer(many=True, read_only=True)


class RedocFilterSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to doc endpoint in redoc"""

    highest_count = serializers.IntegerField(
        min_value=0,
        read_only=True,
        help_text=_(
            "Max number of items per request. If business=3, campaigns=5 and reports=2, then, this value will be 5."
        ),
    )
    overall_total = serializers.IntegerField(
        min_value=0,
        read_only=True,
        help_text=_(
            "Total of results retrieved. If business=3, campaigns=5 and reports=2, then, this value will be 10."
        ),
    )
    previous = serializers.CharField(read_only=True, allow_null=True, help_text=_("Previous page in url format"))
    next = serializers.CharField(read_only=True, allow_null=True, help_text=_("Next page in url format"))
    results = RedocResultsFilterSerializer(read_only=True)


class CommentSerializer(StringErrorsMixin, serializers.Serializer):
    message = serializers.CharField(required=True)
    api_id = serializers.CharField(required=True, allow_null=False, help_text=_("Campaign, adset or ad"))
    organization_id = serializers.CharField(required=False, help_text=_("Only required for private/chart comments"))
    kpi = serializers.CharField(required=False, allow_null=True, help_text=_("Only required for chart comments"))
    date = serializers.DateField(required=False, allow_null=True, help_text=_("Only required for chart comments"))
    scope = serializers.ChoiceField(
        required=False,
        allow_null=True,
        choices=["campaign", "adset", "ad", "chart"],
        help_text=_("Required to build the notification"),
    )
    social_network = serializers.ChoiceField(
        required=True,
        allow_null=False,
        choices=["facebook", "google"],
        help_text=_("Required to build the notification"),
    )

    def validate(self, attrs):
        organization_id = attrs.get("organization_id")
        public = self.context.get("public")
        date = attrs.get("date")
        kpi = attrs.get("kpi")
        scope = attrs.get("scope")

        if not scope and not public:
            raise serializers.ValidationError({"scope": _("Scope is required for comments in private/chart resources")})

        if not organization_id and not public:
            raise serializers.ValidationError(
                {"organization_id": _("Organization is required for comments in private/charts resources")}
            )

        if kpi and not date:
            raise serializers.ValidationError({"date": _("date is required when giving a kpi")})

        if scope == "chart":
            attrs["scope"] = "campaign"

        return attrs


class CommentMutationSerializer(StringErrorsMixin, serializers.Serializer):
    mongo_id = serializers.CharField(required=True)
    message = serializers.CharField(required=True)
    kpi = serializers.CharField(required=False, allow_null=True, help_text=_("Only required for chart comments"))
    date = serializers.DateField(required=False, allow_null=True, help_text=_("Only required for chart comments"))


class CommentDeleteSerializer(StringErrorsMixin, serializers.Serializer):
    mongo_id = serializers.CharField(required=True)
    date = serializers.DateField(required=False, allow_null=True, help_text=_("Only required for chart comments"))


class CommentRetrieveParamsSerializer(StringErrorsMixin, serializers.Serializer):
    api_id = serializers.CharField(required=True)
    prev_page = serializers.CharField(required=False, allow_null=True, default=None)
    next_page = serializers.CharField(required=False, allow_null=True, default=None)
    date = serializers.DateField(required=False, allow_null=False, help_text=_("Only required for chart comments"))
    kpi = serializers.CharField(required=False, allow_null=True, help_text=_("Only required for chart comments"))
    limit = serializers.IntegerField(
        required=False, allow_null=True, help_text=_("Max number of comments to retrieve (20 as default)")
    )


class CommentBaseSerializer(StringErrorsMixin, serializers.Serializer):
    _id = serializers.CharField(read_only=True, allow_null=True)
    date_created = serializers.DateTimeField(read_only=True, allow_null=True)
    message = serializers.CharField(read_only=True, allow_null=True)
    user_id = serializers.CharField(read_only=True, allow_null=True)
    organization_id = serializers.CharField(read_only=True, allow_null=True)


class CommentResponseSerializer(StringErrorsMixin, serializers.Serializer):
    batch_size = serializers.IntegerField(read_only=True, allow_null=True)
    next_page = serializers.CharField(read_only=True, allow_null=True)
    prev_page = serializers.CharField(read_only=True, allow_null=True)
    response = CommentBaseSerializer(many=True, read_only=True)


class FacebookCampaignSerializer(StringErrorsMixin, serializers.ModelSerializer):
    class Meta:
        model = FacebookCampaign
        fields = ("id", "name")


class GoogleCampaignSerializer(StringErrorsMixin, serializers.ModelSerializer):
    class Meta:
        model = GoogleCampaign
        fields = ("id", "name")


class NotificationContextSerializer(StringErrorsMixin, serializers.Serializer):
    who = ModelRepresentationField(
        queryset=User.objects.all(), allow_null=True, displayed_fields=[("name", "get_full_name"), "id"]
    )
    organization = ModelRepresentationField(
        queryset=Organization.objects.all(), allow_null=True, displayed_fields=["name", "id"], source="organization_id"
    )
    business = ModelRepresentationField(
        queryset=Business.objects.all(), allow_null=True, displayed_fields=["name", "id"], source="business_id"
    )
    campaign = ModelRepresentationField(
        queryset=FacebookCampaign.objects.all(),
        allow_null=True,
        displayed_fields=["name", "id"],
        source="campaign_id",
        extra_queryset=[GoogleCampaign.objects.all()],
    )
    adset = ModelRepresentationField(
        queryset=FacebookAdset.objects.all(),
        allow_null=True,
        displayed_fields=["name", "id"],
        source="adset_id",
        extra_queryset=[GoogleAdset.objects.all()],
    )
    ad = ModelRepresentationField(
        queryset=FacebookAd.objects.all(),
        allow_null=True,
        displayed_fields=["name", "id"],
        source="ad_id",
        extra_queryset=[GoogleAd.objects.all()],
    )
    user = ModelRepresentationField(
        queryset=User.objects.all(),
        allow_null=True,
        displayed_fields=[("name", "get_full_name"), "id"],
        source="user_id",
    )
    kpi = serializers.CharField(allow_null=True, read_only=True)
    date = serializers.DateField(allow_null=True, read_only=True)


class NotificationBaseSerializer(StringErrorsMixin, serializers.Serializer):
    _id = serializers.CharField(read_only=True)
    context = NotificationContextSerializer(required=True, allow_null=True)
    notification_type = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    date_added = serializers.DateTimeField(required=True, allow_null=True)
    read = serializers.BooleanField(default=False)
    extra = serializers.JSONField(read_only=True)


class NotificationResponseSerializer(StringErrorsMixin, serializers.Serializer):
    batch_size = serializers.IntegerField(read_only=True, allow_null=True)
    next_page = serializers.CharField(read_only=True, allow_null=True)
    prev_page = serializers.CharField(read_only=True, allow_null=True)
    response = NotificationBaseSerializer(many=True, read_only=True)
    has_unread_notifications = serializers.BooleanField(read_only=True, default=False)


class NotificationRetrieveParamsSerializer(StringErrorsMixin, serializers.Serializer):
    organization_id = serializers.PrimaryKeyRelatedField(queryset=Organization.objects.all(), allow_null=True)
    prev_page = serializers.CharField(required=False, allow_null=True)
    next_page = serializers.CharField(required=False, allow_null=True)
    limit = serializers.IntegerField(required=False, allow_null=True, default=None)


class NotificationMutationSerializer(StringErrorsMixin, serializers.Serializer):
    mongo_id = serializers.CharField(required=True)


class FilteringQuerySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to filter existing business accounts in organization to avoid import again"""

    organization_id = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.all(),
        required=False,
        allow_null=False,
        allow_empty=False,
        help_text=_("Organization where you want to filter businesses, campaigns and reports"),
    )


class TokenSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to get an encoded token"""

    payload = serializers.CharField(read_only=True, allow_null=False)
