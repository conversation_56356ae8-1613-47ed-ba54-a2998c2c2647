from django.conf import settings
from django.http import HttpResponse
from django.utils.timezone import activate as timezone_activate
from django.utils.translation import activate as translation_activate
from rest_framework import status


class AdminI18NMiddleware:
    """Middleware to set django admin in spanish"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith("/admin/"):
            admin_language = getattr(settings, "ADMIN_LANGUAGE_CODE", settings.LANGUAGE_CODE)
            translation_activate(admin_language)

        response = self.get_response(request)
        return response


class SignalingMiddleware:
    """Middleware to handle sigterm accordingly"""

    _stop_handle_requests = False

    def __init__(self, get_response):
        self.get_response = get_response

    @classmethod
    def stop_handle_requests(cls):
        cls._stop_handle_requests = True

    def __call__(self, request):
        """Disable kubernetes probes on this middleware to route the traffic to fresh one"""

        if self._stop_handle_requests and request.path.endswith("/health/"):
            return HttpResponse(
                "500 Internal server error\nServer is restarting - Please, try again later.",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content_type="text/plain",
            )

        response = self.get_response(request)
        return response


class VersionMiddleware:
    """Set version from settings and inject it in response headers"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        response["Access-Control-Expose-Headers"] = "x-api-version"
        response["x-api-Version"] = settings.API_VERSION
        return response


class AdminTimezoneMiddleware:
    """Middleware to set django admin in spanish"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith("/admin/"):
            admin_language = getattr(settings, "ADMIN_TIME_ZONE", settings.TIME_ZONE)
            timezone_activate(admin_language)

        response = self.get_response(request)
        return response
