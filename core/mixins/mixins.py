from collections import OrderedDict

from django.core.exceptions import FieldDoesNotExist
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers, status
from rest_framework.exceptions import ValidationError
from rest_framework.mixins import CreateModelMixin, DestroyModelMixin
from rest_framework.response import Response
from rest_framework.serializers import raise_errors_on_nested_writes
from rest_framework.settings import api_settings
from rest_framework.utils import html
from rest_framework.utils.serializer_helpers import ReturnList

from core.utils import errors_dict_to_string


class PartialUpdateMixin:
    """
    Only update specific fields on partial_update (patch)
    """

    def update(self, instance, validated_data):
        raise_errors_on_nested_writes("update", self, validated_data)

        _updated_fields = []
        for attr, value in validated_data.items():
            field = getattr(instance, attr)
            if field != value:
                setattr(instance, attr, value)
                _updated_fields.append(attr)

        instance.save(update_fields=_updated_fields)
        return instance


class BulkCreateModelMixin(CreateModelMixin):
    """
    Either create a single or many model instances in bulk by using the
    Serializers ``many=True`` ability from Django REST >= 2.2.5.
    .. note::
        This mixin uses the same method to create model instances
        as ``CreateModelMixin`` because both non-bulk and bulk
        requests will use ``POST`` request method.
    """

    def create(self, request, *args, **kwargs):
        """
        Allows bulk create. Send a list of objects to bulk create.\n
        Example: To create two objects: [{'field1': 'value1'}, {'field1': 'value1'}]
        """
        bulk = isinstance(request.data, list)

        if not bulk:
            return super().create(request, *args, **kwargs)

        else:
            serializer = self.get_serializer(data=request.data, many=True)
            serializer.is_valid(raise_exception=True)
            self.perform_bulk_create(serializer)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

    def perform_bulk_create(self, serializer):
        self.perform_create(serializer)


class StringErrorsMixin:
    """
    Mixin for DRF serializers. Changes errors from lists to string
    """

    @property
    def errors(self):
        errors = super().errors

        if isinstance(errors, ReturnList):
            for i, item in enumerate(errors):
                errors[i] = errors_dict_to_string(errors[i])

            return errors
        return errors_dict_to_string(errors)


class BulkUpdateModelMixin:
    """
    Update model instances in bulk by using the Serializers
    ``many=True`` ability from Django REST >= 2.2.5.
    """

    def get_object(self):
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field

        if lookup_url_kwarg in self.kwargs:
            return super(BulkUpdateModelMixin, self).get_object()

        # If the lookup_url_kwarg is not present
        # get_object() is most likely called as part of options()
        # which by default simply checks for object permissions
        # and raises permission denied if necessary.
        # Here we don't need to check for general permissions
        # and can simply return None since general permissions
        # are checked in initial() which always gets executed
        # before any of the API actions (e.g. create, update, etc)
        return

    def _one_update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=[request.data], partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def _bulk_update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)

        # restrict the update to the filtered queryset
        serializer = self.get_serializer(
            self.filter_bulk_response(request, self.get_queryset()),
            data=request.data,
            many=True,
            partial=partial,
        )
        serializer.is_valid(raise_exception=True)
        self.perform_bulk_update(serializer)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def bulk_update(self, request, *args, **kwargs):
        bulk = isinstance(request.data, list)

        if not bulk:
            return self._one_update(request, *args, **kwargs)

        return self._bulk_update(request, *args, **kwargs)

    def partial_bulk_update(self, request, *args, **kwargs):
        kwargs["partial"] = True
        return self.bulk_update(request, *args, **kwargs)

    def perform_update(self, serializer):
        serializer.save()

    def perform_bulk_update(self, serializer):
        return self.perform_update(serializer)


class ManyToManyGetOrCreateOrUpdateSerializerMixin:
    """
    Check if must use a given manytomany related object or create it
    """

    m2m_related_fields = None

    def _extract_related_fields(self, validated_data):
        related_fields = OrderedDict()
        for field_name, field in self.fields.items():
            if field.source not in validated_data:
                # Skip field if field is not required
                continue

            if field.read_only:
                continue

            try:
                related_field = self.Meta.model._meta.get_field(field.source)
                if related_field.many_to_many and field_name in self.m2m_related_fields:
                    related_fields[field_name] = (
                        related_field.remote_field.model,
                        validated_data.pop(field.source),
                        field.source,
                    )

            except FieldDoesNotExist:
                pass

        return related_fields

    def exclude_fields_in_many_to_many(self, instance, attribute):
        raise NotImplementedError("You must update this method in child class for updating many to many relationships")

    # TODO: This method is slightly efficient because create ads in bulk, but their counterpart is that save() method
    #  is not called
    # def bulk_create(self, validated_data):
    #     """This method is intended to be used paired with a ListSerializer only"""
    #
    #     # First one step: extract related fields from body object
    #     related_fields = OrderedDict()
    #
    #     for index, item in enumerate(validated_data):
    #         related_fields[index] = (self._extract_related_fields(item), item)
    #
    #     # Second step: create in bulk objects
    #     ModelClass = self.Meta.model
    #     items = [ModelClass(**item) for _, (_, item) in related_fields.items()]
    #     objects = ModelClass.objects.bulk_create(items)
    #
    #     # Third step: set M2M relationships
    #     for index, (related_field, item) in related_fields.items():
    #         for field_name, (related_field_model, data, field_source) in related_field.items():
    #             existing_data = []
    #             non_existing_data = []
    #
    #             for related_data in data:
    #                 pk = related_data.get("id", None)
    #                 if pk is None:
    #                     non_existing_data.append(related_field_model(**related_data))
    #                 else:
    #                     existing_data.append(pk)
    #
    #             if non_existing_data:
    #                 existing_data.extend(related_field_model.objects.bulk_create(non_existing_data))
    #
    #             m2m_manager = getattr(objects[index], field_source)
    #             m2m_manager.add(*existing_data)
    #     return objects

    def bulk_create(self, validated_data):
        """This method is intended to be used paired with a ListSerializer only"""
        return [self.create(data) for data in validated_data]

    def create(self, validated_data):
        related = self._extract_related_fields(validated_data)

        # First, create the instance
        instance = super().create(validated_data)

        # Then, update many to many relationship accordingly
        for field_name, (related_field_model, data, field_source) in related.items():
            existing_data = []
            non_existing_data = []

            for related_data in data:
                pk = related_data.get("id", None)
                if pk is None:
                    non_existing_data.append(related_field_model(**related_data))
                else:
                    existing_data.append(pk)

            if non_existing_data:
                existing_data.extend(related_field_model.objects.bulk_create(non_existing_data))

            m2m_manager = getattr(instance, field_source)
            m2m_manager.add(*existing_data)
        return instance

    def update(self, instance, validated_data):
        related = self._extract_related_fields(validated_data)

        # First, create the instance
        instance = super().update(instance, validated_data)

        # Then, update many to many relationship accordingly
        for field_name, (related_field_model, data, field_source) in related.items():
            existing_data = []
            non_existing_data = []

            for related_data in data:
                pk = related_data.get("id", None)
                if pk is None:
                    non_existing_data.append(related_field_model(**related_data))
                else:
                    existing_data.append(pk)

            m2m_manager = getattr(instance, field_source)

            # Remove M2M relationship (i.e. items) if needed
            excluded_fields_query = self.exclude_fields_in_many_to_many(instance, field_source) or []
            excluded_fields = m2m_manager.exclude(*excluded_fields_query).values_list("pk", flat=True)
            fields_to_add, fields_to_remove = self._format_m2m_data(existing_data, excluded_fields)

            if non_existing_data:
                fields_to_add.extend(related_field_model.objects.bulk_create(non_existing_data))

            # Set new M2M data
            m2m_manager.filter(id__in=fields_to_remove).delete()
            m2m_manager.set(fields_to_add)
        return instance

    def _format_m2m_data(self, fields_to_add, fields_to_exclude):
        fields_to_add = set(fields_to_add)
        fields_to_exclude = set(fields_to_exclude)

        return list(fields_to_add), list(fields_to_exclude - fields_to_add)


class BaseBulkListSerializer(serializers.ListSerializer):
    """Example taken from https://www.django-rest-framework.org/api-guide/serializers/#listserializer"""

    """https://github.com/miki725/django-rest-framework-bulk/blob/master/rest_framework_bulk/drf3/serializers.py"""

    def to_internal_value(self, data):
        """Override this method to set per child, the instance class"""
        if html.is_html_input(data):
            data = html.parse_html_list(data, default=[])

        if not isinstance(data, list):
            message = self.error_messages["not_a_list"].format(input_type=type(data).__name__)
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: [message]}, code="not_a_list")

        if not self.allow_empty and len(data) == 0:
            message = self.error_messages["empty"]
            raise ValidationError({api_settings.NON_FIELD_ERRORS_KEY: [message]}, code="empty")

        ret = []
        errors = []

        for item in data:
            try:
                # Set every child instance with the right model instance
                self.child.instance = self.instance.get(id=item["id"])
                self.child.initial_data = item

                validated = self.child.run_validation(item)
            except ValidationError as exc:
                errors.append(exc.detail)
            except self.child.Meta.model.DoesNotExist:
                errors.append({"detail": _("Not found")})
            else:
                ret.append(validated)
                errors.append({})

        if any(errors):
            raise ValidationError(errors)

        return ret


class BulkUpdateListSerializer(BaseBulkListSerializer):
    update_lookup_field = "id"

    def update(self, queryset, validated_data):
        id_attr = getattr(self.child.Meta, "update_lookup_field", "id")
        all_validated_data_by_id = {i.pop(id_attr): i for i in validated_data}

        filtered_queryset = queryset.filter(
            **{f"{self.update_lookup_field}__in": list(all_validated_data_by_id.keys())}
        )

        ret = []
        for obj in filtered_queryset:
            obj_id = getattr(obj, id_attr)
            data = all_validated_data_by_id.get(obj_id)
            ret.append(self.child.update(obj, data))

        return ret


class BulkCreateListSerializer(serializers.ListSerializer):
    """Example taken from https://www.django-rest-framework.org/api-guide/serializers/#listserializer"""

    """https://github.com/miki725/django-rest-framework-bulk/blob/master/rest_framework_bulk/drf3/serializers.py"""

    update_lookup_field = "id"

    def create(self, validated_data):
        return self.child.bulk_create(validated_data)


class BulkDeleteModelMixin(DestroyModelMixin):
    """Destroy model instances in bulk by using the Serializers ``many=True`` ability from Django REST >= 2.2.5."""

    def bulk_destroy(self, request, *args, **kwargs):
        queryset = self.filter_bulk_response(request, self.get_queryset())
        serializer = self.get_serializer(queryset, data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        self.perform_bulk_destroy(queryset)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def perform_bulk_destroy(self, instance):
        self.perform_destroy(instance)


class BulkDeleteListSerializer(BaseBulkListSerializer):
    """Just for semantics, override the base list serializer"""

    pass
