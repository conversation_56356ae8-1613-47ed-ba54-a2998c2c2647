from collections import OrderedDict

from drf_yasg.inspectors import SwaggerAutoSchema as DefaultSwaggerAutoSchema
from drf_yasg.utils import no_body

from core.utils import get_openapi_error_response


class ReadOnly:
    def get_fields(self):
        new_fields = OrderedDict()
        for fieldName, field in super().get_fields().items():
            if not field.write_only:
                new_fields[fieldName] = field
        return new_fields


class WriteOnly:
    def get_fields(self):
        new_fields = OrderedDict()
        for fieldName, field in super().get_fields().items():
            if not field.read_only:
                new_fields[fieldName] = field
        return new_fields


class BlankMeta:
    pass


class SwaggerAutoSchema(DefaultSwaggerAutoSchema):
    """Helper class to hide writable fields on responses
    @see https://github.com/axnsan12/drf-yasg/issues/70#issuecomment-652848268
    """

    def get_view_serializer(self):
        return self._convert_serializer(WriteOnly)

    def get_default_response_serializer(self):
        body_override = self._get_request_body_override()
        if body_override and body_override is not no_body:
            return body_override

        return self._convert_serializer(ReadOnly)

    def _convert_serializer(self, new_class):
        serializer = super().get_view_serializer()
        if not serializer:
            return serializer

        class CustomSerializer(new_class, serializer.__class__):
            class Meta(getattr(serializer.__class__, "Meta", BlankMeta)):
                ref_name = new_class.__name__ + serializer.__class__.__name__

        new_serializer = CustomSerializer(data=serializer.data)
        return new_serializer

    def get_summary_and_description(self):
        summary_override = self.overrides.get("operation_summary", None)

        if summary_override is None:
            self.overrides["operation_summary"] = (
                f'{self.operation_keys[1].replace("_", " ").title()} '
                f'{self.operation_keys[0].replace("_", " ").title()}'
            )

        return super().get_summary_and_description()

    def get_default_responses(self):
        responses = super().get_default_responses()

        method = self.method.lower()

        if method in ("post", "put", "patch"):
            try:
                responses["400"] = get_openapi_error_response(self.view.get_serializer_class())
            except AttributeError:
                pass

        return responses
