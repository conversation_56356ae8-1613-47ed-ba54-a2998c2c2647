from drf_yasg.generators import OpenAPISchemaGenerator


class BoxtoBoxOpenAPISchemaGenerator(OpenAPISchemaGenerator):
    def get_schema(self, request=None, public=False):
        schema = super().get_schema(request=request, public=public)

        groups = {
            "x-tagGroups": [
                {"name": "Registration/Access", "tags": ["auth", "activate-account"]},
                {
                    "name": "Contracts",
                    "tags": [
                        "inventory",
                        "contracts",
                        "deliveries",
                        "pickups",
                        "addresses",
                        "services",
                        "tasks",
                        "placements",
                    ],
                },
                {"name": "Core", "tags": ["countries"]},
                {
                    "name": "Intranet",
                    "tags": [
                        "ui-budget-estimation",
                        "ui-users",
                        "ui-inventory",
                        "ui-delivery",
                        "ui-contracts",
                        "ui-users-hs",
                        "ui-contracts-hs",
                        "ui-delivery-update-history",
                        "ui-budget-estimation-update-history",
                        "ui-activate-account",
                        "ui-warehouse-difference",
                        "ui-calendar-fetch-calendars",
                        "ui-calendar-events",
                        "ui-calendar-partial-sync",
                        "ui-calendar-full-sync",
                        "ui-events",
                        "ui-teams",
                        "ui-services",
                        "ui-warehouses",
                        "ui-calendars",
                        "ui-riders",
                        "ui-riders-configuration",
                        "ui-hubspot",
                        "ui-tasks",
                        "ui-reminders",
                        "ui-chargebee",
                        "ui-global-settings",
                        "ui-placements",
                        "ui-calendar-configs",
                        "ui-reviews",
                        "ui-cities",
                        "ui-residences",
                        "ui-residence-groups",
                        "ui-warehouse-users",
                    ],
                },
                {
                    "name": "Payments",
                    "tags": ["billing-data", "payment-methods", "payment-intent", "invoices", "credit-notes"],
                },
                {"name": "Riders", "tags": ["riders", "devices"]},
                {"name": "Users", "tags": ["users"]},
            ]
        }

        all_tags = self._get_tags(schema)
        grouped_tags = {tag for group in groups["x-tagGroups"] for tag in group["tags"]}

        # Add ungrouped tags
        ungrouped_tags = list(set(all_tags) - set(grouped_tags))
        if ungrouped_tags:
            groups["x-tagGroups"].append({"name": "Ignore It", "tags": ungrouped_tags})

        schema.update(groups)
        return schema

    def _get_tags(self, schema):
        tags = []
        for key, path_item in schema.paths.items():
            for key, value in path_item.items():
                if key != "parameters":
                    for tag in value.tags:
                        tags.append(tag)

        return tags
