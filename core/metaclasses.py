class AutoRegistryMetaclass(type):
    """
    A metaclass that automatically registers subclasses into a global registry based on a specific attribute.

    Attributes:
    _REGISTRY (dict): A dictionary to store the registered subclasses. The keys are the values of the "lookup" attribute
                      in the subclasses, and the values are the subclasses themselves.

    Methods:
    __init__(cls, name, bases, attrs): The constructor method that initializes the metaclass. It registers subclasses
                                         into the global registry based on the "lookup" attribute.
    """

    _REGISTRY = {}

    def __init__(cls, name, bases, attrs):
        super().__init__(name, bases, attrs)

        # Skip the base modifier class to be added into global registry
        is_not_base_class = cls.__base__ is not object  # The topmost class inherits from object
        if is_not_base_class:
            lookup = getattr(cls, "lookup", None)
            if lookup is not None:
                cls._REGISTRY[lookup] = cls
