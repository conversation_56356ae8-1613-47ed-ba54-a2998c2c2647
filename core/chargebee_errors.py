from django.utils.translation import gettext_lazy as _

ERROR_MESSAGES = {
    "param_wrong_value": _("Invalid format"),
    "payment_processing_failed": _("Payment cannot be created as the payment collection failed"),
    "payment_method_verification_failed": _("There's a problem adding the payment method"),
    "internal_error": _("Unknown error in the payment gateway. Wait few minutes and try again"),
    "api_request_limit_exceeded": _("Error processing the payment. Try again later"),
    "internal_temporary_error": _("Unknown error in the payment gateway. Wait few minutes and try again"),
    "default": _("Unknown error. Please, wait few minutes and try again"),
}


def get_chargebee_error_message(message):
    """Helper method to get the right error message from chargebee api"""
    try:
        return ERROR_MESSAGES[message]
    except KeyError:
        return ERROR_MESSAGES["default"]
