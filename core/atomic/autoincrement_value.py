# from django.db import connection
#
# from core.models import AutoIncrementSequence
#
# POSTGRESQL_UPSERT = """
#         INSERT INTO {db_table} (name, counter)
#              VALUES (%s, %s)
#         ON CONFLICT (name)
#       DO UPDATE SET counter = {db_table}.counter + 1
#           RETURNING counter;
# """
#
#
# def default_autoincrement_value(name, initial_value=1):
#     """Custom method to be used to generate race-condition free atomic counters.
#        This method must be executed to save its return value to the database within the same transaction
#     @see https://github.com/aaugustin/django-sequences
#     """
#
#     with connection.cursor() as cursor:
#         cursor.execute(POSTGRESQL_UPSERT.format(db_table=AutoIncrementSequence._meta.db_table), [name, initial_value])
#         result = cursor.fetchone()
#
#     return result[0]
