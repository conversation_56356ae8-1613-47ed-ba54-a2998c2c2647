from django.core import checks
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON>ield
from django.db.models.sql import Query
from rest_framework.fields import <PERSON><PERSON><PERSON><PERSON> as DRFCharField


class CharField(DRFCharField):
    BLANK = ""

    def __init__(self, on_null="-", **kwargs):
        self.on_null = on_null
        super().__init__(**kwargs)

    def get_attribute(self, instance):
        instance = super().get_attribute(instance)
        if instance is None and (self.on_null or self.on_null == self.BLANK):
            return self.on_null
        return instance


class RawJSONField(JSONField):
    """Custom implementation of JSON field in order to preserve the original JSON representation.
    Native Postgres Json type has some caveats, like missing equality operator, needed to perform distinct queries.
    For that reason, we set the JSONField to "text" type instead of "json" type.
    @see: https://code.djangoproject.com/ticket/31973#comment:22
    @see: https://github.com/rails/rails/issues/17706
    @see: https://stackoverflow.com/questions/48420438/could-not-identify-an-equality-operator-for-type-json-when-using-distinct
    """

    def db_type(self, connection):
        return "text"


class GeneratedField(models.Field):
    """
    Custom Generated Field inspired from Django 5.0 implementation. Once the project is migrated to 5.2, DELETE it.
    It will only work with postgreSQL backend
    """

    generated = True

    def __init__(self, *, expression, base_field, **kwargs):
        if kwargs.setdefault("editable", False):
            raise ValueError("GeneratedField cannot be editable.")
        if not kwargs.setdefault("blank", True):
            raise ValueError("GeneratedField must be blank.")
        if kwargs.setdefault("default", None):
            raise ValueError("GeneratedField cannot have a default value.")

        # kwargs.setdefault("default", None)

        self.expression = expression
        self.base_field = base_field
        super().__init__(**kwargs)

    def get_col(self, alias, base_field=None):
        if alias != self.model._meta.db_table and base_field in (None, self):
            base_field = self.base_field
        return super().get_col(alias, base_field)

    def contribute_to_class(self, *args, **kwargs):
        super().contribute_to_class(*args, **kwargs)

        self._query = Query(model=self.model, alias_cols=False)
        # Register lookups from the base_field class.
        for lookup_name, lookup in self.base_field.get_lookups().items():
            self.register_lookup(lookup, lookup_name=lookup_name)

    def check(self, **kwargs):
        databases = kwargs.get("databases") or []
        errors = super().check(**kwargs)

        base_field_clone = self.base_field.clone()
        base_field_clone.model = self.model
        base_field_clone.name = self.name
        base_field_checks = base_field_clone.check(databases=databases)
        if base_field_checks:
            separator = "\n    "
            error_messages = separator.join(
                f"{output_check.msg} ({output_check.id})"
                for output_check in base_field_checks
                if isinstance(output_check, checks.Error)
            )
            if error_messages:
                errors.append(
                    checks.Error(
                        f"GeneratedField.base_field has errors:{separator}{error_messages}",
                        obj=self,
                        id="fields.E223",
                    )
                )
            warning_messages = separator.join(
                f"{output_check.msg} ({output_check.id})"
                for output_check in base_field_checks
                if isinstance(output_check, checks.Warning)
            )
            if warning_messages:
                errors.append(
                    checks.Warning(
                        f"GeneratedField.base_field has warnings:{separator}{warning_messages}",
                        obj=self,
                        id="fields.W224",
                    )
                )
        return errors

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        del kwargs["blank"]
        del kwargs["editable"]
        del kwargs["default"]
        kwargs["expression"] = self.expression
        kwargs["base_field"] = self.base_field
        return name, path, args, kwargs

    def generated_sql(self, connection):
        compiler = connection.ops.compiler("SQLCompiler")(self._query, connection=connection, using=None)
        resolved_expression = self.expression.resolve_expression(self._query, allow_joins=False)
        sql, params = compiler.compile(resolved_expression)
        if (
            getattr(self.expression, "conditional", False)
            and not connection.features.supports_boolean_expr_in_select_clause
        ):
            sql = f"CASE WHEN {sql} THEN 1 ELSE 0 END"

        return sql, params

    def db_type(self, connection):
        base_type = self.base_field.db_type(connection)

        expression_sql, params = self.generated_sql(connection)
        expression_sql = expression_sql % tuple(connection.schema_editor().quote_value(p) for p in params)

        # Workaround for NOT NULL/NULL generated query on migration adding the data_type at the beginning of the query
        return f"{base_type} GENERATED ALWAYS AS ({expression_sql}) STORED"


class AutoIncrementField(GeneratedField):
    """
    A custom Django model field that represents an auto-incrementing integer.

    This field is a subclass of `GeneratedField` and uses PostgreSQL's `GENERATED BY DEFAULT AS IDENTITY` feature.
    It generates a unique integer value for each new instance of the model, starting from the specified `start_value`
    and incrementing by the specified `increment_by` value.

    It is not a gap-less solution if a monotonic sequence is required

    Parameters:
    - start_value (int): The initial value for the auto-incrementing integer. Default is 1.
    - increment_by (int): The value by which the auto-incrementing integer increases for each new instance. Default is 1.
    - **kwargs: Additional keyword arguments that are passed to the parent `GeneratedField` class.

    Returns:
    - A string representing the SQL data type for the auto-incrementing integer field, including the
    `GENERATED BY DEFAULT AS IDENTITY` clause.
    """

    def __init__(self, *, start_value=1, increment_by=1, **kwargs):
        self.start_value = start_value
        self.increment_by = increment_by

        super().__init__(
            expression=models.expressions.RawSQL("NULL", []), base_field=models.BigIntegerField(), **kwargs
        )

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        del kwargs["expression"]
        del kwargs["base_field"]
        if "null" in kwargs:
            del kwargs["null"]

        # Needed to use it later in db_type (i.e. persisted across migrations)
        kwargs["start_value"] = self.start_value
        kwargs["increment_by"] = self.increment_by
        return name, path, args, kwargs

    def db_type(self, connection):
        base_type = self.base_field.db_type(connection)

        # Workaround for NOT NULL/NULL generated query on migration adding the data_type at the beginning of the query
        return (
            f"{base_type} GENERATED BY DEFAULT AS IDENTITY (START WITH {self.start_value} INCREMENT BY"
            f" {self.increment_by})"
        )
