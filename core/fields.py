"""Custom fields for Django Rest Framework serializers"""
from datetime import datetime

from django.core.exceptions import ObjectDoesNotExist
from django.utils.timezone import make_aware
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.relations import Object<PERSON><PERSON><PERSON>rror, ObjectValueError


class TimestampField(serializers.DateTimeField):
    """ Convert a django datetime from timestamp"""

    def __init__(self, *args, **kwargs):
        kwargs["read_only"] = True
        super(TimestampField, self).__init__(*args, **kwargs)

    def to_representation(self, value):
        """
        Convert the field to its internal representation (aka timestamp)
        :param value: the DateTime value
        :return: a UTC timestamp integer
        """
        converted = make_aware(datetime.fromtimestamp(value))
        return super(TimestampField, self).to_representation(converted)


class DecimalInCentsField(serializers.DecimalField):
    """Convert cents values into integer with decimal part"""

    def __init__(self, *args, **kwargs):
        kwargs["read_only"] = True
        super(DecimalInCentsField, self).__init__(*args, **kwargs)

    def to_representation(self, value):
        converted = round(value / 100, 2)
        return super(DecimalInCentsField, self).to_representation(converted)


class ModelRepresentationField(serializers.RelatedField):
    """Custom field to get, from given general id a custom representation for models"""

    lookup_field = "pk"
    displayed_fields = ["pk"]
    extra_queryset = []
    help_text = _("This field will be rendered as JSON with {fields} fields")

    def __init__(self, **kwargs):
        self.representation = kwargs.pop("displayed_fields", self.displayed_fields)
        self.lookup_field = kwargs.pop("lookup_field", self.lookup_field)
        self.extra_queryset = kwargs.pop("extra_queryset", self.extra_queryset)

        kwargs["help_text"] = kwargs.get("help_text", self.help_text.format(fields=self.representation))

        super(ModelRepresentationField, self).__init__(**kwargs)

    def get_object(self, lookup_value):
        assert isinstance(self.extra_queryset, list)

        lookup_kwargs = {self.lookup_field: lookup_value}
        querysets = [self.get_queryset()]
        querysets.extend(self.extra_queryset)

        assert querysets

        for queryset in querysets:
            try:
                return queryset.get(**lookup_kwargs)
            except (ObjectDoesNotExist, ObjectValueError, ObjectTypeError, ValueError, TypeError):
                pass
        return None

    def get_field(self, field):
        """Parse field to allow callables instead of plain model attributes"""
        if callable(field):
            return field()
        return field

    def sanitize_field(self, field):
        """Get the right name for displaying names into serialized response"""
        if isinstance(field, tuple):
            key, value = field
            return key, value

        return field, field

    def to_representation(self, value):
        object = self.get_object(value)
        response = {}

        if object:
            for field in self.representation:
                serialized_name, value = self.sanitize_field(field or value)
                response[serialized_name] = self.get_field(getattr(object, value))

        return response or None
