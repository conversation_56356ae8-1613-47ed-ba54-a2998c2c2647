from django.contrib.admin.filters import RelatedFieldListFilter


class SearchableDropdownFilter(RelatedFieldListFilter):
    template = "admin/searchable_dropdown_filter.html"


class RiderSearchableFilter(SearchableDropdownFilter):
    @property
    def include_empty_choice(self):
        return False

    def field_choices(self, field, request, model_admin):
        """
        Override field_choices to filter the users by the `is_rider` field.
        """
        # Filter the queryset to include only users where is_rider is True
        queryset = field.related_model.objects.filter(is_rider=True)
        return [(obj.pk, str(obj)) for obj in queryset]


class WarehouseUserSearchableFilter(SearchableDropdownFilter):
    @property
    def include_empty_choice(self):
        return False
