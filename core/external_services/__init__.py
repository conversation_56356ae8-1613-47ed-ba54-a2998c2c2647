# Export every client
from django.conf import settings
from network_clients.base.pubsub_client import Pub<PERSON><PERSON><PERSON>lient
from network_clients.base.sheets_client import GoogleSheetsClient
from network_clients.base.storage_client import Storage<PERSON>lient
from network_clients.slack.client import <PERSON>lack<PERSON>lient

from core.external_services.microservices.async_api import Asynchronous<PERSON>lient
from core.external_services.microservices.hubspot import <PERSON><PERSON><PERSON><PERSON><PERSON>
from core.external_services.microservices.warehouse import \
    WarehouseServiceClient

slack_client = SlackClient()
asynchronous_client = AsynchronousClient(
    service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
    google_cloud_project_name=settings.GOOGLE_CLOUD_PROJECT_NAME,
    google_cloud_project_location=settings.GOOGLE_CLOUD_LOCATION_NAME,
)
hubspot_async_client = HubspotClient(
    service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
    google_cloud_project_name=settings.GOOGLE_CLOUD_PROJECT_NAME,
)
storage_client = StorageClient(
    service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
)
sheets_client = GoogleSheetsClient(service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS)
warehouse_difference_client = WarehouseServiceClient(
    service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
    google_cloud_project_name=settings.GOOGLE_CLOUD_PROJECT_NAME,
    google_cloud_project_location=settings.GOOGLE_CLOUD_LOCATION_NAME,
)
pubsub_client = PubSubClient(
    service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
    google_cloud_project_name=settings.GOOGLE_CLOUD_PROJECT_NAME,
)
