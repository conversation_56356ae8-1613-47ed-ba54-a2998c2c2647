import logging

from allauth.socialaccount.models import <PERSON><PERSON>pp
from allauth.socialaccount.providers.facebook.provider import FacebookProvider
from django.conf import settings
from facebook_business import FacebookA<PERSON><PERSON><PERSON>, FacebookSession
from facebook_business.exceptions import FacebookRequestError

from core.external_services.facebook.mixins import (AdAccountMixin, AdMixin,
                                                    AdSetMixin, CampaignMixin,
                                                    LeadFormMixin, PageMixin,
                                                    ProxyMixin,
                                                    TargetingSearchMixin,
                                                    TokenMixin, UserMixin,
                                                    VideoMixin)

logger = logging.getLogger(__name__)


class FacebookApi:
    def __init__(self, sandbox_or_production=False):
        self._facebook_api = None
        self._sandbox_or_production = sandbox_or_production

    def __call__(self, access_token, *args, reraise=False):
        app_id, app_secret = self.load_app_credentials(not self._sandbox_or_production)
        return self.lazy_init(access_token, app_id, app_secret)

    def lazy_init(self, access_token, app_id, app_secret):
        session = FacebookSession(app_id, app_secret, access_token)
        api = FacebookAdsApi(session, api_version=f"v{settings.GRAPH_API_VERSION}")
        return api

    def load_app_credentials(self, app_test=False):
        """Get app credentials according to environment"""
        social_app = {"name": settings.FACEBOOK_APPLICATION_NAME, "provider": FacebookProvider.id}

        if app_test:
            social_app["name"] += "-Test"

        social_app = SocialApp.objects.get(**social_app)
        return social_app.client_id, social_app.secret


class FacebookClient(
    PageMixin,
    LeadFormMixin,
    UserMixin,
    AdAccountMixin,
    TargetingSearchMixin,
    CampaignMixin,
    AdSetMixin,
    AdMixin,
    ProxyMixin,
    TokenMixin,
    VideoMixin,
):
    __locale__ = "en_GB"

    def __init__(self, sandbox_or_production=False):
        self._facebook_api = None
        self._sandbox_or_production = sandbox_or_production
        self._api = FacebookApi(sandbox_or_production=sandbox_or_production)

    def __call__(self, klass, fbid=None, access_token=None):
        """Inner call to get facebook api instance"""
        assert klass is not None
        if klass.__name__ == "TargetingSearch":
            return getattr(klass, "search")(params=fbid, api=self._api(access_token))
        elif klass.__name__ == "ProxyCall":
            return self._api(access_token)
        return klass(fbid=fbid, api=self._api(access_token))

    def __getattribute__(self, item):
        """Intercept every call to Facebook client.
        - Reraise: if facebook error must be raised in parent caller
        """

        def fn(*args, **kwargs):
            reraise = kwargs.get("reraise", False)
            try:
                return super(FacebookClient, self).__getattribute__(item)(*args, **kwargs)
            except FacebookRequestError as e:
                is_revoked = self._handle_error(item, e, access_token=kwargs.get("access_token", None))
                if reraise:
                    error_body = self._format_body(e.body())

                    if is_revoked:
                        error_body["service"] = "facebook"

                    error = FacebookRequestError(
                        e.api_error_message(),
                        e.request_context(),
                        e.http_status(),
                        e.http_headers(),
                        self._format_body(e.body()),
                    )
                    error.revoked = is_revoked
                    raise error
            except Exception as e:
                self._handle_error(item, e, debug="info")

        return fn

    def _handle_error(self, item, error, debug="warning", access_token=None):
        if not item.startswith("_"):
            getattr(logger, debug)(f"Error calling facebook in function {item} - {error}")
            # Revoke this token if is invalid
            if (
                access_token
                and error.api_error_code() == 190
                or (error.api_error_code() == 102 and not error.api_error_subcode())
                or (error.api_error_subcode() in [463, 467])
            ):
                from users.auth import SocialToken

                SocialToken.objects.revoke(token=access_token)
                return True

        return False

    def _format_body(self, body):
        if body and "error" in body:
            error = body["error"]
            body["error"] = {**error, **{"message": error.get("error_user_msg", error.get("message"))}}

        return body
