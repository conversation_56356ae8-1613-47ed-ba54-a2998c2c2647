from core.utils import BaseIterator


class FacebookCampaignImporter(BaseIterator):
    """Facebook campaign importer implemented as iterator using a recursive approach, avoiding none results.
    To use it, iterate on a for loop."""

    def __init__(self, campaign_iterator, max_number_of_results=None, metadata=None):
        super(FacebookCampaignImporter, self).__init__(campaign_iterator, max_number_of_results)

        if metadata is None:
            metadata = {}

        self._metadata = metadata
        self._campaigns = {}

    def belong_campaign_to_business(self, actor_id):
        return actor_id == self._metadata.get("page_id")

    def __next__(self):
        item = next(super(FacebookCampaignImporter, self).__next__())

        actor_id = (item.get("adcreatives", {}).get("data") or [{}])[0].get("actor_id", None)
        promoted_object = item.get("promoted_object", {})
        campaign = item.get("campaign", {})

        if self.belong_campaign_to_business(actor_id) and not self._campaigns.get(campaign.get("id")):
            if self.is_full:
                raise StopIteration()

            # Avoid checking the same campaign again
            self._campaigns[campaign.get("id")] = True

            conversion_event_type = promoted_object.get("custom_event_type", None)
            pixel_id = promoted_object.get("pixel_id", None)

            return {
                "remote_id": campaign.get("id"),
                "name": campaign.get("name"),
                "created_at": campaign.get("created_time"),
                "objective": campaign.get("objective"),
                "bid_strategy": campaign.get("bid_strategy"),
                "stop_time": campaign.get("stop_time"),
                "imported": True,
                "facebook_business_account": self._metadata.get("facebook_business_account"),
                "currency": self._metadata.get("currency", "EUR"),
                "pixel_id": pixel_id,
                "conversion_event_type": conversion_event_type if conversion_event_type != "OTHER" else "CUSTOM",
            }

        # Call recursively to skip empty values
        return self.__next__()
