from django.conf import settings

from core.external_services.microservices import BaseAsynchronousClient


class WarehouseServiceClient(BaseAsynchronousClient):
    """Warehouse microservice client to orchestrate data between services"""

    url = settings.WAREHOUSE_DIFFERENCES_MICROSERVICE_URL
    cloud_queue = settings.WAREHOUSE_DIFFERENCE_CLOUD_QUEUE

    def run_warehouse_difference(self, warehouse_model_id, date, warehouse_id, in_seconds=0):
        """Create a pubsub message to call remote service to run the warehouse difference"""

        return self._proxy_call(
            endpoint="run/",
            payload={
                "id": warehouse_model_id,
                "date": date,
                "warehouse_id": warehouse_id,
            },
            in_seconds=in_seconds,
        )
