from django.conf import settings
from network_clients.base.pubsub_client import PubSubClient


class HubspotClient(PubSubClient):
    """Hubspot client to send requests against hubspot async backend based in pub/messages"""

    def update_deal(self, deal_id, properties: dict):
        """Update a deal in hubspot with 'properties' information"""

        if settings.ENVIRONMENT != "PROD":
            return

        return self.request(
            topic=settings.HUBSPOT_PUBSUB_TOPIC,
            data={"deal_id": deal_id, "properties": properties},
            endpoint="update_deal",
        )

    def update_contact(self, contact_id, properties: dict):
        """Update a contact in hubspot with 'properties' information"""

        if settings.ENVIRONMENT != "PROD":
            return

        return self.request(
            topic=settings.HUBSPOT_PUBSUB_TOPIC,
            data={"contact_id": contact_id, "properties": properties},
            endpoint="update_contact",
        )

    def create_note(self, deal_id, body: str):
        """Create an engagement (note) on deals when any update has been made on our side. Accepts all valid HTML tags
        to format the text body."""

        if settings.ENVIRONMENT != "PROD":
            return

        return self.request(
            topic=settings.HUBSPOT_PUBSUB_TOPIC, data={"deal_id": deal_id, "body": body}, endpoint="create_note"
        )
