import importlib
import logging

from django.conf import settings
from django.utils import timezone

from core.external_services.microservices.base import (BaseClient,
                                                       BasePubSubClient,
                                                       ClientError)

logger = logging.getLogger(__name__)

NOTIFICATION_TOPICS = {
    "PROD": "notifications",
    "STAGING": None,
    "LOCAL": None,
    "TEST": None,
}


class NotificationTypes:
    """Notification created entirely by API"""

    import_campaign = "import_campaign"
    pomegranate_campaign = "pomegranate_campaign"


class NotificationMixin:
    @classmethod
    def build_notification_context(
        cls, who, api_id, scope, date, kpi, user_id=None, filter_by="pk", social_network="facebook"
    ):
        ctx = {}
        if scope is not None:
            module = getattr(
                importlib.import_module("campaigns.models"), f"{social_network.capitalize()}{scope.capitalize()}"
            )
            ctx = module.objects.hierarchy(**{filter_by: api_id, "only_first": True})

        if who is not None:
            ctx.update(
                **{
                    "who": str(who.pk),
                    "user_name": who.get_full_name(),
                }
            )

        ctx.update(**{"user_id": user_id, "date": date, "kpi": kpi})
        return ctx


class NotificationClient(NotificationMixin, BaseClient):
    BASE_URL = "/"
    SERVICE_NAME = "notification-service"

    def read_notification(
        self, user_id=None, organization_id=None, date_added=None, next_page=None, prev_page=None, limit=None, **kwargs
    ):
        payload = {
            "next_page": next_page,
            "prev_page": prev_page,
            "limit": limit,
            "user_id": user_id,
            "organization_id": organization_id,
            "date_added": date_added,
        }

        response = self.request(
            url="/notifications",
            method="GET",
            payload=payload,
            url_prefix=settings.NOTIFICATION_MICROSERVICE_URL,
            **kwargs,
        )
        if response:
            return response.get("msg")
        return None

    def create_notification(self, notification_type, context, extra_data=None, **kwargs):
        if extra_data is None:
            extra_data = {}

        response = self.request(
            url="/notifications",
            method="POST",
            json={
                "date_added": timezone.now().isoformat(),
                "notification_type": notification_type,
                "context": context,
                "extra_data": extra_data,
            },
            url_prefix=settings.NOTIFICATION_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")
        return None

    def update_notification(self, mongo_id, user_id, **kwargs):
        response = self.request(
            url=f"notifications/{str(mongo_id)}",
            method="PATCH",
            json={"user_id": user_id},
            url_prefix=settings.NOTIFICATION_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")
        return None

    def delete_notification(self, mongo_id):
        response = self.request(
            url=f"notifications/{str(mongo_id)}",
            method="DELETE",
            url_prefix=settings.NOTIFICATION_MICROSERVICE_URL,
        )

        if response:
            return response.get("msg")
        return None

    def mark_notification_as_unavailable(self, business_id, **kwargs):
        response = self.request(
            url="/notifications/mark_unavailable",
            method="POST",
            json={"business_id": business_id},
            url_prefix=settings.NOTIFICATION_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")
        return None


class NotificationPubSubClient(NotificationMixin, BasePubSubClient):
    """Notification client for sending notifications in Production"""

    def create_notification(self, notification_type, context, extra_data=None):
        try:
            if extra_data is None:
                extra_data = {}

            self.request(
                NOTIFICATION_TOPICS[settings.ENVIRONMENT],
                {
                    "notification_type": notification_type,
                    "context": context,
                    "date_added": timezone.now().isoformat(),
                    "extra_data": extra_data,
                },
                attributes={"endpoint": "CREATE"},
            )

            return True
        except ClientError as e:
            logger.error(f"(create_notification) - {e.message}")

        return False

    def update_notification(self, mongo_id, user_id):
        try:
            self.request(
                NOTIFICATION_TOPICS[settings.ENVIRONMENT],
                {"mongo_id": mongo_id, "user_id": user_id},
                attributes={"endpoint": "UPDATE"},
            )

            return True
        except ClientError as e:
            logger.error(f"(update_notification) - {e.message}")

        return False

    def delete_notification(self, mongo_id):
        try:
            self.request(
                NOTIFICATION_TOPICS[settings.ENVIRONMENT],
                {
                    "mongo_id": mongo_id,
                },
                attributes={"endpoint": "DELETE"},
            )

            return True
        except ClientError as e:
            logger.error(f"(delete_notification) - {e.message}")

        return False

    def mark_notification_as_unavailable(self, business_id, **kwargs):
        """Mark every comment as unavailable due to business deletion"""
        try:
            self.request(
                NOTIFICATION_TOPICS[settings.ENVIRONMENT],
                {
                    "business_id": business_id,
                },
                attributes={"endpoint": "UNAVAILABLE"},
            )

            return True
        except ClientError as e:
            logger.error(f"(delete_message) - {e.message}")

        return False
