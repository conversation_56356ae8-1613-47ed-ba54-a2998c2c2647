import logging
from datetime import timedelta

from django.conf import settings
from django.utils import timezone

from core.external_services.microservices.base import BaseClient

logger = logging.getLogger(__name__)


class RecommendationsClient(BaseClient):
    BASE_URL = "/"
    SERVICE_NAME = "recommendation-service"

    def read_recomendations(self, api_campaign_id, business_id, date_start=None, date_stop=None):
        """Read recommendations from remote service"""

        today = timezone.now().today()
        if not date_start:
            date_start = (today - timedelta(days=today.weekday())).strftime("%Y-%m-%d")
        if not date_stop:
            date_stop = today.strftime("%Y-%m-%d")

        response = self.request(
            url="recommendations",
            method="GET",
            payload={
                "campaign_id": api_campaign_id,
                "business_id": business_id,
                "date_start": date_start,
                "date_stop": date_stop,
            },
            add_token=True,
            url_prefix=settings.RECOMMENDATIONS_MICROSERVICE_URL,
        )
        if response:
            return response.get("msg")
        return None

    def read_traffic_light(self, api_campaign_id, business_id):
        response = self.request(
            url="traffic_light",
            method="GET",
            payload={
                "campaign_id": api_campaign_id,
                "business_id": business_id,
            },
            add_token=True,
            url_prefix=settings.RECOMMENDATIONS_MICROSERVICE_URL,
        )
        if response:
            return response.get("msg")
        return None
