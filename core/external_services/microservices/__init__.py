from django.conf import settings

from core.external_services.microservices.notification import (
    NotificationClient, NotificationPubSubClient)
from core.external_services.microservices.recomendation import \
    RecommendationsClient
from core.external_services.microservices.social import (SocialClient,
                                                         SocialPubSubClient)
from core.external_services.slack.client import SlackNotificationClient

if settings.ENVIRONMENT == "PROD":
    social_pubsub_client = SocialPubSubClient()
    social_client = SocialClient()
    notification_pubsub_client = NotificationPubSubClient()
    notification_client = NotificationClient()
else:
    social_pubsub_client = social_client = SocialClient()
    notification_pubsub_client = notification_client = NotificationClient()


recommendation_client = RecommendationsClient()
slack_notification_client = SlackNotificationClient()
