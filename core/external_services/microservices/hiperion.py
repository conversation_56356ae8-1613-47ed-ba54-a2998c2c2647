from django.conf import settings

from core.external_services.microservices.base import BaseClient


class HiperionAPIServiceClient(BaseClient):
    BASE_URL = "metrics/"
    SERVICE_NAME = "hiperion-api-service"

    def get_metrics(
        self, object_id, level, date_start, date_end, kpis, api_id, social_network, aggregation=None, **kwargs
    ):
        """Fetch metrics for campaign/adset/ad from hiperion"""
        response = self.request(
            "metrics",
            payload={
                "date_start": date_start,
                "date_end": date_end,
                "fields": kpis,
                "object_id": object_id,
                "level": level,
                "api_id": api_id,
                "social_network": social_network,
                "aggregation": aggregation,
            },
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def get_overview_chart(
        self, campaign_id, api_campaign_id, kpis, date_start, date_end, social_network, aggregation=None, **kwargs
    ):
        """Fetch overview chart for campaign """

        response = self.request(
            "overview/",
            path_parameter=campaign_id,
            payload={
                "date_start": date_start,
                "date_end": date_end,
                "fields": kpis,
                "api_id": api_campaign_id,
                "social_network": social_network,
                "aggregation": aggregation,
            },
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def get_overview_table(
        self, campaign_tree, api_ids, remote_ids, date_start, date_end, kpis, social_network, **kwargs
    ):
        """Fetch overview table for campaign tree"""
        response = self.request(
            "overview-table/",
            method="POST",
            payload={"date_start": date_start, "date_end": date_end, "fields": kpis, "social_network": social_network},
            json={"api_ids": api_ids, "remote_ids": remote_ids, "campaign": campaign_tree},
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def get_community_overview(self, campaign_id, campaign_api_id, **kwargs):
        """Fetch overview metrics for community view"""
        response = self.request(
            "community/",
            path_parameter=campaign_id,
            method="GET",
            payload={"api_id": campaign_api_id},
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def get_community_basic_kpis(self, campaign_id, kpis, **kwargs):
        """Fetch basic aggregated kpis from mongo for detailed campaign view in community"""
        response = self.request(
            "community-kpis/",
            path_parameter=campaign_id,
            method="GET",
            payload={"fields": kpis},
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def get_community_total_comments(self, campaign_id, **kwargs):
        """Fetch basic aggregated kpis from mongo for detailed campaign view in community"""
        response = self.request(
            "community-comments/",
            path_parameter=campaign_id,
            method="GET",
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def get_business_insights(
        self,
        ad_account_id,
        objective,
        date_start,
        date_end,
        fields,
        order_by,
        order,
        limit,
        page,
        insight_type,
        **kwargs,
    ):
        """Fetch insights from mongo for given adaccount"""
        response = self.request(
            f"insights_{insight_type}",
            payload={
                "ad_account_id": ad_account_id,
                "objective": objective,
                "date_start": date_start,
                "date_end": date_end,
                "fields": fields,
                "sort_field": order_by,
                "sort_how": order,
                "limit": limit,
                "page": page,
            },
            url_prefix=settings.HIPERION_API_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None


class HiperionWorkerServiceClient(BaseClient):
    BASE_URL = "facebook/"
    SERVICE_NAME = "hiperion-worker-service"

    def post_metrics(
        self,
        params: dict,
        base_url=None,
        **kwargs,
    ):
        """Fetch metrics from facebook or google through hiperion"""

        response = self.request(
            "metrics",
            payload=params,
            base_url=base_url,
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_breakdowns(
        self,
        access_token,
        user_id,
        objective,
        update,
        campaign_id,
        date_start,
        date_end,
        level,
        breakdowns,
        conversion_event_type,
        **kwargs,
    ):
        """Fetch breakdowns from facebook through hiperion"""
        response = self.request(
            "breakdowns",
            payload={
                "access_token": access_token,
                "user_id": user_id,
                "objective": objective,
                "update": update,
                "campaign_id": campaign_id,
                "date_start": date_start,
                "date_end": date_end,
                "level": level,
                "breakdowns": breakdowns,
                "conversion_event_type": conversion_event_type,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_activities(self, access_token, user_id, campaign_id, date_start, **kwargs):
        """Fetch campaign activities from facebook through hiperion"""
        response = self.request(
            "activities",
            payload={
                "access_token": access_token,
                "user_id": user_id,
                "campaign_id": campaign_id,
                "date_start": date_start,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_ad(self, access_token, page_access_token, user_id, ad_id, **kwargs):
        """Fetch ad from facebook through hiperion"""
        response = self.request(
            "ad",
            payload={
                "access_token": access_token,
                "page_access_token": page_access_token,
                "user_id": user_id,
                "ad_id": ad_id,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_adset(self, access_token, user_id, adset_id, **kwargs):
        """Fetch ad from facebook through hiperion"""
        response = self.request(
            "adset",
            payload={
                "access_token": access_token,
                "user_id": user_id,
                "adset_id": adset_id,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_aggregated(
        self,
        params: dict,
        base_url=None,
        **kwargs,
    ):
        """Fetch aggregated data from facebook through hiperion"""
        response = self.request(
            "aggregated",
            payload=params,
            base_url=base_url,
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_campaign(self, access_token, user_id, campaign_id, **kwargs):
        """Fetch campaign from facebook through hiperion"""
        response = self.request(
            "campaign",
            payload={
                "access_token": access_token,
                "user_id": user_id,
                "campaign_id": campaign_id,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_categories(self, access_token, page_access_token, user_id, campaign_id, **kwargs):
        """Fetch categories from facebook through hiperion"""
        response = self.request(
            "categories",
            payload={
                "access_token": access_token,
                "page_access_token": page_access_token,
                "user_id": user_id,
                "campaign_id": campaign_id,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None

    def post_from_user(self, access_token, user_id, **kwargs):
        """Fetch every campaign from facebook through hiperion"""
        response = self.request(
            "from-user",
            payload={
                "access_token": access_token,
                "user_id": user_id,
            },
            url_prefix=settings.HIPERION_WORKERS_MICROSERVICE_URL,
            **kwargs,
        )

        if response:
            return response.get("msg")

        return None
