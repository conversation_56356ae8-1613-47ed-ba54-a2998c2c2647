from django.conf import settings

from core.external_services.microservices.base import BaseClient


class AIServiceClient(BaseClient):
    """Base client to make requests to AI microservice"""

    BASE_URL = "/"
    SERVICE_NAME = "ai-microservice"

    def post_send_campaign_to_ai_service(
        self, business_id, campaign_id, organization_id, api_campaign_id, objective, conversion_event_type=None
    ):
        response = self.request(
            "metadata/",
            method="POST",
            url_prefix=settings.AI_MICROSERVICE_URL,
            payload={
                "business_id": str(business_id),
                "campaign_id": str(campaign_id),
                "organization_id": str(organization_id),
                "api_campaign_id": str(api_campaign_id),
                "objective": objective,
                "conversion_event_type": conversion_event_type,
            },
        )
        if response:
            return response.get("msg")
        return None
