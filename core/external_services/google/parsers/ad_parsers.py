class BaseSpecificAdParser:
    type = None
    mapping_fields = {}

    def __init__(self, ad):
        self._ad = ad

    @property
    def _format_type(self):
        return "".join([name.capitalize() for name in self.type.split("_")])

    @property
    def parse(self):
        """Parse specific type of ad and get the raw data from """
        raw_ad = self._ad.get(self.type, {})
        return {
            "type": self._format_type,
            **{self.mapping_fields[field]: self.safe_get(raw_ad, field) for field in self.mapping_fields},
        }

    def safe_get(self, ad, field):
        """Helper method to save content ad as is because this type is not mapped yet in local db"""
        return ad.get(field) if field != "*" else self._ad


class UnrecognizedAdParser(BaseSpecificAdParser):
    """Dummy class to note a 'content not found' and avoid to link to created ad"""

    type = "unknown_ad"
    mapping_fields = {"*": "raw_content"}


class GoogleTextAdParser(BaseSpecificAdParser):
    """Parse ad content for GoogleTextAd model"""

    type = "text_ad"
    mapping_fields = {"headline": "headline", "description1": "description_one", "description2": "description_two"}


class GoogleExpandedTextAdParser(BaseSpecificAdParser):
    """Parse ad content for GoogleExpandedTextAd model"""

    type = "expanded_text_ad"
    mapping_fields = {
        "headline_part1": "headline_one",
        "headline_part2": "headline_two",
        "headline_part3": "headline_three",
        "description": "description",
        "description2": "description_two",
        "path1": "path_one",
        "path2": "path_two",
    }


class GoogleExpandedDynamicSearchAdParser(BaseSpecificAdParser):
    """Parse ad content for GoogleExpandedDynamicSearchAd model"""

    type = "expanded_dynamic_search_ad"
    mapping_fields = {"description": "description", "description2": "description_two"}


class GoogleResponsiveSearchAdParser(BaseSpecificAdParser):
    """Parse ad content for GoogleResponsiveSearchAd model"""

    type = "responsive_search_ad"
    mapping_fields = {
        "headlines": "headlines",
        "descriptions": "descriptions",
        "path1": "path_one",
        "path2": "path_two",
    }
