from core.external_services.google.parsers.ad_parsers import (
    GoogleExpandedDynamicSearchAdParser, GoogleExpandedTextAdParser,
    GoogleResponsiveSearchAdParser, GoogleTextAdParser, UnrecognizedAdParser)


class ParserStrategy:
    PARSERS = {
        "text_ad": GoogleTextAdParser,
        "expanded_text_ad": GoogleExpandedTextAdParser,
        "expanded_dynamic_search_ad": GoogleExpandedDynamicSearchAdParser,
        "responsive_search_ad": GoogleResponsiveSearchAdParser,
    }

    def __init__(self, ad):
        self.parser = self._choose_parser_from_ad_content(ad)(ad)

    def _choose_parser_from_ad_content(self, ad):
        """Choose the right ad parser to parse the ad accordingly"""
        for ad_field in self.PARSERS.keys():
            if ad_field in ad:
                return self.PARSERS.get(ad_field, UnrecognizedAdParser)
        return UnrecognizedAdParser
