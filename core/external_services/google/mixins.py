import proto
from google.ads.googleads.errors import GoogleAdsException
from google.ads.googleads.v8.enums.types.response_content_type import \
    ResponseContentTypeEnum
from google.ads.googleads.v8.errors.types import (authentication_error,
                                                  authorization_error)
from google.api_core import protobuf_helpers
from google.auth.exceptions import RefreshError

from core.utils import google_protobuf_copy_from

EMPTY_ITERABLE_RESPONSE = list()
EMPTY_OBJECT_RESPONSE = dict()


class SearchStreamMixin:
    def run_search_stream_search(self, customer_id, refresh_token, query, **kwargs):
        """Generic method to make a search stream call against google ads api"""
        service = self(klass="GoogleAdsService", refresh_token=refresh_token, **kwargs)
        resources = service.search_stream(customer_id=customer_id, query=str(query))
        for batch in resources:
            return iter(batch.results)
        return EMPTY_ITERABLE_RESPONSE


class TokenMixin:
    def debug_token(self, input_token, **kwargs):
        """Check token metadata validness and permission"""
        try:
            # Force a real call and read the exception
            _ = next(self.get_accounts_from_user(input_token, reraise=True))

            return True
        except (GoogleAdsException, RefreshError) as error:
            authentication_error_enum = authentication_error.AuthenticationErrorEnum.AuthenticationError
            authorization_error_enum = authorization_error.AuthorizationErrorEnum.AuthorizationError

            error_code = (
                self._parse_google_ads_error(error).code
                if not hasattr(error, "custom_format")
                else authentication_error_enum.OAUTH_TOKEN_REVOKED
            )

            return error_code not in [
                authentication_error_enum.OAUTH_TOKEN_REVOKED,
                authentication_error_enum.OAUTH_TOKEN_INVALID,
                authentication_error_enum.OAUTH_TOKEN_EXPIRED,
                authentication_error_enum.OAUTH_TOKEN_DISABLED,
                authentication_error_enum.GOOGLE_ACCOUNT_DELETED,
                authentication_error_enum.AUTHENTICATION_ERROR,
                authorization_error_enum.USER_PERMISSION_DENIED,
            ]


class UserMixin:
    def _get_customer_from_resource(self, service, resource):
        """Skip exception on first call and fetch every data from google"""
        return service.get_customer(resource_name=resource)

    def get_accounts_from_user(self, refresh_token, **kwargs):
        """Get accessible google accounts from refresh_token"""
        service = self(klass="CustomerService", refresh_token=refresh_token, **kwargs)
        resources = service.list_accessible_customers().resource_names
        for resource in resources:
            customer = self._get_customer_from_resource(service, resource)
            if customer is not None and not customer.manager:
                yield customer

        yield

    def get_account_metadata_from_resource(self, resource_id, refresh_token, **kwargs):
        """Get account metadata from resource name"""
        service = self(klass="CustomerService", refresh_token=refresh_token, **kwargs)
        return self._get_customer_from_resource(service, f"customers/{resource_id}") or EMPTY_OBJECT_RESPONSE

    def get_campaigns_from_account(self, customer_id, refresh_token, query, **kwargs):
        """Get campaigns from concrete account"""
        return self.run_search_stream_search(
            refresh_token=refresh_token, customer_id=customer_id, query=query, **kwargs
        )


class AdMixin:
    def mutate_ad(self, refresh_token, customer_id, adset_id, ad_id, status, **kwargs):
        """Generic method to pause or active a google ad. This method is intended to be used wrapped around
        another concrete method"""

        ad_service = self(klass="AdGroupAdService", refresh_token=refresh_token, **kwargs)
        ad_operation = self(klass="AdGroupAdOperation", refresh_token=refresh_token, resource_type="type", **kwargs)

        ad_status = getattr(
            self(klass="AdGroupStatusEnum", refresh_token=refresh_token, resource_type="type", **kwargs).AdGroupStatus,
            status,
        )
        request = self(klass="MutateAdGroupAdsRequest", refresh_token=refresh_token, resource_type="type", **kwargs)

        ad = ad_operation.update
        ad.resource_name = ad_service.ad_group_ad_path(customer_id, adset_id, ad_id)
        ad.status = ad_status

        google_protobuf_copy_from(
            ad_operation.update_mask,
            protobuf_helpers.field_mask(None, ad._pb),
        )

        # Set request to add extra parameter and retrieve mutated fields
        request.customer_id = customer_id
        request.operations = [ad_operation]
        request.response_content_type = ResponseContentTypeEnum.ResponseContentType.MUTABLE_RESOURCE

        response = ad_service.mutate_ad_group_ads(request=request)

        return proto.Message.to_dict(response.results[0].ad_group_ad, use_integers_for_enums=False).get("status")

    def pause_ad(self, refresh_token, customer_id, adset_id, ad_id, **kwargs):
        """Pause a campaign in Google"""
        return self.mutate_ad(
            refresh_token=refresh_token,
            customer_id=customer_id,
            adset_id=adset_id,
            ad_id=ad_id,
            status="PAUSED",
            **kwargs,
        )

    def activate_ad(self, refresh_token, customer_id, adset_id, ad_id, **kwargs):
        """Activate a campaign in Google"""
        return self.mutate_ad(
            refresh_token=refresh_token,
            customer_id=customer_id,
            adset_id=adset_id,
            ad_id=ad_id,
            status="ENABLED",
            **kwargs,
        )


class AdsetMixin:
    def mutate_adset(self, refresh_token, customer_id, adset_id, status, **kwargs):
        """Generic method to pause or active a google adgroup. This method is intended to be used wrapped around
        another concrete method"""

        adset_service = self(klass="AdGroupService", refresh_token=refresh_token, **kwargs)
        adset_operation = self(klass="AdGroupOperation", refresh_token=refresh_token, resource_type="type", **kwargs)

        adset_status = getattr(
            self(klass="AdGroupStatusEnum", refresh_token=refresh_token, resource_type="type", **kwargs).AdGroupStatus,
            status,
        )
        request = self(klass="MutateAdGroupsRequest", refresh_token=refresh_token, resource_type="type", **kwargs)

        adset = adset_operation.update
        adset.resource_name = adset_service.ad_group_path(customer_id, adset_id)
        adset.status = adset_status

        google_protobuf_copy_from(
            adset_operation.update_mask,
            protobuf_helpers.field_mask(None, adset._pb),
        )

        # Set request to add extra parameter and retrieve mutated fields
        request.customer_id = customer_id
        request.operations = [adset_operation]
        request.response_content_type = ResponseContentTypeEnum.ResponseContentType.MUTABLE_RESOURCE

        response = adset_service.mutate_ad_groups(request=request)

        return proto.Message.to_dict(response.results[0].ad_group, use_integers_for_enums=False).get("status")

    def pause_adset(self, refresh_token, customer_id, adset_id, **kwargs):
        """Pause a campaign in Google"""
        return self.mutate_adset(
            refresh_token=refresh_token, customer_id=customer_id, adset_id=adset_id, status="PAUSED", **kwargs
        )

    def activate_adset(self, refresh_token, customer_id, adset_id, **kwargs):
        """Activate a campaign in Google"""
        return self.mutate_adset(
            refresh_token=refresh_token, customer_id=customer_id, adset_id=adset_id, status="ENABLED", **kwargs
        )


class CampaignMixin:
    def get_adgroups_in_campaign(self, customer_id, refresh_token, query, **kwargs):
        """Get adagroups from given campaign encoded in query"""
        return self.run_search_stream_search(
            refresh_token=refresh_token, customer_id=customer_id, query=query, **kwargs
        )

    def get_ads_in_campaign(self, customer_id, refresh_token, query, **kwargs):
        """Get ads from given campaign encoded in query"""
        return self.run_search_stream_search(
            refresh_token=refresh_token, customer_id=customer_id, query=query, **kwargs
        )

    def get_campaign_metadata(self, customer_id, refresh_token, campaign_id, **kwargs):
        """Get campaign metadata using stream search through encoded query"""
        from core.external_services.google.client import GoogleQueryWrapper

        query = GoogleQueryWrapper(
            select_clause=[
                "campaign.status",
                "campaign.end_date",
            ],
            from_clause="campaign",
            where_clause=[f"campaign.id = {campaign_id}"],
            limit_clause=1,
        )

        campaign_metadata = self.run_search_stream_search(
            refresh_token=refresh_token, customer_id=customer_id, query=query, **kwargs
        )
        try:
            response = list(campaign_metadata)[0]
            return proto.Message.to_dict(response.campaign, use_integers_for_enums=False)
        except IndexError:
            return {}

    def mutate_campaign(self, refresh_token, customer_id, campaign_id, status, **kwargs):
        """Generic method to pause or active a google campaign. This method is intended to be used wrapped around
        another concrete method"""

        campaign_service = self(klass="CampaignService", refresh_token=refresh_token, **kwargs)
        campaign_operation = self(
            klass="CampaignOperation", refresh_token=refresh_token, resource_type="type", **kwargs
        )

        campaign_status = getattr(
            self(
                klass="CampaignStatusEnum", refresh_token=refresh_token, resource_type="type", **kwargs
            ).CampaignStatus,
            status,
        )
        request = self(klass="MutateCampaignsRequest", refresh_token=refresh_token, resource_type="type", **kwargs)

        campaign = campaign_operation.update
        campaign.resource_name = campaign_service.campaign_path(customer_id, campaign_id)
        campaign.status = campaign_status

        google_protobuf_copy_from(
            campaign_operation.update_mask,
            protobuf_helpers.field_mask(None, campaign._pb),
        )

        # Set request to add extra parameter and retrieve mutated fields
        request.customer_id = customer_id
        request.operations = [campaign_operation]
        request.response_content_type = ResponseContentTypeEnum.ResponseContentType.MUTABLE_RESOURCE

        response = campaign_service.mutate_campaigns(request=request)

        return proto.Message.to_dict(response.results[0].campaign, use_integers_for_enums=False).get("status")

    def pause_campaign(self, refresh_token, customer_id, campaign_id, **kwargs):
        """Pause a campaign in Google"""
        return self.mutate_campaign(
            refresh_token=refresh_token, customer_id=customer_id, campaign_id=campaign_id, status="PAUSED", **kwargs
        )

    def activate_campaign(self, refresh_token, customer_id, campaign_id, **kwargs):
        """Activate a campaign in Google"""
        return self.mutate_campaign(
            refresh_token=refresh_token, customer_id=customer_id, campaign_id=campaign_id, status="ENABLED", **kwargs
        )
