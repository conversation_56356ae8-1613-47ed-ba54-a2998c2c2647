from core.base_parser import <PERSON><PERSON>ars<PERSON>
from core.external_services.google.helpers import (STATUSES,
                                                   safe_attribute_getter)
from core.external_services.google.parsers import ParserStrategy


class GoogleAdgroupParser(BaseParser):
    def __init__(self, google_metadata, **kwargs):
        self._metadata = google_metadata

    def _parse_targeting_restrictions(self, targeting_setting):
        """Parse targeting restrictions converting protobuf messages into serializable fields"""
        targeting_setting = safe_attribute_getter(targeting_setting, "targeting_setting")
        return safe_attribute_getter(targeting_setting, "target_restrictions")

    def parse(self):
        """Parse adgroup to internal representation"""

        assert self._metadata is not None

        ad_group = safe_attribute_getter(self._metadata, "ad_group")
        remote_id = str(safe_attribute_getter(ad_group, "id"))

        parsed_message = {
            "name": safe_attribute_getter(ad_group, "name"),
            "remote_id": remote_id,
            "status": STATUSES.get(safe_attribute_getter(ad_group, "status"), "none"),
            "type": safe_attribute_getter(ad_group, "type_"),
            "ad_rotation": safe_attribute_getter(ad_group, "ad_rotation_mode"),
            "url_custom_parameters": safe_attribute_getter(ad_group, "url_custom_parameters"),
            "explorer_auto_optimizer_setting": safe_attribute_getter(
                ad_group, "explorer_auto_optimizer_setting", default_value=False
            ),
            "targeting_dimension": safe_attribute_getter(ad_group, "display_custom_bid_dimension"),
            "excluded_parent_asset_field_types": safe_attribute_getter(ad_group, "excluded_parent_asset_field_types"),
            "targeting_restrictions": self._parse_targeting_restrictions(ad_group),
            "tracking_url_template": safe_attribute_getter(ad_group, "tracking_url_template"),
            "cpc_bid_micros": safe_attribute_getter(ad_group, "cpc_bid_micros"),
            "cpm_bid_micros": safe_attribute_getter(ad_group, "cpm_bid_micros"),
            "target_cpa_micros": safe_attribute_getter(ad_group, "target_cpa_micros"),
            "target_cpm_micros": safe_attribute_getter(ad_group, "target_cpm_micros"),
            "target_roas": safe_attribute_getter(ad_group, "target_roas"),
            "percent_cpc_bid_micros": safe_attribute_getter(ad_group, "percent_cpc_bid_micros"),
            "final_url_suffix": safe_attribute_getter(ad_group, "final_url_suffix"),
            "resource_name": safe_attribute_getter(ad_group, "resource_name"),
            "what_is": "adset",
        }

        return {remote_id: parsed_message}


class GoogleAdParser:
    def __init__(self, google_metadata, **kwargs):
        self._metadata = google_metadata

    def parse(self):
        """Parse ad to internal representation"""

        assert self._metadata is not None

        ad_group = safe_attribute_getter(self._metadata, "ad_group")
        ad_group_ad = safe_attribute_getter(self._metadata, "ad_group_ad")
        ad = safe_attribute_getter(ad_group_ad, "ad", decode_proto_message=True)
        remote_id = str(ad.get("id"))

        # Get parsed ad
        parser_strategy = ParserStrategy(ad).parser
        ad_content = parser_strategy.parse

        parsed_message = {
            "name": ad.get("name"),
            "remote_id": remote_id,
            "status": STATUSES.get(safe_attribute_getter(ad_group_ad, "status"), "none"),
            "final_urls": ad.get("final_urls"),
            "final_app_urls": ad.get("final_app_urls"),
            "final_mobile_urls": ad.get("final_mobile_urls"),
            "url_custom_parameters": ad.get("url_custom_parameters"),
            "device_preference": ad.get("device_preference"),
            "url_collections": ad.get("url_collections"),
            "tracking_url_template": ad.get("tracking_url_template"),
            "final_url_suffix": ad.get("final_url_suffix"),
            "display_url": ad.get("display_url"),
            "resource_name": ad.get("resource_name"),
            "adset_id": str(safe_attribute_getter(ad_group, "id")),
            "ad_content": ad_content,
            "what_is": "ad",
        }

        return {remote_id: parsed_message}


class FactoryParser:
    PARSERS = {"adset": GoogleAdgroupParser, "ad": GoogleAdParser}

    def __init__(self, refresh_token):
        self._refresh_token = refresh_token

    def parser(self, parser_class, *args, **kwargs):
        return self.PARSERS[parser_class](*args, **kwargs)
