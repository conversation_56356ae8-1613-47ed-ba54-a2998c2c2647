from django.utils.translation import gettext_lazy as _

from core.external_services.microservices.base import BaseClient


class SlackNotificationFormatters:
    """Formatters for different slack notifications"""

    @classmethod
    def _format(cls, header_title, url, emoji, main_section, action_text, sub_header=""):
        return {
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f":{emoji}: {header_title} {sub_header}",
                    },
                },
                {"type": "section", **main_section},
                {
                    "type": "actions",
                    "elements": [{"type": "button", "text": {"type": "plain_text", "text": action_text}, "url": url}],
                },
            ]
        }

    @classmethod
    def campaign_imported(cls, business_name, campaign_name, campaign_objective, social_network, url):
        """Slack message format for campaign imports"""
        return cls._format(
            header_title=f"The campaign *<{url}| {campaign_name}>* has been imported",
            url=url,
            emoji="rocket",
            main_section={
                "fields": [
                    {"type": "mrkdwn", "text": f"*Business:*\n{business_name}"},
                    {"type": "mrkdwn", "text": f"*Platform:*\n{social_network.capitalize()} Ads"},
                    {"type": "mrkdwn", "text": f"*Name:*\n{campaign_name}"},
                    {"type": "mrkdwn", "text": f"*Objective:*\n{campaign_objective}"},
                ],
                "accessory": {
                    "type": "image",
                    "image_url": f"https://storage.googleapis.com/adtuo-public-assets/slack/slack_notification_{social_network}.png",
                    "alt_text": f"{social_network.capitalize()} Ads",
                },
            },
            action_text="View campaign",
        )

    @classmethod
    def campaign_created(cls, business_name, campaign_name, campaign_objective, social_network, url, has_errors):
        """Slack message format for campaign imports"""
        success_message = _("The campaign *<{url}| {campaign_name}>* has been successfully created").format(
            url=url, campaign_name=campaign_name
        )
        error_message = _(
            "The campaign *<{url}| {campaign_name}>* could not be created.\nPlease review the errors and republish it."
        ).format(url=url, campaign_name=campaign_name)
        header_title = error_message if has_errors else success_message
        action_text = "Fix campaign errors" if has_errors else "View campaign"

        return cls._format(
            header_title=header_title,
            url=url,
            emoji="x" if has_errors else "white_check_mark",
            main_section={
                "fields": [
                    {"type": "mrkdwn", "text": f"*Business:*\n{business_name}"},
                    {"type": "mrkdwn", "text": f"*Platform:*\n{social_network.capitalize()} Ads"},
                    {"type": "mrkdwn", "text": f"*Name:*\n{campaign_name}"},
                    {"type": "mrkdwn", "text": f"*Objective:*\n{campaign_objective}"},
                ],
                "accessory": {
                    "type": "image",
                    "image_url": f"https://storage.googleapis.com/adtuo-public-assets/slack/slack_notification_{social_network}.png",
                    "alt_text": f"{social_network.capitalize()} Ads",
                },
            },
            action_text=action_text,
        )

    @classmethod
    def new_comment(cls, campaign_name, user_name, message, url):
        """Slack message format for campaign imports"""
        return cls._format(
            header_title=f"*{user_name}* has left a comment on campaign *<{url}|{campaign_name}>*",
            url=url,
            emoji="speech_balloon",
            main_section={"text": {"type": "mrkdwn", "text": f">>> {message}"}},
            action_text="View comment",
        )


class SlackNotificationClient(BaseClient):
    """This client is NOT json compliant with server responses"""

    BASE_URL = ""
    SERVICE_NAME = "slack-service"

    def create_notification(self, webhook_url, notification_metadata, **kwargs):
        response = self.request(
            url=webhook_url,
            method="POST",
            json=notification_metadata,
            url_prefix="",
            headers={"Content-Type": "application/json"},
            **kwargs,
        )

        if response:
            return response
        return None
