# from django.db import models
# from django.utils.translation import gettext_lazy as _
#
#
# class AutoIncrementSequence(models.Model):
#     """Helper model based on django-sequences package to guarantee unique auto incremental integer ids without race conditions."""
#
#     name = models.CharField(
#         verbose_name=_("name"),
#         max_length=100,
#         primary_key=True,
#     )
#
#     counter = models.PositiveBigIntegerField(
#         verbose_name=_("last value"),
#     )
