import base64
import binascii
import imghdr
import uuid

from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils.translation import gettext_lazy as _
from rest_framework.fields import <PERSON>Field
from rest_framework.serializers import <PERSON>r<PERSON>ield


class Base64<PERSON><PERSON>Field(CharField):
    """
    Heavily inspired by https://github.com/Hipo/drf-extra-fields/blob/master/drf_extra_fields/fields.py#L32-L99
    but based on <PERSON>r<PERSON>ield
    """

    EMPTY_VALUES = (None, "", [], (), {})
    ALLOWED_TYPES = (
        "jpeg",
        "jpg",
        "png",
        "gif",
        "svg",
    )
    INVALID_FILE_MESSAGE = _("Please upload a valid image.")
    INVALID_TYPE_MESSAGE = _("The type of the image couldn't be determined.")

    def to_internal_value(self, base64_data):
        # Check if this is a base64 string
        if base64_data in self.EMPTY_VALUES:
            return None

        if isinstance(base64_data, str):
            # Strip base64 header.
            data = None
            if ";base64," in base64_data:
                header, data = base64_data.split(";base64,")

            # Try to decode the file. Return validation error if it fails.
            try:
                decoded_file = base64.b64decode(data)
            except (TypeError, binascii.Error, ValueError):
                raise ValidationError(self.INVALID_FILE_MESSAGE)

            # Generate file name:
            file_name = str(uuid.uuid4())[:12]  # 12 characters are more than enough.
            # Get the file name extension:
            file_extension = self.get_file_extension(header, file_name, decoded_file)
            if file_extension not in self.ALLOWED_TYPES:
                raise ValidationError(self.INVALID_TYPE_MESSAGE)

            # Return base64_data
            return super().to_internal_value(base64_data)
        raise ValidationError(_("This is not an base64 string"))

    def get_file_extension(self, header, filename, decoded_file):
        extension = imghdr.what(filename, decoded_file)
        if extension is None:
            if decoded_file.startswith(b"<svg"):
                extension = "svg"
            elif header == "data:image/jpeg":
                extension = "jpg"
        extension = "jpg" if extension == "jpeg" else extension
        return extension


class Base64VideoField(FileField):
    EMPTY_VALUES = (None, "", [], (), {})

    ALLOWED_TYPES = ("mp4", "mov", "mpg")
    INVALID_FILE_MESSAGE = _("Please upload a valid video.")
    INVALID_TYPE_MESSAGE = _("The type of the video couldn't be determined.")

    def __init__(self, **kwargs):
        self.encoded_variable = kwargs.pop("encoded", None)
        super().__init__(**kwargs)

    def to_internal_value(self, base64_data):
        # Check if this is a base64 string
        if base64_data in self.EMPTY_VALUES:
            return None

        if isinstance(base64_data, str):
            file_mime_type = None

            # Strip base64 header, get mime_type from base64 header.
            if ";base64," in base64_data:
                header, base64_data = base64_data.split(";base64,")
                file_mime_type = header.replace("data:", "")

            # Try to decode the file. Return validation error if it fails.
            try:
                decoded_file = base64.b64decode(base64_data)
            except (TypeError, binascii.Error, ValueError):
                raise ValidationError(self.INVALID_FILE_MESSAGE)

            # Generate file name:
            file_name = uuid.uuid4().hex

            # Get the file name extension:
            file_extension = self.get_file_extension(header)

            complete_file_name = file_name + "." + file_extension
            data = SimpleUploadedFile(name=complete_file_name, content=decoded_file, content_type=file_mime_type)

            # Return base64_data
            internal_value = super().to_internal_value(data)

            if self.encoded_variable:
                internal_value = (self.encoded_variable, (complete_file_name, data))

            return internal_value
        raise ValidationError(_("This is not an base64 string"))

    def get_file_extension(self, header):
        header = header.split("/")[-1]

        if header == "quicktime":
            return "mov"

        return header
