import string

from django.db.models import NOT_PROVIDED, CharField


class QuerySetMixin:
    def _is_full_of_blanks(self, field, value):
        """Check if field is an instance of charfield and return if is full of blanks"""
        if isinstance(field, CharField):
            return not str(value).translate(str.maketrans("", "", string.whitespace))

        return False

    def is_field_filled(self, obj, field_name):
        """Helper method to check if field is filled"""
        model_field_value = getattr(obj, field_name)
        model_field_instance = obj._meta.get_field(field_name)

        if model_field_instance.is_relation:
            return False

        if model_field_instance.default != NOT_PROVIDED and model_field_value != model_field_instance.default:
            return True

        if (
            model_field_value is not None
            and not self._is_full_of_blanks(model_field_instance, model_field_value)
            and model_field_value
        ):
            return True

        return False
