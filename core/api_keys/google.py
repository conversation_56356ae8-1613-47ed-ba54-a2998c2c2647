import logging
from dataclasses import dataclass
from typing import List

from django.conf import settings
from google.cloud import api_keys_v2
from google.cloud.api_keys_v2 import Key
from google.oauth2 import service_account

logger = logging.getLogger(__name__)


@dataclass
class ApiTarget:
    service: str
    methods: List[str] = None


class BaseWrapper:
    def __init__(self, *args, **kwargs):
        self._client = None

    @property
    def client(self):
        """Initialize client if needed"""
        if self._client is None:
            self._client = self.lazy_init_client()
        return self._client

    def lazy_init_client(self):
        raise NotImplementedError("Not implemented yet!")


class APIKeyWrapper(BaseWrapper):
    project_id = settings.GOOGLE_CLOUD_PROJECT_NAME
    api_key = None
    service_list = []

    def lazy_init_client(self):
        """Lazy init for clients, delaying it upto first time is used"""
        credentials = service_account.Credentials.from_service_account_file(settings.GOOGLE_CLOUD_JSON_ACCOUNT)
        return api_keys_v2.ApiKeysClient(credentials=credentials)

    def create(self, display_name, timeout=None) -> Key:
        key = api_keys_v2.Key()
        key.display_name = display_name

        # Initialize request and set arguments.
        request = api_keys_v2.CreateKeyRequest()
        request.parent = f"projects/{self.project_id}/locations/global"
        request.key = key

        # Make the request and wait for the operation to complete.
        return self.client.create_key(request=request).result(timeout=timeout)

    def delete(self, api_key_id, timeout=None) -> Key:
        # Initialize request and set arguments.
        request = api_keys_v2.DeleteKeyRequest()
        request.name = f"projects/{self.project_id}/locations/global/keys/{api_key_id}"

        # Make the request and wait for the operation to complete.
        return self.client.delete_key(request=request).result(timeout=timeout)

    def restrict_for_service(self, api_key_id, service_list=None, timeout=None) -> [Key, None]:
        # Init service list
        if service_list is None:
            service_list = []

        service_list = service_list or self.service_list

        if not service_list:
            logger.warning(f"Nothing to set on api_key {api_key_id}")
            return None

        api_targets = []
        for service in service_list:
            # Restrict the API key usage by specifying the target service and methods.
            # The API key can only be used to authenticate the specified methods in the service.
            api_target = api_keys_v2.ApiTarget()
            api_target.service = service.service

            if service.methods:
                api_target.methods = service.methods

            # Add to targets
            api_targets.append(api_target)

        # Set the API restriction(s).
        # For more information on API key restriction, see:
        # https://cloud.google.com/docs/authentication/api-keys
        restrictions = api_keys_v2.Restrictions()
        restrictions.api_targets = api_targets

        key = api_keys_v2.Key()
        key.name = f"projects/{self.project_id}/locations/global/keys/{api_key_id}"
        key.restrictions = restrictions

        # Initialize request and set arguments.
        request = api_keys_v2.UpdateKeyRequest()
        request.key = key
        request.update_mask = "restrictions"

        # Make the request and wait for the operation to complete.
        response = self.client.update_key(request=request).result(timeout=timeout)
        return response


class APIKeyForAPIGatewayWrapper(APIKeyWrapper):
    """Wrapper to create api keys for API Gateway"""

    service_list = [
        ApiTarget(service=settings.API_KEY_TARGET_API_GATEWAY),
        # ApiTarget(service="firebasestorage.googleapis.com"),  # Not working on APP with Firebase SDK :(
    ]


class APIKeyDisabledWrapper(BaseWrapper):
    """Wrapper to disable API key generation in local environment"""

    def lazy_init_client(self):
        return None


API_KEY_WRAPPER = {
    "LOCAL": APIKeyForAPIGatewayWrapper, #APIKeyDisabledWrapper,
    "STAGING": APIKeyDisabledWrapper,
    "PROD": APIKeyForAPIGatewayWrapper,
}
