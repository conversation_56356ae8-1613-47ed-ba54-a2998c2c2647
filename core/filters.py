import coreapi
import coreschema
from django.db.models.constants import LOOKUP_SEP
from django.db.models.functions import Lower
from django.utils.encoding import force_str
from rest_framework.filters import OrderingFilter
from rest_framework.filters import SearchFilter as DefaultSearchFilter


class SearchFilter(DefaultSearchFilter):
    """Overriding rest_framework filter to allow unaccent lookups"""

    lookup_prefixes = {
        "^": "unaccent__istartswith",
        "=": "unaccent__iexact",
        "@": "unaccent__search",
        "$": "unaccent__iregex",
    }

    def construct_search(self, field_name):
        lookup = self.lookup_prefixes.get(field_name[0])
        if lookup:
            field_name = field_name[1:]
        else:
            lookup = "unaccent__icontains"
        return LOOKUP_SEP.join([field_name, lookup])

    def get_schema_fields(self, view):
        valid_fields = getattr(view, "search_fields", [])
        search_description = self.search_description
        search_description += f' **Available fields**: {", ".join(valid_fields)}'

        assert coreapi is not None, "coreapi must be installed to use `get_schema_fields()`"
        assert coreschema is not None, "coreschema must be installed to use `get_schema_fields()`"
        return [
            coreapi.Field(
                name=self.search_param,
                required=False,
                location="query",
                schema=coreschema.String(title=force_str(self.search_title), description=force_str(search_description)),
            )
        ]


class CaseInsensitiveOrderingFilter(OrderingFilter):
    """Overriding ordering filter to allow case insensitive lookups"""

    def filter_queryset(self, request, queryset, view):
        ordering = self.get_ordering(request, queryset, view)

        if ordering:
            for index, field in enumerate(ordering):
                # Don't modify date fields
                if "date" in field:
                    continue
                if field.startswith("-"):
                    ordering[index] = Lower(field[1:]).desc()
                else:
                    ordering[index] = Lower(field).asc()
            return queryset.order_by(*ordering)

        return queryset

    def get_schema_fields(self, view):
        valid_fields = getattr(view, "ordering_fields", self.ordering_fields)
        ordering_description = self.ordering_description
        ordering_description += f' **Available fields**: {", ".join(valid_fields)}'

        assert coreapi is not None, "coreapi must be installed to use `get_schema_fields()`"
        assert coreschema is not None, "coreschema must be installed to use `get_schema_fields()`"

        return [
            coreapi.Field(
                name=self.ordering_param,
                required=False,
                location="query",
                schema=coreschema.String(
                    title=force_str(self.ordering_title), description=force_str(ordering_description)
                ),
            )
        ]
