from django.db import models
from django.utils.text import camel_case_to_spaces
from django.utils.translation import gettext_lazy as _


class _RecursiveMeta(models.base.ModelBase):
    """Metaclass for adding recursive fields to Django models.

    This metaclass is designed to be used with Django models that require tracking of
    changes made to the entries. It adds two foreign key fields to the model:
    - `self_modelname`: This field references the old entry that is replaced by a new one.
    - `root_modelname`: This field improves lookups by pointing to the most up-to-date entry related to this object.

    The fields are added only to non-abstract models, and their names are dynamically generated based on the model's name.

    Attributes:
        name (str): The name of the model class.
        bases (tuple): The base classes of the model class.
        attrs (dict): The attributes of the model class.

    Returns:
        type: The newly created model class with the added movement tracking fields.
    """

    def __new__(cls, name, bases, attrs):
        new_class = super().__new__(cls, name, bases, attrs)

        if not new_class._meta.abstract:  # Ensure it applies only to non-abstract models
            name = camel_case_to_spaces(name).replace(" ", "_").lower()
            parent_field_name = f"parent_{name}"
            root_parent_field_name = f"root_{name}"
            parent_field_name_related = f"{parent_field_name}_related"
            root_parent_field_name_related = f"{root_parent_field_name}_related"

            new_class.add_to_class(
                parent_field_name,
                models.ForeignKey(
                    "self",
                    null=True,
                    blank=True,
                    on_delete=models.SET_NULL,
                    help_text=_("If this FK is set means this is an old entry which is replaced by a new entry"),
                    related_name=parent_field_name_related,
                ),
            )

            new_class.add_to_class(
                root_parent_field_name,
                models.ForeignKey(
                    "self",
                    null=True,
                    blank=True,
                    on_delete=models.SET_NULL,
                    help_text=_("FK to improve lookups setting the most up-to-date entry related with this object"),
                    related_name=root_parent_field_name_related,
                ),
            )

        return new_class


class RecursiveModel(models.Model, metaclass=_RecursiveMeta):
    class Meta:
        abstract = True
