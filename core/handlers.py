from rest_framework.utils.serializer_helpers import ReturnList
from rest_framework.views import exception_handler

from core.utils import errors_dict_to_string


def custom_exception_handler(exc, context):
    """Custom exception handler to flat error response from framework"""
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = exception_handler(exc, context)

    if response and isinstance(response.data, ReturnList):
        for i, item in enumerate(response.data):
            response.data[i] = errors_dict_to_string(item)

    return response
