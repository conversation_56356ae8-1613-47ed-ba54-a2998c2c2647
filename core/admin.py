from django.contrib import admin

from core.forms.models import MoloniMetadataForm
from core.models import (Event, Holiday, MoloniMetadata, Product,
                         TemporaryDeletionEntries)


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ("name", "moreapp_internal_id", "chargebee_product_id", "city", "billing_cycle", "is_legacy")
    search_fields = (
        "chargebee_product_id",
        "name",
        "moreapp_internal_id",
    )
    raw_id_fields = ("city",)
    list_filter = ("is_legacy", "city")

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("city")

    def city(self, obj):
        return obj.city.name if obj.city else None


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ("type", "event_id", "created_at")
    search_fields = ("type", "event_id")


@admin.register(MoloniMetadata)
class MoloniMetadataAdmin(admin.ModelAdmin):
    list_display = ("resource_type", "date_added", "processed")
    raw_id_fields = ("dependents",)
    form = MoloniMetadataForm


@admin.register(TemporaryDeletionEntries)
class TemporaryDeletionEntriesAdmin(admin.ModelAdmin):
    list_display = ("token", "table_name", "created_at")


@admin.register(Holiday)
class HolidayAdmin(admin.ModelAdmin):
    list_display = ("state", "description", "type", "date", "city", "country", "iso_code")
