import enum
import operator
from typing import TypeVar

from core.metaclasses import AutoRegistryMetaclass

T = TypeVar("T")


class Operators(str, enum.Enum):
    NONE = None
    AND = "and"
    OR = "or"


class Operator(metaclass=AutoRegistryMetaclass):
    lookup = Operators.NONE
    function = lambda _, __: _  # NOQA: E731

    @classmethod
    def create(cls, operator: Operators) -> operator:
        operator_class = cls._REGISTRY.get(operator)
        if not operator_class:
            operator_class = cls

        return operator_class.function


class AndOperator(Operator):
    lookup = Operators.AND
    function = operator.and_


class OrOperator(Operator):
    lookup = Operators.OR
    function = operator.or_
