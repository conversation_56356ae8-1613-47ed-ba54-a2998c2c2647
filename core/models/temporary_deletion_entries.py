import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _

from core.managers import TemporaryDeletionEntriesManager


class TemporaryDeletionEntries(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Bulk deletion metadata
    token = models.UUIDField(help_text=_("Used to retrieve the ids to be deleted"))
    related_id = models.UUIDField(help_text=_("Id to be deleted"))
    table_name = models.CharField(
        max_length=200, null=False, blank=False, help_text=_("Table name to search the ids to delete")
    )

    # Some metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [models.Index(fields=("token",))]
        verbose_name_plural = "Temporary Deletion Entries"

    objects = TemporaryDeletionEntriesManager()

    @classmethod
    def generate_token(cls):
        return uuid.uuid4()
