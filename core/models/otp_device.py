from django.conf import settings
from django_otp.plugins.otp_email.models import \
    EmailDevice as DefaultEmailDevice


class EmailDevice(DefaultEmailDevice):

    class Meta:
        proxy = True

    def generate_challenge(self, extra_context=None):
        """
        Generates a random token and emails it to the user.

        :param extra_context: Additional context variables for rendering the
            email template.
        :type extra_context: dict

        """
        generate_allowed, data_dict = self.generate_is_allowed()
        context = None
        if generate_allowed:
            self.cooldown_set(commit=False)
            self.generate_token(valid_secs=settings.OTP_EMAIL_TOKEN_VALIDITY, commit=True)
            context = {"token": self.token, **(extra_context or {})}

        return context
