import uuid

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_countries.fields import CountryField


class Holiday(models.Model):
    LOCAL = "local"
    NATIONAL = "national"
    REGIONAL = "regional"

    TYPE_CHOICES = (
        (LOCAL, _("Local")),
        (NATIONAL, _("National")),
        (REGIONAL, _("Regional")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    state = models.CharField(
        max_length=100, null=True, blank=True, help_text=_("State where the holiday is celebrated")
    )
    type = models.CharField(max_length=100, choices=TYPE_CHOICES, default=NATIONAL, null=True, blank=True)
    description = models.TextField(null=True, blank=True, help_text=_("Description of the holiday"))
    date = models.DateField(null=False, blank=False, help_text=_("Date of the holiday"))
    city = models.Char<PERSON>ield(max_length=100, null=True, blank=True, help_text=_("City where the holiday is celebrated"))
    iso_code = models.CharField(max_length=10, null=True, blank=True, help_text=_("ISO code for the admin level 2"))

    country = CountryField(null=False, blank=False)

    created_at = models.DateTimeField(default=timezone.now, db_index=True)

    class Meta:
        ordering = ("-created_at",)
        constraints = [
            models.UniqueConstraint(
                fields=["description", "state"],
                condition=models.Q(type="regional"),
                name="unique_regional_description_state",
            )
        ]
