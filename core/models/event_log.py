import uuid

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from core.encoders import CustomDjangoJSONEncoder
from core.managers import EventManager


class Event(models.Model):
    UNKNOWN = "unknown"
    HUBSPOT = "hubspot"
    MOREAPP = "moreapp"
    MOREAPP_INVENTORY = "moreapp-inventory"
    RIDERAPP = "riderapp"
    CHARGEBEE = "chargebee"
    MICROSERVICE = "microservice"
    STORAGE = "storage"

    EVENT_TYPE_CHOICES = (
        (UNKNOWN, _("Unknown event")),
        (HUBSPOT, _("Hubspot event")),
        (MOREAPP, _("Moreapp event")),
        (MOREAPP_INVENTORY, _("Moreapp inventory event")),
        (RIDERAPP, _("Riderapp event")),
        (CHARGEBEE, _("Chargebee event")),
        (MICROSERVICE, _("Microservice event")),
        (STORAGE, _("Storage event")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=100, choices=EVENT_TYPE_CHOICES, default=UNKNOWN, null=True, blank=True)
    event_id = models.CharField(max_length=200, null=False, blank=False, db_index=True)
    raw_data = models.JSONField(encoder=CustomDjangoJSONEncoder)
    created_at = models.DateTimeField(default=timezone.now, db_index=True)

    # Override default manager
    objects = EventManager()

    class Meta:
        unique_together = ("type", "event_id")
        ordering = ("-created_at",)
