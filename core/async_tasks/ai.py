from django.conf import settings

from core.async_tasks.base import async_task_helper
from core.external_services.microservices.ai import AIServiceClient


def post_send_campaign_to_ai_service_asynchronously(
    business_id, organization_id, campaign_id, api_campaign_id, objective, conversion_event_type, **kwargs
):
    """Send campaign to ai service asynchronously"""

    if settings.USE_CLOUD_TASKS:
        async_task_helper.create_task(
            f"{settings.GOOGLE_CLOUD_API_AI_TASKS_BASE_URL}metadata/",
            queue=settings.GOOGLE_CLOUD_API_AI_TASK_QUEUE,
            dispatch_deadline=1200,
            params={
                "business_id": str(business_id),
                "campaign_id": str(campaign_id),
                "organization_id": str(organization_id),
                "api_campaign_id": str(api_campaign_id),
                "objective": objective,
                "conversion_event_type": conversion_event_type,
            },
        )
    else:
        if settings.ENVIRONMENT != "STAGING" and settings.ENVIRONMENT != "LOCAL":  # Skip in dev
            # Call sync
            ai_client = AIServiceClient()
            ai_client.post_send_campaign_to_ai_service(
                business_id, campaign_id, organization_id, api_campaign_id, objective, conversion_event_type
            )
