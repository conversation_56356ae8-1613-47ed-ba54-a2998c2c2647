import importlib
import logging

from django.conf import settings
from django.db import transaction
from django.db.models import F, Prefetch
from django.utils import timezone

from campaigns.models import GoogleAd, GoogleAdset, GoogleCampaign
from core.async_tasks.base import async_task_helper
from core.external_services.google.client import (GoogleAdsClient,
                                                  GoogleQueryWrapper)
from core.external_services.google.parser import FactoryParser
from core.utils import generate_token_on_demand

logger = logging.getLogger(__name__)

CAMPAIGN_PROXY = {"campaign": GoogleCampaign, "adset": GoogleAdset, "ad": GoogleAd}


def _get_existing_google_campaign(google_campaign_internal_id):
    """Get from local database existing facebook campaign parts"""

    google_objects = (
        GoogleAdset.objects.filter(campaign__id=google_campaign_internal_id)
        .prefetch_related(
            Prefetch(
                "googlead_set",
                queryset=GoogleAd.objects.all().annotate(
                    ad_google=F("remote_id"),
                    ad_id=F("id"),
                ),
                to_attr="ads",
            )
        )
        .annotate(adset_google=F("remote_id"), adset_id=F("id"))
    )

    # Format output as dicts
    parsed_google_objects = {}
    for adset in google_objects:
        adset_google = adset.adset_google
        if adset_google:
            parsed_google_objects[str(adset_google)] = {"id": str(adset.adset_id), "what_is": "adset"}

            for ad in adset.ads:
                ad_google = ad.ad_google
                if ad_google:
                    parsed_google_objects[str(ad_google)] = {
                        "id": str(ad.ad_id),
                        "adset_google": str(adset_google),
                        "adset_id": str(adset.adset_id),
                        "what_is": "ad",
                    }
    return parsed_google_objects


def _sync_imported_google_campaigns(
    refresh_token, google_campaign_internal_id, google_campaign_remote_id, customer_id, business_id, update
):
    local_campaign_metadata = _get_existing_google_campaign(google_campaign_internal_id)
    google_factory_parser = FactoryParser(refresh_token)

    google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")

    results = {"campaign": {}, "adset": [], "ad": []}

    def get_campaign_details(google_metadata, parser_class):
        try:
            parser = google_factory_parser.parser(
                parser_class=parser_class,
                google_metadata=google_metadata,
            )
            data = parser.parse()

            ((google_object_key, google_object_value),) = data.items()
            what_is = google_object_value.pop("what_is")
            results[what_is].append(data)
        except Exception as exc:
            logger.warning("%r generated an exception: %s" % (parser_class, exc))

    adgroup_query = GoogleQueryWrapper(
        select_clause=[
            "ad_group.ad_rotation_mode",
            "ad_group.base_ad_group",
            "ad_group.campaign",
            "ad_group.cpc_bid_micros",
            "ad_group.cpm_bid_micros",
            "ad_group.cpv_bid_micros",
            "ad_group.display_custom_bid_dimension",
            "ad_group.effective_target_cpa_micros",
            "ad_group.effective_target_cpa_source",
            "ad_group.effective_target_roas",
            "ad_group.effective_target_roas_source",
            "ad_group.excluded_parent_asset_field_types",
            "ad_group.explorer_auto_optimizer_setting.opt_in",
            "ad_group.final_url_suffix",
            "ad_group.id",
            "ad_group.labels",
            "ad_group.name",
            "ad_group.status",
            "ad_group.resource_name",
            "ad_group.percent_cpc_bid_micros",
            "ad_group.target_cpa_micros",
            "ad_group.target_cpm_micros",
            "ad_group.target_roas",
            "ad_group.targeting_setting.target_restrictions",
            "ad_group.tracking_url_template",
            "ad_group.type",
            "ad_group.url_custom_parameters",
            "bidding_strategy.id",
        ],
        from_clause="ad_group",
        where_clause=[f"campaign.id = {google_campaign_remote_id}", "ad_group.status IN ('ENABLED', 'PAUSED')"],
    )
    ad_query = GoogleQueryWrapper(
        select_clause=[
            "ad_group_ad.ad.resource_name",
            "ad_group_ad.ad.id",
            "ad_group_ad.status",
            "ad_group_ad.ad.final_urls",
            "ad_group_ad.ad.final_app_urls",
            "ad_group_ad.ad.final_mobile_urls",
            "ad_group_ad.ad.url_custom_parameters",
            "ad_group_ad.ad.type",
            "ad_group_ad.ad.device_preference",
            "ad_group_ad.ad.url_collections",
            "ad_group_ad.ad.system_managed_resource_source",
            "ad_group_ad.ad.tracking_url_template",
            "ad_group_ad.ad.final_url_suffix",
            "ad_group_ad.ad.display_url",
            "ad_group_ad.ad.name",
            "ad_group_ad.ad.text_ad.description1",
            "ad_group_ad.ad.text_ad.description2",
            "ad_group_ad.ad.text_ad.headline",
            "ad_group_ad.ad.expanded_text_ad.description",
            "ad_group_ad.ad.expanded_text_ad.description2",
            "ad_group_ad.ad.expanded_text_ad.headline_part1",
            "ad_group_ad.ad.expanded_text_ad.headline_part2",
            "ad_group_ad.ad.expanded_text_ad.headline_part3",
            "ad_group_ad.ad.expanded_text_ad.path1",
            "ad_group_ad.ad.expanded_text_ad.path2",
            "ad_group_ad.ad.expanded_dynamic_search_ad.description",
            "ad_group_ad.ad.expanded_dynamic_search_ad.description2",
            "ad_group_ad.ad.responsive_search_ad.descriptions",
            "ad_group_ad.ad.responsive_search_ad.headlines",
            "ad_group_ad.ad.responsive_search_ad.path1",
            "ad_group_ad.ad.responsive_search_ad.path2",
            "ad_group.id",
        ],
        from_clause="ad_group_ad",
        where_clause=[f"campaign.id = {google_campaign_remote_id}", "ad_group_ad.status IN ('ENABLED', 'PAUSED')"],
    )

    for adgroup in google_client.get_adgroups_in_campaign(
        customer_id=customer_id, refresh_token=refresh_token, query=adgroup_query
    ):
        get_campaign_details(adgroup, "adset")

    for ad in google_client.get_ads_in_campaign(customer_id=customer_id, refresh_token=refresh_token, query=ad_query):
        get_campaign_details(ad, "ad")

    # Metadata data structures
    new_adgroups = {}
    update_insights = {"adset": False, "ad": False}

    with transaction.atomic():
        # First, parse adgroups
        for result in results["adset"]:
            ((google_object_key, google_object_value),) = result.items()
            local_object_metadata = local_campaign_metadata.pop(google_object_key, None)

            adset_id = google_object_value.get("remote_id")

            # Reset related metadata just in case
            google_object_value.update(**{"deleted_from_remote_source": False, "errors": None, "draft": None})

            # Exists?
            if local_object_metadata:
                # Save current used adgroup for later
                GoogleAdset.objects.filter(id=local_object_metadata.get("id")).update(**google_object_value)
                new_adgroups[adset_id] = local_object_metadata.get("id")
            else:
                # Create it
                google_object_value["campaign_id"] = google_campaign_internal_id
                created = GoogleAdset.objects.create(**google_object_value)

                # Save new adgroup for later
                new_adgroups[adset_id] = str(created.id)
                update_insights["adset"] = update

        # Then, parse ads
        for result in results["ad"]:
            ((google_object_key, google_object_value),) = result.items()
            local_object_metadata = local_campaign_metadata.pop(google_object_key, None)

            adset = google_object_value.pop("adset_id", None)
            ad_content = google_object_value.pop("ad_content", {})
            adset_id = new_adgroups.get(adset, None)

            # We only add ads with related adsets. If we have an ad without an adset, is very likely to adset is
            # removed but ad is marked as paused/active, causing inconsistencies
            if adset_id:
                # Set the right ad content and link it with the ad
                ad_content_type = ad_content.pop("type", None)
                if ad_content_type:
                    model_class = getattr(
                        importlib.import_module("campaigns.models.google_ad_types"), f"Google{ad_content_type}"
                    )
                    google_object_value["content"] = model_class.objects.create(**ad_content)

                # Set the right adgroup and related metadata
                google_object_value.update(
                    **{
                        "adset_id": adset_id,
                        "deleted_from_remote_source": False,
                        "errors": None,
                        "draft": None,
                    }
                )

                # Exists?
                if local_object_metadata:
                    content = google_object_value.pop("content", None)
                    if content:
                        google_object_value["object_id"] = content.pk

                    # Save current used ad for later
                    google_ad_qs = GoogleAd.objects.filter(id=local_object_metadata.get("id"))
                    google_ad_qs.update(**google_object_value)

                else:
                    # Create it
                    GoogleAd.objects.create(**google_object_value)
                    update_insights["ad"] = update

        # Mark to delete non existing adsets/ads
        for local_metadata in local_campaign_metadata:
            local_metadata = local_campaign_metadata[local_metadata]
            CAMPAIGN_PROXY[local_metadata.get("what_is")].objects.filter(id=local_metadata.get("id")).update(
                deleted_from_remote_source=True
            )

        # Remove orphan content ads
        for model_class in [
            "GoogleTextAd",
            "GoogleExpandedTextAd",
            "GoogleExpandedDynamicSearchAd",
            "GoogleResponsiveSearchAd",
        ]:
            model_class = getattr(importlib.import_module("campaigns.models.google_ad_types"), model_class)
            model_class.objects.filter(google_ad__isnull=True).delete()

        # Update campaign internal statuses
        update_query = {"creation_status": GoogleCampaign.FINISHED, "end_utc_time": timezone.now()}
        google_campaign = GoogleCampaign.objects.filter(id=google_campaign_internal_id)
        google_campaign.update(**update_query)
        google_campaign = google_campaign.first()

        # Call hiperion service to update insights if it is marked
        for level in update_insights:
            if update_insights[level]:
                google_campaign.update_insights(level)


def sync_imported_google_campaigns_asynchronously(
    refresh_token, google_campaign_internal_id, google_campaign_remote_id, customer_id, business_id, **kwargs
):
    """Sync google campaign structure for core models"""

    update = kwargs.pop("update", False)
    if settings.USE_CLOUD_TASKS:
        async_task_helper.create_task(
            f"{settings.GOOGLE_CLOUD_CAMPAIGN_IMPORTER_TASK_URL}sync_imported_campaign/",
            queue=settings.GOOGLE_CLOUD_CAMPAIGN_IMPORTER_TASK_QUEUE,
            dispatch_deadline=1800,
            api_token=generate_token_on_demand(minutes=28800),  # 20 days
            payload={
                "access_token": refresh_token,
                "campaign_internal_id": google_campaign_internal_id,
                "campaign_remote_id": google_campaign_remote_id,
                "ad_account_or_customer_id": customer_id,
                "business_id": business_id,
                "update": update,
                "social_network": "google",
            },
        )
    else:
        # Call sync
        _sync_imported_google_campaigns(
            refresh_token, google_campaign_internal_id, google_campaign_remote_id, customer_id, business_id, update
        )
