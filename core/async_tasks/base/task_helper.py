import json
import logging
from datetime import datetime, timedelta
from urllib.parse import urlencode

from django.conf import settings
from google.cloud import tasks_v2
from google.oauth2 import service_account
from google.protobuf import duration_pb2, timestamp_pb2

logger = logging.getLogger(__name__)


class HTTPTasksMixin:
    """Mixin to wrap google cloud async tasks"""

    def __init__(self, service_account_json=None):
        if service_account_json is not None:
            self._client = tasks_v2.CloudTasksClient.from_service_account_json(service_account_json)
        else:
            service_account_json = settings.GOOGLE_CLOUD_SERVICE_ACCOUNT
            if service_account_json:
                self._client = tasks_v2.CloudTasksClient.from_service_account_json(service_account_json)
            else:
                logger.warning(
                    "Setting cloudtaskclient with environment GOOGLE_APPLICATION_CREDENTIALS service account"
                )
                self._client = tasks_v2.CloudTasksClient()

        self._credentials = service_account.Credentials.from_service_account_file(service_account_json)

    def _build_task(
        self, url, http_method, payload, in_seconds, params, token, dispatch_deadline, ws_token, api_token, headers=None
    ):

        if params:
            url = f"{url}?{urlencode(params)}"

        task = {
            "http_request": {  # Specify the type of request.
                "http_method": http_method,
                "url": url,  # The full url path that the task will be sent to.
                "headers": {"Content-Type": "application/json"},
            }
        }
        if ws_token is not None:
            task["http_request"]["headers"]["Hiperion-token-X"] = ws_token

        if api_token is not None:
            task["http_request"]["headers"]["Authorization"] = f"Bearer {api_token}"
        elif token is not None:
            task["http_request"]["headers"]["Authorization"] = f"jwt {token}"
        else:
            task["http_request"]["oidc_token"] = {"service_account_email": self._credentials.signer_email}

        if headers is not None:
            task["http_request"]["headers"].update(**headers)

        if payload is not None:
            # The API expects a payload of type bytes.
            converted_payload = json.dumps(payload).encode()

            # Add the payload to the request.
            task["http_request"]["body"] = converted_payload

        if in_seconds is not None:
            # Convert "seconds from now" into an rfc3339 datetime string.
            d = datetime.utcnow() + timedelta(seconds=in_seconds)

            # Create Timestamp protobuf.
            timestamp = timestamp_pb2.Timestamp()
            timestamp.FromDatetime(d)

            # Add the timestamp to the tasks.
            task["schedule_time"] = timestamp

        if dispatch_deadline is not None:
            duration = duration_pb2.Duration()

            # Max time for workers to wait before retry
            task["dispatch_deadline"] = duration.FromSeconds(dispatch_deadline)

        return task

    def create_task(
        self,
        url,
        queue=settings.GOOGLE_CLOUD_TASK_QUEUE,
        http_method="POST",
        payload=None,
        in_seconds=None,
        params=None,
        token=None,
        dispatch_deadline=None,
        ws_token=None,
        api_token=None,
        headers=None,
    ):
        # try:
        task = self._build_task(
            url, http_method, payload, in_seconds, params, token, dispatch_deadline, ws_token, api_token, headers
        )

        # Construct the fully qualified queue name
        parent = self._client.queue_path(settings.GOOGLE_CLOUD_PROJECT, settings.GOOGLE_CLOUD_TASK_LOCATION, queue)

        # Build an send task
        return self._client.create_task(parent=parent, task=task)


class AsyncTask(HTTPTasksMixin):
    """ Main abstraction of cloud tasks """

    pass
