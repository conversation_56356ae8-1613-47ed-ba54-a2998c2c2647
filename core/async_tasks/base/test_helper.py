from django.conf import settings


class HTTPTasksMixin:
    def __init__(self, service_account_json=None):
        pass

    def create_task(
        self, url, queue=settings.GOOGLE_CLOUD_TASK_QUEUE, http_method="POST", payload=None, in_seconds=None, **kwargs
    ):
        # Build an send task
        return True


class AsyncTaskTesting(HTTPTasksMixin):
    """ Main abstraction of cloud tasks """

    pass
