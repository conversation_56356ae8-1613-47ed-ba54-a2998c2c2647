import os

from django.conf import settings


class Liveness:
    liveness_file_dir = "/tmp/alive"

    def __enter__(self):
        """Defer signal handling to wait until code block ends"""
        if not os.path.exists(self.liveness_file_dir):
            os.mknod(self.liveness_file_dir)

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Restore signals and propagate them to parent"""
        if os.path.exists(self.liveness_file_dir):
            os.unlink(self.liveness_file_dir)


class NoLiveness:
    """Dummy liveness class to skip it in non-production environment"""

    def __enter__(self):
        pass

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


def is_alive():
    """Dummy method to export Liveness class to be compliant with PEP8"""
    return Liveness() if settings.ENVIRONMENT == "PROD" else NoLiveness()
