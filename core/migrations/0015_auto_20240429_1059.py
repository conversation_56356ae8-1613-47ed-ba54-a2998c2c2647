# Generated by Django 3.2.25 on 2024-04-29 10:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0014_alter_event_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="product",
            name="base",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ES-M", "Madrid"),
                    ("PT-P", "Porto"),
                    ("ES-S", "Sevilla"),
                    ("ES-B", "Barcelona"),
                    ("ES-ML", "Malaga"),
                    ("ES-V", "Valencia"),
                    ("IT-M", "Milano"),
                    ("FR-P", "Paris"),
                    ("PT-L", "Lisboa"),
                    ("MX-Q", "Queretaro"),
                    ("ES-GR", "Granada"),
                    ("ES-SA", "Salamanca"),
                    ("ES-BI", "Bilbao"),
                    ("ES-SS", "San Sebastian"),
                    ("ES-NA", "Pamplona"),
                    ("ES", "Spain"),
                    ("FR", "France"),
                    ("PT", "Portugal"),
                    ("IT", "Italia"),
                    ("MX", "Mexico"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="billing_cycle",
            field=models.CharField(
                blank=True,
                choices=[
                    ("one_time", "One time"),
                    ("monthly", "Monthly"),
                    ("quarterly", "Quarterly"),
                    ("biannual", "Biannual"),
                    ("annual", "Annual"),
                ],
                default="monthly",
                max_length=100,
                null=True,
            ),
        ),
    ]
