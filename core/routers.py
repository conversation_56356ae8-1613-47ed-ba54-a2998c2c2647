import copy

from django.urls import re_path
from rest_framework.routers import DefaultRouter


class OptionalSlashRouter(DefaultRouter):
    def get_urls(self):
        """
        Use the registered viewsets to generate a list of URL patterns.
        """
        ret = []

        for prefix, viewset, basename in self.registry:
            lookup = self.get_lookup_regex(viewset)
            routes = self.get_routes(viewset)

            for route in routes:

                # Only actions which actually exist on the viewset will be bound
                mapping = self.get_method_map(viewset, route.mapping)
                if not mapping:
                    continue

                # Build the url pattern
                regex = route.url.format(prefix=prefix, lookup=lookup, trailing_slash="/?")

                # If there is no prefix, the first part of the url is probably
                #   controlled by project's urls.py and the router is in an app,
                #   so a slash in the beginning will (A) cause Django to give
                #   warnings and (B) generate URLS that will require using '//'.
                if not prefix and regex[:2] == "^/":
                    regex = "^" + regex[2:]

                initkwargs = route.initkwargs.copy()
                initkwargs.update({
                    "basename": basename,
                    "detail": route.detail,
                })

                view = viewset.as_view(mapping, **initkwargs)
                name = route.name.format(basename=basename)
                ret.append(re_path(regex, view, name=name))

        return ret


class BulkRouter(DefaultRouter):
    """
    A custom router that extends the DefaultRouter and adds support for bulk operations.

    This router inherits from DefaultRouter and modifies the first route's mapping to include
    'patch' and 'delete' methods for bulk update and bulk deletion operations respectively.
    """

    routes = copy.deepcopy(DefaultRouter.routes)
    routes[0].mapping.update({
        "patch": "bulk_update",
        "delete": "bulk_destroy",
    })
