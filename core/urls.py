from django.urls import path

from core.views import (BusinessCampaignReportView, CommentsView,
                        FacebookGraphAPIView, FacebookTargetingSearchView,
                        FacebookTargetingSuggestionsView, ListCountriesView,
                        LocationsSearchView, NotificationsView)

urlpatterns = [
    path("graph/", FacebookGraphAPIView.as_view(), name="facebook-graph"),
    path("targeting-search/", FacebookTargetingSearchView.as_view(), name="ad_account_targeting_search"),
    path("targeting-suggestions/", FacebookTargetingSuggestionsView.as_view(), name="ad_account_targeting_suggestions"),
    path("locations-search/", LocationsSearchView.as_view(), name="locations_search"),
    path("countries/", ListCountriesView.as_view(), name="list_countries"),
    path("filtering/", BusinessCampaignReportView.as_view(), name="filtering"),
    path("comments/", CommentsView.as_view(), name="comment"),
    path("notifications/", NotificationsView.as_view(), name="notification"),
]

app_name = "core"
