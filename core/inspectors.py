from collections import OrderedDict

from drf_yasg import openapi
from drf_yasg.inspectors import PaginatorInspector
from rest_framework.pagination import (CursorPagination, LimitOffsetPagination,
                                       PageNumberPagination)


class BasePaginationInspector(PaginatorInspector):
    """Custom pagination inspector to display in redoc"""

    def get_paginated_response(self, paginator, response_schema):
        assert response_schema.type == openapi.TYPE_ARRAY, "array return expected for paged response"
        paged_schema = None
        if isinstance(paginator, (LimitOffsetPagination, PageNumberPagination, CursorPagination)):
            has_count = not isinstance(paginator, CursorPagination)
            paged_schema = openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties=OrderedDict((
                    ("count", openapi.Schema(type=openapi.TYPE_INTEGER) if has_count else None),
                    ("next", openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_URI, x_nullable=True)),
                    (
                        "previous",
                        openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_URI, x_nullable=True),
                    ),
                    ("total_pages", openapi.Schema(type=openapi.TYPE_INTEGER)),
                    ("results", response_schema),
                )),
                required=["results"],
            )

        return paged_schema
