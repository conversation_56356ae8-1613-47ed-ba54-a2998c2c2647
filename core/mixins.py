import operator
from collections import OrderedDict
from functools import reduce

from django.core.exceptions import FieldDoesNotExist
from django.db.models import Q
from django.shortcuts import get_list_or_404
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.generics import get_object_or_404
from rest_framework.serializers import raise_errors_on_nested_writes
from rest_framework.utils.serializer_helpers import ReturnList

from core.managers import RelatedManager
from core.utils import errors_dict_to_string


class StringErrorsMixin:
    """Mixin for DRF serializers. Changes errors from lists to string"""

    @property
    def errors(self):
        errors = super().errors

        if isinstance(errors, ReturnList):
            for i, __ in enumerate(errors):
                errors[i] = errors_dict_to_string(errors[i])

            return errors

        return errors_dict_to_string(errors)


class PartialUpdateMixin:
    """Only update specific fields on partial_update (patch)"""

    def update(self, instance, validated_data):
        raise_errors_on_nested_writes("update", self, validated_data)

        _updated_fields = []
        for attr, value in validated_data.items():
            field = getattr(instance, attr)
            if field != value:
                setattr(instance, attr, value)
                _updated_fields.append(attr)

        self.perform_save(instance, update_fields=_updated_fields, validated_data=validated_data)
        return instance

    def perform_save(self, instance, validated_data, update_fields):
        instance.save(update_fields=update_fields)


class PartialUpdateVersionedMixin(PartialUpdateMixin):
    """
    A mixin class for performing partial updates with versioning.

    This mixin extends the functionality of PartialUpdateMixin by adding support for versioning.
    It overrides the perform_save method to handle versioning and update only the specified fields.
    """

    def perform_save(self, instance, validated_data, update_fields):
        """
        Perform a save operation with versioning and update only the specified fields.

        Parameters:
        - instance (object): The instance to be updated.
        - validated_data (dict): The validated data to be used for updating the instance.
        - update_fields (list): A list of fields to be updated.

        Returns:
        None

        Raises:
        serializers.ValidationError: If the 'version' field is not provided in the validated_data.
        """

        version = validated_data.pop("version", None)
        if version is None:
            raise serializers.ValidationError({"non_field_errors": _("version is required")})

        if not update_fields:
            # Nothing changed, no update needed
            return

        update_dict = {attr: getattr(instance, attr) for attr in update_fields if attr != "version"}
        instance.__class__.objects.filter(pk=instance.pk).versioned_update(expected_version=version, **update_dict)
        instance.refresh_from_db()


class RestrictQuerysetBasedOnGroupMixin:
    """Inject additional filter in get_queryset to discard results based on given condition"""

    default_query = {}

    def parsed_filter_condition(self, filter_by, filter_data):
        filter_by = filter_by.split("|")
        filter_query = Q()

        for condition in filter_by:
            filter_query |= Q(**{condition: filter_data})

        return filter_query

    def restrict_queryset_based_on_group(self, queryset, filter_by, user):
        assert hasattr(self, "user_attr")

        user_groups = getattr(user, self.user_attr) or []
        filtered_queryset_data = []

        for group in user_groups:
            filtered_queryset_data.append(group.name)

        filter_query = Q(**self.default_query)
        if filtered_queryset_data:
            # Add default filter to retrieve data from users who don't allow to any group
            filter_query = self.parsed_filter_condition(filter_by, filtered_queryset_data) | filter_query

        queryset = queryset.filter(filter_query)
        return queryset


class ManyToManyGetOrCreateOrUpdateSerializerMixin:
    """
    Check if must use a given manytomany related object or create it
    """

    m2m_related_fields = None

    def _extract_related_fields(self, validated_data):
        related_fields = OrderedDict()
        for field_name, field in self.fields.items():
            if field.source not in validated_data:
                # Skip field if field is not required
                continue

            if field.read_only:
                continue

            try:
                related_field = self.Meta.model._meta.get_field(field.source)
                if related_field.many_to_many and field_name in self.m2m_related_fields:
                    related_fields[field_name] = (
                        related_field.remote_field.model,
                        validated_data.pop(field.source),
                        field.source,
                    )

            except FieldDoesNotExist:
                pass

        return related_fields

    def exclude_fields_in_many_to_many(self, instance, attribute):
        raise NotImplementedError("You must update this method in child class for updating many to many relationships")

    def bulk_create(self, validated_data):
        """This method is intended to be used paired with a ListSerializer only"""
        return [self.create(data) for data in validated_data]

    def create(self, validated_data):
        related = self._extract_related_fields(validated_data)

        # First, create the instance
        instance = super().create(validated_data)

        # Then, update many to many relationship accordingly
        for __, (related_field_model, data, field_source) in related.items():
            existing_data = []
            non_existing_data = []

            for related_data in data:
                pk = related_data.get("id", None)
                if pk is None:
                    non_existing_data.append(related_field_model(**related_data))
                else:
                    existing_data.append(pk)

            if non_existing_data:
                existing_data.extend(related_field_model.objects.bulk_create(non_existing_data))

            m2m_manager = getattr(instance, field_source)
            m2m_manager.add(*existing_data)
        return instance

    def update(self, instance, validated_data):
        related = self._extract_related_fields(validated_data)

        # First, create the instance
        instance = super().update(instance, validated_data)

        # Then, update many to many relationship accordingly
        for __, (related_field_model, data, field_source) in related.items():
            existing_data = []
            non_existing_data = []

            for related_data in data:
                pk = related_data.get("id", None)
                if pk is None:
                    non_existing_data.append(related_field_model(**related_data))
                else:
                    existing_data.append(pk)

            m2m_manager = getattr(instance, field_source)

            # Remove M2M relationship (i.e. items) if needed
            excluded_fields_query = self.exclude_fields_in_many_to_many(instance, field_source) or []
            excluded_fields = m2m_manager.exclude(*excluded_fields_query).values_list("pk", flat=True)
            fields_to_add, fields_to_remove = self._format_m2m_data(existing_data, excluded_fields)

            if non_existing_data:
                fields_to_add.extend(related_field_model.objects.bulk_create(non_existing_data))

            # Set new M2M data
            if fields_to_remove:
                m2m_manager.filter(id__in=fields_to_remove).delete()
            m2m_manager.set(fields_to_add)
        return instance

    def _format_m2m_data(self, fields_to_add, fields_to_exclude):
        fields_to_add = set(fields_to_add)
        fields_to_exclude = set(fields_to_exclude)

        return list(fields_to_add), list(fields_to_exclude - fields_to_add)


class MultipleFieldLookupMixin:
    """
    Mixin to handle multiple lookup fields on a view or viewset
    """

    lookup_serializer_class = None

    def _ensure_field(self, field):
        """Handle both direct and related fields."""

        model = self.lookup_serializer_class.Meta.model
        parts = field.split("__")
        for part in parts[:-1]:  # Traverse through related models if needed
            model = model._meta.get_field(part).related_model
        field_obj = model._meta.get_field(parts[-1])  # Get the final field
        return field_obj.to_python(self.kwargs[self.lookup_field])

    def get_object(self):
        """Override the get_object() method to validate every field and avoid 404 not found errors"""

        assert self.lookup_serializer_class, (
            f"View {self.__class__.__name__} accept multiple lookup fields."
            " Please, add a `lookup_serializer_class` which contains all the needed fields "
        )

        lookup_fields = self.lookup_serializer_class.Meta.fields
        filter_kwargs = {}

        queryset = self.filter_queryset(self.get_queryset())
        for field in lookup_fields:
            try:
                self._ensure_field(field)
                filter_kwargs[field] = self.kwargs[self.lookup_field]
            except Exception:
                continue

        filter_kwargs = reduce(operator.or_, (Q(x) for x in filter_kwargs.items()))

        try:
            obj = get_object_or_404(queryset, filter_kwargs)

            # May raise a permission denied
            self.check_object_permissions(self.request, obj)
        except self.lookup_serializer_class.Meta.model.MultipleObjectsReturned:
            obj = get_list_or_404(queryset, filter_kwargs)

        return obj


class SubsetOfFieldsSerializerMixin:
    def __init__(self, *args, **kwargs):
        assert (
            hasattr(self.Meta, "model") and self.Meta.model is not None
        ), "SubsetOfFieldsSerializerMixin must be used with a Meta.model defined."

        only = None
        exclude = None
        request = (kwargs.get("context", {}) or {}).get("request", None)
        if request:
            only = request.query_params.get("only", None)
            exclude = request.query_params.get("exclude", None)

        if only is not None and exclude is not None:
            raise ValueError("Only and exclude are mutually exclusive. Please, choose one of them")

        super().__init__(*args, **kwargs)

        if only:
            selected_fields = set(only.split(","))
            existing_fields = set(self.fields)
            for field in existing_fields - selected_fields:
                self.fields.pop(field, None)

        if exclude:
            selected_fields = set(exclude.split(","))

            for field in selected_fields:
                self.fields.pop(field, None)


class PaginationMixin:
    """
    A mixin class for pagination in Django REST Framework views.

    This mixin injects a queryset into the paginator when using a cursor-based pagination.
    This is useful when performing aggregations or other operations that require the full queryset.

    Use BaseCursorPagination when the table has millions of rows because page or limit/offset pagination
    can hit the performance several orders of magnitude.
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the PaginationMixin.

        If the pagination_class is a subclass of BaseCursorPagination,
        it injects the queryset into the paginator.

        Parameters:
        - *args: Variable length argument list.
        - **kwargs: Arbitrary keyword arguments.
        """
        from core.paginations import BaseCursorPagination

        if issubclass(self.pagination_class, BaseCursorPagination):
            # Inject the queryset needed to make some aggregations
            def get_paginated_response(self, data):
                """
                Override the get_paginated_response method to inject the queryset into the paginator.

                Parameters:
                - data: The serialized data to be paginated.

                Returns:
                - The paginated response with injected queryset.
                """

                self.paginator.queryset = self.queryset
                return super(PaginationMixin, self).get_paginated_response(data)

            self.get_paginated_response = get_paginated_response.__get__(self)

    def get_pagination_class(self):
        """
        Get the pagination class.

        Returns:
        - The pagination class.
        """

        return self.pagination_class

    def paginate_queryset(self, queryset):
        """
        Paginate the queryset.

        Parameters:
        - queryset: The queryset to be paginated.

        Returns:
        - The paginated queryset.
        """

        self.pagination_class = self.get_pagination_class()
        return super().paginate_queryset(queryset)

    def get_paginated_response(self, data):
        """
        Get the paginated response.

        Parameters:
        - data: The serialized data to be paginated.

        Returns:
        - The paginated response.
        """

        self.pagination_class = self.get_pagination_class()
        return super().get_paginated_response(data)


class RelatedManagerModelMixin:
    @property
    def is_alone(self):
        """
        Check if the current instance is the only one related to another instance.

        This method uses the RelatedManager to determine if the current instance is the only one related
        to another instance. It checks if the `objects` attribute of the model is an instance of RelatedManager.
        If it is, it calls the `is_alone` method of the RelatedManager with the current instance as a parameter.
        If the `objects` attribute is not an instance of RelatedManager, it raises an AttributeError.

        Parameters:
        None

        Returns:
        bool: True if the current instance is the only one related to another instance, False otherwise.
        """
        if isinstance(self.__class__.objects, RelatedManager):
            return self.__class__.objects.is_alone(instance=self)
        raise AttributeError(
            f"{self.__class__.__name__}.objects is not an instance of RelatedManager. "
            "Make sure to explicitly set `objects = RelatedManager()` in your model."
        )
