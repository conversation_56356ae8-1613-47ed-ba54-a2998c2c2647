{% extends "layout.html" %}

{% block content %}

<div class="row">
    <div class="column column-80 column-offset-10">
        <h3>Webhooks initialization:</h3>

        <h5>1. All active subscriptions will be paused</h5>
        <h5>2. Target url will be updated to {{ target_url }}</h5>
        <h5>3. Following subscriptions will be created and activated:</h5>
        <ul>
            <li>Contact's Creation (contact.creation)</li>
            <li>Changing of Contact's Fist Name (contact.propertyChange)</li>
            <li>Contact's Deletion (contact.deletion)</li>
        </ul>

<pre>
// src/routes/init.py
pause_active_subscriptions(hubspot_client=hubspot, app_id=app_id)

target_url = url_for("webhooks.handle", _external=True)
configure_target_url(hubspot_client=hubspot, app_id=app_id, target_url=target_url)

subscriptions = [
    {"event_type": "contact.propertyChange", "property_name": "firstname"},
    {"event_type": "contact.creation"},
    {"event_type": "contact.deletion"},
]
for subscription in subscriptions:
    create_or_activate_subscription(
        hubspot_client=hubspot,
        app_id=app_id,
        **subscription,
    )
</pre>
        <form method="post" class="text-center">
            <button type="submit">Continue</button>
        </form>
    </div>
</div>

{% endblock %}
