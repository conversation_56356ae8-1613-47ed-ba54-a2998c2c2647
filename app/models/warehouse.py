from datetime import date
from enum import Enum
from typing import Optional

from pydantic import BaseModel


class StatusChoices(str, Enum):
    SUCCESS = "success"
    FAILED = "failed"


class WarehouseDifference(BaseModel):
    id: str
    warehouse_id: str
    date: date


class WarehouseResponse(BaseModel):
    id: str
    status: StatusChoices
    processed_file: Optional[str]
    result: Optional[float]
    error_reason: Optional[str]
    warehouse_space: Optional[float]
    our_space: Optional[float]
