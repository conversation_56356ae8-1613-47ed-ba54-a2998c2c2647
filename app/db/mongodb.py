from typing import Dict, List

from fastapi import Depends

from app.db.base import CRUDMixin
from app.models.mongo import MongoResponse
from app.settings import Settings
from conf import get_settings


class MongoBackend(CRUDMixin):
    def __init__(self, mongo_client):
        self._client = mongo_client

    async def get_all_inventory(self, warehouse, settings: Settings = Depends(get_settings)) -> List[Dict]:
        """Retrieve every item from inventory"""

        mongo_db_collection = settings.COLLECTION_MAPPING.get(warehouse)
        mongo_collection = self._client[settings.MONGODB_DATABASE][mongo_db_collection]

        inventory_items = []
        index = 0
        async for item in mongo_collection.find({"DeliveryDate": {"$eq": None}}, projection={"_id": False}):
            inventory_items.append(
                MongoResponse(
                    id=index,
                    ObjectCode=item.get("ObjectCode"),
                    Contract_id=item.get("Contract_id"),
                    ObjectDescription=item.get("ObjectDescription"),
                    EnterDate=item.get("EnterDate"),
                    DeliveryDate=item.get("DeliveryDate"),
                    Name=item.get("Name"),
                ).__dict__
            )
            index += 1

        return inventory_items
