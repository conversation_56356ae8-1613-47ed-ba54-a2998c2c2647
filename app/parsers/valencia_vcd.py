import pandas as pd

from app.parsers.base import BaseWarehouseParser


class ValenciaVCDWarehouseParser(BaseWarehouseParser):
    country = "ES"
    deal_column = 1

    def preprocess(self):
        """Override basic preprocessing to handle accordingly deal_id - odoo number using the right columns"""
        self.warehouse_data = super(ValenciaVCDWarehouseParser, self).preprocess()

        self.warehouse_data.iloc[:, self.deal_column] = (
            self.warehouse_data.iloc[:, self.deal_column].astype("Int64").astype(str)
        )
        merged_df = pd.merge(
            self.warehouse_data,
            self.translation_data,
            left_on=self.warehouse_data.iloc[:, self.deal_column].name,
            right_on=["odoo"],
            how="left",
        )
        merged_df["deal_id"].fillna(merged_df.iloc[:, self.deal_column], inplace=True)

        # Drop unneeded columns
        merged_df.drop(["country", "odoo"], axis=1, inplace=True)
        return merged_df[:-1]

    def transform(self):
        """Parse warehouse data"""

        self.warehouse_data["warehouse_total"] = self.warehouse_data.iloc[:, 2].astype(float)
