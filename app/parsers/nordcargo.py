import pandas as pd

from app.parsers.base import BaseWarehouseParser


class MalagaNordcargoWarehouseParser(BaseWarehouseParser):
    country = "ES"
    deal_column = 1
    delta_value = -10000000
    cadre_to_palets = {7: 4, 12: 7}

    def preprocess(self):
        """Override basic preprocessing to handle accordingly deal_id - odoo number using the right columns"""
        self.warehouse_data = super(MalagaNordcargoWarehouseParser, self).preprocess()

        # Skip blank deals to fetch data later from chargebee
        self.warehouse_data.iloc[:, self.deal_column].fillna(self.delta_value, inplace=True)
        self.warehouse_data.iloc[:, self.deal_column] = (
            self.warehouse_data.iloc[:, self.deal_column].astype("Int64").astype(str)
        )
        merged_df = pd.merge(
            self.warehouse_data,
            self.translation_data,
            left_on=self.warehouse_data.iloc[:, self.deal_column].name,
            right_on=["odoo"],
            how="left",
        )
        merged_df["deal_id"].fillna(merged_df.iloc[:, self.deal_column], inplace=True)
        merged_df["deal_id"].replace(self.delta_value, inplace=True)

        # Drop unneeded columns
        merged_df.drop(["country", "odoo"], axis=1, inplace=True)
        return merged_df

    def transform(self):
        """Parse warehouse data"""

        self.warehouse_data["cadre"] = (
            self.warehouse_data.iloc[:, 4].astype("Int64").apply(self.cadre_to_palets.get).fillna(0)
        )
        self.warehouse_data["palets"] = self.warehouse_data.iloc[:, 5].fillna(0) * self.warehouse_data.iloc[
            :, 6
        ].fillna(0)
        self.warehouse_data["warehouse_total"] = self.warehouse_data["cadre"] + self.warehouse_data["palets"]
