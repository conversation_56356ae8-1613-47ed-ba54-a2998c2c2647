from app.models.warehouse import WarehouseDifference
from app.parsers.ascor import AscorWarehouseParser
from app.parsers.lqs_es import LqsESWarehouseParser
from app.parsers.lqs_pt import LqsPTWarehouseParser
from app.parsers.nordcargo import MalagaNordcargoWarehouseParser
from app.parsers.suardiaz import SuardiazWarehouseParser
from app.parsers.valencia_vcd import ValenciaVCDWarehouseParser
from app.utils import get_chargebee_instance

PARSERS_FACTORY = {
    "1004": LqsESWarehouseParser,
    "1021": AscorWarehouseParser,
    "1032": ValenciaVCDWarehouseParser,
    "1041": MalagaNordcargoWarehouseParser,
    "1052": SuardiazWarehouseParser,
    "2001": LqsPTWarehouseParser,
}


def run_discrepancies(warehouse: WarehouseDifference, storage_client_instance, settings):
    """Entrypoint for parsers"""
    parser = PARSERS_FACTORY.get(warehouse.warehouse_id)
    if parser:
        chargebee_instance = get_chargebee_instance(site_country=parser.country, settings=settings)
        return parser().run(
            warehouse, storage_client_instance, chargebee_instance, settings.BUCKET_NAME, settings.BUCKET_FOLDER_NAME
        )
