import pandas as pd

from app.parsers.base import BaseWarehouseParser


class AscorWarehouseParser(BaseWarehouseParser):
    country = "ES"
    header_row = 3

    def preprocess(self):
        """Override basic preprocessing to handle accordingly deal_id - odoo number using the right columns"""

        self.warehouse_data = super(AscorWarehouseParser, self).preprocess()
        self.warehouse_data.iloc[:, self.deal_column] = self.warehouse_data.iloc[:, self.deal_column].astype(str)

        merged_df = pd.merge(
            self.warehouse_data,
            self.translation_data,
            left_on=self.warehouse_data.iloc[:, self.deal_column].name,
            right_on=["odoo"],
            how="left",
        )
        merged_df["deal_id"].fillna(merged_df.iloc[:, self.deal_column], inplace=True)

        # Drop unneeded columns
        merged_df.drop(["country", "odoo"], axis=1, inplace=True)
        merged_df.drop(merged_df[merged_df.iloc[:, self.deal_column].isna()].index, axis=0, inplace=True)
        merged_df.drop_duplicates(subset=[merged_df.iloc[:, 0].name, merged_df.iloc[:, 1].name], inplace=True)

        # Last row is "total" which we are not interested on it
        return merged_df[:-1]

    def transform(self):
        """Parse warehouse data

        < 1,60cm palet/día	        0	0,22€	100%	    x1
        < 2m palet/día	            1	0,30€	136%	    x1,4
        < 2m Americanos y dobles	2	0,30€	136%	    x1,4
        > 2m palet/día	            3	0,42€	191%	    x2
        > 2m Americanos y dobles	4	0,42€	191%	    x2
        Palet triple	            Triple	0,42€	191%	x2
        Colchones ud	            Colch	0,07€	32%	    x0,4

        """

        self.warehouse_data["warehouse_total"] = (
            (self.warehouse_data.iloc[:, 1].fillna(0) * 1)  # 0
            + (self.warehouse_data.iloc[:, 2].fillna(0) * 1.4)  # 1
            + (self.warehouse_data.iloc[:, 3].fillna(0) * 1.4)  # 2
            + (self.warehouse_data.iloc[:, 4].fillna(0) * 2)  # 3
            + (self.warehouse_data.iloc[:, 5].fillna(0) * 2)  # 4
            + (self.warehouse_data.iloc[:, 6].fillna(0) * 0.4)  # Colchon
            + (self.warehouse_data.iloc[:, 7].fillna(0) * 2)  # Triple
        )
