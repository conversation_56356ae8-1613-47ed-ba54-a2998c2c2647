from pydantic import BaseSettings


class Settings(BaseSettings):
    __name__ = None

    ENVIRONMENT: str = __name__
    ODOO_URL: str = "https://140921.odoo.com"
    ODOO_DATABASE: str = "140921"
    ODOO_USER_ID: str = "12"
    ODOO_PASSWORD: str

    # Authentication JWT
    authjwt_secret_key: str = "local-secret"  # must be the same as main api

    # Default logging configuration
    DEFAULT_LOG = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "format": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
            }
        },
        "handlers": {
            "default": {
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
                "formatter": "default",
            },
        },
        "loggers": {
            "": {
                "level": "INFO",
                "handlers": [
                    "default",
                ],
                "propagate": True,
            }
        },
    }

    # Redoc auth
    REDOC_USER: str
    REDOC_PASSWORD: str

    class Config:
        env_file = ".env"


class LocalSettings(Settings):
    __name__ = "local"


class StagingSettings(Settings):
    __name__ = "staging"


class TestingSettings(Settings):
    __name__ = "test"

    authjwt_secret_key: str = "test-secret"  # must be the same as main api
    ODOO_URL: str = "https://fakeurl.com"
    ODOO_DATABASE: str = "fakedb"
    ODOO_USER_ID: str = "12"
    ODOO_PASSWORD: str = "password"

    # Redoc auth
    REDOC_USER: str = "fakeuser"
    REDOC_PASSWORD: str = "fakepassword"
