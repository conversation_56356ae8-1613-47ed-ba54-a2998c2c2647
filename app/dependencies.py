import urllib.parse
from functools import lru_cache

import motor.motor_asyncio
from fastapi import Depends

from app.db.mongodb import MongoBackend
from app.settings import Settings
from conf import get_settings


class ConnectionWrapper:
    """Create a global object with cache in order to reuse connection pooling from mongodb"""

    connection = None

    @lru_cache()
    def mongo_client(self, mongo_db_uri):
        if not self.connection:
            self.connection = motor.motor_asyncio.AsyncIOMotorClient(mongo_db_uri, connect=False)
        return self.connection


connection_wrapper = ConnectionWrapper()


def mongo_instance(settings: Settings = Depends(get_settings)):
    """Global instance for mongo db connection"""
    mongo_db_uri = (  # NOQA: F821
        f"mongodb://{settings.MONGODB_USER}:{urllib.parse.quote_plus(settings.MONGODB_PASSWORD)}@{settings.MONGODB_URL}:27017/admin"
    )
    mongo_client = connection_wrapper.mongo_client(mongo_db_uri)
    return MongoBackend(mongo_client)
