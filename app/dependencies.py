from io import <PERSON><PERSON>

from fastapi import Depends
from google.cloud import storage
from network_clients.base.pubsub_client import PubSubClient
from network_clients.base.storage_client import StorageClient

from app.settings import Settings
from conf import get_settings


def storage_client(settings: Settings = Depends(get_settings)):
    """Global instance for storage client"""

    return storage.Client.from_service_account_json(
        settings.GOOGLE_CLOUD_JSON_ACCOUNT,
    )


class FastAPIStorageClient(StorageClient):
    """Make StorageClient compatible with FastAPI injecting the needed settings as dependency"""

    def __init__(self, settings: Settings = Depends(get_settings)):
        super(FastAPIStorageClient, self).__init__(
            service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

    def download_file(self, bucket_name: str, file_name: str) -> StringIO:
        """Override it to return a StringIO object compatible with pandas dataframe"""
        return StringIO(
            super(FastAPIStorageClient, self).download_file(bucket_name, file_name).getvalue().decode("utf-8")
        )


class FastAPIPubsubClient(PubSubClient):
    """Make Pubsubclient compatible with FastAPI injecting the needed settings as dependency"""

    def __init__(self, settings: Settings = Depends(get_settings)):
        super(FastAPIPubsubClient, self).__init__(
            service_account_json=settings.GOOGLE_CLOUD_JSON_ACCOUNT,
            google_cloud_project_name=settings.GOOGLE_CLOUD_PROJECT_NAME,
        )
