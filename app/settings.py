from pydantic import BaseSettings


class Settings(BaseSettings):
    ENVIRONMENT: str = None
    MONGODB_DATABASE: str = "moreapp"
    MONGODB_COLLECTION_1001: str = "madrid-nuestro-1001"
    MONGODB_COLLECTION_1002: str = "madrid-logwin-1002"
    MONGODB_COLLECTION_1003: str = "madrid-intelligent-1003"
    MONGODB_COLLECTION_1004: str = "madrid-lqs-1004"
    MONGODB_COLLECTION_1021: str = "barcelona-ascor-1021"
    MONGODB_COLLECTION_1031: str = "valencia-nuestro-1031"
    MONGODB_COLLECTION_1032: str = "valencia-vcd-1032"
    MONGODB_COLLECTION_1041: str = "malaga-nordcargo-1041"
    MONGODB_COLLECTION_1051: str = "sevilla-old-1051"
    MONGODB_COLLECTION_1052: str = "sevilla-suardiaz-1052"
    MONGODB_COLLECTION_2001: str = "lisboa-lqs-2001"
    MONGODB_COLLECTION_2002: str = "lisboa-thyman-2002"
    MONGODB_COLLECTION_2010: str = "oporto-pantoja-2010"
    MONGODB_COLLECTION_2011: str = "oporto-pantoja-2010"
    MONGODB_COLLECTION_3001: str = "paris-3001"
    MONGODB_COLLECTION_4000: str = "milano-4000"
    MONGODB_COLLECTION_5000: str = "queretaro-5000"

    COLLECTION_MAPPING = {
        "1001": MONGODB_COLLECTION_1001,
        "1002": MONGODB_COLLECTION_1002,
        "1003": MONGODB_COLLECTION_1003,
        "1004": MONGODB_COLLECTION_1004,
        "1021": MONGODB_COLLECTION_1021,
        "1031": MONGODB_COLLECTION_1031,
        "1032": MONGODB_COLLECTION_1032,
        "1041": MONGODB_COLLECTION_1041,
        "1051": MONGODB_COLLECTION_1051,
        "1052": MONGODB_COLLECTION_1052,
        "2001": MONGODB_COLLECTION_2001,
        "2002": MONGODB_COLLECTION_2002,
        "2010": MONGODB_COLLECTION_2010,
        "2011": MONGODB_COLLECTION_2011,
        "3001": MONGODB_COLLECTION_3001,
        "4000": MONGODB_COLLECTION_4000,
        "5000": MONGODB_COLLECTION_5000,
    }

    MONGODB_USER: str
    MONGODB_PASSWORD: str
    MONGODB_URL: str

    # Sentry configuration
    SENTRY_SDK_DSN: str

    # Default logging configuration
    DEFAULT_LOG = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "format": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
            }
        },
        "handlers": {
            "default": {
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
                "formatter": "default",
            },
        },
        "loggers": {
            "": {
                "level": "INFO",
                "handlers": [
                    "default",
                ],
                "propagate": True,
            }
        },
    }

    # Moreapp basic auth credentials
    MOREAPP_USERNAME = "<EMAIL>"
    MOREAPP_PASSWORD: str

    class Config:
        env_file = ".env"


class LocalSettings(Settings):
    ENVIRONMENT = "local"

    MONGODB_USER = "testwrite"
    MONGODB_PASSWORD = "1822_07@x!bu99R$"
    MONGODB_URL = "localhost"
    SENTRY_SDK_DSN: str = None


class TestingSettings(Settings):
    ENVIRONMENT = "test"

    MONGODB_USER: str = "user"
    MONGODB_PASSWORD: str = "password"
    SENTRY_SDK_DSN: str = None
    MOREAPP_PASSWORD: str = "password"
    MONGODB_URL: str = "localhost"


class ProductionSettings(Settings):
    ENVIRONMENT = "production"

    MONGODB_USER = "moreapp"
