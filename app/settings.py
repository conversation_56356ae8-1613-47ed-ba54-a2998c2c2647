from pydantic import BaseSettings


class Settings(BaseSettings):
    ENVIRONMENT: str = None
    CHARGEBEE_API_KEY_ES: str
    CHARGEBEE_API_KEY_FR: str
    CHARGEBEE_API_KEY_PT: str
    GOOGLE_CLOUD_JSON_ACCOUNT: str
    SENTRY_SDK_DSN: str
    VERSION: str
    CHARGEBEE_SITE_ES: str = "box2box"
    CHARGEBEE_SITE_FR: str = "box2boxfr"
    CHARGEBEE_SITE_PT: str = "box2boxpt"
    BUCKET_NAME: str = "box2box-intranet"
    BUCKET_FOLDER_NAME: str = "raw"
    GOOGLE_CLOUD_PROJECT_NAME: str = "box2box-cloud"

    # Authentication JWT
    authjwt_secret_key: str

    # Default logging configuration
    DEFAULT_LOG = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "format": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
            }
        },
        "handlers": {
            "default": {
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
                "formatter": "default",
            },
        },
        "loggers": {
            "": {
                "level": "INFO",
                "handlers": [
                    "default",
                ],
                "propagate": True,
            }
        },
    }

    class Config:
        env_file = ".env"


class LocalSettings(Settings):
    ENVIRONMENT = "local"

    authjwt_secret_key: str = "local-secret"  # must be the same as main api
    BACKEND_TOPIC_NAME: str = "gcf-webhooks-to-backend-microservices"
    VERSION = "1.0.0"


class TestingSettings(Settings):
    ENVIRONMENT = "test"

    CHARGEBEE_API_KEY_ES = "es"
    CHARGEBEE_API_KEY_FR = "fr"
    CHARGEBEE_API_KEY_PT = "pt"
    GOOGLE_CLOUD_JSON_ACCOUNT = "account"
    authjwt_secret_key = "test-secret"
    SENTRY_SDK_DSN = "http://localhost"
    VERSION = "test@0.0.1"
    BACKEND_TOPIC_NAME: str = "gcf-webhooks-to-backend-microservices"


class ProductionSettings(Settings):
    ENVIRONMENT = "production"

    BACKEND_TOPIC_NAME = "gcf-webhooks-to-backend-microservices-prod"
