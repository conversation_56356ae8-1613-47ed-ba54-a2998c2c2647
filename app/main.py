from fastapi import Depends, FastAPI, Request
from fastapi.openapi.utils import get_openapi
from fastapi_jwt_auth.exceptions import AuthJWTException
from starlette.responses import JSONResponse

from app.routers import docs, odoo
from conf import get_settings

# Disable swagger ui and redoc url to add custom authentication
app = FastAPI(docs_url=None, redoc_url=None, version="1.0.0")

# Inject settings dependencies to this router
app.include_router(docs.router)
app.include_router(odoo.router, dependencies=[Depends(get_settings)])


@app.exception_handler(AuthJWTException)
def authjwt_exception_handler(request: Request, exc: AuthJWTException):
    """Default response on authorization error"""
    return JSONResponse(status_code=exc.status_code, content={"detail": exc.message})


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Notifications Service",
        version=app.version,
        description="Microservice to notify/create external resources using third-party APIs",
        routes=app.routes,
    )

    # Custom documentation fastapi-jwt-auth
    headers = {
        "name": "Authorization",
        "in": "header",
        "required": True,
        "schema": {"title": "Authorization", "type": "string"},
    }

    # Get routes from index 4 because before that fastapi define router for /openapi.json, /redoc, /docs, etc
    # Get all router where operation_id is authorize
    router_authorize = [
        route for route in app.routes[2:] if route.operation_id == "authorize"
    ]

    for route in router_authorize:
        method = list(route.methods)[0].lower()
        try:
            # If the router has another parameter
            openapi_schema["paths"][route.path][method]["parameters"].append(headers)
        except Exception:
            # If the router doesn't have a parameter
            openapi_schema["paths"][route.path][method].update(
                {"parameters": [headers]}
            )

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi
