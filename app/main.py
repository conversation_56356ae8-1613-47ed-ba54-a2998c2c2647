import logging

from fastapi import Depends, FastAPI

from app.routers import moreapp
from conf import get_settings

# Disable swagger ui and redoc url to add custom authentication
app = FastAPI(docs_url=None, redoc_url=None, version="1.0.0")

# Inject sentry only in production environment
settings = get_settings()
if settings.ENVIRONMENT == "production":
    import sentry_sdk
    from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
    from sentry_sdk.integrations.logging import LoggingIntegration

    try:

        sentry_logging = LoggingIntegration(
            level=logging.WARNING,  # Capture warning and above as breadcrumbs
            event_level=logging.WARNING,  # Send no events from log messages
        )
        SENTRY_CONFIGURATION = {
            "dsn": settings.SENTRY_SDK_DSN,
            "traces_sample_rate": 0.25,
            "send_default_pii": True,
            "environment": settings.ENVIRONMENT,
            "integrations": [sentry_logging],
            "release": f"ws-service@{settings.VERSION}",
        }
        sentry_sdk.init(**SENTRY_CONFIGURATION)
        app.add_middleware(SentryAsgiMiddleware)
    except Exception:
        # pass silently if the Sentry integration failed
        pass

# Inject settings dependencies to this router
app.include_router(moreapp.router, dependencies=[Depends(get_settings)])
