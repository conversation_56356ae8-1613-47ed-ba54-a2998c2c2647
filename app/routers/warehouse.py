import logging
import time
import traceback
from datetime import datetime

import pytz
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi_jwt_auth import AuthJWT
from network_clients.base.base import ClientError
from starlette import status
from starlette.responses import JSONResponse

from app.dependencies import FastAPIPubsubClient, FastAPIStorageClient
from app.models.warehouse import (StatusChoices, WarehouseDifference,
                                  WarehouseResponse)
from app.parsers import run_discrepancies
from app.settings import Settings
from app.utils import transform_dataframe_to_memory_file
from conf import get_settings

# Instance the router
router = APIRouter()

logger = logging.getLogger(__name__)


@router.post("/run/", tags=["warehouse"], operation_id="run")
def perform_warehouse_differences(
    warehouse: WarehouseDifference,
    storage_client_instance: FastAPIStorageClient = Depends(FastAPIStorageClient),
    publisher_client_instance: FastAPIPubsubClient = Depends(FastAPIPubsubClient),
    settings: Settings = Depends(get_settings),
    Authorize: AuthJWT = Depends(),  # as doc stated
):
    """Run the warehouse differences and notify it to backend"""
    Authorize.jwt_required()

    # Initial data
    status_choice = StatusChoices.FAILED
    file_name = None
    result = None
    our_space = None
    warehouse_space = None
    error_reason = None

    try:
        response = run_discrepancies(warehouse, storage_client_instance, settings)

        if response.data is not None:
            # Upload to storage
            file_name = f"processed/{warehouse.warehouse_id}_{warehouse.date.strftime('%Y-%m')}.csv"
            memory_file = transform_dataframe_to_memory_file(response.data)
            storage_client_instance.upload_file(memory_file.getvalue(), settings.BUCKET_NAME, file_name)

            # Set status
            status_choice = StatusChoices.SUCCESS
            result = response.result
            our_space = response.our_space
            warehouse_space = response.warehouse_space
    except Exception as exc:
        error_reason = str(exc)
        logger.error(f"Error running warehouse differences for {warehouse.warehouse_id} due to: {exc}")
        logger.error(traceback.print_exc(chain=False))
    finally:
        # Notify to client through pub/sub message
        response = WarehouseResponse(
            id=warehouse.id,
            status=status_choice,
            processed_file=file_name,
            result=result,
            error_reason=error_reason,
            our_space=our_space,
            warehouse_space=warehouse_space,
        )
        data = jsonable_encoder(response)
        data["event_type"] = "warehouse_service"
        data.update(
            **{
                "event_type": "warehouse_service",
                "occurred_at": int(time.mktime(datetime.now().replace(tzinfo=pytz.utc).timetuple())),
            }
        )

        try:
            publisher_client_instance.request(
                topic=settings.BACKEND_TOPIC_NAME,
                data=data,
            )
        except ClientError as e:
            logger.error(
                f"Error publishing message into {settings.BACKEND_TOPIC_NAME} due to"
                f" {str(traceback.print_exc(chain=False))}"
            )
            raise HTTPException(
                status_code=299,
                detail=(
                    f"Error publishing message into {settings.BACKEND_TOPIC_NAME} due to `{str(e)}`. Please, check"
                    " logs."
                ),
            )

    # If everything goes well, return
    return JSONResponse(content=data, status_code=status.HTTP_200_OK)
