from io import StringIO

import pandas as pd
from network_clients.chargebee.client import ChargebeeClient


def get_chargebee_instance(site_country, settings):
    """Helper method to get the right chargebee instance given a country"""
    api_key = getattr(settings, f"CHARGEBEE_API_KEY_{site_country.upper()}")
    site = getattr(settings, f"CHARGEBEE_SITE_{site_country.upper()}")
    return ChargebeeClient(api_key=api_key, site=site)


def transform_dataframe_to_memory_file(dataframe: pd.DataFrame):
    """Transform pandas datafrome into StringIO"""

    memory_file = StringIO()
    dataframe.to_csv(memory_file, sep=",", index=False)

    return memory_file
