[tool.black]
line-length = 120
target-version = ['py38']
include = '\.pyi?$'
preview = true
enable-unstable-feature = ['string_processing']
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  # The following are specific to <PERSON>, you probably don't want those.
  | blib2to3
  | tests/data
  | profiling
)/
'''
[tool.vulture]
exclude = [
    "*/settings.py",
    "*/settings/*.py",
    "*/migrations/*.py",
    "*/deployment/**",
    "*/local_development/**",
]
ignore_decorators = [
    # Django
    "@receiver",
    "@register.filter",
    "@register.inclusion_tag",
    "@register.simple_tag",
    # django.contrib.admin
    "@admin.action",
    "@admin.display",
    "@admin.register",
    # pytest
    "@pytest.fixture",
]
ignore_names = [
    # Django
    "*Config", # AppConfig subclasses
    "*Middleware",
    "clean_*",
    "Meta",
    "urlpatterns",
    # django.contrib.admin
    "get_extra",
    "get_fieldsets",
    "has_add_permission",
    "has_change_permission",
    "has_delete_permission",
    "has_view_permission",
    "lookups",
]
make_whitelist = true
min_confidence = 80
paths = ["contracts", "core", "intranet", "moreapp", "payments", "riders", "users", "webhooks"]
sort_by_size = true
verbose = true

[tool.djlint]
profile="django"
ignore = "H021"
