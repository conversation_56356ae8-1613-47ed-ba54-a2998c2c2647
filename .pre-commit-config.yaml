fail_fast: false
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: check-yaml
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-json
      - id: check-toml
      - id: check-added-large-files
  - repo: https://gitlab.com/pycqa/flake8
    rev: 3.9.2
    hooks:
      - id: flake8
        additional_dependencies: ['flake8-print', 'flake8-walrus']
  - repo: https://github.com/psf/black
    rev: 21.8b0
    hooks:
      - id: black
        language_version: python3
  - repo: https://github.com/pycqa/isort
    rev: 5.9.3
    hooks:
      - id: isort
