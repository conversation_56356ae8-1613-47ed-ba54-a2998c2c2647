fail_fast: false
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-yaml
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-json
      - id: check-toml
      - id: check-added-large-files
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        additional_dependencies: ['flake8-print', 'flake8-walrus', 'flake8-bugbear', 'flake8-tuple']
  - repo: https://github.com/psf/black
    rev: 24.1.1
    hooks:
      - id: black
        language_version: python3
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
  - repo: https://github.com/djlint/djLint
    rev: v1.35.2
    hooks:
      - id: djlint-reformat-django
      - id: djlint-django
#  - repo: https://github.com/jendrikseipp/vulture
#    rev: 'v2.10'
#    hooks:
#      - id: vulture
