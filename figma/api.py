import requests

api_token = "figd_m_77GZCjLZSW2ObKtX7WAQNhpt_9YYhtLxdsZCBK"

# req = requests.request(
#     'GET',
#     headers={
#         "X-FIGMA-TOKEN": api_token,
#     },
#     url="https://api.figma.com/v1/files/Ke3RS59I3Rrivwb0rVrqkz"
# )
#
# print(req.json())
#
#
# req = requests.request(
#     'GET',
#     headers={
#         "X-FIGMA-TOKEN": api_token,
#     },
#     url="https://api.figma.com/v1/images/Ke3RS59I3Rrivwb0rVrqkz/?ids=102:58&format=svg"
# )


req = requests.request(
    'POST',
    headers={
        "X-FIGMA-TOKEN": api_token,
        "Content-Type": "application/json"
    },
    json={
        "event_type": "FILE_UPDATE",
        "endpoint": "https://8619-213-94-35-36.ngrok-free.app/updates",
        "passcode": "test",
        "team_id": "1016643891920416010"
    },
    url="https://api.figma.com/v2/webhooks"
)
print(req.content)
