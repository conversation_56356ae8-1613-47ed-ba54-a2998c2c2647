from allauth.account.models import EmailAddress
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import UserChangeForm
from django.contrib.auth.forms import \
    UserCreationForm as DjangoUserCreationForm
from django.forms import forms
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class UserCreationForm(DjangoUserCreationForm):
    class Meta:
        model = User
        fields = (
            "email",
            "first_name",
            "last_name",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["first_name"].required = True
        self.fields["last_name"].required = True

    def clean_email(self):
        email = self.cleaned_data["email"]
        return email.lower()

    def save(self, commit=True):
        user = super().save(commit=False)
        user.username = user.email
        user.save()
        EmailAddress.objects.create(user=user, email=user.email, primary=True)
        return user


class UserForm(UserChangeForm):
    """Form to skip requireness fields over User model"""

    def __init__(self, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)

        self.fields["alias"].required = False

    def clean_alias(self):
        alias = self.cleaned_data["alias"]

        if alias:
            obj = User.objects.filter(alias__iexact=alias).exclude(id=self.instance.pk)
            if obj:
                raise forms.ValidationError(
                    _("User with this alias already exists"), code="invalid", params={"alias": alias}
                )

        return alias
