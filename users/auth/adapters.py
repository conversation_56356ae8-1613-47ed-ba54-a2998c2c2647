import requests
from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.providers.google.views import \
    GoogleOAuth2Adapter as DefaultGoogleOAuth2Adapter
from allauth.socialaccount.providers.oauth2.client import OAuth2Error
from allauth.socialaccount.providers.slack.views import \
    SlackOAuth2Adapter as DefaultSlackOAuth2Adapter
from django.conf import settings
from django.contrib.auth import get_user_model
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django_adtuo_email.message import AdtuoEmailMessage

User = get_user_model()


class AdtuoAccountAdapter(DefaultAccountAdapter):
    """Helper class to send email using AdtuoEmailMessage if settings.ACCOUNT_EMAIL_VERIFICATION is optional.
    Used internally by django-allauth library
    """

    def render_mail(self, subject, template, email, context):
        msg = AdtuoEmailMessage(
            subject=f"{settings.ACCOUNT_EMAIL_SUBJECT_PREFIX}{subject}",
            to=[User.objects.customerio_recipient(model=User, email=email)],
            template=template,
            context=context,
        )
        return msg

    def send_mail(self, subject, template, email, context):
        msg = self.render_mail(subject, template, email, context)
        msg.send()

    def get_email_confirmation_url(self, emailconfirmation):
        return f"{settings.WEBAPP_URL}account-validation/{emailconfirmation.key}"

    def send_confirmation_mail(self, request, emailconfirmation, signup):
        """Send confirmation using custom template. user_name key will be email because in this stage, we don't
        have any first_name or something like that."""
        activate_url = self.get_email_confirmation_url(emailconfirmation)

        ctx = {
            "activate_url": activate_url,
        }

        if signup:
            subject = _("Confirm your account in Adtuo")
            email_template = "email_confirmation_signup"
        else:
            subject = _("Confirm your email")
            email_template = "email_confirmation"
            ctx["user_name"] = emailconfirmation.email_address.user.first_name

        self.send_mail(subject, email_template, emailconfirmation.email_address.email, ctx)

    def respond_email_verification_sent(self, request, user):
        """This method is needed to override because email verification path is inside users app with app_name"""
        return HttpResponseRedirect(reverse("users:account_email_verification_sent"))


class AdtuoSocialAccountAdapter(DefaultSocialAccountAdapter):
    """Helper class to point out to point out the position of the urls to allauth"""

    def get_connect_redirect_url(self, request, socialaccount):
        assert request.user.is_authenticated
        url = reverse("users:socialaccount_connections")
        return url


class GoogleOAuth2Adapter(DefaultGoogleOAuth2Adapter):
    """Helper class to override parse_token method in order to avoid set expires_at attribute in model to standardise
    every OAuth2 account."""

    def parse_token(self, data):
        token = super(GoogleOAuth2Adapter, self).parse_token(data)
        token.expires_at = None
        return token


class SlackOAuth2Adapter(DefaultSlackOAuth2Adapter):
    """Override default oauth2 adapter for Slack to update the access token url for v2.
    @see https://github.com/slackapi/node-slack-sdk/issues/1017
    """

    access_token_url = "https://slack.com/api/oauth.v2.access"

    def complete_login(self, request, app, token, **kwargs):
        """Override complete_login method to store webhook url"""

        response = kwargs.get("response")
        authed_user = response.get("authed_user", {})
        incoming_webhook = response.get("incoming_webhook", {})
        extra_data = self.get_data(authed_user.get("access_token")) if authed_user.get("access_token") else {}
        extra_data.update(
            **{
                "hook_url": incoming_webhook.pop("url", None),
                "team": response.get("team", None),
                "user": authed_user,
                "scope": response.get("scope", None),
                **incoming_webhook,
            }
        )

        return self.get_provider().sociallogin_from_response(request, extra_data)

    def get_data(self, token):
        """Override get_data to adapt the flow to v2"""

        resp = requests.get(self.identity_url, headers={"Authorization": f"Bearer {token}"})
        resp = resp.json()

        if not resp.pop("ok", False):
            raise OAuth2Error()

        return resp
