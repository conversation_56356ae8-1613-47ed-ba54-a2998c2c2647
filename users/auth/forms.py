from allauth.account.utils import _unicode_ci_compare, user_pk_to_url_str
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import \
    PasswordResetForm as DjangoPasswordResetForm

from core.emails.message import EmailMessage

if "allauth" in settings.INSTALLED_APPS:
    from allauth.account.forms import default_token_generator
else:
    from django.contrib.auth.tokens import default_token_generator

from django.utils.translation import gettext_lazy as _
from django.utils.translation import override

User = get_user_model()


class PasswordResetForm(DjangoPasswordResetForm):
    """Password reset form override in order to set custom url and fail reverse from named path"""

    def get_users(self, email):
        email_field_name = User.get_email_field_name()
        active_users = User._default_manager.filter(**{
            "%s__iexact" % email_field_name: email,
            "is_active": True,
            "backoffice_allowed": False,
            "is_rider": False,
        })
        return (
            u
            for u in active_users
            if u.has_usable_password() and _unicode_ci_compare(email, getattr(u, email_field_name))
        )

    def send_mail(self, user, context):
        with override(user.email_domain):
            message = EmailMessage(
                subject=_("{prefix}Recovery your password").format(prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX),
                to=[
                    user.email,
                ],
                template="password_reset_email",
                sender=user.email_domain,
                context=context,
            )
            message.send()

    def save(self, request, *args, **kwargs):
        """Generate a one-use only link for resetting password and send it to the user."""
        email = self.cleaned_data["email"]
        for user in self.get_users(email):
            uid = user_pk_to_url_str(user)
            token = default_token_generator.make_token(user)
            user_name = (user.first_name if user.backoffice_allowed else user.customer_first_name) or user.email

            context = {
                "url": f"{settings.WEBAPP_PUBLIC_URLS.get(user.email_domain, 'ES')}recover-password/{uid}/{token}/",
                "user_name": user_name,
            }
            self.send_mail(user, context)
