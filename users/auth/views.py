import logging
import traceback

from dj_rest_auth.views import <PERSON>ginView as DefaultLoginView
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response

from contracts.models import Contract
from core.serializers import EmptySerializer
from core.utils import get_openapi_error_response
from users.auth.serializers import FirebaseTokenSerializer
from users.serializers.user_serializers import (JWTCustomerSerializer,
                                                JWTIntranetSerializer,
                                                JWTRiderSerializer)
from users.tokens import firebase_token

logger = logging.getLogger(__name__)


def load_user_details_serializer(user):
    """Factory to load the right serializer according to the user type"""

    if user.is_rider:
        return JWTRiderSerializer
    elif user.backoffice_allowed:
        return JWTIntranetSerializer

    return JWTCustomerSerializer


class LoginView(DefaultLoginView):
    def get_response_serializer(self):
        return load_user_details_serializer(self.user)


class FirebaseTokenView(GenericAPIView):
    """Endpoints to get a fresh firebase token"""

    serializer_class = FirebaseTokenSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        operation_summary="Activate account",
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: FirebaseTokenSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error generating token for requested user"),
        },
    )
    def post(self, request, *args, **kwargs):
        try:
            token = firebase_token.create_token(
                claims={
                    "contracts": list(
                        Contract.objects.filter(user=self.request.user).values_list("contract_document_id", flat=True)
                    )
                }
            )

            return Response(FirebaseTokenSerializer({"token": token.decode()}).data, status=status.HTTP_200_OK)
        except Exception:
            logger.error(
                f"Error generating firebase token for user {self.request.user.id}: {traceback.print_exc(chain=False)}"
            )

        return Response(_("Error generating token for requested user"), status=status.HTTP_400_BAD_REQUEST)
