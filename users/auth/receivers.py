from allauth.socialaccount.signals import social_account_added
from django.dispatch import receiver


@receiver(social_account_added)
def create_third_party_integration(request, sociallogin, **kwargs):
    from organizations.models import (ALLOWED_THIRD_PARTY_INTEGRATIONS,
                                      ThirdPartyIntegration)

    if sociallogin.account.provider in ALLOWED_THIRD_PARTY_INTEGRATIONS:
        # Link automatically this social account as third party integration in the organization
        ThirdPartyIntegration.objects.create(organization=sociallogin.organization, social_account=sociallogin.account)


@receiver(social_account_added)
def create_business_accounts(request, sociallogin, **kwargs):
    from business.models import BusinessSocialAccount

    if sociallogin.account.provider in ["facebook", "google"]:
        # Link automatically this social account to existing businesses
        BusinessSocialAccount.objects.filter(uid=sociallogin.account.uid).update(social_account=sociallogin.account)

        # TODO: temporal fix
        # As we only have one facebook social account connected at the moment, will be linked to every
        # business social account iff user belongs to that organization
        # businesses = Business.objects.filter(organization__membership__user=sociallogin.account.user).exclude(
        #     businesssocialaccount__uid=sociallogin.account.uid
        # )
        #
        # # Add an entry to business social accounts
        # social_account_list = [
        #     BusinessSocialAccount(
        #         business=business,
        #         user=sociallogin.account.user,
        #         social_account=sociallogin.account,
        #         uid=sociallogin.account.uid,
        #     )
        #     for business in businesses
        # ]
        # if social_account_list:
        #     BusinessSocialAccount.objects.bulk_create(social_account_list)

        # TODO: call to async task to fetch historic investment
        # Get historic campaign investment
        # post_investment_asynchronously(sociallogin.account.socialtoken_set.first(),
        #                                request.user.email,
        #                                sociallogin.account.id,
        #                                sociallogin.account.date_joined.strftime('%Y-%m-%d %H:%M'),
        #                                request.user.id)
