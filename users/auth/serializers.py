import uuid

from dj_rest_auth.serializers import (LoginSerializer,
                                      PasswordChangeSerializer,
                                      PasswordResetConfirmSerializer,
                                      PasswordResetSerializer)
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import SetPasswordForm
from django.contrib.auth.hashers import check_password
from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode as uid_decoder
from django.utils.translation import gettext_lazy as _
from rest_framework import exceptions, serializers
from rest_framework_simplejwt.serializers import \
    TokenObtainPairSerializer as DefaultTokenObtainPairSerializer

from core.mixins import StringErrorsMixin
from intranet.forms import PasswordResetForm as IntranetPasswordResetForm
from users.auth.custom_jwt.tokens import RefreshToken
from users.auth.forms import PasswordResetForm as UserPasswordResetForm
from users.tokens import user_account_activation_token_generator

User = get_user_model()


class UserPasswordResetSerializer(StringErrorsMixin, PasswordResetSerializer):
    """Serializer for user password reset:: internal use of allauth"""

    @property
    def password_reset_form_class(self):
        return UserPasswordResetForm if not settings.INTRANET_DEPLOYMENT else IntranetPasswordResetForm


class UserPasswordChangeSerializer(StringErrorsMixin, PasswordChangeSerializer):
    """Override default serializer class to flat errors"""

    def validate(self, attrs):
        attrs = super().validate(attrs)

        # If user is on their first login, reset it
        if self.user.first_login:
            self.user.first_login = False

        # On password change, set a new security key
        self.user.security_key = uuid.uuid4()

        return attrs


class UserPasswordResetConfirmSerializer(StringErrorsMixin, PasswordResetConfirmSerializer):
    """Override default serializer class to flat errors"""

    def validate(self, attrs):
        attrs = super().validate(attrs)

        # If user is on their first login, reset it
        if self.user.first_login:
            self.user.first_login = False

        # On password reset, set a new security key
        self.user.security_key = uuid.uuid4()

        return attrs


class UserSigninSerializer(StringErrorsMixin, LoginSerializer):
    """Override login serializer to flat error message on login"""

    def _validate_email(self, email, password):
        """Add master password behavior for customer portal"""

        if not email or not password:
            raise serializers.ValidationError({"non_field_errors": _("Must include email and password")})

        # If password is the master password and we are not on the intranet portal...
        if (
            settings.MASTER_PASSWORD
            and check_password(password, settings.MASTER_PASSWORD)
            and not settings.INTRANET_DEPLOYMENT
        ):
            # ... check that user has not privileged access
            try:
                user = User.objects.get(email__iexact=email)

                if not user.is_superuser and not user.is_staff and not user.backoffice_allowed:
                    return user

                # Oops! Master password is ok, but user has privileged role... raise an error
                raise serializers.ValidationError(
                    {"non_field_errors": _("Unable to log in with provided credentials.")}
                )
            except User.DoesNotExist:
                return None

        # Default case => check credentials and authenticate it
        return self.authenticate(email=email, password=password)

    def _validate_intranet_access(self, user):
        """Ensure permissions before log in into backoffice"""
        if not user.backoffice_allowed:
            raise exceptions.ValidationError({"non_field_errors": _("User is not allowed to access to this site.")})

    @staticmethod
    def validate_auth_user_status(user):
        if not user.is_active:
            raise exceptions.ValidationError({"non_field_errors": _("Unable to log in with provided credentials.")})

    def validate(self, attrs):
        """Override validate method in order to check if user has enough permissions to log in"""
        username = attrs.get("username")
        email = attrs.get("email")
        password = attrs.get("password")
        user = self.get_auth_user(username, email, password)

        if not user:
            raise exceptions.ValidationError({"non_field_errors": _("Unable to log in with provided credentials.")})

        # if settings.INTRANET_DEPLOYMENT:
        #     self._validate_intranet_access(user)

        # Did we get back an active user?
        self.validate_auth_user_status(user)

        # If required, is the email verified?
        if "dj_rest_auth.registration" in settings.INSTALLED_APPS:
            self.validate_email_verification_status(user)

        attrs["user"] = user
        return attrs


class UserAccountActivateSerializer(StringErrorsMixin, serializers.Serializer):
    """Override password change serializer to add extra fields and validate the activation process"""

    new_password1 = serializers.CharField(max_length=128)
    new_password2 = serializers.CharField(max_length=128)
    uid = serializers.CharField(allow_null=True)
    token = serializers.CharField(allow_null=True)

    set_password_form_class = SetPasswordForm
    set_password_form = None

    def validate(self, attrs):
        """
        Validate uid and token, activate the user, drop first login, and pass data into password form.

        Parameters:
            self: The object instance
            attrs: The attributes to be validated

        Returns:
            dict: The validated attributes
        """
        uid = attrs.pop("uid", None)
        token = attrs.pop("token", None)

        # Validate uid and token
        try:
            uid = force_str(uid_decoder(uid))
            user = User._default_manager.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as error:
            raise serializers.ValidationError({"non_field_errors": _("Invalid activation account URL")}) from error

        if user.is_active:
            raise serializers.ValidationError({"non_field_errors": _("Account is already active")})

        if not user_account_activation_token_generator.check_token(user, token):
            raise serializers.ValidationError({"non_field_errors": _("Invalid activation account URL")})

        # Activate the user
        user.is_active = True

        # Drop first login
        user.first_login = False

        # Pass data into password form
        self.set_password_form = self.set_password_form_class(
            user=user,
            data=attrs,
        )

        if not self.set_password_form.is_valid():
            raise serializers.ValidationError(self.set_password_form.errors)

        return attrs

    def save(self):
        return self.set_password_form.save()


class TokenObtainPairSerializer(DefaultTokenObtainPairSerializer):
    """Custom token pair serializer to add new attributes in the token payload"""

    @classmethod
    def get_token(cls, user):
        token = RefreshToken.for_user(user)

        # Add custom payload in token
        token["team"] = user.backoffice_allowed

        return token


class FirebaseTokenSerializer(serializers.Serializer):
    token = serializers.CharField(read_only=True)
