from dj_rest_auth.registration.serializers import (RegisterSerializer,
                                                   VerifyEmailSerializer)
from dj_rest_auth.registration.views import RegisterView, VerifyEmailView
from dj_rest_auth.serializers import (JWTSerializer, LoginSerializer,
                                      PasswordResetConfirmSerializer,
                                      PasswordResetSerializer)
from dj_rest_auth.views import (LoginView, PasswordResetConfirmView,
                                PasswordResetView)
from django.urls import include, path
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.serializers import TokenObtainSerializer
from rest_framework_simplejwt.views import TokenRefreshView

from core.utils import (get_openapi_error_response, get_openapi_response,
                        get_openapi_tokens_response)

from .serializers import InvitedUserRegisterSerializer, UserSignupSerializer
from .views import (FacebookConnectView, GoogleConnectView,
                    InvitedRegisterView,
                    PasswordChangeWithTokenBlacklistingView, SlackConnectView,
                    SocialAccountDisconnectView, SocialAccountViewSet)

# For API documentation
decorated_register_view = swagger_auto_schema(
    method="post",
    operation_summary="Register new user",
    request_body=UserSignupSerializer,
    responses={
        status.HTTP_201_CREATED: JWTSerializer,
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(RegisterSerializer),
    },
)(RegisterView.as_view())

decorated_login_view = swagger_auto_schema(
    method="post",
    operation_summary="Login user",
    request_body=LoginSerializer,
    responses={
        status.HTTP_200_OK: JWTSerializer,
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(LoginSerializer),
    },
)(LoginView.as_view())

decorated_verifyemail_view = swagger_auto_schema(
    method="post",
    operation_summary="Verify Email",
    responses={
        status.HTTP_200_OK: get_openapi_response("ok", "detail"),
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(VerifyEmailSerializer),
    },
)(VerifyEmailView.as_view())

decorated_password_reset_view = swagger_auto_schema(
    method="post",
    operation_summary="Reset Password",
    responses={
        status.HTTP_200_OK: get_openapi_response("Password reset e-mail has been sent", "detail"),
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(PasswordResetSerializer),
    },
)(PasswordResetView.as_view())

decorated_password_reset_confirm_view = swagger_auto_schema(
    method="post",
    operation_summary="Reset Password confirm",
    responses={
        status.HTTP_200_OK: get_openapi_response("Password has been reset with the new password.", "detail"),
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(PasswordResetConfirmSerializer),
    },
)(PasswordResetConfirmView.as_view())

decorated_password_change_view = swagger_auto_schema(
    method="post",
    operation_summary="Password Change",
    responses={
        status.HTTP_200_OK: get_openapi_response("Password has been reset with the new password.", "detail"),
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(PasswordResetConfirmSerializer),
    },
)(PasswordChangeWithTokenBlacklistingView.as_view())

decorated_obtain_jwt_refresh_token = swagger_auto_schema(
    method="post",
    operation_summary="Refresh a given access token",
    responses={
        status.HTTP_200_OK: get_openapi_tokens_response(refresh=False),
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(TokenObtainSerializer),
    },
)(TokenRefreshView.as_view())

decorated_invited_register_view = swagger_auto_schema(
    method="post",
    operation_summary="Register an invited user",
    request_body=InvitedUserRegisterSerializer,
    responses={
        status.HTTP_200_OK: JWTSerializer,
        status.HTTP_400_BAD_REQUEST: get_openapi_error_response(InvitedUserRegisterSerializer),
    },
)(InvitedRegisterView.as_view())

router = DefaultRouter()
router.register("auth/socialaccounts", SocialAccountViewSet, basename="social_accounts")

urlpatterns = [
    # Basic urls for user signup, login, mail verification and password recovery
    path("auth/signup/", decorated_register_view, name="signup"),
    path("auth/login/", decorated_login_view, name="login"),
    path("auth/verify-email/", decorated_verifyemail_view, name="account_email_verification_sent"),
    path("auth/password/reset/", decorated_password_reset_view, name="password_reset"),
    path("auth/password/reset/confirm/", decorated_password_reset_confirm_view, name="password_reset_confirm"),
    path("auth/password/change/", decorated_password_change_view, name="password_change"),
    path("auth/refresh/", decorated_obtain_jwt_refresh_token, name="refresh_token"),
    path("auth/registration/invited/", decorated_invited_register_view, name="register_invited"),
    # Allauth
    path("accounts/", include("allauth.urls")),
    # SocialAccount connect
    path("auth/facebook/connect/", FacebookConnectView.as_view(), name="facebook_connect"),
    path("auth/google/connect/", GoogleConnectView.as_view(), name="google_connect"),
    path("auth/slack/connect/", SlackConnectView.as_view(), name="slack_connect"),
    # SocialAccount disconnect
    path(
        "auth/socialaccounts/<int:pk>/disconnect/",
        SocialAccountDisconnectView.as_view(),
        name="social_account_disconnect",
    ),
] + router.urls
