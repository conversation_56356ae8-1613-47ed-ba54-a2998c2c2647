from rest_framework import serializers
from rest_framework_simplejwt.settings import api_settings

from users.auth.custom_jwt.tokens import RefreshToken


class TokenRefreshSerializer(serializers.Serializer):
    refresh = serializers.CharField()
    access = serializers.ReadOnlyField()

    def validate(self, attrs):
        user_parameter_key = RefreshToken.get_user_parameter_key(attrs["refresh"])
        refresh = RefreshToken(attrs["refresh"], user_parameter_key=user_parameter_key)

        data = {"access": str(refresh.access_token)}

        if api_settings.ROTATE_REFRESH_TOKENS:
            if api_settings.BLACKLIST_AFTER_ROTATION:
                try:
                    # Attempt to blacklist the given refresh token
                    refresh.blacklist()
                except AttributeError:
                    # If blacklist app not installed, `blacklist` method will
                    # not be present
                    pass

            refresh.set_jti()
            refresh.set_exp()

            data["refresh"] = str(refresh)

        return data
