from allauth.socialaccount.models import SocialAccount as DefaultSocialAccount
from allauth.socialaccount.models import SocialToken as DefaultSocialToken
from django.conf import settings

from core.external_services.facebook.client import FacebookClient
from core.external_services.google.client import GoogleAdsClient
from users.auth.managers import TokenManager


class SocialAccount(DefaultSocialAccount):
    """Extend social account model adding new helper method without new attributes"""

    class Meta:
        proxy = True

    # TODO: temporarily disable cache to avoid inconsistencies on panel
    # @cached_model_property(cache_time_in_seconds=2629800)  # 1 month
    @property
    def facebook_pages(self):
        if self.provider != "facebook":
            return {}

        facebook_client = FacebookClient(sandbox_or_production=settings.ENVIRONMENT == "PROD")
        pages = facebook_client.get_pages_from_user(access_token=self.socialtoken_set.first().token)

        pages_metadata = {}
        for page in pages:
            pages_metadata[page["id"]] = {
                "category": page["category"],
                "id": page["id"],
                "name": page["name"],
                "picture": page["picture"]["data"]["url"],
                "can_advertise": "ADVERTISE" in page.get("tasks", []),
                "is_published": page["is_published"],
            }
        return pages_metadata

    # TODO: temporarily disable cache to avoid inconsistencies on panel
    # @cached_model_property(cache_time_in_seconds=********)  # 6 months
    @property
    def google_accounts(self):
        if self.provider != "google":
            return {}

        ads_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")
        accounts = ads_client.get_accounts_from_user(self.socialtoken_set.first().token_secret)

        accounts_metadata = {}
        for account in accounts:
            if account is not None:
                accounts_metadata[str(account.id)] = {
                    "customer_id": account.id,
                    "name": account.descriptive_name,
                    "is_test": account.test_account,
                    "is_manager": account.manager,
                    "currency_code": account.currency_code,
                    "time_zone": account.time_zone,
                }
        return accounts_metadata


class SocialToken(DefaultSocialToken):
    objects = TokenManager()

    class Meta:
        proxy = True
