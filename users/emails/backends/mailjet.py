import logging
from uuid import UUID

from django.core.mail.backends.base import BaseEmailBackend
from django.utils.encoding import force_str

from users.emails.mailjet import MessageSchema

logger = logging.getLogger(__name__)


class PubSubEmailBackend(BaseEmailBackend):
    """Agnostic backend to send mails through pub/sub messages calling transactional API"""

    def _clean_context(self, context):
        """
        Clean up context to make it JSON serializable
        """
        for key, value in context.items():
            if isinstance(value, UUID):
                context[key] = str(value)
        return context

    def send_messages(self, email_messages):
        for message in email_messages:
            data = {
                "subject": force_str(message.subject),
                "recipients": message.to,
                "template": message.template,
                "context": self._clean_context(message.context),
            }

            try:
                data = MessageSchema().load(data)
                MessageSchema.send_message(data)

                logger.info("Message Sent")
            except Exception as e:
                logger.error(f"Error sending message: {data}. Reason: {str(e)}")

        return len(email_messages)
