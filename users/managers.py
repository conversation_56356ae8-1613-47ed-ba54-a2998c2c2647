from django.contrib.auth.models import UserManager as DjangoUserManager


class CustomerioMixin:
    def customerio_recipients_from_user(self, email, invited_by=None):
        return (
            self.filter(email=email).values("id", "email").first()
            if invited_by is None
            else {"email": email, "id": invited_by.id}
        )

    def customerio_recipients_from_organization(self, organization_id, permissions, exclude):
        permissions = {permission: True for permission in permissions}

        return (
            self.filter(membership__organization__id=organization_id, **permissions)
            .values("id", "email")
            .exclude(email__in=exclude)
            .distinct()
        )

    def customerio_recipient(self, model, *args, **kwargs):
        """Main recipient to get customerio format"""
        model_name = model._meta.model.__name__.lower()
        assert model_name in ["user", "organization"]

        return getattr(self, f"customerio_recipients_from_{model_name}")(*args, **kwargs)


class UserManager(CustomerioMixin, DjangoUserManager):
    def _create_user(self, email, password, **extra_fields):
        if not email:
            raise ValueError("The given email must be set")
        email = self.normalize_email(email)

        user = self.model(username=email, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(email, password, **extra_fields)

    def get_by_natural_key(self, username):
        return self.get(**{self.model.USERNAME_FIELD: username.lower()})
