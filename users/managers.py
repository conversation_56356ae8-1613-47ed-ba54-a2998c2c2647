import datetime

import pytz
from django.contrib.auth.models import Group
from django.contrib.auth.models import UserManager as DjangoUserManager
from django.db import IntegrityError, models, transaction
from django.db.models import Prefetch
from django.db.models.utils import resolve_callables

from core.queryset import QuerySetMixin


class UserQueryset(models.QuerySet, QuerySetMixin):
    def _get_or_create(self, defaults=None, **kwargs):
        obj, created = self.select_for_update().get_or_create(defaults, **kwargs)
        return obj, created

    def _clean_defaults(self, defaults):
        """Drop email/username from defaults dict to avoid clashing with existing users"""
        defaults = defaults.copy()

        if "username" in defaults:
            del defaults["username"]

        if "email" in defaults:
            del defaults["email"]

        return defaults

    def _create_item(self, defaults, **kwargs):
        """Wrap create logic"""

        try:
            obj, created = self._get_or_create(defaults, **kwargs)
        except IntegrityError:
            # Drop email if is present in defaults and retry one more time
            defaults = self._clean_defaults(defaults)

            # Try again without username and email
            obj, created = self._get_or_create(defaults, **kwargs)

        return obj, created

    def _update_item(self, obj, defaults, force_replace, retry=False):
        """Wrap update business logic"""

        update_model = False
        for k, v in resolve_callables(defaults):
            # Avoid replacing if is not set explicitly or field is not filled
            if not self.is_field_filled(obj, k) or force_replace:
                setattr(obj, k, v)
                update_model = True

        if update_model:
            try:
                obj.save(using=self.db)
            except IntegrityError:
                if not retry:
                    return self._update_item(obj, self._clean_defaults(defaults), force_replace, retry=True)

        return obj, update_model

    def _fill_in_parent(self, obj, defaults):
        """If user has not a valid email filled in and parent is null, try to infer the parent automatically"""
        if not obj.email and "email" in defaults and not obj.parent:
            obj.parent_id = self.model.objects.filter(email=defaults["email"]).values_list("pk", flat=True).first()
            obj.save(using=self.db)

        return obj

    def update_or_create(self, defaults=None, force_replace=False, **kwargs):
        """
        Look up an object with the given kwargs, updating one with defaults
        if it exists, otherwise create a new one.
        Return a tuple (object, created), where created is a boolean
        specifying whether an object was created.
        Wrap Integrity error exception to retry it in order to avoid duplicate error for emails"""
        defaults = defaults or {}
        self._for_write = True
        with transaction.atomic(using=self.db):
            # Lock the row so that a concurrent update is blocked until
            # update_or_create() has performed its save.

            # Create the item
            obj, created = self._create_item(defaults, **kwargs)
            obj = self._fill_in_parent(obj, defaults)

            if created:
                return obj, created

            # Update the item
            obj, _ = self._update_item(obj, defaults, force_replace)

        return obj, created


class UserManager(DjangoUserManager):
    """Custom manager to override default manager in order to add normal and staff users using the email and or phone
    as username"""

    def get_queryset(self):
        return UserQueryset(self.model)

    def get(self, *args, **kwargs):
        return (
            super()
            .prefetch_related(
                Prefetch(
                    "groups",
                    queryset=Group.objects.all().only("name").distinct(),
                    to_attr="group_list",
                )
            )
            .get(*args, **kwargs)
        )

    def _create_user(self, email, password, **extra_fields):
        """Create the user using their phone number if email is not provided"""
        if not email:
            raise ValueError("The given email must be set")
        email = self.normalize_email(email)

        user = self.model(username=email, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)
        extra_fields.setdefault("backoffice_allowed", True)
        extra_fields.setdefault("date_joined_in_crm", datetime.datetime(2015, 1, 1, tzinfo=pytz.utc))
        extra_fields.setdefault("first_login", False)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(email, password, **extra_fields)

    def get_by_natural_key(self, username):
        return self.get(**{self.model.USERNAME_FIELD: username.lower()})

    def get_backoffice_users(self):
        return self.filter(is_active=True, backoffice_allowed=True).distinct()
