# Generated by Django 3.1 on 2021-04-14 11:23

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("socialaccount", "0003_extra_data_default_dict"),
        ("users", "0006_auto_20210304_1401"),
    ]

    operations = [
        migrations.CreateModel(
            name="SocialToken",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("socialaccount.socialtoken",),
        ),
        migrations.AlterField(
            model_name="user",
            name="avatar",
            field=models.TextField(blank=True, null=True, validators=[django.core.validators.URLValidator()]),
        ),
    ]
