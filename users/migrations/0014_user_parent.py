# Generated by Django 3.2.12 on 2022-04-18 09:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0013_user_moloni_id"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="parent",
            field=models.OneToOneField(
                blank=True,
                help_text="Handle third-party software duplicity on emails",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="parent_user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
