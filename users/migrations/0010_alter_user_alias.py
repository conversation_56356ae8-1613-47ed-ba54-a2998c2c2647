# Generated by Django 3.2 on 2021-04-27 18:24

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0009_merge_20210423_1535"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="alias",
            field=models.CharField(
                max_length=200,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        "^[a-zA-Z0-9]+(\\-[A-Za-z0-9]+)*(\\.[A-Za-z0-9]+)*$",
                        "Only alphanumeric characters or characters containing a period or hyphen are allowed.",
                    )
                ],
            ),
        ),
    ]
