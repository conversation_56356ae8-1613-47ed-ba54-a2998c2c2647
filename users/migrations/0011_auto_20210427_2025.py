# Generated by Django 3.2 on 2021-04-27 18:25

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0010_alter_user_alias"),
    ]

    operations = [
        migrations.RunSQL(
            sql="CREATE UNIQUE INDEX alias_case_insensitive_idx ON users_user(lower(alias));",
            reverse_sql="DROP INDEX IF EXISTS alias_case_insensitive_idx",
        )
    ]
