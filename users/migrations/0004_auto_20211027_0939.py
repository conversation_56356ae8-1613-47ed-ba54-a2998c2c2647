# Generated by Django 3.2.8 on 2021-10-27 07:39

import phonenumber_field.modelfields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0003_user_stripe_customer_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="is_active",
            field=models.BooleanField(
                default=False,
                help_text=(
                    "Designates whether this user should be treated as active. Unselect this instead of deleting"
                    " accounts."
                ),
                verbose_name="active",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone",
            field=phonenumber_field.modelfields.PhoneNumberField(
                max_length=128, null=True, region=None, verbose_name="Phone number"
            ),
        ),
    ]
