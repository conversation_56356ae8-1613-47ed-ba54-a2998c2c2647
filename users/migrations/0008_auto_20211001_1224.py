# Generated by Django 3.2.7 on 2021-10-01 12:24

from django.db import migrations


def add_initial_configuration(apps, schema_editor):
    Configuration = apps.get_model("users", "Configuration")

    # Only add if we don't have any configuration yet
    count = Configuration.objects.count()

    if count == 0:
        _ = Configuration.objects.create()


class Migration(migrations.Migration):
    dependencies = [
        ('users', '0007_configuration'),
    ]

    operations = [
        migrations.RunPython(add_initial_configuration, reverse_code=migrations.RunPython.noop),
    ]
