from datetime import timed<PERSON><PERSON>

from allauth.account.models import EmailAddress
from allauth.account.utils import setup_user_email
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.validators import validate_email
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.validators import UniqueValidator

from campaigns.utils.defaults import DEFAULT_KPIS
from core.mixins import StringErrorsMixin
from core.models import CampaignConfiguration
from organizations.models import Membership
from users.validators import debounce_validate_email

User = get_user_model()


class UserProfileCampaignSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to link campaign in user public profile to display in community"""

    class Meta:
        model = CampaignConfiguration
        fields = "__all__"
        extra_kwargs = {
            "business": {"help_text": _("Facebook business id"), "required": True, "allow_null": False},
            "campaign": {"help_text": _("Facebook campaign id"), "required": True, "allow_null": False},
            "comments_allow": {"default": True},
            "anonymize_business_name": {"default": False},
            "anonymize_campaign_name": {"default": False},
            "show_campaign_hierarchy": {"default": False},
            "show_campaign_chart": {"default": False},
            "user": {"read_only": True},
        }

    def validate(self, attrs):
        basic_kpis = attrs.get("basic_kpis", None)

        if not basic_kpis:
            attrs["basic_kpis"] = DEFAULT_KPIS

        attrs["user"] = self.context["user"]
        return attrs


class OrganizationsInUserSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get organizations linked to concrete user"""

    id = serializers.UUIDField(source="organization.id", read_only=True)
    name = serializers.CharField(source="organization.name", read_only=True)
    role = serializers.CharField(source="role.name", read_only=True)

    class Meta:
        model = Membership
        fields = ("id", "name", "role")


class UserSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Main user serializer for get responses"""

    email_valid = serializers.BooleanField(required=False, read_only=True)
    onboarding_setup = serializers.BooleanField(required=False, read_only=True)
    organizations = OrganizationsInUserSerializer(source="membership_set", read_only=True, many=True)

    class Meta:
        model = User
        exclude = (
            "username",
            "password",
            "last_login",
            "is_superuser",
            "is_staff",
            "is_active",
            "groups",
            "user_permissions",
        )
        extra_kwargs = {
            "alias": {
                "validators": [
                    UniqueValidator(
                        queryset=User.objects.all(), lookup="iexact", message=_("user with this alias already exists")
                    )
                ]
            }
        }


class UserConcreteSerializer(UserSerializer):
    """Main user serializer for concrete get responses"""

    hs_hmac = serializers.CharField(read_only=True)


class UserPublicProfileSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get public metadata from user"""

    class Meta:
        model = User
        exclude = (
            "username",
            "password",
            "last_login",
            "is_superuser",
            "is_staff",
            "is_active",
            "groups",
            "user_permissions",
        )


class ChangeEmailSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to change email for an already registered user"""

    email = serializers.EmailField(required=True, validators=[validate_email, debounce_validate_email])
    password = serializers.CharField(required=True)

    def validate_password(self, password):
        if not self.instance.check_password(password):
            raise serializers.ValidationError(_("Wrong password"))
        return password

    def validate_email(self, email):
        if User.objects.filter(email=email):
            raise serializers.ValidationError(_("An user with that email already exists."))
        return email.lower()

    def save(self, request):
        """Override default model save in order to:
        - Update email validation address from 'accounts' app. This is used by allauth third-party library.
         When an user signup, this email address must be updated in order to keep updated every relationship.
        """

        with transaction.atomic():
            try:
                email_address = EmailAddress.objects.get_for_user(self.instance, self.instance.email)
                email_address.change(request, self.validated_data["email"])
            except EmailAddress.DoesNotExist:
                EmailAddress.objects.add_email(
                    request=None, user=self.instance, email=self.validated_data["email"], signup=True, confirm=True
                )
                self.instance.email = self.validated_data["email"]

            self.instance.username = self.validated_data["email"]
            self.instance.save()

        # Update email in stripe for those organizations that user is owner
        self.instance.update_email_in_stripe()


class ResendEmailConfirmationSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to send the confirmation email again to user, just in case it is not send on user signup"""

    email = serializers.EmailField()

    def validate_email(self, email):
        if not self.instance.email == email:
            raise serializers.ValidationError(_("Email mismatch."))
        return email

    def _cannot_resend_email(self, email_address):
        return email_address.emailconfirmation_set.all() and (
            (
                email_address.emailconfirmation_set.last().created
                + timedelta(seconds=settings.TIME_TO_WAIT_TO_RESEND_EMAIL_CONFIRMATION_IN_SECONDS)
            )
            > timezone.now()
        )

    def save(self, request):
        # If email doesn't exists, create it
        try:
            email_address = self.instance.emailaddress_set.get(email=self.instance.email)
        except EmailAddress.DoesNotExist:
            email_address = setup_user_email(request, self.instance, [])

        if self._cannot_resend_email(email_address):
            raise serializers.ValidationError(
                {"non_field_errors": _("Please wait for at least 1 minute before requesting another email.")}
            )

        email_address.send_confirmation(request=request, signup=True)
