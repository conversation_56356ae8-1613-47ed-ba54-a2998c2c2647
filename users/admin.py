from django.contrib import admin
from django.contrib.auth import get_user_model
from django.contrib.auth.admin import UserAdmin as DjangoUserAdmin
from django.db import transaction
from django.db.models import Q
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.token_blacklist.models import (BlacklistedToken,
                                                             OutstandingToken)

from users.forms import UserCreationForm, UserForm

User = get_user_model()

# Override blacklisted app because default admin view don't allow to delete users from admin panel
# https://github.com/SimpleJWT/django-rest-framework-simplejwt/issues/266
admin.site.unregister(OutstandingToken)


@admin.register(OutstandingToken)
class OutstandingTokenAdmin(admin.ModelAdmin):
    list_display = ("jti", "user", "created_at", "expires_at")
    raw_id_fields = ("user",)
    readonly_fields = (
        "created_at",
        "expires_at",
        "jti",
        "user",
    )

    def has_add_permission(self, request):
        return True

    def has_delete_permission(self, request, obj=None):
        return True


@admin.register(User)
class UserAdmin(DjangoUserAdmin):
    add_form = UserCreationForm
    form = UserForm
    list_display = (
        "email",
        "first_name",
        "last_name",
        "date_joined",
        "email_valid",
        "facebook",
        "has_avatar",
        "alias",
    )
    ordering = ("-date_joined",)

    def email_valid(self, instance):
        return instance.email_valid

    email_valid.boolean = True

    def has_avatar(self, instance):
        return instance.avatar is not None

    has_avatar.boolean = True

    def facebook(self, instance):
        return instance.socialaccount_set.filter(provider="facebook").exists()

    facebook.boolean = True

    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("email", ("first_name", "last_name"), "password1", "password2"),
            },
        ),
    )

    fieldsets = (
        (
            _("Personal info"),
            {
                "fields": (
                    ("first_name", "last_name"),
                    "email",
                )
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "date_joined")}),
        (
            _("Social"),
            {
                "fields": (
                    "alias",
                    "avatar_image",
                    "link",
                    "description",
                )
            },
        ),
        (_("Experience Level"), {"fields": ("in_beta_program",)}),
        (_("Configuration"), {"fields": ("notify_campaigns", "notify_comments")}),
        (_("Permissions"), {"fields": ("is_active",)}),
        (
            _("Django Admin Permissions"),
            {"classes": ("collapse",), "fields": ("is_staff", "is_superuser", "groups", "user_permissions")},
        ),
        (
            None,
            {
                "fields": (
                    "username",
                    "password",
                )
            },
        ),
    )

    readonly_fields = ("avatar_image",)

    def avatar_image(self, instance):
        if instance.avatar:
            return mark_safe(
                f'<a href="{instance.avatar}" target=_blank> <img src="{instance.avatar}" width="150px"> </a>\n'
            )

    avatar_image.allow_tags = True
    avatar_image.short_description = _("Avatar")

    def save_model(self, request, obj, form, change):
        with transaction.atomic():
            # Save the model first
            super(UserAdmin, self).save_model(request, obj, form, change)

            # Declare every user token as outdated if user is in beta program
            if "in_beta_program" in form.changed_data or "is_staff" in form.changed_data:
                user_token_list = [
                    BlacklistedToken(token=token)
                    for token in obj.outstandingtoken_set.filter(
                        ~Q(id__in=BlacklistedToken.objects.filter(token__user=obj).values("token_id"))
                    )
                ]

                # Finally, if we have token to blacklist, add into blacklist model in bulk fashion
                if user_token_list:
                    BlacklistedToken.objects.bulk_create(user_token_list)
