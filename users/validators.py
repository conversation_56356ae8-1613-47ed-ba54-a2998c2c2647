import logging

import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.deconstruct import deconstructible
from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


@deconstructible
class DebounceEmailValidator:
    message = _("Enter a valid email address.")
    code = "invalid"

    def __init__(self, message=None, code=None):
        if message is not None:
            self.message = message
        if code is not None:
            self.code = code

    def __call__(self, value):
        value = force_str(value)

        valid, message = self.validate_address(value)

        if not valid:
            raise ValidationError(message, code=self.code)

    def validate_address(self, email):
        # if settings.ENVIRONMENT != "PROD":
        #     return True, ""

        # Temporal: if environment is != prod or staging, skip validation
        if settings.ENVIRONMENT not in ["PROD", "STAGING"]:
            return True, ""

        response = requests.get(
            "https://api.debounce.io/v1/",
            params={"api": settings.DEBOUNCE_API_KEY, "email": email},
        )

        if response.ok:
            response_body = response.json()
            if response_body.get("debounce", {}).get("send_transactional") == "0":
                return False, _("Email validation error, email not valid.")
        else:
            logger.warning(f"Debounce connect exception. Status: {response.status_code} Content: {response.content}")

        return True, ""


debounce_validate_email = DebounceEmailValidator()


class MailgunEmailValidator(object):
    pass
