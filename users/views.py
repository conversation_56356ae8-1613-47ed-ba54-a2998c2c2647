# Create your views here.
from django.contrib.auth import get_user_model
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from core.utils import get_openapi_error_response, get_openapi_response
from users.serializers import (ChangeEmailSerializer,
                               ResendEmailConfirmationSerializer,
                               UserConcreteSerializer,
                               UserProfileCampaignSerializer,
                               UserPublicProfileSerializer, UserSerializer)

User = get_user_model()


@method_decorator(
    name="list",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="create",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="update",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
@method_decorator(
    name="destroy",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
class UserViewSet(viewsets.ModelViewSet):
    """User app main entrypoint for urls"""

    serializer_class = UserSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = User.objects.all()

    def get_queryset(self):
        queryset = super(UserViewSet, self).get_queryset()
        return queryset.filter(pk=self.request.user.pk)

    def get_serializer_class(self):
        if self.action == "retrieve":
            return UserConcreteSerializer
        return self.serializer_class

    @swagger_auto_schema(
        operation_summary="Change Email",
        request_body=ChangeEmailSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Email changed"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(ChangeEmailSerializer, "Error"),
        },
    )
    @action(methods=["post"], detail=True)
    def change_email(self, request, pk=None):
        user = self.get_object()
        serializer = ChangeEmailSerializer(user, data=request.data)
        if serializer.is_valid():
            serializer.save(request)
            return Response({"status": _("Email changed")})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Resend Confirmation Email",
        request_body=ResendEmailConfirmationSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Email confirmation sent"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(ResendEmailConfirmationSerializer, "Error"),
        },
    )
    @action(methods=["post"], detail=True)
    def resend_email_confirmation(self, request, pk=None):
        user = self.get_object()
        serializer = ResendEmailConfirmationSerializer(user, data=request.data)
        if serializer.is_valid():
            serializer.save(request)
            return Response({"status": _("Email confirmation sent")})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Read public profile by id",
        responses={
            status.HTTP_200_OK: UserPublicProfileSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(UserPublicProfileSerializer, "Error"),
        },
    )
    @action(detail=True, methods=["get"], url_path="public-profile", permission_classes=(permissions.AllowAny,))
    def get_public_profile(self, request, pk=None):
        user = self.get_object()

        user_serializer = UserPublicProfileSerializer(user)

        return Response(user_serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Add a campaign to user public profile",
        operation_description="Add a campaign to user profile and make publicly available",
        request_body=UserProfileCampaignSerializer,
        responses={
            status.HTTP_200_OK: UserProfileCampaignSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(UserProfileCampaignSerializer, "Error"),
        },
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="make-campaign-public",
        permission_classes=(permissions.IsAuthenticated,),
    )
    def make_campaign_public(self, request, pk=None):
        """Mark a campaign as public to display in user public profile"""
        user = self.get_object()

        serializer = UserProfileCampaignSerializer(
            data=request.data,
            context={
                "user": user,
            },
        )

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Avoid mark public a campaign what has been already marked public
        if user.campaignconfiguration_set.filter(
            campaign=serializer.validated_data["campaign"], business=serializer.validated_data["business"]
        ).exists():

            return Response({"campaign": _("Campaign is already marked as public")}, status=status.HTTP_400_BAD_REQUEST)

        # Mark this campaign as public
        instance = serializer.save()

        return Response(UserProfileCampaignSerializer(instance).data, status=status.HTTP_200_OK)


class UserPublicProfileViewSet(viewsets.ModelViewSet):
    """User view to filter by alias"""

    serializer_class = UserSerializer
    queryset = User.objects.all()
    lookup_url_kwarg = "alias"
    lookup_field = "alias__iexact"

    @swagger_auto_schema(
        operation_summary="Read public profile by Alias",
        responses={
            status.HTTP_200_OK: UserPublicProfileSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(UserPublicProfileSerializer, "Error"),
        },
    )
    @action(detail=True, methods=["get"], url_path="public-alias", permission_classes=(permissions.AllowAny,))
    def get_public_profile(self, request, alias=None, pk=None):
        user = self.get_object()

        user_serializer = UserPublicProfileSerializer(user)
        return Response(user_serializer.data, status=status.HTTP_200_OK)
