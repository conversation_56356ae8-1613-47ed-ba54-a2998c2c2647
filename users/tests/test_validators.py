from dataclasses import dataclass
from unittest.mock import Mock, patch

import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _
from pytest import mark

from organizations.models import Organization
from organizations.tokens import user_invite_token_generator
from users.auth.serializers import InvitedUserRegisterSerializer
from users.validators import debounce_validate_email

pytestmark = mark.django_db
User = get_user_model()


@pytest.fixture
def user():
    def user(email):
        return User.objects.create_user(first_name="namy", last_name="lasty", email=email, password="passy")

    return user


@pytest.fixture(autouse=True)
def organization():
    yield Organization.objects.create(
        name="Test Organization",
        type_organization="business",
    )


@dataclass
class Request:
    user: str


class TestMailgunValidator:
    def test_validation_ok(self):
        with patch("users.validators.requests") as mock_mailgun_request:
            mailgun_response = Mock()
            mailgun_response.ok = True
            mailgun_response.json.return_value = {
                "address": "<EMAIL>",
                "did_you_mean": None,
                "is_disposable_address": False,
                "is_role_address": False,
                "is_valid": True,
                "mailbox_verification": "true",
                "parts": {"display_name": None, "domain": "fuck.com", "local_part": "foo"},
                "reason": None,
            }
            mock_mailgun_request.get.return_value = mailgun_response

            # No validation error
            debounce_validate_email("<EMAIL>")

    def test_validation_disposable_address(self):
        with patch("users.validators.requests") as mock_mailgun_request:
            mailgun_response = Mock()
            mailgun_response.ok = True
            mailgun_response.json.return_value = {
                "address": "<EMAIL>",
                "did_you_mean": None,
                "is_disposable_address": True,
                "is_role_address": False,
                "is_valid": True,
                "mailbox_verification": "true",
                "parts": {"display_name": None, "domain": "fuck.com", "local_part": "foo"},
                "reason": None,
            }
            mock_mailgun_request.get.return_value = mailgun_response

            with pytest.raises(ValidationError) as error:
                debounce_validate_email("<EMAIL>")

            assert str(error.value.message) == _("Disposable email address.")

    def test_validation_mailbox_verification_failed(self):
        with patch("users.validators.requests") as mock_mailgun_request:
            mailgun_response = Mock()
            mailgun_response.ok = True
            mailgun_response.json.return_value = {
                "address": "<EMAIL>",
                "did_you_mean": None,
                "is_disposable_address": False,
                "is_role_address": False,
                "is_valid": True,
                "mailbox_verification": "false",
                "parts": {"display_name": None, "domain": "fuck.com", "local_part": "foo"},
                "reason": None,
            }
            mock_mailgun_request.get.return_value = mailgun_response

            with pytest.raises(ValidationError) as error:
                debounce_validate_email("<EMAIL>")

            assert str(error.value.message) == _(
                "Mailbox verification failed. Please make sure your email address is valid."
            )

    def test_validation_not_valid(self):
        with patch("users.validators.requests") as mock_mailgun_request:
            mailgun_response = Mock()
            mailgun_response.ok = True
            mailgun_response.json.return_value = {
                "address": "<EMAIL>",
                "did_you_mean": None,
                "is_disposable_address": False,
                "is_role_address": False,
                "is_valid": False,
                "mailbox_verification": "true",
                "parts": {"display_name": None, "domain": "fuck.com", "local_part": "foo"},
                "reason": None,
            }
            mock_mailgun_request.get.return_value = mailgun_response

            with pytest.raises(ValidationError) as error:
                debounce_validate_email("<EMAIL>")

            assert str(error.value.message) == _(
                "Mailbox verification failed. Please make sure your email address is valid."
            )

    def test_validation_not_ok(self):
        with patch("users.validators.requests") as mock_mailgun_request:
            mailgun_response = Mock()
            mailgun_response.ok = False
            mailgun_response.json.return_value = {
                "address": "<EMAIL>",
                "did_you_mean": None,
                "is_disposable_address": False,
                "is_role_address": False,
                "is_valid": False,
                "mailbox_verification": "true",
                "parts": {"display_name": None, "domain": "fuck.com", "local_part": "foo"},
                "reason": None,
            }
            mock_mailgun_request.get.return_value = mailgun_response

            debounce_validate_email("<EMAIL>")


class TestUserInvitationValidator:
    def test_validate_invitation_bad_organization(self):
        object_id = urlsafe_base64_encode(force_bytes("872d7389-be70-4f03-9d17-ade1668a731a"))
        token = user_invite_token_generator.make_token(object_id, "<EMAIL>")
        serializer = InvitedUserRegisterSerializer(
            data={
                "email": "<EMAIL>",
                "password": "cliente12345",
                "oid": object_id,
                "token": token,
            }
        )
        assert not serializer.is_valid()
        assert serializer.errors == {"details": _("Invalid register URL.")}

    def test_validate_invitation_bad_email(self, organization):
        organization_id = organization.id
        object_id = urlsafe_base64_encode(force_bytes(organization_id))
        token = user_invite_token_generator.make_token(object_id, "<EMAIL>")
        serializer = InvitedUserRegisterSerializer(
            data={
                "email": "<EMAIL>",
                "password": "cliente12345",
                "oid": object_id,
                "token": token,
            }
        )
        assert not serializer.is_valid()
        assert serializer.errors == {"details": _("Invalid register URL.")}

    def test_validate_invitation_exist(self, organization):
        organization_id = organization.id
        object_id = urlsafe_base64_encode(force_bytes(organization_id))
        token = user_invite_token_generator.make_token(object_id, "<EMAIL>")
        serializer = InvitedUserRegisterSerializer(
            data={
                "email": "<EMAIL>",
                "password": "cliente12345",
                "oid": object_id,
                "token": token,
            }
        )
        assert not serializer.is_valid()
        assert serializer.errors == {"details": _("Invitation does not exists or was removed.")}
