from unittest.mock import patch

import pytest
import requests
from allauth.socialaccount.providers.oauth2.client import OAuth2Error
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.contrib.sites.models import Site
from django.core import mail
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _
from pytest import mark
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.token_blacklist.models import (BlacklistedToken,
                                                             OutstandingToken)

from organizations.models import Membership, Organization, UserInvitation
from organizations.tokens import user_invite_token_generator

pytestmark = mark.django_db
User = get_user_model()


@pytest.fixture
def client():
    client = APIClient()
    yield client


@pytest.fixture(autouse=True)
def create_social_providers():
    current_site = Site.objects.get_current()
    current_site.socialapp_set.create(
        provider="facebook",
        name="facebook",
        client_id="**********",
        secret="**********",
    )

    current_site.socialapp_set.create(
        provider="google",
        name="google",
        client_id="0000054",
        secret="54849415",
    )


@pytest.fixture(autouse=True)
def organization():
    yield Organization.objects.create(
        name="Test Organization",
        type_organization="business",
    )


@pytest.fixture(autouse=True)
def invitation_admin(organization):
    yield UserInvitation.objects.create(
        email="<EMAIL>",
        organization=organization,
        role=Group.objects.filter(name="admin").first(),
    )


class MockResponse:
    """Helper  class to mock with parameters requests calls"""

    def __init__(self, json_data, status_code=200):
        self.json_data = json_data
        self.status_code = status_code

    def json(self):
        return self.json_data

    def raise_for_status(self):
        return True


class TestViews:
    def test_register_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            fake_response = ["access_token", "refresh_token", "user"]

            response = client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            keys_response = list(response.json().keys())
            assert response.status_code == 201
            assert keys_response == fake_response

    def test_register_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response = client.post("/auth/signup/", data={"email": "<EMAIL>"})

            assert response.status_code == 400
            assert "password" in response.json()

    def test_login_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            # Register user to check login afterwards
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "cliente12345"},
            )
            keys_response = list(response.json().keys())
            assert response.status_code == 200
            assert keys_response == ["access_token", "refresh_token", "user"]

    def test_login_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            # Register user to check login afterwards
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "error_pass"},
            )
            assert response.status_code == 400
            assert "non_field_errors" in response.json()

    def test_verify_email_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
            fake_token = mail.outbox[0].context["activate_url"].split("/")[-1]

            response = client.post("/auth/verify-email/", data={"key": fake_token})

            assert response.status_code == 200
            assert "detail" in response.json()

    def test_verify_email_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            response = client.post("/auth/verify-email/", data={"key": "error_key"})
            assert response.status_code == 404
            assert "detail" in response.json()

    def test_password_reset_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            response = client.post(
                "/auth/password/reset/",
                data={"email": "<EMAIL>"},
            )

            assert response.status_code == 200
            assert "detail" in response.json()

    def test_password_reset_confirm_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            client.post(
                "/auth/password/reset/",
                data={"email": "<EMAIL>"},
            )

            url_recover_password = mail.outbox[1].context["url"].split("/")
            token = url_recover_password[-2]
            uid = url_recover_password[-3]

            response = client.post(
                "/auth/password/reset/confirm/",
                data={
                    "new_password1": "usuario12345",
                    "new_password2": "usuario12345",
                    "uid": uid,
                    "token": token,
                },
            )

            assert response.status_code == 200
            assert "detail" in response.json()

    def test_password_reset_confirm_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})

            client.post(
                "/auth/password/reset/",
                data={"email": "<EMAIL>"},
            )

            url_recover_password = mail.outbox[1].context["url"].split("/")
            token = url_recover_password[-2]
            uid = url_recover_password[-3]

            response = client.post(
                "/auth/password/reset/confirm/",
                data={
                    "new_password1": "usuario12345",
                    "new_password2": "error_pass",
                    "uid": uid,
                    "token": token,
                },
            )

            assert response.status_code == 400
            assert "new_password2" in response.json()

    def test_password_change_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response_sign_up = client.post(
                "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
            ).json()
            access_token = response_sign_up["access_token"]
            refresh_token = response_sign_up["refresh_token"]

            # Check that refresh_token is valid
            valid_refresh_token = OutstandingToken.objects.filter(token=refresh_token).first()
            assert BlacklistedToken.objects.filter(token=valid_refresh_token).exists() is False

            # Set credentials as noted in https://www.django-rest-framework.org/api-guide/testing/#credentialskwargs
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
            response = client.post(
                "/auth/password/change/",
                data={"old_password": "cliente12345", "new_password1": "user1234", "new_password2": "user1234"},
            )

            assert response.status_code == 200
            assert "detail" in response.json()

            # Check that refresh_token is blacklisted
            valid_refresh_token.refresh_from_db()
            assert BlacklistedToken.objects.filter(token=valid_refresh_token).exists() is True

    def test_password_change_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response_sign_up = client.post(
                "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
            )
            access_token = response_sign_up.json()["access_token"]

            # Set credentials as noted in https://www.django-rest-framework.org/api-guide/testing/#credentialskwargs
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

            response = client.post(
                "/auth/password/change/",
                data={"old_password": "error_pass", "new_password1": "user1234", "new_password2": "user1234"},
            )

            assert response.status_code == 400
            assert "old_password" in response.json()

    def test_refresh_token_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response_sign_up = client.post(
                "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
            )
            refresh_token = response_sign_up.json()["refresh_token"]

            response = client.post("/auth/refresh/", data={"refresh": refresh_token})
            assert response.status_code == 200
            assert "access" in response.json()

    def test_refresh_token_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response_sign_up = client.post(
                "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
            )
            refresh_token = response_sign_up.json()["access_token"]

            response = client.post("/auth/refresh/", data={"refresh": refresh_token})
            data = response.json()

            assert response.status_code == 401
            assert "detail" in data
            assert data["code"] == "token_not_valid"

    def test_facebook_connect_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            with patch("requests.get", spec=requests.get) as mock_get:
                mock_get.return_value = MockResponse(
                    json_data={"id": "facebook_id", "access_token": "facebook_access_token"}
                )
                response_sign_up = client.post(
                    "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
                )
                access_token = response_sign_up.json()["access_token"]

                client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
                response = client.post("/auth/facebook/connect/", data={"access_token": "fake_token"})

                user = User.objects.get(id=response.json()["user"]["pk"])
                assert response.status_code == 200
                assert user.socialaccount_set.exists()
                assert user.socialaccount_set.first().provider == "facebook"

    def test_facebook_connect_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            with patch("requests.get", spec=requests.get) as mock_get:
                mock_get.side_effect = OAuth2Error("Error calling to facebook connect")
                response_sign_up = client.post(
                    "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
                )
                response_json = response_sign_up.json()
                access_token = response_json["access_token"]
                user = User.objects.get(id=response_json["user"]["pk"])

                client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
                response = client.post("/auth/facebook/connect/", data={"access_token": "fake_token"})
                response_json = response.json()

                assert response.status_code == 400
                assert response_json == {
                    "details": _("Error retrieving access token. Maybe the access_token is outdated or wrong.")
                }
                assert not user.socialaccount_set.exists()

    def test_google_connect_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            with patch("requests.get", spec=requests.get) as mock_get:
                mock_get.return_value = MockResponse(
                    json_data={"id": "google_id", "access_token": "google_access_token"}
                )
                response_sign_up = client.post(
                    "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
                )
                access_token = response_sign_up.json()["access_token"]

                client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
                response = client.post("/auth/google/connect/", data={"access_token": "fake_token"})

                user = User.objects.get(id=response.json()["user"]["pk"])
                assert response.status_code == 200
                assert user.socialaccount_set.exists()
                assert user.socialaccount_set.first().provider == "google"

    def test_google_connect_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            with patch("requests.get", spec=requests.get) as mock_get:
                mock_get.side_effect = OAuth2Error("Error calling to google connect")
                response_sign_up = client.post(
                    "/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"}
                )
                response_json = response_sign_up.json()
                access_token = response_json["access_token"]
                user = User.objects.get(id=response_json["user"]["pk"])

                client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
                response = client.post("/auth/google/connect/", data={"access_token": "fake_token"})
                response_json = response.json()

                assert response.status_code == 400
                assert response_json == {"details": _("Error retrieving access token. Maybe the code is outdated.")}
                assert not user.socialaccount_set.exists()


class TestInvitedRegisterView:
    def test_registration_invited(self, client, organization, invitation_admin):
        email = "<EMAIL>"
        organization_id = organization.id
        object_id = urlsafe_base64_encode(force_bytes(organization_id))
        token = user_invite_token_generator.make_token(object_id, email)
        response = client.post(
            "/auth/registration/invited/",
            data={"email": email, "password": "cliente12345", "token": token, "oid": object_id},
        )
        membership = Membership.objects.filter(organization=organization_id).first()
        assert response.status_code == status.HTTP_201_CREATED
        assert response.json()["user"]["email"] == email
        assert response.json()["user"]["pk"] == str(membership.user.id)
        assert membership.organization == organization
        assert membership.role.name == "admin"
