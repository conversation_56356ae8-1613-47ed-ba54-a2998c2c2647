import json
from unittest.mock import patch

import pytest
from django.contrib.auth import get_user_model
from django.core import mail
from django.test import override_settings
from django.utils.translation import gettext_lazy as _
from pytest import mark
from rest_framework import status
from rest_framework.test import APIClient

pytestmark = mark.django_db
User = get_user_model()


@pytest.fixture
def client():
    client = APIClient()
    yield client


@pytest.fixture(autouse=True)
def user():
    yield User.objects.create_user(
        first_name="namy",
        last_name="lasty",
        email="<EMAIL>",
        password="passy",
        alias="aliasy",
        date_joined="2021-03-14T20:26:29.228002+01:00",
    )


@pytest.fixture(autouse=True)
def user_not_visibility():
    yield User.objects.create_user(
        first_name="namy",
        last_name="lasty",
        email="<EMAIL>",
        password="passy",
        alias="aliasy2",
        date_joined="2021-03-14T20:26:29.228002+01:00",
    )


class TestUserViewSet:
    def test_change_email_ok(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response = client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
            user_uuid = response.json()["user"]["pk"]

            assert response.status_code == status.HTTP_201_CREATED

            # Change email
            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "cliente12345"},
            )
            token = response.json()["access_token"]
            auth = f"Bearer {token}"

            # Update
            change_email_data = {"email": "<EMAIL>", "password": "cliente12345"}

            # Get user for later
            user = User.objects.get(id=user_uuid)

            client.credentials(HTTP_AUTHORIZATION=auth)
            response = client.post(
                f"/users/{user_uuid}/change_email/", data=json.dumps(change_email_data), content_type="application/json"
            )

            # Refresh user instance from db
            user.refresh_from_db()

            # Check assertions
            assert response.status_code == status.HTTP_200_OK
            assert response.json()["status"] == "Email changed"
            assert user.username == user.email == "<EMAIL>"
            assert mail.outbox[0].template == "email_confirmation_signup"

    def test_change_email_wrong_password(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response = client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
            user_uuid = response.json()["user"]["pk"]

            assert response.status_code == status.HTTP_201_CREATED

            # Change email
            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "cliente12345"},
            )
            token = response.json()["access_token"]
            auth = f"Bearer {token}"

            # Update
            change_email_data = {"email": "<EMAIL>", "password": "wrong_password"}

            # Get user for later
            user = User.objects.get(id=user_uuid)

            client.credentials(HTTP_AUTHORIZATION=auth)
            response = client.post(
                f"/users/{user_uuid}/change_email/", data=json.dumps(change_email_data), content_type="application/json"
            )

            # Refresh user instance from db
            user.refresh_from_db()

            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == {"password": _("Wrong password")}
            assert user.username != "<EMAIL>"
            assert user.email != "<EMAIL>"
            assert mail.outbox[0].template == "email_confirmation_signup"

    def test_change_email_already_exists(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            # Create first user to try to change the email to this user email
            client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
            response = client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
            user_uuid = response.json()["user"]["pk"]

            assert response.status_code == status.HTTP_201_CREATED

            # Change email
            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "cliente12345"},
            )
            token = response.json()["access_token"]
            auth = f"Bearer {token}"

            # Update
            change_email_data = {"email": "<EMAIL>", "password": "cliente12345"}

            # Get user for later
            user = User.objects.get(id=user_uuid)

            client.credentials(HTTP_AUTHORIZATION=auth)
            response = client.post(
                f"/users/{user_uuid}/change_email/", data=json.dumps(change_email_data), content_type="application/json"
            )

            # Refresh user instance from db
            user.refresh_from_db()

            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == {"email": _("An user with that email already exists.")}
            assert user.username != "<EMAIL>"
            assert user.email != "<EMAIL>"
            assert mail.outbox[0].template == "email_confirmation_signup"

    def test_resend_email_confirmation_ok(self, client):
        # Avoid to wait
        with patch("users.validators.MailgunEmailValidator.__call__"):
            with override_settings(TIME_TO_WAIT_TO_RESEND_EMAIL_CONFIRMATION_IN_SECONDS=0):
                response = client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
                user_uuid = response.json()["user"]["pk"]

                assert response.status_code == status.HTTP_201_CREATED

                # Change email
                response = client.post(
                    "/auth/login/",
                    data={"email": "<EMAIL>", "password": "cliente12345"},
                )
                token = response.json()["access_token"]
                auth = f"Bearer {token}"

                client.credentials(HTTP_AUTHORIZATION=auth)

                # Data
                data = {"email": "<EMAIL>"}
                response = client.post(
                    f"/users/{user_uuid}/resend_email_confirmation/",
                    data=json.dumps(data),
                    content_type="application/json",
                )

                # Check assertions
                assert response.status_code == status.HTTP_200_OK
                assert response.json() == {"status": "Email confirmation sent"}
                assert mail.outbox[1].template == "email_confirmation"

    def test_resend_email_confirmation_sent_twice_in_a_row_ko(self, client):
        with patch("users.validators.MailgunEmailValidator.__call__"):
            response = client.post("/auth/signup/", data={"email": "<EMAIL>", "password": "cliente12345"})
            user_uuid = response.json()["user"]["pk"]

            assert response.status_code == status.HTTP_201_CREATED

            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "cliente12345"},
            )
            token = response.json()["access_token"]
            auth = f"Bearer {token}"

            client.credentials(HTTP_AUTHORIZATION=auth)

            # Data
            data = {"email": "<EMAIL>"}

            # Send twice email confirmation
            response = client.post(
                f"/users/{user_uuid}/resend_email_confirmation/", data=json.dumps(data), content_type="application/json"
            )
            response = client.post(
                f"/users/{user_uuid}/resend_email_confirmation/", data=json.dumps(data), content_type="application/json"
            )

            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == {
                "non_field_errors": _("Please wait for at least 1 minute before requesting another email.")
            }

    def test_get_public_profile_ok(self, client, user):
        user_id = user.id
        user_email = user.email
        response = client.get(f"/users/{user_id}/public-profile/")
        fake_response = {
            "alias": "aliasy",
            "avatar": None,
            "date_joined": "2021-03-14T20:26:29.228002+01:00",
            "description": None,
            "email": user_email,
            "first_name": "namy",
            "id": str(user_id),
            "last_name": "lasty",
            "link": None,
        }
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == fake_response


class TestUserPublicProfileViewSet:
    def test_get_public_alias_ok(self, client, user):
        user_alias = user.alias
        user_id = user.id
        user_email = user.email
        response = client.get(f"/users/{user_alias}/public-alias/")
        fake_response = {
            "alias": user_alias,
            "avatar": None,
            "date_joined": "2021-03-14T20:26:29.228002+01:00",
            "description": None,
            "email": user_email,
            "first_name": "namy",
            "id": str(user_id),
            "last_name": "lasty",
            "link": None,
        }
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == fake_response
