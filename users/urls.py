from django.urls import path
from rest_framework.routers import DefaultRouter

from users.auth.urls import urlpatterns
from users.views import ActivateAccountView, UserViewSet

router = DefaultRouter()
router.register("users", UserViewSet, basename="users")

# Override internal auth urlpatterns with user viewset methods
urlpatterns += [
    path("activate-account/", ActivateAccountView.as_view(), name="activate-account"),
] + router.urls

# Needed to add namespace in backoffice_api urls.py
app_name = "users"
