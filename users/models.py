import uuid

from django.contrib.auth.models import AbstractUser
from django.core import validators
from django.core.validators import validate_email
from django.db import models
from django.db.models import F, Q
from django.utils.translation import gettext_lazy as _

from core.async_tasks.stripe import post_update_asynchronously
from core.utils import helpscout_hmac_generator
from users.validators import debounce_validate_email

from .managers import UserManager


class User(AbstractUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(_("email address"), unique=True, validators=[validate_email, debounce_validate_email])
    first_name = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    last_name = models.CharField(max_length=200, blank=True, null=True)
    avatar = models.TextField(blank=True, null=True, validators=[validators.URLValidator()])
    alias = models.Char<PERSON><PERSON>(
        max_length=200,
        null=True,
        validators=[
            validators.RegexValidator(
                r"^[a-zA-Z0-9]+(\-[A-Za-z0-9]+)*(\.[A-Za-z0-9]+)*$",
                _("Only alphanumeric characters or characters containing a period or hyphen are allowed."),
            )
        ],
    )
    description = models.TextField(max_length=200, blank=True, null=True)
    link = models.CharField(max_length=200, blank=True, null=True)
    in_beta_program = models.BooleanField(default=False)

    # Configuration parameters
    notify_campaigns = models.BooleanField(default=True)
    notify_comments = models.BooleanField(default=True)

    # Fields required to create django admin superuser
    REQUIRED_FIELDS = ["first_name", "last_name"]
    USERNAME_FIELD = "email"

    # Custom manager to override superuser command
    objects = UserManager()

    @property
    def email_valid(self):
        return self.is_superuser or self.emailaddress_set.filter(primary=True, verified=True).exists()

    @property
    def onboarding_setup(self):
        """
        Onboarding will be ready if email is valid and user belongs to organization and the user is not a role owner
        and has an associated social account
        """

        return (
            self.email_valid
            and self.alias is not None
            and self.membership_set.filter(
                Q(organization__isnull=False),
                (Q(role__name="owner") & Q(user__socialaccount__isnull=False)) | ~Q(role__name="owner"),
            ).exists()
            and self.socialaccount_set.exists()
        )

    @property
    def hs_hmac(self):
        """Helpscout hmac"""
        return helpscout_hmac_generator(self.email)

    def update_email_in_stripe(self):
        """Update email in Stripe for organizations where user is owner in background"""
        stripe_customers = (
            self.membership_set.filter(role__name="owner", organization__stripe_customer_id__isnull=False)
            .annotate(customer_id=F("organization__stripe_customer_id"))
            .values_list("customer_id", flat=True)
        )

        # Propagate email change in stripe in async-fashion
        if stripe_customers:
            post_update_asynchronously(self.email, stripe_customers)
