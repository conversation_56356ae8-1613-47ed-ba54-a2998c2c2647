import uuid

from django.contrib.auth.models import AbstractUser
from django.core.validators import validate_email
from django.db import models
from django.db.models.signals import post_save
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from users.managers import UserManager
from users.receivers import create_coupon_on_user_creation


class MailSent(models.Model):
    date = models.DateField(default=timezone.now)
    count = models.PositiveIntegerField(default=0)


class Voucher(models.Model):
    """Auxiliary model to generate coupon codes"""

    email_sent = models.BooleanField(default=False)
    user = models.OneToOneField("users.User", null=False, blank=False, on_delete=models.CASCADE)

    def __str__(self):
        return self.voucher_name

    @property
    def voucher_name(self):
        order_num = self.pk if self.pk < 80 else self.pk + 1
        return f"BOX2BOXSUMMIT{order_num}"


class User(AbstractUser):
    """Basic user model to store customer (lead, customer) and staff users"""

    ROULETTE = "1"
    HOME = "2"

    GAME_CHOICES = (
        (ROULETTE, _("Roulette")),
        (HOME, _("Home")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(
        _("Email address"), blank=False, null=True, unique=True, validators=[validate_email]
    )  # Add validator to check valid emails
    first_name = models.CharField(max_length=200, blank=True, null=True)
    last_name = models.CharField(max_length=200, blank=True, null=True)
    commercial_info = models.BooleanField(default=False)
    game = models.CharField(max_length=1, choices=GAME_CHOICES, blank=True, null=False)

    # Fields required to create django admin superuser
    REQUIRED_FIELDS = ["first_name", "last_name"]
    USERNAME_FIELD = "email"

    # Custom manager to override superuser command
    objects = UserManager()

    def __str__(self):
        """Parse str object to avoid null errors"""
        return self.email


class Configuration(models.Model):
    PUBLIC = "public"
    UNIQUE = "unique"

    TEMPLATE_CHOICES = (
        (PUBLIC, _("Public")),
        (UNIQUE, _("Unique")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template_name = models.CharField(max_length=50, choices=TEMPLATE_CHOICES, default=PUBLIC)


post_save.connect(create_coupon_on_user_creation, sender=User)
