import uuid

import pytz
from allauth.account.models import EmailAddress
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.contrib.postgres.indexes import GinIndex
from django.core import validators
from django.core.validators import validate_email
from django.db import models, transaction
from django.utils import timezone
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _
from django_countries.fields import CountryField
from phonenumber_field.modelfields import PhoneNumberField

from core.emails.message import EmailMessage
from users.managers import UserManager
from users.tokens import user_account_activation_token_generator


class ActivationAccountEmail(models.Model):
    """Model to handle activation account email send"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    send_at = models.DateTimeField(null=True, blank=True, help_text=_("Date when activation account email was sent"))

    # Internal metadata
    created_at = models.DateTimeField(default=timezone.now)

    # Foreign keys
    user = models.OneToOneField("users.user", null=True, blank=True, on_delete=models.CASCADE)

    def send_activation_account_email(self):
        activate(self.user.email_domain)
        message = EmailMessage(
            subject=_("{prefix} Activate your account in Box2box").format(prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX),
            to=[self.user.email],
            bcc=[
                "<EMAIL>",
            ],
            sender=self.user.email_domain,
            template="account_activation_email",
            context={"url": self.user.activate_account_url},
        )
        return message.send()


class User(AbstractUser):
    """Basic user model to store customer (lead, customer) and staff users"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = models.CharField(
        _("username"),
        max_length=150,
        unique=True,
        help_text=_("Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."),
        validators=[AbstractUser.username_validator],
        null=True,
        error_messages={
            "unique": _("A user with that username already exists."),
        },
    )
    email = models.EmailField(
        _("Email address"), unique=True, null=True, validators=[validate_email]
    )  # Add validator to check valid emails
    first_name = models.CharField(max_length=200, blank=True, null=True)
    last_name = models.CharField(max_length=200, blank=True, null=True)
    phone = PhoneNumberField(_("Phone number"), blank=False, null=True)
    first_login = models.BooleanField(
        default=True, help_text=_("Used to enforce to user to change password and fill missing data")
    )
    date_joined_in_crm = models.DateTimeField(default=timezone.now)
    avatar = models.TextField(blank=True, null=True, validators=[validators.URLValidator()])
    crm_internal_id = models.CharField(
        max_length=200, blank=True, null=True, unique=True, help_text=_("User identifier in external CRM")
    )
    chargebee_customer_id = models.CharField(max_length=200, blank=True, null=True, help_text=_("Chargebee identifier"))
    is_active = models.BooleanField(
        _("active"),
        default=False,
        help_text=_(
            "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
        ),
    )
    registration_country = CountryField(default="ES", help_text=_("Source domain from which user has signed in"))
    created_in_chargebee = models.BooleanField(default=False)
    moloni_id = models.CharField(
        max_length=100, blank=True, null=True, help_text=_("Customer identifier in moloni software billing")
    )
    parent = models.OneToOneField(
        "self",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="parent_user",
        help_text=_("Handle third-party software duplicity on emails"),
    )
    backoffice_allowed = models.BooleanField(default=False)
    language_portal = models.CharField(max_length=2, blank=True, null=True, help_text=_("Customer preferred language"))

    # Custom fields to handle customer webapp names
    customer_first_name = models.CharField(
        max_length=200, blank=True, null=True, help_text=_("First name set by the customer")
    )
    customer_last_name = models.CharField(
        max_length=200, blank=True, null=True, help_text=_("Last name set by the customer")
    )

    # Security key to handle token revocation
    security_key = models.UUIDField(null=True, blank=False)

    # For riders app
    is_rider = models.BooleanField(default=False)
    signature_remote_dir = models.CharField(max_length=300, null=True, blank=True)
    date_leave = models.DateTimeField(_("date leave"), null=True, blank=True)

    # Fields required to create django admin superuser
    REQUIRED_FIELDS = ["first_name", "last_name"]
    USERNAME_FIELD = "email"

    # Custom manager to override superuser command
    objects = UserManager()

    class Meta(AbstractUser.Meta):
        indexes = [GinIndex(fields=("email",)), GinIndex(fields=("first_name",)), GinIndex(fields=("last_name",))]

    def __str__(self):
        """Parse str object to avoid null errors"""
        return f"{self.email or self.phone or self.crm_internal_id or '-'}"

    def get_full_name(self):
        """Override full name to avoid send Nones"""
        return f"{self.first_name or ''} {self.last_name or ''}".strip()

    @property
    def safe_email(self):
        """Get the right email from this user taking into account the parent in case of empty email"""
        return self.email or (self.parent and self.parent.email)

    @property
    def has_payment_method(self):
        """Check if user has a valid payment method linked"""
        return hasattr(self, "card")

    @property
    def payment_method_or_empty_queryset(self):
        """Helper method to get the related payment if has any linked or empty queryset"""
        return getattr(self, "card", None) or getattr(self, "sepamandate", None) or self._meta.model.objects.none()

    @property
    def email_domain(self):
        """Get sender domain to send transactional emails to user"""
        return str(self.registration_country)

    @property
    def activate_account_url(self):
        object_id = self.pk
        url = "activate/"

        # Encode the url
        uid = urlsafe_base64_encode(force_bytes(object_id))
        token = user_account_activation_token_generator.make_token(self)
        return f"{settings.WEBAPP_PUBLIC_URLS.get(self.registration_country, 'ES')}{url}{uid}/{token}/"

    # Temporal flow: mark emails pending to be activated and send them one time per day
    def schedule_activation_account_mail(self):
        """Schedule the activation account email and send it at fixed time every day"""

        if not (self.email and not self.is_staff and not self.is_active):
            return False

        with transaction.atomic():
            _, created = EmailAddress.objects.get_or_create(
                user=self, email__iexact=self.email, defaults={"email": self.email}
            )

            if created:
                ActivationAccountEmail.objects.get_or_create(user=self)

        return created

    # TODO: handle it later if we change the activation email flow
    def send_activation_account_mail_to_user(self, force=False):
        """Email given user to activate their account"""

        if not (self.email and not self.is_staff and not self.is_active):
            return 0

        with transaction.atomic():
            email_address, created = EmailAddress.objects.get_or_create(
                user=self, email__iexact=self.email, defaults={"email": self.email}
            )

        if created or force:
            activate(self.email_domain)
            message = EmailMessage(
                subject=_("{prefix} Activate your account in Box2box").format(
                    prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX
                ),
                to=[self.email],
                sender=self.email_domain,
                template="account_activation_email",
                context={"url": self.activate_account_url},
            )
            return message.send()

        # Already sent
        return 1

    @property
    def chargebee_full_url(self):
        """Build the chargebee full url taking the country and id into account"""
        chargebee_site = settings.CHARGEBEE_SITES.get(self.registration_country, None) or None
        if chargebee_site and self.chargebee_customer_id and self.created_in_chargebee:
            return settings.CHARGEBEE_URL_DOMAIN.format(
                site=chargebee_site["site"], model="customers", id=self.chargebee_customer_id
            )
        return None

    @property
    def hubspot_full_url(self):
        """Build the hubspot full url taking the hubspot id into account"""
        return (
            settings.HUBSPOT_URL_DOMAIN.format(model="contact", id=self.crm_internal_id)
            if self.crm_internal_id
            else None
        )

    @property
    def onboarding_finished(self):
        """Flag to check"""
        return bool(self.customer_first_name and self.customer_last_name)

    @property
    def timezone(self):
        """Return the timezone given their registration country or None by default.
        This method is intended to be used along 'make_aware'"""
        timezones = pytz.country_timezones.get(self.email_domain)
        if timezones:
            return pytz.timezone(timezones[0])
        return None
