import logging
import random
import threading
from dataclasses import dataclass
from multiprocessing.pool import ThreadPool
from time import sleep

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoadBalancerException(Exception):
    pass


@dataclass
class Server:
    ip: str = None
    weight: int = 1

    def __str__(self):
        return f"server[{self.ip}]: executed!"

    def do_something(self, request: int):
        # Simulate heavy task
        sleep(random.uniform(0, 1))
        return f"Server [{self.ip}] -- request: {request}"


class BaseLoadBalancer:
    instances = {}
    flatten_instances = []

    def __init__(self, max_size: int = 10):
        self._max_size = max_size

    @property
    def is_full(self):
        return len(self.instances.keys()) == self._max_size

    def register_server(self, server: Server):
        with threading.Lock():
            if self.is_full:
                raise LoadBalancerException("Load balancer is full")

            if server.ip not in self.instances:
                self.instances[server.ip] = server
                self.flatten_instances.append(server)

    def unregister_server(self, server: Server):
        with threading.Lock():
            if server.ip not in self.instances:
                raise LoadBalancerException(f"Server {server.ip} not found in load balancer")

            del self.instances[server.ip]
            self.flatten_instances.remove(server)

    @property
    def get_server(self):
        raise NotImplementedError("Not Implemented yet!")

    def run(self, request: int):
        instance = self.get_server()
        logger.info(instance.do_something(request))
        return instance


class RoundRobinLoadBalancer(BaseLoadBalancer):
    instance_index = 0

    def get_server(self) -> Server:
        with threading.Lock():
            instance = self.flatten_instances[self.instance_index]
            self.instance_index = (self.instance_index + 1) % len(self.flatten_instances)

        return instance


class RandomLoadBalancer(BaseLoadBalancer):
    def get_server(self) -> Server:
        return self.flatten_instances[random.randint(0, len(self.flatten_instances) - 1)]


class WeightedRoundRobinLoadBalancer(BaseLoadBalancer):
    unrolled_instances = None
    instance_index = 0

    def get_server(self) -> Server:
        with threading.Lock():
            if self.unrolled_instances is None:
                self.unrolled_instances = []

                for instance in sorted(self.flatten_instances, key=lambda x: x.weight, reverse=True):
                    self.unrolled_instances.extend([instance] * instance.weight)

            instance = self.unrolled_instances[self.instance_index]
            self.instance_index = (self.instance_index + 1) % len(self.unrolled_instances)

        return instance


class MonitoringLoadBalancer:
    def __init__(self, load_balancer: BaseLoadBalancer):
        self._load_balancer = load_balancer
        self._statistics = {}

    def run(self, request: int):
        instance = self._load_balancer.run(request)

        if instance.ip not in self._statistics:
            self._statistics[instance.ip] = 0

        self._statistics[instance.ip] += 1

    @property
    def metrics(self):
        return f"Metrics for {self._load_balancer.__class__.__name__} - {self._statistics}"


LOAD_BALANCERS = {
    "simple": RoundRobinLoadBalancer,
    "random": RandomLoadBalancer,
    "weighted": WeightedRoundRobinLoadBalancer
}


def get_load_balancer(name: str) -> BaseLoadBalancer:
    try:
        load_balancer = LOAD_BALANCERS[name]
    except KeyError:
        load_balancer = LOAD_BALANCERS["default"]

    return load_balancer()


if __name__ == '__main__':
    base = WeightedRoundRobinLoadBalancer()
    # server_list = [
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="*************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    #     Server(ip="************"),
    # ]

    server_list = [
        Server(ip="************", weight=2),
        Server(ip="************", weight=2),
        Server(ip="************", weight=1),
        Server(ip="*************", weight=4),
    ]

    load_balancer_types = list(LOAD_BALANCERS.keys())
    for load_balancer_type in load_balancer_types:
        load_balancer = get_load_balancer(load_balancer_type)

        # Load servers
        try:
            for server in server_list:
                load_balancer.register_server(server)
        except LoadBalancerException:
            pass

        monitoring_load_balancer = MonitoringLoadBalancer(load_balancer)

        with ThreadPool(4) as pool:
            result = pool.map_async(monitoring_load_balancer.run, range(10))
            result.wait()

        print(">>>>>> ", monitoring_load_balancer.metrics)

    # try:
    #     for server in server_list:
    #         base.register_server(server)
    # except LoadBalancerException:
    #     pass
    #
    # base = MonitoringLoadBalancer(base)
    #
    # with pool.ThreadPool(8) as pool:
    #     result = pool.map_async(base.run, range(21))
    #     result.wait()
    #
    # print(base.metrics)
