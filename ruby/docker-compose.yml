version: '3.9'

services:
  web:
    env_file:
      - .env
    environment:
      DB_HOST: db
      DB_NAME: events
      DB_USERNAME: events
      DB_PASSWORD: events
    build:
      dockerfile: ./Dockerfile
      context: ./
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails db:migrate RAILS_ENV=development && bundle exec rails s -p 3000 -b '0.0.0.0'"
    depends_on:
      - db
    ports:
      - 3000:3000
  db:
    image: mysql:8.0
    command: mysqld --default-authentication-plugin=mysql_native_password
    volumes:
      - ./db/mysql:/var/lib/mysql
    ports:
      - 3306:3306
    logging:
      driver: none
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: events_development
      MYSQL_USER: events
      MYSQL_PASSWORD: events
  ngrok:
    image: gtriggiano/ngrok-tunnel
    ports:
      - 4040:4040
    environment:
      TARGET_HOST: web
      TARGET_PORT: 3000
    depends_on:
      - web