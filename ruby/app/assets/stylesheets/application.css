/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS/SCSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

 .wrapper .container {
  padding-bottom: 2rem;
  padding-top: 2rem;
}

.navigation {
  background: #f4f5f6;
  border-bottom: .1rem solid #d1d1d1;
  display: block;
  height: 5.2rem;
  left: 0;
  max-width: 100%;
  width: 100%;
}

.navigation .container {
  padding-bottom: 0;
  padding-top: 0
}

.navigation .navigation-list {
  list-style: none;
  margin-bottom: 0;
}

.navigation .navigation-item {
  float: left;
  margin-bottom: 0;
  margin-left: 2.5rem;
  position: relative
}

.navigation .navigation-title, .navigation .title {
  color: #606c76;
  position: relative
}

.navigation .navigation-link, .navigation .navigation-title, .navigation .title {
  display: inline;
  font-size: 1.6rem;
  line-height: 5.2rem;
  padding: 0;
  text-decoration: none
}

.navigation .navigation-link.active {
  color: #606c76
}

.hidden {
  display: none;
}

.event.deletion {
  color: white;
  background-color: red;
}

.event.creation {
  color: #3c763d;
  background-color: #dff0d8;
}

.event.propertyChange {
  background-color: #d8aa65;
  color: #ffffff;
}

.authorize-button {
  justify-content: center;
}

.text-center {
  text-align: center;
}

