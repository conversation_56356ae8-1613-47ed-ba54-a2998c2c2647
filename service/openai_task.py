import time
import uuid
from datetime import datetime
from io import BytesIO

import pytz
from celery import Task
from celery.exceptions import Reject
from flask_socketio import Socket<PERSON>

from service.debugger import DebuggerMixin
from service.openai_monkey_patch import audio


class OpenAITask(Task, DebuggerMixin):
    name = __name__

    def __call__(self, speech_file, who, *args, **kwargs):
        debug_in_remote = kwargs.get("in_remote")
        redis_uri = kwargs.get("redis_uri")
        model = kwargs.get("model")
        credentials = kwargs.get("credentials")
        api_key = kwargs.get("api_key")
        room_id = kwargs.get("room_id")

        speech_file = BytesIO(speech_file)
        speech_file.filename = "output.wav"

        # Webockets support
        socket_io = SocketIO(message_queue=redis_uri)
        now = time.mktime(datetime.utcnow().replace(tzinfo=pytz.utc).timetuple())

        # For debugging purposes
        if debug_in_remote:
            parent = kwargs.get("parent")
            self.create_checkpoint_in_remote(
                f"{str(int(now))}_{datetime.fromtimestamp(now).strftime('%Y-%m-%d-%H:%M:%S.%s')}_{uuid.uuid4()}",
                parent,
                speech_file,
                credentials
            )
        else:
            destination_folder = kwargs.get("destination_folder")
            self.create_checkpoint(destination_folder,
                                   f"{str(int(now))}_{datetime.fromtimestamp(now).strftime('%Y-%m-%d-%H:%M:%S.%s')}_{uuid.uuid4()}",
                                   speech_file)

        try:
            speech_file = BytesIO(speech_file.getbuffer().tobytes())
            speech_file.filename = "output.wav"

            result = audio.transcribe(model, speech_file, api_key=api_key, language="es")
            print("RESULTADO ", result)

            # Notify to frontend only if we have data
            if result.get("text"):
                socket_io.emit("message", {"timestamp": now, "data": result.get("text"), "who": who}, to=room_id)
            return result
        except Exception as err:
            print("ERROR ", err)
            socket_io.emit("error", {"timestamp": now})
            raise Reject()
