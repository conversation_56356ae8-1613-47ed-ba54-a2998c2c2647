import os

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseUpload

from service.google.base import BaseClient, ClientError


class GoogleDriveClient(BaseClient):
    default_scopes = ["https://www.googleapis.com/auth/drive"]

    def __init__(
            self,
            *,
            service_account_json,
            scopes=None,
    ):
        super(GoogleDriveClient, self).__init__(service_account_json)
        self._service_account_json = service_account_json
        self._scopes = scopes or self.default_scopes

    def lazy_init_client(self):
        """Lazy init for clients, delaying it upto first time is used"""
        try:
            credentials = service_account.Credentials.from_service_account_file(
                self._service_account_json, scopes=self._scopes
            )
            service = build("drive", "v3", credentials=credentials)
        except (FileNotFoundError, TypeError):
            raise ClientError(
                f"Wrong `{self._service_account_json}` credentials to init the google sheets client"
            )
        return service

    @property
    def files(self):
        return self.client.files()

    # Public API
    def create_folder(self, folder_name, parents):
        file_metadata = {
            "name": folder_name,
            "mimeType": "application/vnd.google-apps.folder",
            "parents": parents
        }

        try:
            folder = self.files.create(body=file_metadata, fields="id").execute()
            print(folder)
            return folder.get("id")
        except Exception as error:
            raise ClientError(f"Error creating folder {folder_name}: {error}")

    def get_folder(self, folder_name):
        page_token = None

        while True:
            response = self.files.list(
                q=f"mimeType = 'application/vnd.google-apps.folder' and name = '{folder_name}' and trashed = false",
                pageToken=page_token).execute()
            for file in response.get("files", []):
                return file.get("id")

            page_token = response.get('nextPageToken', None)
            if page_token is None:
                break

        return None

    def list_folder(self, folder_id):
        page_token = None

        while True:
            response = self.files.list(
                q=f"mimeType = 'application/vnd.google-apps.folder' and '{folder_id}' in parents",
                pageToken=page_token).execute()

            yield from response.get("files", [])

            page_token = response.get('nextPageToken', None)
            if page_token is None:
                break

        return []

    def delete_folder_or_file(self, folder_id):
        try:
            self.files.delete(fileId=folder_id).execute()
        except Exception as error:
            raise ClientError(f"Error deleting the folder or file with id {folder_id}: {error}")

        return None

    def create_file(self, file_name, parents, content, mime_type):
        file_metadata = {
            "name": file_name,
            "parents": parents
        }
        media = MediaIoBaseUpload(
            content, mime_type
        )

        try:
            folder = self.files.create(body=file_metadata, media_body=media, fields="id").execute()
            return folder.get("id")
        except Exception as error:
            raise ClientError(f"Error creating folder {file_name}: {error}")


if __name__ == '__main__':
    client = GoogleDriveClient(
        service_account_json=os.getenv("GOOGLE_CREDENTIALS_FILE")
    )

    # client.delete_folder_or_file("1EgQg-yr0iJ4omOYo-eun6Ygm4F4p6nUt")
    # print(list(client.list_folder("1fqWr-vl_f-F0wZOXYD_AGECgb0kbQgeA")))
    # print(client.create_folder("example2", ["1fqWr-vl_f-F0wZOXYD_AGECgb0kbQgeA"]))
    # f = BytesIO()
    # f.write(b'content')

    # client.create_file(
    #     "prueba.json",
    #     ["1fqWr-vl_f-F0wZOXYD_AGECgb0kbQgeA"],
    #     f
    # )

    client.delete_folder_or_file("1q-hDc5wXm2o7FdGYpm06XXeEWS0jAkF9")
