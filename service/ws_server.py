from service.server_inits import app

if app.config["ENV"] == "local":
    from gevent import monkey

    monkey.patch_all()

from flask_socketio import Socket<PERSON>, ConnectionRefusedError, join_room
from service.google.utils import init_drive_folders

from flask_cors import CORS
from flask import request

CORS(app)

PARENT_GOOGLE_DRIVE_FOLDER = app.config["PARENT_GOOGLE_DRIVE_FOLDER"]
REDIS_URI = app.config["REDIS_URI"]
GOOGLE_CREDENTIALS_KEY = app.config["GOOGLE_CREDENTIALS_KEY"]
API_KEY = app.config["API_KEY"]

socket_io = SocketIO(app, message_queue=REDIS_URI, logger=True, engineio_logger=True,
                     cors_allowed_origins="*")


@socket_io.on("connect")
def connect(auth):
    print("AUTH ", auth, request.args)
    try:
        token = auth.get("token")
        if token != API_KEY:
            raise Exception("Unauthorized")
    except Exception:
        raise ConnectionRefusedError('unauthorized!')

    # All good here, notify to the frontend
    socket_io.emit("connected")

@socket_io.on("connected")
def connected(user_id):
    join_room(user_id)
    try:
        metadata = init_drive_folders(user_id,
                                      [PARENT_GOOGLE_DRIVE_FOLDER],
                                      service_account_json=GOOGLE_CREDENTIALS_KEY)

        # Circle back to frontend with the response
        socket_io.emit("completed", {
            "msg": metadata.to_json(),
            "status": 200,
        }, to=user_id)
    except Exception:
        socket_io.emit("completed", {
            "msg": "Error",
            "status": 400,
        }, to=user_id)


# For ngrok: only needed to change in the javascript extension code.
# This one holds without changes
if __name__ == '__main__':
    socket_io.run(app, host="0.0.0.0", port=5000, debug=True)
