import time
from datetime import datetime

import numpy as np
import pytz
import whisperx
from celery import Task
from celery.exceptions import Reject
from flask_socketio import SocketIO


class WhisperTask(Task):
    name = __name__
    serializer = "json"
    ignore_result = False

    HF_TOKEN = "*************************************"
    model_size = "tiny"

    def __call__(self, audio_wave, who, *args, **kwargs):
        self.model = whisperx.load_model(self.model_size, device="cpu", compute_type="int8", language="es")
        data = np.frombuffer(audio_wave, np.int16).flatten().astype(np.float32) / 32768.0
        print("HOLAAAAA!!!", self.model, self.model.transcribe)

        # Webockets support
        socket_io = SocketIO(message_queue="redis://127.0.0.1:6379/0")
        now = time.mktime(datetime.utcnow().replace(tzinfo=pytz.utc).timetuple())

        try:
            result = self.model.transcribe(data, batch_size=16)
            print("RESULTADO ", result)

            # Notify to frontend only if we have data
            if result.get("segments"):
                socket_io.emit("message", {"timestamp": now, "data": result.get("segments")[0]["text"], "who": who})
            print("RESUSLT ", result)
            return result
        except Exception as err:
            print("ERROR ", err)
            socket_io.emit("error", {"timestamp": now})
            raise Reject()
