import os

workers = 1  # (2 * multiprocessing.cpu_count()) + 1
worker_class = "geventwebsocket.gunicorn.workers.GeventWebSocketWorker"
# worker_class = "gevent"
accesslog = "-"
worker_connections = 1024
timeout = 300
bind = "0.0.0.0:5001"
keepalive = 5
worker_tmp_dir = "/dev/shm"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(l)s "%(T)s"'
max_requests = 1024


def post_fork(server, worker):
    # BugFix: https://github.com/googleapis/google-cloud-python/issues/9192
    # grpcio with gevent doesn't work unless init grpc with gevent explicitly
    from gevent import monkey

    monkey.patch_all()
    worker.log.info("Green green green!!")