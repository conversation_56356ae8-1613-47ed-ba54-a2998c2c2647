import os

workers = 2  # (2 * multiprocessing.cpu_count()) + 1
worker_class = "gevent_wsgi"
# worker_class = "gevent"
accesslog = "-"
worker_connections = 1024
timeout = 300
bind = "0.0.0.0:5000"
keepalive = 5
worker_tmp_dir = "/dev/shm"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(l)s "%(T)s"'
max_requests = 1024


def post_fork(server, worker):
    # BugFix: https://github.com/googleapis/google-cloud-python/issues/9192
    # grpcio with gevent doesn't work unless init grpc with gevent explicitly
    from gevent import monkey

    monkey.patch_all()
    worker.log.info("Green green green!!")

    worker_id = os.getpid()
    cache_dir = f"/root/.cache/torch/hub/worker_{worker_id}"
    os.environ["TORCH_HOME"] = cache_dir

    worker.log.info(f"Preforking worker... {cache_dir}")
