from service.server_inits import app

if app.config["ENV"] == "local":
    from gevent import monkey

    monkey.patch_all()

import sys
from gevent.pywsgi import WSGIServer
from service.server_inits import openai_vad_task, openai_task
from service.google.utils import save_dump
from service.api_key_manager import api_key_required, api_key_manager

import datetime
import json
from flask_cors import CORS
from flask import jsonify, request

CORS(app)

USE_VAD_MODE = app.config["USE_VAD_MODE"]
DESTINATION_FOLDER = app.config["DESTINATION_FOLDER"]
SAVE_IN_DRIVE = app.config["SAVE_IN_DRIVE"]
PARENT_GOOGLE_DRIVE_FOLDER = app.config["PARENT_GOOGLE_DRIVE_FOLDER"]
REDIS_URI = app.config["REDIS_URI"]
WHISPER_MODEL = app.config["WHISPER_MODEL"]
WHISPER_API_KEY = app.config["WHISPER_API_KEY"]
GOOGLE_CREDENTIALS_KEY = app.config["GOOGLE_CREDENTIALS_KEY"]
API_KEY = app.config["API_KEY"]

# Check if API Key is set or kill the server
if not API_KEY:
    api_key = api_key_manager.create(32)
    print(f"=====> Export this API key as is before running the program: export API_KEY={api_key}. "
          f"Replace in the extension code every `Bearer XXX` you find on it with this value `{api_key}`")
    sys.exit(1)


@app.route("/parse", methods=["POST"])
@api_key_required
def parse():
    speech_file = request.files["audio-blob"]
    who = request.form.get("who")
    parent = request.form.get("parent")
    room_id = request.form.get("room_id")

    if USE_VAD_MODE:
        result = openai_vad_task.delay(speech_file.stream.read(), who, destination_folder=DESTINATION_FOLDER,
                                       parent=parent, in_remote=SAVE_IN_DRIVE, redis_uri=REDIS_URI, model=WHISPER_MODEL,
                                       api_key=WHISPER_API_KEY, credentials=GOOGLE_CREDENTIALS_KEY, room_id=room_id)
    else:
        result = openai_task.delay(speech_file.stream.read(), who, destination_folder=DESTINATION_FOLDER, parent=parent,
                                   in_remote=SAVE_IN_DRIVE, redis_uri=REDIS_URI, model=WHISPER_MODEL,
                                   api_key=WHISPER_API_KEY, credentials=GOOGLE_CREDENTIALS_KEY, room_id=room_id)

    print("RESULTADO ", result)
    return jsonify({"msg": "received"}), 200


@app.route("/dump", methods=["POST"])
@api_key_required
def dump():
    dump_file = request.files["dump"]

    # TODO: move it into celery task
    if SAVE_IN_DRIVE:
        parent_id = request.form["remote_id"]
        save_dump(
            f"{datetime.datetime.utcnow()}.json",
            parent_id,
            dump_file.stream,
            service_account_json=GOOGLE_CREDENTIALS_KEY
        )
    else:
        with open(f"{DESTINATION_FOLDER}/{datetime.datetime.utcnow()}.json", "w") as fp:
            json.dump(json.loads(dump_file.stream.read().decode("utf-8")), fp, ensure_ascii=False)

    return jsonify({"msg": "dumped"})


# For ngrok: only needed to change in the javascript extension code.
# This one holds without changes
if __name__ == '__main__':
    server = WSGIServer(('0.0.0.0', 5000), app)
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        server.stop(timeout=10)
