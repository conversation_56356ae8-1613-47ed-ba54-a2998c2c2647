from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.mixins import StringErrorsMixin


class MoreappAnnexPDFSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse annex pdf fields and retrieve them from moreapp"""

    form_id = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    submission_id = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    destination_name = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    document_type = serializers.ChoiceField(
        required=True, allow_null=False, allow_blank=False, choices=["Annex", "Box", "Moving"]
    )


class MoreappInventoryImageSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse inventory images and retrieve them from moreapp"""

    file_id = serializers.Char<PERSON><PERSON>(required=True, allow_blank=False, allow_null=False)
    destination_name = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    form_id = serializers.CharField(required=True, allow_blank=False, allow_null=False)


class ChargebeeInvoicePDFSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse chargebee pdf fields and retrieve from chargebee"""

    invoice_id = serializers.CharField(required=True, allow_null=False, allow_blank=False, help_text=_("Chargebee id"))
    invoice_pk = serializers.CharField(required=True, allow_null=False, allow_blank=False, help_text=_("Internal id"))
    destination_name = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    chargebee_site_country = serializers.CharField(required=True, allow_null=False, allow_blank=False, max_length=2)


class MoreappEmailSerializer(serializers.Serializer):
    """Serializer to parse moreapp email fields"""

    inventory_email_id = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    message = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    force_send = serializers.BooleanField(required=True, allow_null=False)


class InventorySheetsSerializer(serializers.Serializer):
    """Serializer to parse inventory sheets fields"""

    warehouse_id = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    data = serializers.JSONField(required=True, allow_null=False)

    def validate(self, attrs):
        """Skip newer warehouses from google sheets processing"""
        warehouse_id = attrs.get("warehouse")

        if warehouse_id in ["4000", "5000"]:
            raise serializers.ValidationError("Invalid warehouse id")

        return attrs


class HubspotDealCreationSerializer(serializers.Serializer):
    """Serializer to parse hubspot metadata fields"""

    query = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    contact_id = serializers.CharField(required=True, allow_null=True, allow_blank=False)
    type = serializers.ChoiceField(required=True, allow_null=False, allow_blank=False, choices=["id", "email", "deal"])

    def validate(self, attrs):
        contact_id = attrs.get("contact_id")
        type = attrs.get("type")

        if type != "deal" and not contact_id:
            raise serializers.ValidationError("Contact id cannot be empty")

        return attrs


class RidersAppSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to build pdfs from ridersapp"""

    service_id = serializers.UUIDField(required=True, allow_null=False)
    submission_id = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    contract_destination = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    tos_destination = serializers.CharField(required=True, allow_blank=False, allow_null=False)
    document_type = serializers.ChoiceField(
        required=True, allow_null=False, allow_blank=False, choices=["Annex", "Box", "Moving"]
    )
    force_send = serializers.BooleanField(required=True, allow_null=False)


class CalendarEventSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to update calendar events"""

    services = serializers.ListSerializer(
        child=serializers.UUIDField(required=True, allow_null=False), allow_null=False
    )
    tasks = serializers.ListSerializer(child=serializers.UUIDField(required=True, allow_null=False), allow_null=False)
    quota_user = serializers.CharField(required=True, allow_blank=False, allow_null=False)


class CalendarHolidaysSerializer(StringErrorsMixin, serializers.Serializer):
    city_id = serializers.UUIDField(required=False, allow_null=True)
    calendar_id = serializers.UUIDField(required=False, allow_null=True)

    def validate(self, attrs):
        city_id = attrs.get("city_id")
        calendar_id = attrs.get("calendar_id")

        if not city_id and not calendar_id:
            raise serializers.ValidationError("City id or calendar id is required")

        return attrs
