import datetime
import json
import logging

import pytz
from django.db import transaction
from django.db.models import Q
from django.forms import model_to_dict
from django.utils import timezone
from django.utils.timezone import make_aware

from contracts.models import Contract, Service, Warehouse
from core.models.event_log import Event
from core.utils import is_valid_uuid
from intranet.models import Task, WarehouseMovement
from riders.models import Team
from webhooks.callbacks.base import BaseCallback
from webhooks.callbacks.exceptions import SkipCallbackException
from webhooks.callbacks.ridersapp_ext.base import RidersappBaseParser
from webhooks.callbacks.ridersapp_ext.boxes import BoxParser
from webhooks.callbacks.ridersapp_ext.delivery.final_delivery import \
    FinalDeliveryParser
from webhooks.callbacks.ridersapp_ext.delivery.partial_delivery import \
    PartialDeliveryParser
from webhooks.callbacks.ridersapp_ext.movings import MovingParser
from webhooks.callbacks.ridersapp_ext.pickup.extra_pickup import \
    ExtraPickupParser
from webhooks.callbacks.ridersapp_ext.pickup.initial_pickup import \
    InitialPickupParser
from webhooks.callbacks.ridersapp_ext.strategies.tracking.AddWarehouseMovementStrategy import \
    AddWarehouseMovementStrategy

logger = logging.getLogger(__name__)

RIDERAPP_FORMS = {
    Service.INITIAL_PICKUP: InitialPickupParser,
    Service.EXTRA_PICKUP: ExtraPickupParser,
    Service.PARTIAL_DELIVERY: PartialDeliveryParser,
    Service.FINAL_DELIVERY: FinalDeliveryParser,
    Service.BOX: BoxParser,
    Service.MOVING: MovingParser,
}


class Handler:
    @classmethod
    def handle(cls, message):
        """
        A class method to handle message data and update service information accordingly.

        Args:
            message: The message data to be handled.

        Returns:
            None
        """
        raise NotImplementedError("Not implemented yet!")


class UtilityMixin:
    def ensure_date(self, timestamp=None):
        """
        Ensure the given timestamp is converted to a UTC date and time.

        Parameters:
            timestamp (int, optional): The timestamp to convert. Defaults to None.

        Returns:
            datetime: The converted UTC date and time, or None if an error occurs.
        """
        try:
            date = datetime.datetime.fromtimestamp(
                (timestamp or (timezone.now().timestamp() * 1000)) / 1000
            ).astimezone(pytz.utc)
        except Exception:  # NOQA:E722
            date = None

        return date


class ServiceHandler(Handler, UtilityMixin):
    @classmethod
    def handle(cls, message):
        # Parse riderapp forms accordingly
        metadata = message.get("raw_data", {}) or {}

        # event_id = message.get("id")
        service_id = metadata.get("id")
        rider_id = metadata.get("customer_id")
        is_updated = metadata.get("is_updated")
        data = metadata.get("data", {}) or {}
        # contract_id = metadata.get("contract_id")
        # service_type = metadata.get("type")

        # Flag to recreate some parts when parsing the form on update
        should_be_updated = False

        with transaction.atomic():
            # Get the service and update it with raw_data
            service = (
                Service.objects.filter(
                    Q(id=service_id),
                    Q(team__rider_id=rider_id),
                    ~Q(status=Service.FINISHED) | (Q(status=Service.FINISHED) & Q(updated_at__isnull=True)),
                )
                .select_for_update()
                .first()
            )

            if service:
                warehouse_id = data.get("warehouse_id")

                # TODO: Remove this when app is fully deployed
                filter = {"warehouse_id": warehouse_id}
                if is_valid_uuid(warehouse_id):
                    filter = {"id": warehouse_id}
                # TODO END

                warehouse = Warehouse.objects.filter(**filter).select_related("city").first()

                # Generated column MUST BE a UUID, so if we don't get a valid warehouse entry in the db, set as None
                message["raw_data"]["data"]["warehouse_id"] = str(warehouse.id) if warehouse else None
                if not warehouse:
                    message["raw_data"]["data"]["original_warehouse_id"] = warehouse_id

                service.raw_data = message
                service.status = Service.FINISHED
                service.submitted_by_id = rider_id
                updated_fields = [
                    "raw_data",
                    "status",
                    "submitted_by",
                ]
                sent_at = cls.ensure_date(metadata.get("timestamp"))

                # Update sent date on related team to retrieve finished services later to the app
                service.team_set.filter(rider_id=rider_id).update(sent_at=sent_at)

                if is_updated:
                    service.updated_at = timezone.now()
                    should_be_updated = True
                    updated_fields += ["updated_at"]
                else:
                    service.submitted_at = timezone.now()
                    updated_fields += ["submitted_at"]

                service.save(update_fields=updated_fields)

                # Inject some warehouse data in the data object for later
                if warehouse and warehouse.city:
                    city = warehouse.city
                    if city:
                        metadata["data"]["city"] = {
                            "id": str(city.id),
                            **model_to_dict(city),
                        }
        if not service:
            # TODO: enable when new version of the app is deployed
            #     added_items = (metadata.get("data", {}) or {}).get("added_items")
            #
            #     if added_items:
            #         try:
            #             # Publish a message to delete added items from storage
            #             pubsub_client.request(
            #                 topic=settings.STORAGE_TOPIC,
            #                 data={
            #                     "id": event_id,
            #                     "items": added_items,
            #                     "event_type": "clear_storage",
            #                     "occurred_at": int(
            #                         time.mktime(datetime.datetime.now().replace(tzinfo=pytz.utc).timetuple())
            #                     ),
            #                     "root_path": f"documentation/{contract_id}/inventory",
            #                     "service_type": service_type,
            #                 },
            #             )
            #         except ClientError as err:
            #             logger.warning(f"Message to clear storage could not be sent:: {err}")
            raise SkipCallbackException("No service found or rider is not authorized to submit it")

        # Send notification outside the transaction and run the parser accordingly
        # try:
        #     notification_service = MulticastFCMNotification()
        #     notification_response = notification_service.notify(service, msg_data={"status": Service.FINISHED})
        #
        #     # Prepare response
        #     teams = []
        #     for idx, team in enumerate(notification_response.configuration):
        #         message_response = notification_response.response.responses[idx]
        #         if message_response.success:
        #             teams.append(Team(id=team, status=Team.SENT, sent_at=timezone.now()))
        #
        #     if teams:
        #         Team.objects.bulk_update(teams, fields=["status", "sent_at"])
        # except NotificationException as error:
        #     logger.error(f"Error sending notification when finishing the service {service.contract_id}: {error}")

        # Parse the form outside the transaction
        event = RIDERAPP_FORMS.get(service.type, RidersappBaseParser)()
        event.parse(service, metadata, should_be_updated)


class ServiceWithNoWarehouseHandler(Handler, UtilityMixin):
    """Temporal handler until app is fully deployed to avoid errors due to missing warehouse"""

    @classmethod
    def handle(cls, message):
        # Parse riderapp forms accordingly
        metadata = message.get("raw_data", {}) or {}

        # event_id = message.get("id")
        service_id = metadata.get("id")
        rider_id = metadata.get("customer_id")
        is_updated = metadata.get("is_updated")

        # Flag to recreate some parts when parsing the form on update
        should_be_updated = False

        with transaction.atomic():
            # Get the service and update it with raw_data
            service = (
                Service.objects.filter(
                    Q(id=service_id),
                    Q(team__rider_id=rider_id),
                    ~Q(status=Service.FINISHED) | (Q(status=Service.FINISHED) & Q(updated_at__isnull=True)),
                )
                .select_for_update()
                .first()
            )

            if service:
                service.raw_data = message
                service.status = Service.FINISHED
                service.submitted_by_id = rider_id
                updated_fields = [
                    "raw_data",
                    "status",
                    "submitted_by",
                ]
                sent_at = cls.ensure_date(metadata.get("timestamp"))

                # Update sent date on related team to retrieve finished services later to the app
                service.team_set.filter(rider_id=rider_id).update(sent_at=sent_at)

                if is_updated:
                    service.updated_at = timezone.now()
                    should_be_updated = True
                    updated_fields += ["updated_at"]
                else:
                    service.submitted_at = timezone.now()
                    updated_fields += ["submitted_at"]

                service.save(update_fields=updated_fields)

        if not service:
            raise SkipCallbackException("No service found or rider is not authorized to submit it")

        # Parse the form outside the transaction
        event = RIDERAPP_FORMS.get(service.type, RidersappBaseParser)()
        event.parse(service, metadata, should_be_updated)


class TaskHandler(Handler, UtilityMixin):

    @classmethod
    def handle(cls, message):
        metadata = message.get("raw_data", {}) or {}
        team_id = metadata.get("team_id")
        sent_at = cls.ensure_date(metadata.get("timestamp"))

        # Get the team related to this task and update it with raw_data
        Team.objects.filter(id=team_id, raw_data__isnull=True).update(raw_data=message, sent_at=sent_at)


class WarehouseMovementHandler(Handler, UtilityMixin):
    strategies = [
        AddWarehouseMovementStrategy,
    ]

    @classmethod
    def handle(cls, message):
        """
        Handle warehouse movement data and create corresponding WarehouseMovement objects.

        This method processes the incoming message containing warehouse movement data,
        creates WarehouseMovement objects for each placement, and updates the team's
        warehouse movement status if new movements are created.

        Parameters:
        message (dict): A dictionary containing the raw data of the warehouse movement.
                        Expected to have a 'raw_data' key with nested information.

        Returns:
        None

        Side effects:
        - Creates WarehouseMovement objects in the database.
        - Updates the warehouse_movement_status of the associated Team object.
        - Logs a warning if the associated Contract object cannot be found.
        """
        metadata = message.get("raw_data", {}) or {}
        sent_at = cls.ensure_date(metadata.get("timestamp"))
        contract_id = metadata.get("contract_id")
        service_id = metadata.get("id")
        rider_id = metadata.get("rider_id")
        team_id = metadata.get("team_id")
        warehouse_id = metadata.get("warehouse_id")
        meta = metadata.get("meta")
        data = metadata.get("data", {}) or {}
        placements = data.get("placements", []) or []

        try:
            contract = Contract.objects.get(contract_document_id=contract_id)
        except (Contract.DoesNotExist, Contract.MultipleObjectsReturned) as err:
            logger.warning(
                f"Error getting contract for {contract_id} when adding warehouse movements due to {err} Cannot "
                "be added."
            )
            return

        context = {
            "metadata": metadata,
            "sent_at": sent_at,
            "contract_id": contract_id,
            "service_id": service_id,
            "rider_id": rider_id,
            "team_id": team_id,
            "warehouse_id": warehouse_id,
            "meta": meta,
            "placements": placements,
            "contract": contract,
        }

        for strategy_cls in cls.strategies:
            strategy_cls = strategy_cls()
            if strategy_cls.should_be_executed(context):
                # Run the strategy
                strategy_cls.run(context)

                # Break the loop if everything went well
                break
        else:
            logger.warning(f"No strategy was executed for {service_id}")


HANDLERS = {
    Service.INITIAL_PICKUP: ServiceHandler,
    Service.EXTRA_PICKUP: ServiceHandler,
    Service.PARTIAL_DELIVERY: ServiceHandler,
    Service.FINAL_DELIVERY: ServiceHandler,
    Service.BOX: ServiceWithNoWarehouseHandler,
    Service.MOVING: ServiceWithNoWarehouseHandler,
    Task.TASK: TaskHandler,
    WarehouseMovement.TRACKING: WarehouseMovementHandler,
}


class Callback(BaseCallback):
    def callback(self, message, **kwargs):
        """
        This function decodes the message into a Python native object, parses riderapp forms accordingly,
        performs a fast fail check if no handler is present for the given service type, gets the last event date
        (taking care if the date is in milliseconds), and updates date data.

        Args:
            message: the message to be decoded
            **kwargs: additional keyword arguments

        Returns:
            None
        """
        # Decode message into python native object
        message = json.loads(message.data)

        logger.info(f">>> Message: {message}")

        # Parse riderapp forms accordingly
        metadata = message.get("raw_data", {}) or {}
        event_id = message.get("id")
        type = metadata.get("type")

        timestamp = metadata.get("timestamp")

        # Fast fail check if no handler is present for given service type
        handler = HANDLERS.get(type)
        if not handler:
            raise SkipCallbackException(f"Handler {type} not found. Skipping message {event_id}")

        # Get last event date, taking care if date is in milliseconds
        try:
            event_date = make_aware(datetime.datetime.fromtimestamp(timestamp), timezone=pytz.utc)
        except ValueError:
            event_date = make_aware(datetime.datetime.fromtimestamp(timestamp / 1000.0), timezone=pytz.utc)

        with transaction.atomic():
            event_log, created = Event.objects.select_for_update().get_or_create(
                type=Event.RIDERAPP,
                event_id=event_id,
                defaults={
                    "raw_data": message,
                },
            )

            if not created and event_log.created_at > event_date:
                logger.warning(f"Event {event_id} discarded because is older than already processed")
                return

            # Update date data
            event_log.created_at = event_date
            event_log.save(update_fields=["created_at"])

        handler.handle(message)
