import json
import logging

from dateutil.parser import parse
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from intranet.models import PaymentReminder
from webhooks.callbacks.base import BaseCallback

logger = logging.getLogger(__name__)


class Callback(BaseCallback):
    """Listen for payment reminder events"""

    def sanitize_date(self, date):
        try:
            return parse(date, dayfirst=True).date()  # Assumes day comes before month in the date string
        except Exception:
            return None

    def callback(self, message, **kwargs):
        message = json.loads(message.data) or {}

        logger.info(f">>> Message: {message}")

        # Load message
        event_id = str(message.get("event_id") or "").strip()
        metadata = message.get("metadata") or {}

        try:
            PaymentReminder.objects.create(
                email=metadata.get("email"),
                first_name=metadata.get("name"),
                address=metadata.get("pickup_address"),
                date=self.sanitize_date(metadata.get("pickup_date")),
                time_slot=metadata.get("time_slot"),
                pipeline=metadata.get("pipeline"),
                deal_id=metadata.get("deal_id"),
                channel=message.get("channel"),
                event_ts=message.get("event_ts"),
                event_id=event_id,
            )
        except (IntegrityError, ValidationError) as error:
            logger.warning(f"Error creating payment reminder for event {event_id}: {error}")
