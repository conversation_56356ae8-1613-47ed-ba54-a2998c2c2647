import datetime
import json
import logging

import pytz
from dateutil import parser
from django.conf import settings
from django.db import transaction
from django.utils.timezone import make_aware
from pymongo import InsertOne, MongoClient, UpdateOne
from pymongo.errors import BulkWriteError, PyMongoError

from contracts.models import Contract
from core.models.event_log import Event
from webhooks.callbacks.base import BaseCallback
from webhooks.callbacks.exceptions import CallbackException

logger = logging.getLogger(__name__)

COLLECTIONS = {
    "1001": settings.MONGODB_COLLECTION_1001,
    "1002": settings.MONGODB_COLLECTION_1002,
    "1003": settings.MONGODB_COLLECTION_1003,
    "1004": settings.MONGODB_COLLECTION_1004,
    "1021": settings.MONGODB_COLLECTION_1021,
    "1031": settings.MONGODB_COLLECTION_1031,
    "1032": settings.MONGODB_COLLECTION_1032,
    "1041": settings.MONGODB_COLLECTION_1041,
    "1051": settings.MONGODB_COLLECTION_1051,
    "1052": settings.MONGODB_COLLECTION_1052,
    "2001": settings.MONGODB_COLLECTION_2001,
    "2002": settings.MONGODB_COLLECTION_2002,
    "2010": settings.MONGODB_COLLECTION_2010,
    "2011": settings.MONGODB_COLLECTION_2011,
    "3001": settings.MONGODB_COLLECTION_3001,
    "4000": settings.MONGODB_COLLECTION_4000,
    "5000": settings.MONGODB_COLLECTION_5000,
}


class Callback(BaseCallback):
    """Parse inventories and save the raw data as well in different collections under the same database"""

    database_instance = None

    @property
    def database(self):
        """Get database connection lazily"""
        if not self.database_instance:
            self.database_instance = MongoClient(settings.MONGODB_URI, connect=False)

        return self.database_instance

    def parse_pickup_inventory(self, data, contract_id, signed_user_name):
        """Parse inventory data"""
        inventory_pickup_list = data.get("inventory_pickup", []) or [{}]
        service_date = parser.parse(data.get("date")).replace(tzinfo=pytz.utc).strftime("%Y-%m-%d")

        return [
            InsertOne({
                "EnterDate": service_date,
                "Contract_id": contract_id,
                "ObjectCode": item["barcode_pickup"],
                "ObjectDescription": item["description_pickup"],
                "DeliveryDate": None,
                "Name": signed_user_name,
            })
            for item in inventory_pickup_list
        ]

    def parse_delivery_inventory(self, data, contract_id, signed_user_name):
        """Parse delivery inventory to update them"""

        inventory_delivery_list = data.get("inventory_delivery") or []
        service_date = parser.parse(data.get("date")).replace(tzinfo=pytz.utc).strftime("%Y-%m-%d")

        return [
            UpdateOne(
                {
                    "ObjectCode": (item.get("barcode_auto", {}) or {}).get("objectCode") or item.get("barcode_manual"),
                    "Contract_id": contract_id,
                },
                {
                    "$setOnInsert": {
                        "EnterDate": None,
                        "ObjectCode": (item.get("barcode_auto", {}) or {}).get("objectCode")
                        or item.get("barcode_manual"),
                        "Contract_id": contract_id,
                        "ObjectDescription": (item.get("barcode_auto", {}) or {}).get("objectDescription")
                        or item.get("description_delivery"),
                        "Name": signed_user_name,
                    },
                    "$set": {
                        "DeliveryDate": service_date,
                    },
                },
                upsert=True,
            )
            for item in inventory_delivery_list
        ]

    def set_signed_user_name(self, signed_user_name: dict) -> str:
        first_name = signed_user_name.get("user__first_name")
        last_name = signed_user_name.get("user__last_name")
        signed_user_name = signed_user_name.get("signed_user_name")

        if signed_user_name:
            return signed_user_name

        if first_name and last_name:
            return f"{first_name} {last_name}"

        if first_name:
            return first_name

        return last_name

    def callback(self, message, **kwargs):
        # Decode message into python native object
        message = json.loads(message.data)

        logger.info(f">>> Message: {message}")

        # Parse moreapp forms accordingly
        data = message.get("data") or {}
        metadata = data.get("data") or {}
        info = data.get("info") or {}

        # Get last event date, taking care if date is in milliseconds
        try:
            event_date = make_aware(datetime.datetime.fromtimestamp(info.get("date")), timezone=pytz.utc)
        except ValueError:
            event_date = make_aware(datetime.datetime.fromtimestamp(info.get("date") / 1000.0), timezone=pytz.utc)

        with transaction.atomic():
            event_id = message.get("id")
            event_log, created = Event.objects.select_for_update().get_or_create(
                type=Event.MOREAPP_INVENTORY,
                event_id=event_id,
                defaults={
                    "raw_data": message,
                },
            )

            if not created and event_log.created_at > event_date:
                logger.warning(f"Event {event_id} discarded because is older than already processed")
                return

            # Update date data
            event_log.created_at = event_date
            event_log.save(update_fields=["created_at"])

        try:
            # Save it as raw data
            mongo_collection = self.database[settings.MONGODB_DATABASE]["raw"]
            mongo_collection.insert_one(message)

            # Perform the database write
            warehouse_id = metadata.get("warehouse_id")
            if warehouse_id:
                service_type = metadata.get("service_type", None)
                contract_id = metadata.get("contract_id")

                # Before continuing, get the person who paid the service
                signed_user_name = (
                    Contract.objects.select_related("user")
                    .filter(contract_document_id=contract_id)
                    .values("signed_user_name", "user__first_name", "user__last_name")
                    .first()
                    or {}
                )

                # Set username by preference
                signed_user_name = self.set_signed_user_name(signed_user_name)

                # Parse inventory
                is_delivery = service_type in ["partial_delivery", "final_delivery"]
                if is_delivery:
                    inventory = self.parse_delivery_inventory(metadata, contract_id, signed_user_name)
                else:
                    inventory = self.parse_pickup_inventory(metadata, contract_id, signed_user_name)

                mongo_collection = self.database[settings.MONGODB_DATABASE][COLLECTIONS.get(warehouse_id)]
                mongo_collection.bulk_write(inventory, ordered=False)
        except KeyError:
            raise CallbackException("Unknown mongodb collection possibly due to wrong warehouse id")
        except BulkWriteError as error:
            logger.error(f"Error writing data into mongodb {error}")
            raise CallbackException(error)
        except PyMongoError as error:
            logger.error(f"Broad error performing mongodb operation {error}")
            raise CallbackException(error)
