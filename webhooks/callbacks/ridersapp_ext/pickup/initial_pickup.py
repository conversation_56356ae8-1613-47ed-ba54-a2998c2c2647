from contracts.models import Service
from contracts.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>
from core.models import Product
from webhooks.callbacks.ridersapp_ext.base import AdditionalChargeMixin
from webhooks.callbacks.ridersapp_ext.pickup import BasePickupParser
from webhooks.callbacks.ridersapp_ext.utils import (format_product_item,
                                                    parse_additional_services,
                                                    set_quantity,
                                                    set_warehouse_space_key)


class InitialPickupParser(BasePickupParser, AdditionalChargeMixin):
    service_type = Service.INITIAL_PICKUP
    should_update_deal_stage = True
    should_create_subscription = True

    def build_products(self, base, metadata, service, warehouse_space, warehouse_space_more_than_upper_case, is_legacy):
        warehouse_space_key = set_warehouse_space_key(service, warehouse_space, warehouse_space_more_than_upper_case)
        quantity = set_quantity(service, warehouse_space, warehouse_space_more_than_upper_case)
        products = parse_additional_services(metadata, warehouse_space_key)

        # Automatic plans only allows monthly/biannual/yearly plans if has been set on the service previously
        billing_cycle = f"{service.billing_cycle_for_planes or MONTHLY}_{warehouse_space_key}"

        # Set extra charge if needed
        extra_charge = self.set_additional_charge(service, warehouse_space_key, not bool(products))

        # Create the product dict to query against database
        products.update(**{
            billing_cycle: format_product_item(
                quantity=quantity,
                exclude_charge_on_event=True,
            ),
            **extra_charge,
        })

        filter_keys = {
            "moreapp_internal_id__in": products.keys(),
            "is_legacy": is_legacy,
        }

        if is_legacy:
            country = "ES"
            if base:
                country = base.country
            elif service and service.warehouse and service.warehouse.city:
                country = service.warehouse.city.country
            filter_keys.update(**{
                "city__country": country,
            })
        else:
            filter_keys.update(**{
                "city": base,
            })

        # As default, set base as ES-M, because we don't have any base in this case
        product_list = Product.objects.filter(**filter_keys).values("moreapp_internal_id", "chargebee_product_id")

        # Parse hired services in the annex to create/update the subscription
        return {
            product["chargebee_product_id"]: (
                products[product["moreapp_internal_id"]],
                product["moreapp_internal_id"] == billing_cycle,
            )
            for product in product_list
        }
