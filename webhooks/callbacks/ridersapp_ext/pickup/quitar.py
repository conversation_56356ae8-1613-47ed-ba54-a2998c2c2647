import json

from webhooks.callbacks.ridersapp_ext.pickup.initial_pickup import *
from webhooks.callbacks.ridersapp_ext.pickup.extra_pickup import *
from webhooks.callbacks.ridersapp_ext.delivery.final_delivery import *
from webhooks.callbacks.ridersapp_ext.delivery.partial_delivery import *

message = '''{"id": "5eb97cbb-91d1-4b6c-8fe4-9288d0b6b837", "raw_data": {"id": "4e07da48-7665-4ed7-8b49-186da7cfd572", "data": {"date": "2024-11-15", "review": false, "address": "Rua dos ciprestes n74 1*esq \n2820-555 Charneca da Caparica", "blanket": 0, "comment": "Margem sul", "parking": false, "last_name": ".", "packaging": true, "first_name": "Nadal", "kilometers": 0.0, "added_items": [{"id": "6e306b68-12d5-4fd3-ad1b-ad29ae14b01a", "photos": [], "tag_id": "EAAA22056", "description": "Mochila cão rosa "}, {"id": "74fb9cd4-0b48-40d3-9a22-887254878797", "photos": [], "tag_id": "EAAA22047", "description": "Trottinette "}, {"id": "e7731134-8a78-4c6c-ad00-4690f033d8a0", "photos": [], "tag_id": "EAAA22061", "description": "Saco 7"}, {"id": "39076bf8-df8f-4b83-acfc-550e4790090d", "photos": [], "tag_id": "EAAA22010", "description": "Cadeiras "}, {"id": "369ebd39-3c59-413c-a53a-a4b277d51e40", "photos": [], "tag_id": "EAAA22042", "description": "Pé da mesa "}, {"id": "1eebe1a0-315e-428a-8531-ce53f5e915ae", "photos": [], "tag_id": "EAAA22046", "description": "Maq "}, {"id": "27e46a7b-be52-4aee-973d-b2cf33fef26e", "photos": [], "tag_id": "EAAA22023", "description": "Candeeiro pequeno "}, {"id": "abce25fc-9158-4203-bd10-c7a06ec02c40", "photos": [], "tag_id": "EAAA22064", "description": "Caixa 12"}, {"id": "09205cdd-d9e0-436b-bf49-5b3fbd743f68", "photos": [], "tag_id": "EAAA22017", "description": "Mesa "}, {"id": "1cb6e732-9694-4e2e-a002-d2388dad5041", "photos": [], "tag_id": "EAAA22077", "description": "Cadeira bebé "}, {"id": "75afc9a8-e692-400a-ab2a-89bd98fb1371", "photos": [], "tag_id": "EAAA22079", "description": "Caixa 13"}, {"id": "6ecd2010-7b00-46bd-84dc-512e63e8b857", "photos": [], "tag_id": "EAAA22076", "description": "Colchão "}, {"id": "e04d5959-c303-466c-9906-2318b8d4d00e", "photos": [], "tag_id": "EAAA22009", "description": "Pé secretaria branco "}, {"id": "e3e253a2-fc15-4809-898e-ff5b781867b3", "photos": [], "tag_id": "EAAA22027", "description": "Cadeira escritório "}, {"id": "57d2eb3c-234e-47a8-b26c-9e5a12a036a9", "photos": [], "tag_id": "EAAA22060", "description": "Pés "}, {"id": "6e3b4d18-df8a-41ec-bdfd-202929d2781e", "photos": [], "tag_id": "EAAA22080", "description": "Caixa 14"}, {"id": "31a9778c-4739-4ade-814c-db17856e2d05", "photos": [], "tag_id": "EAAA22013", "description": "Mochila cão "}, {"id": "e950f2e2-fde6-4b58-9249-fa291a155557", "photos": [], "tag_id": "EAAA22048", "description": "Quadros frágeis "}, {"id": "8fb35d05-115b-4fa1-af7f-0bcb8d2c900a", "photos": [], "tag_id": "EAAA22012", "description": "Saco 3"}, {"id": "ade445f6-6644-42be-acbd-d6b1b177eb67", "photos": [], "tag_id": "EAAA22038", "description": "Aquecedo "}, {"id": "fdf55f59-b781-4789-9010-6012d345e6b5", "photos": [], "tag_id": "EAAA22068", "description": "Saco 8"}, {"id": "2427a068-fe0e-4d53-b2a6-1b507288a694", "photos": [], "tag_id": "EAAA22053", "description": "Mala yelow "}, {"id": "1b83eb13-14a9-4d73-9bc2-51f56927860b", "photos": [], "tag_id": "EAAA22083", "description": "Frigorífico "}, {"id": "cf45b198-87c8-4ada-9aba-3c8f3e05bd3a", "photos": [], "tag_id": "EAAA22066", "description": "Caixa 9"}, {"id": "6742a41b-b6a2-4cf6-a9f2-2088e928702f", "photos": [], "tag_id": "EAAA22058", "description": "Mesa redonda "}, {"id": "4d698e06-27c1-4c1d-8cf2-cc1b3d63aca0", "photos": [], "tag_id": "EAAA22051", "description": "Banco branco "}, {"id": "3a0e017a-1cfd-4de0-9168-b89a1e249919", "photos": [], "tag_id": "EAAA22022", "description": "Quadro "}, {"id": "1b1472cd-ebf0-46a0-a893-41eefcb1f5c9", "photos": [], "tag_id": "EAAA22036", "description": "Mala black "}, {"id": "fe646555-a82f-442d-b12c-a6868006be49", "photos": [], "tag_id": "EAAA22073", "description": "Madeiras "}, {"id": "6c3f876b-2b4b-4b07-8f2b-0c735dd88a5b", "photos": [], "tag_id": "EAAA22055", "description": "Tupperware "}, {"id": "59333a81-1e43-45c9-9e0b-837a4ff24db0", "photos": [], "tag_id": "EAAA22014", "description": "Chapeus praia "}, {"id": "66fd08df-2a57-439a-b87d-e94c18720032", "photos": [], "tag_id": "EAAA22075", "description": "Caixa 10"}, {"id": "ebdaa9e0-b0f7-4bbe-87dc-4d84ecc77ad2", "photos": [], "tag_id": "EAAA22034", "description": "Mala amarela "}, {"id": "da8e9efa-9b5d-474c-acb7-5f14b2a37253", "photos": [], "tag_id": "EAAA22082", "description": "Tv"}, {"id": "ba2ebf8e-085f-4f01-be6a-6cd4dbae16dc", "photos": [], "tag_id": "EAAA22049", "description": "Mala rosa "}, {"id": "f5888890-d93e-41e8-a0c9-4028539799ca", "photos": [], "tag_id": "EAAA22041", "description": "Móvel branco peq"}, {"id": "4ee9dbee-b2ed-45b3-a360-c6fb0ddf2f4c", "photos": [], "tag_id": "EAAA22019", "description": "Caixa 4"}, {"id": "96c5f677-7723-40ab-b489-18952b8233e7", "photos": [], "tag_id": "EAAA22030", "description": "Saco 6"}, {"id": "ab08e0e8-3269-4daf-b356-8d24a1e7115b", "photos": [], "tag_id": "EAAA22018", "description": "Saco branco "}, {"id": "1ba69051-c02d-4419-a323-3d620836f4b5", "photos": [], "tag_id": "EAAA22008", "description": "Microondas "}, {"id": "6a70a5ae-11b2-4ef6-bb3b-091a308cef9b", "photos": [], "tag_id": "EAAA22065", "description": "Caixa 11"}, {"id": "7e93c5b4-d416-4aff-b0d0-723f2ae6b3d9", "photos": [], "tag_id": "EAAA22011", "description": "Saco 2"}, {"id": "cf809970-7d0a-4c04-b7c2-9df1086a6148", "photos": [], "tag_id": "EAAA22057", "description": "Saco 9"}, {"id": "b8720ca2-46e5-4d58-b750-7c45e315e311", "photos": [], "tag_id": "EAAA22050", "description": "Base da cama"}, {"id": "ca134b5f-23bd-467e-81ad-f474d1c39d67", "photos": [], "tag_id": "EAAA22007", "description": "Candeeiro "}, {"id": "9da014d9-0cca-48cb-ad5b-35f66308c265", "photos": [], "tag_id": "EAAA22081", "description": "Madeiras cama "}, {"id": "5d93d990-61ab-441b-8a2d-7be33319e04e", "photos": [], "tag_id": "EAAA22067", "description": "Caixa 8"}, {"id": "2a406f58-4f5c-46af-a3c4-b23dc3186707", "photos": [], "tag_id": "EAAA22054", "description": "Tapete verde "}, {"id": "349bdd79-cee4-4bd9-a7c1-5fcf5a11fdaa", "photos": [], "tag_id": "EAAA22016", "description": "Saco 1"}, {"id": "b1161238-df26-4461-aa57-db889e3b636c", "photos": [], "tag_id": "EAAA22024", "description": "Mochila "}, {"id": "23c7c36d-184f-41fd-a55e-52080160691d", "photos": [], "tag_id": "EAAA22020", "description": "Caixa 1"}, {"id": "62a3a0b5-3437-42ae-bc57-e3701cb15d83", "photos": [], "tag_id": "EAAA22063", "description": "Saco desmontado "}, {"id": "a53dd07e-6752-4f83-a995-c44a56605fd2", "photos": [], "tag_id": "EAAA22040", "description": "Mochila azul "}, {"id": "8b845c9a-bbcc-41e6-bc0c-283efb5fd9c1", "photos": [], "tag_id": "EAAA22072", "description": "Caixa 5"}, {"id": "3ccf4df0-b5ee-4e5d-a72e-f3ad37c28de7", "photos": [], "tag_id": "EAAA22026", "description": "Saco bimba "}, {"id": "a5722882-2cbc-4d27-a100-cd9ba868a765", "photos": [], "tag_id": "EAAA22021", "description": "Caixa 3"}, {"id": "6f96672a-f850-4577-b41f-ed7ee5be438d", "photos": [], "tag_id": "EAAA22028", "description": "Caixa 2"}, {"id": "74844503-b2e8-4a7e-b342-72fe0900d22d", "photos": [], "tag_id": "EAAA22031", "description": "Saco 4"}, {"id": "64833a8f-f463-41e1-91a7-b4f63c329b6d", "photos": [], "tag_id": "EAAA22006", "description": "Saco primark castanho "}, {"id": "90d7fff6-d7a1-499e-8553-cfae2ab3c0b8", "photos": [], "tag_id": "EAAA22078", "description": "Cesto "}, {"id": "99513897-bd14-4177-8e90-2628d407bc8c", "photos": [], "tag_id": "EAAA22035", "description": "Mala orange"}, {"id": "44aa0ab6-e898-42ef-ad02-63ad504bc09d", "photos": [], "tag_id": "EAAA22074", "description": "Carrinho "}, {"id": "4c5df3e5-0574-49f5-bc80-ac796c447a18", "photos": [], "tag_id": "EAAA22052", "description": "Caixa 6"}, {"id": "06f38f2b-62d2-4251-9ff5-1b752334977f", "photos": [], "tag_id": "EAAA22044", "description": "Base de ferro "}, {"id": "edcb23c7-7b78-42a0-9eaf-a7b723eab6e0", "photos": [], "tag_id": "EAAA22069", "description": "Cesto rede "}, {"id": "1d49e96e-7313-499c-b982-6285d11de1cd", "photos": [], "tag_id": "EAAA22070", "description": "Caixa 7"}, {"id": "14e09c5f-6cdb-4d6e-ab37-63921546afe8", "photos": [], "tag_id": "EAAA22043", "description": "Aspirador "}, {"id": "2f7fd0fb-21e6-4492-a9ee-d5cb0671459e", "photos": [], "tag_id": "EAAA22037", "description": "Saco com balança "}, {"id": "89155686-60b9-4eb8-8ad6-a783c1429411", "photos": [], "tag_id": "EAAA22062", "description": "Cesto da cozinha "}, {"id": "0b9b7806-349d-4ab6-b983-88e88db55b13", "photos": [], "tag_id": "EAAA22045", "description": "Saco declatlon "}, {"id": "ae196642-a1d5-4107-b7a7-6df2c87cb481", "photos": [], "tag_id": "EAAA22039", "description": "Estendal "}, {"id": "d96cbe59-d23b-4579-94d9-6a0e85aae4b6", "photos": [], "tag_id": "EAAA22033", "description": "Mala rosa"}, {"id": "c314c258-1761-4bc9-982a-cc3385ededa9", "photos": [], "tag_id": "EAAA22025", "description": "Bacia "}], "clean_space": 0.0, "national_id": "18249535933", "stairs_floor": 0, "warehouse_id": "2002", "removed_items": [], "stairs_volume": 0.0, "stairs_options": false, "additional_stop": false, "blanket_options": false, "rider_signature": "documentation/riders/13647f94-943e-4d0f-a2a6-3d5345d2324e/signatures/13647f94-943e-4d0f-a2a6-3d5345d2324e.jpg", "warehouse_space": "7.5", "amount_box_normal": 0, "plus_fifty_meters": null, "box_closet_options": false, "box_normal_options": false, "customer_signature": "documentation/18249535933/signatures/4e07da48-7665-4ed7-8b49-186da7cfd572-13647f94-943e-4d0f-a2a6-3d5345d2324e_U.jpg", "kilometers_options": false, "amount_delivery_box": 0, "clean_space_options": false, "additional_stop_comments": null, "furniture_assembly_extra": null, "furniture_assembly_units": null, "furniture_assembly_options": false, "amount_box_closet_packaging": 0, "amount_box_normal_packaging": 0, "packaging_box_closet_options": false, "packaging_box_normal_options": false, "furniture_assembly_description": null}, "meta": {"id": "9B25245A-F2D1-462D-9348-8BBEE3B5DD0D", "os": "iOS 17.6.1", "model": "iPhone", "device": "iPhone", "version": "v2.0.6", "location": {"latitude": 38.768139563335446, "longitude": -9.307866711861024}, "metadata": {"machine": "iPhone16,2", "release": "23.6.0", "sysname": "Darwin", "version": "Darwin Kernel Version 23.6.0: Fri Jul  5 18:04:28 PDT 2024; root:xnu-10063.142.1~1/RELEASE_ARM64_T8122", "nodename": "localhost"}}, "type": "initial_pickup", "timestamp": 1731675854320, "is_updated": true, "contract_id": "18249535933", "customer_id": "13647f94-943e-4d0f-a2a6-3d5345d2324e"}, "contract_id": "18249535933"}'''
message = json.loads(message, strict=False)
data = message.get("raw_data", {}) or {}
metadata = data['data']

PARSERS = {
    "pickup": {
        "parser": InitialPickupParser,
        "id": "4e07da48-7665-4ed7-8b49-186da7cfd572"
    },
    "delivery": {
        "parser": PartialDeliveryParser,
        "id": "14d908c4-24f6-444c-b132-b8fbec243eb7"
    }
}

mode = "pickup"
parser = PARSERS[mode]["parser"]()
service = Service.objects.get(id=PARSERS[mode]["id"])
parser.run(service, data, metadata, service.service_name, False)
