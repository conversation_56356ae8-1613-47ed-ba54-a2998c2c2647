from moreapp.models import *

self = MoreappEmail.objects.get(id="ddc32878-9c8c-407d-8da6-c0bf8fc4f49a")
filled_in_excel = self.build_document()

with open("/home/<USER>/Documents/salida.xls", "wb") as fp:
    fp.write(filled_in_excel.getbuffer())

from contracts.models import *

self = Service.objects.get(id="3613f583-1761-4a55-a16c-9fbc4a3b9958")
language = "ES"
doc = self.to_file(language)

with open("/home/<USER>/Documents/doc.pdf", "wb") as fp:
    fp.write(doc.getbuffer())

doc = self.conditions_file(language)

with open("/home/<USER>/Documents/docCF.pdf", "wb") as fp:
    fp.write(doc.getbuffer())

["#7986CB", "#33B679", "#8E24AA", "#E67C73", "#F6BF26", "#F4511E", "#039BE5", "#616161", "#3F51B5", "#0B8043", "#D50000"]