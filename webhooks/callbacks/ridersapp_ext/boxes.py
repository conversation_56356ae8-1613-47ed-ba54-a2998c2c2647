import logging

import pytz
from dateutil import parser
from django.db import IntegrityError, transaction

from contracts.models import Box, Contract
from core.models import Product
from intranet.models import City
from payments.models import Quote
from webhooks.callbacks.exceptions import CallbackException
from webhooks.callbacks.ridersapp_ext.base import BaseServiceParser
from webhooks.callbacks.ridersapp_ext.utils import (build_service_documents,
                                                    create_chargebee_item,
                                                    parse_additional_services)

logger = logging.getLogger(__name__)


class BoxParser(BaseServiceParser):
    def run(self, service, data, metadata, update):
        contract_id = data.get("contract_id")
        is_updated = data.get("is_updated")
        first_name = metadata.get("first_name")
        last_name = metadata.get("last_name")
        identity_document = metadata.get("national_id")
        service_date = parser.parse(metadata.get("date")).replace(tzinfo=pytz.utc)
        # address = metadata.get("address")
        country = service.country

        # Appendix for remote_file to note that has been updated to avoid overriding the original document
        appendix = "" if not is_updated else "_U"

        # Metadata
        internal_id = data.get("id")
        remote_file = f"/{contract_id}/boxes{appendix}_{internal_id}.pdf"
        remote_tos_file = f"/{contract_id}/terms_of_service_{internal_id}.pdf"

        # Service options
        box_normal_units = int(metadata.get("amount_box_normal") or 0)
        box_wardrobe_units = int(metadata.get("amount_delivery_box") or 0)
        seal_units = int(metadata.get("seal") or 0)
        bubble_paper_units = int(metadata.get("bubble_paper") or 0)

        # Create the product dict to query against database
        products = parse_additional_services(metadata, None)

        # Quote creation
        quote = None
        quote_created = None

        try:
            with transaction.atomic():
                # Get or create the contract related to this form
                contract, _ = Contract.objects.get_or_create(contract_document_id=contract_id)

                # Create or update the box contract type and link to "parent" contract
                _, box_created = Box.objects.only(
                    "remote_file",
                    "raw_data",
                    "signed_date",
                    "internal_id",
                    "contract",
                    "contract_document_id",
                    "first_name",
                    "last_name",
                    "identity_document",
                    "normal_boxes",
                    "wardrobe_boxes",
                    "seal",
                    "bubble_paper",
                    "should_send_review_mail",
                    "in_queue",
                ).update_or_create(
                    internal_id=internal_id,
                    defaults={
                        "remote_file": remote_file,
                        "signed_date": service_date,
                        "raw_data": metadata,
                        "contract": contract,
                        "contract_document_id": contract_id,
                        "first_name": first_name,
                        "last_name": last_name,
                        "identity_document": identity_document,
                        "normal_boxes": box_normal_units,
                        "wardrobe_boxes": box_wardrobe_units,
                        "seal": seal_units,
                        "bubble_paper": bubble_paper_units,
                        "should_send_review_mail": False,
                        "service": service,
                    },
                )

                if products:
                    city = City.objects.filter(country=country).first()

                    filter_keys = {
                        "moreapp_internal_id__in": products.keys(),
                        "is_legacy": contract.imported_legacy,
                    }

                    if contract.imported_legacy:
                        filter_keys.update(**{
                            "city__country": country,
                        })
                    else:
                        filter_keys.update(**{
                            "city": city,
                        })
                    product_list = Product.objects.filter(**filter_keys).values(
                        "moreapp_internal_id", "chargebee_product_id"
                    )
                    # Parse hired services in the annex to create/update the subscription
                    products = {
                        product["chargebee_product_id"]: (products[product["moreapp_internal_id"]], False)
                        for product in product_list
                    }

                    # Create the quote in order to create the chargebee quote
                    quote, quote_created = Quote.objects.update_or_create(
                        form_id=f"{internal_id}{appendix}",
                        defaults={
                            "service_type": Quote.BOX,
                            "line_items": [create_chargebee_item(products, product) for product in products],
                            "contract": contract,
                            "is_updating_another_service": appendix == "_U",
                        },
                    )

            if quote and (quote_created or update):
                quote.create_in_chargebee()
        except IntegrityError as err:
            raise CallbackException(err) from err

        # Build the .pdf file, store it into google cloud storage and send it to the customer
        build_service_documents(
            service_id=str(service.pk),
            submission_id=internal_id,
            contract_destination=remote_file,
            tos_destination=remote_tos_file,
            document_type="Box",
            force_send=update,
        )
