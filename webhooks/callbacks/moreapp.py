import datetime
import json
import logging

import pytz
from django.db import transaction
from django.utils.timezone import make_aware

from core.models.event_log import Event
from webhooks.callbacks.base import BaseCallback
from webhooks.callbacks.moreapp_ext.annexes import AnnexParser
from webhooks.callbacks.moreapp_ext.base import <PERSON>appBaseParser
from webhooks.callbacks.moreapp_ext.boxes import BoxParser
from webhooks.callbacks.moreapp_ext.movings import MovingParser

logger = logging.getLogger(__name__)

MOREAPP_FORMS = {
    "npanexo": AnnexParser,
    "npboîtes": BoxParser,
    "npcaixas": BoxParser,
    "npcajas": BoxParser,
    "npdéménagement": MovingParser,
    "npmudança": MovingParser,
    "npmudanza": MovingParser,
}

DEFAULT_BASES_FOR_BOXES_AND_MOVINGS = {
    "npcaixas": "PT-L",
    "npmudança": "PT-L",
    "npcajas": "ES-M",
    "npmudanza": "ES-M",
    "npboîtes": "FR-P",
    "npdéménagement": "FR-P",
}


class Callback(BaseCallback):
    """Process moreapp response accordingly. We are only interested in annexes, because contracts will be signed
    in third-party CRM"""

    def callback(self, message, **kwargs):
        # Decode message into python native object
        message = json.loads(message.data)

        logger.info(f">>> Message: {message}")

        # Parse moreapp forms accordingly
        data = message.get("data") or {}
        metadata = data.get("data") or {}
        info = data.get("info") or {}

        # Get last event date, taking care if date is in milliseconds
        try:
            event_date = make_aware(datetime.datetime.fromtimestamp(info.get("date")), timezone=pytz.utc)
        except ValueError:
            event_date = make_aware(datetime.datetime.fromtimestamp(info.get("date") / 1000.0), timezone=pytz.utc)

        with transaction.atomic():
            event_id = message.get("id")
            event_log, created = Event.objects.select_for_update().get_or_create(
                type=Event.MOREAPP,
                event_id=event_id,
                defaults={
                    "raw_data": message,
                },
            )

            if not created and event_log.created_at > event_date:
                logger.warning(f"Event {event_id} discarded because is older than already processed")
                return

            # Update date data
            event_log.created_at = event_date
            event_log.save(update_fields=["created_at"])

        # Parse the moreapp form outside the transaction
        event_type = info.get("formName").split(" ")[0].lower().strip()
        default_base = DEFAULT_BASES_FOR_BOXES_AND_MOVINGS.get(event_type)
        event = MOREAPP_FORMS.get(event_type, MoreappBaseParser)()
        event.parse(data, metadata, info, default_base)
