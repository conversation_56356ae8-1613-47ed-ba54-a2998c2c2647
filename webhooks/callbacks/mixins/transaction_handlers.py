import logging
from collections import defaultdict

import chargebee.api_error
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from sentry_sdk import capture_exception

from core.emails.message import EmailMessage
from core.utils import get_chargebee_instance
from payments.models import Invoice

logger = logging.getLogger("name")


class NotificationMixin:
    """Mixin to set send notification after transaction has been handled"""

    should_notify = True

    EMAILS_BY_SITE = {
        "ES": settings.SUPPORT_TEAM_ATC_EMAILS,
        "PT": settings.SUPPORT_TEAM_ATC_EMAILS,
        "FR": settings.SUPPORT_TEAM_ATC_EMAILS,
        "IT": settings.SUPPORT_TEAM_ATC_EMAILS,
        "MX": settings.SUPPORT_TEAM_ATC_EMAILS_MX,
    }

    def ensure_emails(self, site, default="ES"):
        """
        Ensure emails for a given site, with an optional default.
        Returns the emails associated with the given site, or the default emails if none are found.

        Args:
            site: The site for which to ensure emails.
            default: The default site to use if no emails are found for the given site.

        Returns:
            The emails associated with the given site, or the default emails if none are found.
        """

        emails = self.EMAILS_BY_SITE.get(site)
        if not emails:
            return self.EMAILS_BY_SITE[default]

        return emails

    def notify(self, subject, data, site):
        """
        Notify function to send an email message with the provided subject, data, and site.

        Args:
            subject (str): The subject of the email.
            data (dict): The data to be included in the email.
            site (str): The site for which the email is being sent.

        Returns:
            bool: True if the email is successfully sent, False otherwise.
        """
        if self.should_notify:
            emails = self.ensure_emails(site)

            try:
                message = EmailMessage(
                    subject=subject,
                    to=emails
                    + [
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                    sender="ES",
                    template="generic_email",
                    context={"data": data},
                )

                return message.send()
            except Exception as error:
                logger.error(
                    f"Error sending debugging email for chargeback with this subject {subject} due to: {error}"
                )


class FeeMixin:
    DEFAULT_FEE_BY_SITE = {
        "ES": 500,  # 5€
        "PT": 500,  # 5€
        "FR": 500,  # 5€
        "IT": 500,  # 5€
        "MX": 50000,  # 500MXN
    }

    ITEM_PRICE_ID_BY_SITE = {
        "ES": "fee-EUR",
        "PT": "fee-EUR",
        "FR": "fee-EUR",
        "IT": "fee-EUR",
        "MX": "fee-MXN",
    }

    CURRENCY_BY_SITE = {
        "ES": "€",
        "PT": "€",
        "FR": "€",
        "IT": "€",
        "MX": "MXN",
    }

    def get_default_fee_by_site(self, site):
        return self.DEFAULT_FEE_BY_SITE.get(site)

    def get_item_price_id_by_site(self, site):
        return self.ITEM_PRICE_ID_BY_SITE.get(site)

    def get_currency_by_site(self, site):
        return self.CURRENCY_BY_SITE.get(site)


class BaseTransaction:
    fee = None

    def run(self, transaction_data, site, **kwargs):
        raise NotImplementedError("Not implemented yet!")


class EmptyTransaction(BaseTransaction):
    def run(self, *args, **kwargs):
        return None


class DirectDebitTransaction(BaseTransaction, NotificationMixin, FeeMixin):
    fee = 15  # % of fee regarding transaction

    def chargebee_url(self, site, customer_id):
        chargebee_site = settings.CHARGEBEE_SITES.get(site, None) or None
        if chargebee_site:
            return settings.CHARGEBEE_URL_DOMAIN.format(site=chargebee_site["site"], model="customers", id=customer_id)

    def is_chargeback_invoice(self, invoice_line_items):
        """Check if this invoice is a chargeback invoice"""
        for line_item in invoice_line_items:
            if line_item.entity_id == "fee-EUR":
                return True

        return False

    def run(self, transaction_data, site, **kwargs):
        """On chargeback transaction, charge a fee from transaction total amount"""
        transaction_id = transaction_data.get("id")
        amount = transaction_data.get("amount")
        customer_id = transaction_data.get("customer_id")

        # Apply the fee over the transaction amount and create the one_time_charge
        fee_amount = max(int((amount * self.fee) / 100), self.get_default_fee_by_site(site))  # at least, 5€ of fee

        # Notification body
        notification_body = {
            "subject": _("[CHARGEBACK] Fee on the transaction {transaction} and invoice {invoice} due to chargeback"),
            "data": (
                f"Fee of {fee_amount / 100:.2f}{self.get_currency_by_site(site)} applied ({self.fee}%) over the total"
                f" {amount / 100:.2f}{self.get_currency_by_site(site)} to customer {customer_id} ( Chargebee {site} -"
                f" {self.chargebee_url(site, customer_id)} )."
            ),
            "site": site,
        }

        try:
            # Get invoice metadata avoiding apply the chargeback twice
            with transaction.atomic():
                invoice = (
                    Invoice.objects.only("remote_id", "chargeback_fee_date")
                    .select_for_update()
                    .filter(transaction_id=transaction_id, chargeback_fee_date__isnull=True)
                    .first()
                )

                if invoice:
                    chargebee_client = get_chargebee_instance(site)

                    # Before creating the charge, check if this invoice is not a "fee" invoice yet
                    chargebee_invoice = chargebee_client.get_invoice(invoice.remote_id).invoice
                    if self.is_chargeback_invoice(chargebee_invoice.line_items):
                        logging.warning(f"Chargeback invoice {invoice.remote_id} for transaction {transaction_id}")
                        notification_body["data"] = (
                            f"Invoice {invoice.remote_id} from customer {customer_id} ( Chargebee {site} -"
                            f" {self.chargebee_url(site, customer_id)} ) related to transaction {transaction_id}"
                            " is a chargeback invoice. In order to avoid entering a loop, it will be discarded."
                        )
                    else:
                        chargebee_client.create_invoice_charge(
                            customer_id=customer_id,
                            item_prices=[
                                {"item_price_id": self.get_item_price_id_by_site(site), "unit_price": fee_amount}
                            ],
                            auto_collection="off",
                        )

                        notification_body["subject"] = notification_body["subject"].format(
                            transaction=transaction_id, invoice=invoice.remote_id
                        )
                        invoice.chargeback_fee_date = timezone.now()
                        invoice.save(update_fields=["chargeback_fee_date"])
                else:
                    logging.warning(f"Invoice not found for transaction {transaction_id}")
                    notification_body["data"] = (
                        f"{notification_body['data']}. Invoice related to transaction {transaction_id} not found "
                        " or has been addressed before. Skipping chargeback management."
                    )
        except chargebee.api_error.APIError as error:
            logger.error(f"Error creating invoice charge in chargebee for transaction: {transaction_id} due to {error}")
            notification_body["data"] = (
                f"Error adding the fee to customer {customer_id} ( Chargebee {site} -"
                f" {self.chargebee_url(site, customer_id)} ) for transaction {transaction_id}. For security reasons in"
                " order to avoid charge customers multiple times on retries, this step won't be retried."
            )
            capture_exception(error)  # Manual capture of exception to send the message to sentry
        except Exception as error:
            logger.error(
                f"Unknown error creating invoice charge in chargebee for transaction: {transaction_id} - {error}"
            )
            notification_body["data"] = (
                f"Unknown error adding the fee to customer {customer_id} ( Chargebee {site} -"
                f" {self.chargebee_url(site, customer_id)} ) for transaction {transaction_id}. See logs for details."
                " For security reasons in order to avoid charge customers multiple times on retries, this step won't"
                " be retried. "
            )
            capture_exception(error)  # Manual capture of exception to send the message to sentry
        finally:
            # Set the default subject if is not set
            notification_body["subject"] = notification_body["subject"].format(transaction=transaction_id, invoice="-")

            # Send the email
            self.notify(**notification_body)


class CreditCardTransaction(BaseTransaction, NotificationMixin, FeeMixin):
    fee = 15  # % of fee regarding transaction

    def chargebee_url(self, site, customer_id):
        chargebee_site = settings.CHARGEBEE_SITES.get(site, None) or None
        if chargebee_site:
            return settings.CHARGEBEE_URL_DOMAIN.format(site=chargebee_site["site"], model="customers", id=customer_id)

    def is_chargeback_invoice(self, invoice_line_items):
        """Check if this invoice is a chargeback invoice"""
        for line_item in invoice_line_items:
            description = line_item.get("description", "").lower()
            entity_id = line_item.get("entity_id")
            if entity_id == "fee-EUR" or (entity_id != "fee-EUR" and description == "gastos de recobro"):
                return True

        return False

    def run(self, invoice_data, site, **kwargs):
        """On exhausted invoice, charge a fee from transaction total amount"""
        amount = invoice_data.get("amount_due")
        invoice_id = invoice_data.get("id")
        customer_id = invoice_data.get("customer_id")

        # Apply the fee over the transaction amount and create the one_time_charge
        fee_amount = max(int((amount * self.fee) / 100), self.get_default_fee_by_site(site))  # at least, 5€ of fee

        # Notification body
        notification_body = {
            "subject": _("[CHARGEBACK] Fee on the invoice {invoice} due to chargeback coming from card"),
            "data": (
                f"Fee of {fee_amount / 100:.2f}{self.get_currency_by_site(site)} applied ({self.fee}%) over the total"
                f" {amount / 100:.2f}{self.get_currency_by_site(site)} to customer {customer_id} ( Chargebee {site} -"
                f" {self.chargebee_url(site, customer_id)} )."
            ),
            "site": site,
        }

        try:
            with transaction.atomic():
                invoice = (
                    Invoice.objects.only("remote_id", "chargeback_fee_date")
                    .select_for_update()
                    .filter(remote_id=invoice_id, chargeback_fee_date__isnull=True)
                    .first()
                )

                if invoice:
                    chargebee_client = get_chargebee_instance(site)

                    # Before creating the charge, check if this invoice is not a "fee" invoice yet
                    if self.is_chargeback_invoice(invoice_data.get("line_items")):
                        logging.warning(f"Chargeback invoice {invoice_id} for card payment")
                        notification_body["data"] = (
                            f"Invoice {invoice_id} from customer {customer_id} ( Chargebee {site} -"
                            f" {self.chargebee_url(site, customer_id)} )"
                            " is a chargeback invoice. In order to avoid entering a loop, it will be discarded."
                        )
                    else:
                        chargebee_client.create_invoice_charge(
                            customer_id=customer_id,
                            item_prices=[
                                {"item_price_id": self.get_item_price_id_by_site(site), "unit_price": fee_amount}
                            ],
                            auto_collection="off",
                        )

                        notification_body["subject"] = notification_body["subject"].format(invoice=invoice.remote_id)
                        invoice.chargeback_fee_date = timezone.now()
                        invoice.save(update_fields=["chargeback_fee_date"])
                else:
                    logging.warning(f"Invoice {invoice_id} not found")
                    notification_body["data"] = (
                        f"{notification_body['data']} Invoice not found "
                        " or has been addressed before. Skipping chargeback management."
                    )
        except chargebee.api_error.APIError as error:
            logger.error(f"Error creating invoice charge in chargebee for invoice: {invoice_id} due to {error}")
            notification_body["data"] = (
                f"Error adding the fee to customer {customer_id} ( Chargebee {site} -"
                f" {self.chargebee_url(site, customer_id)} ) for invoice {invoice_id}. For security reasons in"
                " order to avoid charge customers multiple times on retries, this step won't be retried."
            )
            capture_exception(error)  # Manual capture of exception to send the message to sentry
        except Exception as error:
            logger.error(f"Unknown error creating invoice charge in chargebee for invoice: {invoice_id} - {error}")
            notification_body["data"] = (
                f"Unknown error adding the fee to customer {customer_id} ( Chargebee {site} -"
                f" {self.chargebee_url(site, customer_id)} ) for invoice {invoice_id}. See logs for details."
                " For security reasons in order to avoid charge customers multiple times on retries, this step won't"
                " be retried. "
            )
            capture_exception(error)  # Manual capture of exception to send the message to sentry
        finally:
            # Set the default subject if is not set
            notification_body["subject"] = notification_body["subject"].format(invoice="-")

            # Send the email
            self.notify(**notification_body)


TRANSACTION_TYPES = defaultdict(EmptyTransaction)
TRANSACTION_TYPES.update(**{
    "direct_debit": DirectDebitTransaction(),
    "card": CreditCardTransaction(),
})
