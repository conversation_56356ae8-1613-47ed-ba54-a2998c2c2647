import json
import logging
import traceback

import firebase_admin
import requests
from django.conf import settings
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _
from firebase_admin import firestore
from network_clients.base.base import ClientError
from requests import HTTPError

from core.emails.message import EmailMessage
from core.external_services import asynchronous_client
from core.utils import sanitize_email
from webhooks.callbacks.base import BaseCallback
from webhooks.callbacks.exceptions import CallbackException
from webhooks.clients.hubspot import hubspot_client

logger = logging.getLogger(__name__)


@firestore.transactional
def check_valid_processing_status(transaction, db_ref):
    """Run this transaction to avoid processing two or more payments for the same customer at the same time"""
    snapshot = db_ref.get(transaction=transaction)

    # If snapshot does not exist, do nothing
    if not snapshot.exists:
        return -1

    #  Abort this operation if it is currently in the 'status' state
    if snapshot.exists and snapshot.get("status") in ["in_progress", "success"]:
        return -1

    # Get current step for later
    step = snapshot.get("current_step")

    # Update the code atomically
    transaction.update(
        db_ref,
        {
            "status": "in_progress",
        },
    )

    return step


@firestore.transactional
def update_processing_status(transaction, db_ref, status, current_step):
    """Run this transaction to avoid processing two or more payments for the same customer at the same time"""

    # Update the code atomically
    transaction.update(
        db_ref,
        {
            "status": status,
            "current_step": current_step,
        },
    )


class Step:
    """Base class for running steps"""

    def run(self, *args, **kwargs):
        raise NotImplementedError("Not implemented yet!")


class ContactStep(Step):
    def is_phone_form(self, form_submission_metadata):
        """Check if this submission is coming from phone form"""
        email = form_submission_metadata.get("email")
        phone_number = form_submission_metadata.get("phone")

        return email == phone_number

    def create_hubspot_contact(self, metadata):
        """Create a hubspot contact without using the hubspot form. This is useful for only phone numbers"""
        referrer_code = metadata.get("partnership_code")
        contact_properties = {
            "phone": metadata.get("phone"),
            "is_partner_contact": True if referrer_code else False,
        }

        return hubspot_client.create_hubspot_contact(contact_properties)

    def submit_hubspot_form(self, metadata):
        form_id = settings.HUBSPOT_FORM_IDS.get(metadata.get("pipeline"))
        bricodepot_code = metadata.get("code")
        referrer_code = metadata.get("partnership_code")
        fields = [
            {"objectTypeId": "0-1", "name": "email", "value": metadata.get("email")},
            {"objectTypeId": "0-1", "name": "firstname", "value": metadata.get("firstname")},
            {"objectTypeId": "0-1", "name": "lastname", "value": metadata.get("lastname")},
            {"objectTypeId": "0-1", "name": "phone", "value": metadata.get("phone")},
            {
                "objectTypeId": "0-1",
                "name": "commercial_auth",
                "value": metadata.get("allow_commercial_notifications") or False,
            },
        ]

        # Hubspot form context
        form_context = {
            "hutk": metadata.get("hsutk"),
            "pageUri": metadata.get("page_uri"),
            "pageName": metadata.get("page_name"),
            "ipAddress": metadata.get("ip_address"),
        }

        if bricodepot_code:
            fields.append({"objectTypeId": "0-1", "name": "bricodepot_promo", "value": f";{bricodepot_code}"})

        if referrer_code:
            fields.append({"objectTypeId": "0-1", "name": "is_partner_contact", "value": True})

            # Pop hubspot user-token cookie in order to avoid deduplication of contacts
            form_context.pop("hutk")

        response = requests.request(
            "POST",
            f"https://api.hsforms.com/submissions/v3/integration/secure/submit/{settings.HUBSPOT_SITE_ID}/{form_id}",
            data=json.dumps({"fields": fields, "context": form_context}),
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.HUBSPOT_API_KEY}",
            },
        )

        response.raise_for_status()

    def run(self, form_submission_metadata):
        if self.is_phone_form(form_submission_metadata):
            # Create the contact using the simple api
            contact_id = self.create_hubspot_contact(form_submission_metadata)
            identifier_type = "id"
        else:
            # Create the hubspot form submission
            self.submit_hubspot_form(form_submission_metadata)
            contact_id = form_submission_metadata.get("email")
            identifier_type = "email"

        # Inject valid values in the original data
        form_submission_metadata.update(**{
            "query": form_submission_metadata.get("email"),
            "contact_id": contact_id,
            "identifier_type": identifier_type,
        })
        return form_submission_metadata


class DealStep(Step):
    def run(self, form_submission_metadata):
        query = form_submission_metadata.get("query")
        contact_id = form_submission_metadata.get("contact_id")
        identifier_type = form_submission_metadata.get("identifier_type")

        asynchronous_client.create_or_update_deal_on_form_submission(
            query=query, email_or_id=contact_id, identifier_type=identifier_type, in_seconds=60
        )

        return form_submission_metadata


class BookingInitialStep(Step):
    def run(self, form_submission_metadata):
        # Inject valid values in the original data
        form_submission_metadata.update(**{
            "query": form_submission_metadata.get("email"),
            "identifier_type": "deal",
        })

        return form_submission_metadata


class EmailTemplateMixin:
    templates = {"moving": "welcome_moving_email", "default": "welcome_email"}

    def email_template(self, order_type):
        return self.templates.get(order_type, self.templates["default"])


class EmailStep(Step, EmailTemplateMixin):
    def run(self, form_submission_metadata):
        language = form_submission_metadata.get("pipeline")
        email = form_submission_metadata.get("email")
        order_type = form_submission_metadata.get("orderType")

        # Check if we have a valid email before sending it
        if not sanitize_email(email):
            logger.warning(f"Wrong email: {email}")
            return

        activate(language)
        message = EmailMessage(
            subject=_("Thank you for your request! We will contact you as soon as possible. 😀"),
            to=[email],
            template=self.email_template(order_type),
            sender=language,
        )

        return message.send()


class PartnershipEmailsStep(Step):
    subject = None
    template = None

    def build_context(self, form_submission_metadata) -> str:
        return "".join([
            "Representante: <br>",
            f"<li>Nombre: {form_submission_metadata.get('firstname') or '-'}</li>",
            f"<li>Apellidos: {form_submission_metadata.get('lastname') or '-'}</li>",
            f"<li>DNI: {form_submission_metadata.get('collaboratorIdNumber') or '-'}</li>",
            f"<li>Teléfono: {form_submission_metadata.get('phone') or '-'}</li>",
            f"<li>Email: {form_submission_metadata.get('email') or '-'}</li>",
            "Empresa: <br>",
            f"<li>Tipo: {'Empresa' if form_submission_metadata.get('isCompany') else 'Autónomo'}</li>",
            f"<li>Código postal: {form_submission_metadata.get('zipCode') or '-'}</li>",
            f"<li>Provincia: {form_submission_metadata.get('state') or '-'}</li>",
            f"<li>Nombre comercial: {form_submission_metadata.get('tradeName') or '-'}</li>",
            f"<li>Nombre empresa: {form_submission_metadata.get('companyName') or '-'}</li>",
            f"<li>CIF: {form_submission_metadata.get('companyId') or '-'}</li>",
            f"<li>Dirección fiscal: {form_submission_metadata.get('address') or '-'}</li>",
        ])

    def run(self, form_submission_metadata):
        activate("ES")
        message = EmailMessage(
            subject=_("Congrats! You have a new collaborator"),
            to=settings.PARTNERSHIP_EMAIL_USERS,
            bcc=[
                "<EMAIL>",
            ],
            template="partnership_colab_email",
            sender="ES",
            context={
                "data": self.build_context(form_submission_metadata),
                "company_name": form_submission_metadata.get("companyName") or "---",
            },
        )
        return message.send()


# Factories
CREATION_STEPS_FACTORY = {
    0: ContactStep(),
    1: DealStep(),
    2: EmailStep(),
}

BOOKING_STEPS_FACTORY = {
    0: BookingInitialStep(),
    1: DealStep(),
}

# Partnership flows
PARTNERSHIP_COLLABORATOR_STEPS_FACTORY = {
    0: PartnershipEmailsStep(),
}


class StrategyRunner:
    def __init__(self, strategy):
        self._strategy = strategy

    def run(self, *args, **kwargs):
        if not self._strategy.should_be_executed(*args, **kwargs):
            return

        return self._strategy.run(*args, **kwargs)


class Strategy:
    factory = None

    def should_be_executed(self, *args, **kwargs):
        """Check if this strategy must be executed or not"""
        return True

    def run(self, form_submission_metadata):
        current_step = form_submission_metadata["current_step"]

        metadata = {"current_step": current_step, "factory_steps": len(self.factory)}

        for current_step in range(current_step, len(self.factory)):  # NOQA: B020
            try:
                # Run every step and get the current valid step
                form_submission_metadata = self.factory[current_step].run(form_submission_metadata)
            except ClientError as error:
                raise CallbackException(f"Error creating hubspot form submission: {error}") from error
            except HTTPError as error:
                logger.error(
                    f"({error.__class__}) Error running step {current_step} on factory {self.__class__.__name__} due"
                    f" to: {error}"
                )
                break
            finally:
                # Update request data
                metadata["current_step"] = current_step

        return metadata


class FormSubmissionStrategy(Strategy):
    factory = CREATION_STEPS_FACTORY


class BookingFormSubmissionHandler(Strategy):
    factory = BOOKING_STEPS_FACTORY

    def should_be_executed(self, form_submission_metadata):
        return form_submission_metadata.get("date")


class PartnershipFormSubmissionStrategy(Strategy):
    factory = PARTNERSHIP_COLLABORATOR_STEPS_FACTORY

    def should_be_executed(self, form_submission_metadata):
        return form_submission_metadata.get("collaboratorIdNumber")


# Available strategy list. Strategies should be ordered from the most restrictive perspective to the least restrictive
STRATEGIES = [PartnershipFormSubmissionStrategy, BookingFormSubmissionHandler, FormSubmissionStrategy]


class Callback(BaseCallback):
    """Listen for form submission and get data in order to create a deal and link it with user"""

    firestore_client = None

    @property
    def firebase(self):
        if not self.firestore_client:
            cred = firebase_admin.credentials.Certificate(settings.GOOGLE_CLOUD_JSON_ACCOUNT)
            firebase_admin.initialize_app(
                cred,
                {
                    "projectId": settings.GOOGLE_CLOUD_PROJECT_NAME,
                },
            )
            self.firestore_client = firestore.client()
        return self.firestore_client

    def fetch_submission_metadata(self, email):
        """Get the form submission metadata into valid dict"""
        try:
            db_ref = self.firebase.collection(settings.FIRESTORE_FORMS_DATABASE).document(email)

            return db_ref.get().to_dict() or {}
        except Exception:
            logger.error(f"Error fetching submission metadata due to: {traceback.print_exc(chain=False)}")

            # Return empty dict as fallback
            return {}

    def callback(self, message, **kwargs):
        # Decode message into python native object
        message = message.data.decode("utf-8")

        logger.info(f">>> Message: {message} - {type(message)}")

        form_submission_metadata = self.fetch_submission_metadata(message)
        firestore_id = form_submission_metadata.get("email")

        # If we don't have a valid submission form data, does not worth to retry it, so do nothing
        if not firestore_id:
            logger.error(f"Form not found in firestore database {firestore_id}")
            return

        transaction = self.firebase.transaction()
        db_ref = self.firebase.collection(settings.FIRESTORE_FORMS_DATABASE).document(firestore_id)

        current_step = check_valid_processing_status(transaction, db_ref)

        # Inject current step into form_submission_metadata
        form_submission_metadata["current_step"] = current_step

        try:
            # Major outage of Pubsub caused a loop due to this, because messages could not be acked
            # If current_step < 0 probably means that it is already in progress by another worker
            if current_step < 0:
                logger.warning(f"Submission {firestore_id} already in progress or finished")
                return

            for strategy in STRATEGIES:
                strategy = strategy()

                # Check if the strategy should be executed and break the loop in case
                if strategy.should_be_executed(form_submission_metadata):
                    strategy_context = StrategyRunner(strategy)

                    # Run the strategy
                    form_submission_metadata = strategy_context.run(form_submission_metadata)

                    # Break the loop if everything went well
                    break
        except HTTPError as error:
            raise CallbackException(f"Error creating hubspot form submission: {error}") from error
        except ClientError as error:
            logger.error(f"Error creating task for deal creation in hubspot: {error}")
        finally:
            status = (
                "failed"
                if form_submission_metadata["current_step"] + 1
                < (form_submission_metadata.get("factory_steps", -1) or -1)
                else "success"
            )
            update_processing_status(transaction, db_ref, status, current_step)
