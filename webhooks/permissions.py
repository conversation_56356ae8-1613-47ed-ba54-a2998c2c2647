from datetime import datetime

from django.conf import settings
from google.auth.transport import requests
from google.oauth2 import service_account
from google.oauth2.id_token import verify_oauth2_token
from rest_framework import permissions


class CloudTasksPermissions(permissions.BasePermission):
    """Base permission for cloud tasks endpoints"""

    def has_permission(self, request, view):
        """Only system user will have permissions to access to cloud tasks endpoints"""
        return request.user.email == "<EMAIL>" and request.user.groups.first().name == "system"


class CloudFunctionPermissions(permissions.BasePermission):
    """Base permission for pomegranate responses"""

    def __init__(self):
        self._credentials = service_account.Credentials.from_service_account_file(settings.GOOGLE_CLOUD_SERVICE_ACCOUNT)

    def has_permission(self, request, view):
        auth = request.META["HTTP_AUTHORIZATION"]
        header, token = auth.split(" ")
        response = verify_oauth2_token(token, requests.Request())

        email = self._credentials.signer_email

        exp = datetime.fromtimestamp(int(response["exp"]))
        return (
            header == "JWT",
            response["email"] == email
            and exp >= datetime.now()
            and response["email_verified"]
            and response["iss"] in ["accounts.google.com", "https://accounts.google.com"],
        )
