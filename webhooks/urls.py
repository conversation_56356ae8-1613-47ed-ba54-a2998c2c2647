from django.urls import path

from webhooks.stripe.stripe import StripeWebhookResponseView
from webhooks.views import (CloudTasksSocialAccountLinkedHandlerView,
                            CloudTasksSyncFacebookImportedCampaignHandlerView,
                            CloudTasksUpdateStripeEmailHandlerView,
                            HiperionMetricsImportedView,
                            PomegranateResponseView)

app_name = "webhooks"
urlpatterns = [
    path(
        "sync_imported_campaign/",
        CloudTasksSyncFacebookImportedCampaignHandlerView.as_view(),
        name="sync_imported_campaign",
    ),
    path(
        "update_stripe_email/",
        CloudTasksUpdateStripeEmailHandlerView.as_view(),
        name="update_stripe_email",
    ),
    path(
        "pomegranate_webhooks/",
        PomegranateResponseView.as_view(),
        name="pomegranate_webhooks",
    ),
    path(
        "stripe/",
        StripeWebhookResponseView.as_view(),
        name="stripe_webhooks",
    ),
    path(
        "historic-data/",
        CloudTasksSocialAccountLinkedHandlerView.as_view(),
        name="historic_webhooks",
    ),
    path(
        "sync_metrics_imported_campaign/",
        HiperionMetricsImportedView.as_view(),
        name="hiperion_webhooks",
    ),
]
