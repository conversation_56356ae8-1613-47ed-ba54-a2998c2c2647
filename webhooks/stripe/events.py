from payments.models import Subscription
from payments.utils import create_invoice


def invoice_paid(stripe_event):
    """listen invoice paid and trigger side effects for this hook"""

    subscription_object = stripe_event["data"]["object"]

    # Update subscription status if needed
    Subscription.objects.filter(remote_id=subscription_object["subscription"]).update(
        status=Subscription.SUBSCRIPTION_ACTIVE
    )

    # Create local invoice only if is a subscription cycle or subscription update
    create_invoice(subscription_object)


def invoice_payment_failed(stripe_event):
    """Listen invoice payment failed and trigger side effects for this hook"""

    subscription_object = stripe_event["data"]["object"]
    should_update_subscription_status = subscription_object["billing_reason"] == "subscription_cycle"

    if should_update_subscription_status:
        # Update subscription status
        Subscription.objects.filter(remote_id=subscription_object["id"]).update(status=Subscription.SUBSCRIPTION_FAILED)


def customer_subscription_deleted(stripe_event):
    """Listen customer subscription deleted and trigger side effects for this hook"""

    subscription_object = stripe_event["data"]["object"]

    # Update subscription status
    Subscription.objects.filter(remote_id=subscription_object["id"]).update(status=Subscription.SUBSCRIPTION_CANCELED)
