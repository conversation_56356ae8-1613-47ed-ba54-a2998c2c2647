from dateutil import parser
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from campaigns.models import FacebookAd, FacebookAdset, FacebookCampaign
from core.external_services.microservices import notification_pubsub_client
from core.external_services.microservices.notification import NotificationTypes
from core.external_services.microservices.pomegranate import PomegranateClient

User = get_user_model()


def parse_ad_account_and_instagram_message(message):
    """Parsing ad_account message only to check if there's errors. If everything is OK, do nothing"""
    error_msg = message.get("error") or [{}]

    error_msg = error_msg[0]
    internal_id = error_msg.pop("internal_id", None)

    update_fields = None
    if internal_id:
        update_fields = {"errors": error_msg}

    return internal_id, update_fields


def parse_campaign_message(message):
    """Parsing campaign message to update db parts accordingly"""
    success_msg = message.get("success") or [{}]
    error_msg = message.get("error") or [{}]

    internal_id = success_msg[0].get("internal_id", error_msg[0].get("internal_id", None))
    remote_id = success_msg[0].get("id", error_msg[0].get("id", None))
    remote_status = success_msg[0].get("status", "paused").lower()
    error_msg = error_msg[0]

    update_fields = None

    if internal_id:
        update_fields = {"errors": None}

        # If we have an error, update error message
        if error_msg:
            error_msg.pop("internal_id", None)
            update_fields["errors"] = error_msg
        else:
            # Everything goes well, so we clean draft status
            update_fields["draft"] = None
            update_fields.update(**{"remote_id": remote_id, "status": remote_status})

    return internal_id, update_fields


def pomegranate_finish(data):
    """Update campaign from pomegranate message"""

    has_errors = data.pop("has_errors")
    start_utc_time = data.pop("start_utc_time")
    end_utc_time = timezone.now().strftime("%Y-%m-%d %H:%M")
    campaign_id = data.pop("campaign_id")

    ad_account_response = data.pop("ad_account", {})
    instagram_response = data.pop("instagram", {})
    campaign_response = data.pop("campaign", {})
    adsets_response = data.pop("adsets", {})
    ads_response = data.pop("ads", {})

    # Parse adset responses
    adset_success_messages = adsets_response.get("success", [])
    adset_error_messages = adsets_response.get("error", [])

    for msg in adset_success_messages:
        internal_id = msg.get("internal_id")
        remote_id = msg.get("id")
        remote_status = msg.get("status", "paused").lower()
        update_fields = {"remote_id": remote_id, "status": remote_status, "errors": None, "draft": None}

        # Update facebook adset
        FacebookAdset.objects.filter(id=internal_id).update(**update_fields)

    for msg in adset_error_messages:
        internal_id = msg.pop("internal_id", None)
        update_fields = {"errors": msg}

        # Update facebook adset
        FacebookAdset.objects.filter(id=internal_id).update(**update_fields)

    # Parse ad responses
    ad_success_messages = ads_response.get("success", [])
    ad_error_messages = ads_response.get("error", [])

    for msg in ad_success_messages:
        internal_id = msg.get("internal_id")
        remote_id = msg.get("id")
        remote_status = msg.get("status", "paused").lower()
        update_fields = {"remote_id": remote_id, "status": remote_status, "errors": None, "draft": None}

        # Update facebook ad
        FacebookAd.objects.filter(id=internal_id).update(**update_fields)

    for msg in ad_error_messages:
        internal_id = msg.pop("internal_id", None)
        update_fields = {"errors": msg}

        # Update facebook ad
        FacebookAd.objects.filter(id=internal_id).update(**update_fields)

    # Finally, parse simple responses and update campaign
    ad_account_internal_id, ad_account_message = parse_ad_account_and_instagram_message(ad_account_response)
    instagram_internal_id, instagram_message = parse_ad_account_and_instagram_message(instagram_response)
    campaign_internal_id, campaign_message = parse_campaign_message(campaign_response)

    campaign_internal_id = ad_account_internal_id or instagram_internal_id or campaign_internal_id
    campaign_message = ad_account_message or instagram_message or campaign_message

    campaign_update_fields = {
        "creation_status": FacebookCampaign.FINISHED,
        "start_utc_time": (parser.parse(start_utc_time, yearfirst=True)),
        "end_utc_time": (parser.parse(end_utc_time, yearfirst=True)),
        "has_errors": has_errors,
    }

    if campaign_message:
        campaign_update_fields.update(**campaign_message)

    campaign = FacebookCampaign.objects.filter(id=campaign_internal_id)
    campaign.update(**campaign_update_fields)

    # Get with campaign instance
    campaign = campaign.first()

    if not has_errors:
        if settings.ENVIRONMENT == "PROD":
            access_token = campaign.facebook_business_account.tokens.first()
            if access_token:
                access_token = access_token["facebook"]
        else:
            access_token = settings.SANDBOX_TOKEN

        # If we have an access token and campaign is currently paused -> activate campaign
        if access_token and campaign.status != FacebookCampaign.ACTIVE:
            pomegranate_client = PomegranateClient()
            pomegranate_client.activate_campaign(
                access_token=access_token,
                campaign_id=campaign.remote_id,
                internal_id=str(campaign.pk),
                metadata=None,
                hook_url=settings.HOOK_URL,
            )

    campaign_type = campaign.__social_network__  # facebook/google

    # Finally, call to notification service to notify to frontend that campaign creation process has finished
    notification_ctx = notification_pubsub_client.build_notification_context(
        who=None, api_id=campaign_id, scope="campaign", date=None, kpi=None, social_network=campaign_type
    )

    notification_pubsub_client.create_notification(
        notification_type=NotificationTypes.pomegranate_campaign,
        context=notification_ctx,
        extra_data={"has_errors": has_errors, "social_network": campaign_type},
    )

    # Notify by email to users
    organization = campaign.business_account.business.organization
    url = (
        f"{settings.WEBAPP_URL}business/"
        f"{campaign.business_account.business.pk}/campaigns/{campaign.pk}/overview?platform="
        f"{campaign_type}"
    )

    organization.send_email(
        email_metadata={
            "subject": _("Campaign created"),
            "template": "campaign_creation_ok" if not has_errors else "campaign_creation_errors",
            "ctx": {
                "campaign_name": campaign.name,
                "url": url,
            },
        },
        permissions=["notify_campaigns"],
    )

    # Send a slack notification
    organization.send_slack_notification(
        slack_metadata={
            "format_type": "campaign_created",
            "business_name": campaign.business_account.business.name,
            "campaign_name": campaign.name,
            "campaign_objective": campaign.objective,
            "social_network": campaign_type,
            "url": url,
            "has_errors": has_errors,
        }
    )
