from core.external_services import pubsub_client
from datetime import datetime

dt = datetime(2024, 12, 2, 15, 19, 0, )
timestamp_ms = int(dt.timestamp() * 1000)

pubsub_client.request(
    topic="gcf-webhooks-to-backend-hubspot-prod",
    data={
        "appId": 599927,
        "eventId": 100,
        "subscriptionId": 1470059,
        "portalId": 25119083,
        "occurredAt": timestamp_ms,
        "subscriptionType": "deal.propertyChange",
        "attemptNumber": 0,
        "objectId": 60588585183, # EESTO ES LO QUE HAY QUE CAMBIAR
        "changeSource": "CRM",
        "propertyName": "num_associated_contacts",
        "propertyValue": "1"
    }
)
