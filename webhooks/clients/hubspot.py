import json
import logging
import time

import requests
from django.conf import settings
from hubspot import Hu<PERSON><PERSON><PERSON>
from hubspot.auth.oauth import <PERSON><PERSON><PERSON>x<PERSON> as OAuthA<PERSON>Exception
from hubspot.crm.associations import ApiException as CrmApiException
from hubspot.crm.associations import BatchInputPublicAssociation
from hubspot.crm.contacts import ApiException as ContactApiException
from hubspot.crm.contacts import SimplePublicObjectInputForCreate
from hubspot.crm.deals import ApiException as DealApiException
from hubspot.crm.deals import (BatchReadInputSimplePublicObjectId,
                               SimplePublicObjectInput)
from requests import RequestException

logger = logging.getLogger(__name__)


def parse_conflict_call(exception_traceback):
    """Parse hubspot error message on conflict to get the existing id"""
    reason_error = (json.loads(exception_traceback.body).get("message", "") or "").split("ID:")[-1]
    try:
        return int(reason_error.strip())
    except ValueError:
        pass

    return None


class HubspotClientException(Exception):
    pass


class HubspotClientV3(HubSpot):
    tokens = None

    def __init__(self, *args, **kwargs):
        if self.tokens is None:
            self.tokens = {}

        super().__init__(*args, **kwargs)

    @property
    def token(self):
        if time.time() > (self.tokens.get("expires_at") or 0):
            token_metadata = self._create_token(
                refresh_token=self.tokens.get("refresh_token") or settings.HUBSPOT_REFRESH_TOKEN
            )

            # Save token metadata
            self.tokens = {
                "access_token": token_metadata.access_token,
                "refresh_token": token_metadata.refresh_token,
                "expires_in": token_metadata.expires_in,
                "expires_at": time.time() + token_metadata.expires_in * 0.95,
            }

        return self.tokens.get("access_token")

    def _create_token(self, refresh_token=settings.HUBSPOT_REFRESH_TOKEN):
        """Get access token given the refresh token"""
        try:
            return self.auth.oauth.tokens_api.create(
                grant_type="refresh_token",
                refresh_token=refresh_token,
                client_id=settings.HUBSPOT_CLIENT_ID,
                client_secret=settings.HUBSPOT_CLIENT_SECRET,
            )
        except OAuthApiException as error:
            if refresh_token != settings.HUBSPOT_REFRESH_TOKEN:
                return self._create_token(refresh_token=settings.HUBSPOT_REFRESH_TOKEN)

            logger.error(f"Error fetching access token from hubspot: {error}")
            raise HubspotClientException(f"Error fetching access token from hubspot: {error}") from error

    def __enter__(self):
        self.access_token = self.token

    def __exit__(self, exc_type, exc_value, trace):
        pass

    def get_contact_by_id(self, contact_id, to_dict=False, properties=None):
        try:
            # Set default properties if not provided
            if properties is None:
                properties = [
                    "email",
                    "payment_link",
                    "phone",
                    "payment_method_added",
                    "firstname",
                    "lastname",
                    "with_sepa",
                ]

            response = self.crm.contacts.basic_api.get_by_id(
                contact_id=contact_id,
                archived=False,
                properties=properties,
            )

            if to_dict:
                return response.to_dict()
            return response
        except ContactApiException as error:
            logger.error(f"Error fetching contact data from hubspot: {error}")
            raise HubspotClientException(f"Error fetching contact data from hubspot: {error}") from error

    def get_contact_by_email(self, email, with_deals=False, to_dict=False, properties=None):
        try:
            # Set default properties if not provided
            if properties is None:
                properties = [
                    "email",
                    "phone",
                    "firstname",
                    "lastname",
                    "hs_object_id",
                ]

            response = self.crm.contacts.basic_api.get_by_id(
                contact_id=email,
                id_property="email",
                archived=False,
                properties=properties,
                associations=["deals"] if with_deals else None,
            )

            if to_dict:
                return response.to_dict()
            return response
        except ContactApiException as error:
            logger.error(f"Error fetching contact data by email from hubspot: {error}")
            raise HubspotClientException(f"Error fetching contact data by data from hubspot: {error}") from error

    def update_contact_by_id(self, contact_id, properties, to_dict=False):
        try:
            simple_public_object_input = SimplePublicObjectInput(properties=properties)
            response = self.crm.contacts.basic_api.update(
                contact_id=contact_id, simple_public_object_input=simple_public_object_input
            )

            if to_dict:
                return response.to_dict()
            return response
        except ContactApiException as error:
            logger.error(f"Error updating contact data in hubspot: {error}")
            raise HubspotClientException(f"Error updating contact data in hubspot: {error}") from error

    def get_deal_by_id(self, deal_id, to_dict=False, properties=None):
        try:
            # Set default properties if not provided
            if properties is None:
                properties = ["deposit", "base", "pipeline", "num_associated_contacts"]

            response = self.crm.deals.basic_api.get_by_id(
                deal_id=deal_id,
                archived=False,
                associations=["contact"],
                properties=properties,
            )

            if to_dict:
                return response.to_dict()
            return response
        except DealApiException as error:
            logger.error(f"Error fetching deal data from hubspot: {error}")
            raise HubspotClientException(f"Error fetching deal data from hubspot: {error}") from error

    def update_deal_by_id(self, deal_id, properties, to_dict=False):
        try:
            simple_public_object_input = SimplePublicObjectInput(properties=properties)
            response = self.crm.deals.basic_api.update(
                deal_id=deal_id, simple_public_object_input=simple_public_object_input
            )

            if to_dict:
                return response.to_dict()
            return response
        except DealApiException as error:
            logger.error(f"Error updating deal data in hubspot: {error}")
            raise HubspotClientException(f"Error updating deal data in hubspot: {error}") from error

    def get_deal_details(self, deal_ids: [list, str], property_list: list, to_dict: bool = False):
        # Sanity check
        if not isinstance(deal_ids, list):
            deal_ids = [deal_ids]

        try:
            batch_read_input_simple_public_object_id = BatchReadInputSimplePublicObjectId(
                properties=property_list, inputs=[{"id": deal_id} for deal_id in deal_ids]
            )

            response = self.crm.deals.batch_api.read(
                batch_read_input_simple_public_object_id=batch_read_input_simple_public_object_id,
                archived=False,
            )

            if to_dict:
                return response.to_dict()
            return response
        except DealApiException as error:
            logger.error(f"Error fetching deal details from hubspot: {error}")
            raise HubspotClientException(f"Error fetching deal details from hubspot: {error}") from error

    def associate_company_to_contact(self, company_id, contact_id, to_dict=False):
        try:
            batch_input_public_association = BatchInputPublicAssociation(
                inputs=[{"from": {"id": contact_id}, "to": {"id": company_id}, "type": "contact_to_company"}]
            )
            response = self.crm.associations.batch_api.create(
                from_object_type="contact",
                to_object_type="company",
                batch_input_public_association=batch_input_public_association,
            )

            if to_dict:
                return response.to_dict()
            return response
        except CrmApiException as error:
            logger.error(f"Error associating contact with company: {error}")
            raise HubspotClientException(f"Error associating contact with company in hubspot: {error}") from error

    def create_hubspot_contact(self, contact_properties: dict):
        simple_public_object_input = SimplePublicObjectInputForCreate(properties=contact_properties)
        try:
            response = self.crm.contacts.basic_api.create(
                simple_public_object_input_for_create=simple_public_object_input
            )
            return response.id
        except ContactApiException as error:
            if error.status == 409:
                return parse_conflict_call(error)

            logger.error(f"Error creating a contact in hubspot: {error}")
            raise HubspotClientException(f"Error creating a contact in hubspot: {error}") from error


class HubspotClientV1:
    """Hubspot client compliant with v1 version of hubspot api. This is intended to be used on complex calls where
    new approach is not possible at all"""

    url = "https://api.hubapi.com/{object}/v1/{object_path}"
    hubspot_api_key = settings.HUBSPOT_API_KEY

    def create_deal(
        self,
        contact_id,
        deal_name,
        space,
        gclid,
        base,
        pipeline,
        pipeline_stage,
        is_partner_deal,
        company_id=None,
        hubspot_owner_id=None,
        livensa_origin=None,
        ciudadela_promo=False,
        cbre_promo=False,
        students_promo=False,
        resa_promo=False,
        order_type=None,
        wuolah_promo=False,
        amro_promo=False,
        yugo_promo=False,
        xior_promo=False,
        nido_promo=False,
    ):
        """Create a deal in hubspot"""

        try:
            response = requests.request(
                "POST",
                self.url.format(object="deals", object_path="deal"),
                data=json.dumps({
                    "associations": {
                        "associatedVids": [contact_id],
                        "associatedCompanyIds": [company_id] if company_id else [],
                    },
                    "properties": [
                        {"value": pipeline, "name": "pipeline"},
                        {"value": pipeline_stage, "name": "dealstage"},
                        {"value": deal_name, "name": "dealname"},
                        {"value": gclid, "name": "gclid"},
                        {"value": base, "name": "base"},
                        {"value": space, "name": "hired_space"},
                        {"value": True, "name": "form_submitted"},
                        {"value": is_partner_deal, "name": "is_partner_deal"},
                        {"value": hubspot_owner_id, "name": "hubspot_owner_id"},
                        {"value": livensa_origin, "name": "livensa_origin"},
                        {"value": ciudadela_promo, "name": "ciudadela_promo"},
                        {"value": cbre_promo, "name": "cbre_promo"},
                        {"value": students_promo, "name": "students_promo"},
                        {"value": resa_promo, "name": "resa_promo"},
                        {"value": order_type, "name": "order_type"},
                        {"value": wuolah_promo, "name": "wuolah_promo"},
                        {"value": amro_promo, "name": "amro_promo"},
                        {"value": yugo_promo, "name": "yugo_promo"},
                        {"value": xior_promo, "name": "xior_promo"},
                        {"value": nido_promo, "name": "nido_promo"},
                    ],
                }),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.hubspot_api_key}",
                },
            )
            response.raise_for_status()
            return response.json().get("dealId")
        except RequestException as error:
            logger.error(f"Error creating a deal in hubspot: {error.response.content}")
            raise HubspotClientException(f"Error creating a deal in hubspot: {error}") from error

    def _create_note(self, deal_id, comment):
        """Create a note linked in the deal"""

        try:
            response = requests.request(
                "POST",
                self.url.format(object="engagements", object_path="engagements"),
                data=json.dumps({
                    "engagement": {
                        "active": True,
                        "ownerId": settings.HUBSPOT_OWNER_ID,  # Not relevant to create notes in hubspot
                        "type": "NOTE",
                    },
                    "associations": {
                        "contactIds": [],
                        "companyIds": [],
                        "dealIds": [deal_id],
                        "ownerIds": [],
                    },
                    "metadata": {"body": f"<b>[BOX2BOT &#129302;]</b> <br> {comment}"},
                }),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.hubspot_api_key}",
                },
            )

            response.raise_for_status()
        except RequestException as error:
            logger.error(f"Error creating a note in hubspot: {error}")
            raise HubspotClientException(f"Error creating a note in hubspot: {error}") from error

    def create_new_form_note(self, deal_id, comment):
        """Create a note for new form submission"""

        content_note = f"<u>Nuevo formulario recibido:</u> <br><ul>{comment}</ul>"

        return self._create_note(deal_id, content_note)

    def create_booking_note(self, deal_id, comment):
        """Create a note for new booking submission"""

        content_note = f"<u>Datos de reserva del cliente:</u> <br><ul>{comment}</ul>"
        return self._create_note(deal_id, content_note)


class Proxy:
    """Proxy class for hubspot client to wrap every call in the context manager to inject the access token to the client"""

    def __init__(self, klass):
        self._klass = klass

    def __getattr__(self, item):
        def inner(*args, **kwargs):
            with self._klass:
                return getattr(self._klass, item)(*args, **kwargs)

        return inner


class HubspotClient(HubspotClientV1, HubspotClientV3):
    pass


# Instance the class in order to be available on the whole app
hubspot_client = Proxy(HubspotClient())
