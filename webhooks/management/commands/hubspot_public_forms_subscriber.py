import logging

from django.conf import settings
from django.core.management import BaseCommand

from webhooks.callbacks.hubspot_public_forms import Callback
from webhooks.management.commands.subscriber_mixin import SubscriberMixin

logger = logging.getLogger(__name__)


class Command(BaseCommand, SubscriberMixin):
    topic = settings.HUBSPOT_PUBLIC_FORMS_TOPIC
    callback = Callback()
    name = "hubspot_public_forms"

    def handle(self, *args, **options):
        self.run_command(*args, **options)
