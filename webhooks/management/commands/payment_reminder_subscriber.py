import logging

from django.conf import settings
from django.core.management import BaseCommand

from webhooks.callbacks.payment_reminder import Callback
from webhooks.management.commands.subscriber_mixin import SubscriberMixin

logger = logging.getLogger(__name__)


class Command(BaseCommand, SubscriberMixin):
    topic = settings.SLACK_PAYMENT_REMINDER_TOPIC
    callback = Callback()
    name = "slack_payment_reminder"

    def handle(self, *args, **options):
        self.run_command(*args, **options)
