import logging

from django.conf import settings
from django.core.management import BaseCommand
from pymongo import MongoClient, UpdateOne

from contracts.models import Contract

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Parse mongodb contracts and update them with signed user name
    THIS CODE IS NOT INTENDED TO BE EFFICIENT, JUST FUNCTIONAL. FOR THAT REASON, IT IS SUITABLE FOR SMALL DATASETS
    """

    def read_from_collection(self, database, collection):
        collection = database[collection]
        return collection.find(projection={"_id": True, "Contract_id": True}), collection

    def get_contract_ids(self, mongo_data, contracts):
        for data in mongo_data:
            contract_id = data["Contract_id"]
            if contract_id not in contracts:
                contracts[contract_id] = contract_id

        return contracts

    def create_bulk_objects(self, mongo_data, contracts_data):
        return [
            UpdateOne(
                {"_id": data["_id"]}, {"$set": {"Name": contracts_data.get(str(data["Contract_id"]), None) or None}}
            )
            for data in mongo_data
        ]

    def handle(self, *args, **options):
        database = MongoClient(settings.MONGODB_URI, connect=False)[settings.MONGODB_DATABASE]

        # Helper data structures
        unique_contracts = {}

        mongo_datas = [
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1001),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1002),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1003),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1004),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1021),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1031),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1041),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1051),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1052),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_2001),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_2002),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_2010),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_3001),
        ]

        for data, collection in mongo_datas:
            self.get_contract_ids(data, unique_contracts)

        # Query contracts from database
        contracts = Contract.objects.filter(contract_document_id__in=list(unique_contracts.keys())).values(
            "contract_document_id", "signed_user_name"
        )

        # Prepare database contracts
        unique_contracts = {}
        for contract in contracts:
            contract_id = contract["contract_document_id"]
            signed_user_name = contract["signed_user_name"]

            if contract_id not in unique_contracts:
                unique_contracts[contract_id] = signed_user_name

        # Second data fetch from mongodb (cursor comes exhausted from above)
        mongo_datas = [
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1001),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1002),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1003),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1004),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1021),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1031),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1041),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1051),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_1052),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_2001),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_2002),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_2010),
            self.read_from_collection(database, settings.MONGODB_COLLECTION_3001),
        ]

        # Parse mongo data again and create UpdateOne item
        for data, collection in mongo_datas:
            data = self.create_bulk_objects(data, unique_contracts)
            if data:
                collection.bulk_write(data, ordered=False)
