import logging

from django.conf import settings
from django.core.management import BaseCommand

from webhooks.callbacks.ridersapp import Callback
from webhooks.management.commands.subscriber_mixin import SubscriberMixin

logger = logging.getLogger(__name__)


class Command(BaseCommand, SubscriberMixin):
    topic = settings.RIDERSAPP_TOPIC
    callback = Callback()
    name = "riders"

    def handle(self, *args, **options):
        self.run_command(*args, **options)
