import logging

from django.conf import settings
from django.core.management import <PERSON><PERSON>ommand
from django.db.models import ExpressionWrapper, F, IntegerField
from django.db.models.functions import Abs
from django.utils import timezone

from contracts.models import Annex
from core.sql import ExtractEpoch

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Sending pending mails to users on contract creation"""

    def handle(self, *args, **options):
        instances = []
        now = timezone.now()

        annexes = (
            Annex.objects.filter(
                uploaded_remote_file=True, sent=False, contract__user__email__isnull=False, warehouse__isnull=False
            )
            .annotate(
                minutes=Abs(ExpressionWrapper(ExtractEpoch(now - F("in_queue")) / 60, output_field=IntegerField()))
            )
            .select_related("contract__user", "service", "warehouse__city")
            .filter(minutes__gte=3)
        )

        for document in annexes:
            try:
                additional_recipients = settings.INTERNAL_CONTRACT_EMAILS

                if document.service:
                    sender = document.contract.user.email_domain
                    additional_recipients = additional_recipients + settings.SUPPORT_TEAM_EMAILS.get(sender)
                # Send document to user and update the internal attribute
                instances.append(
                    Annex(
                        id=document.pk,
                        sent=document.send_pdf_document_to_user(additional_recipients=additional_recipients),
                    )
                )
            except Exception as err:
                logger.warning(f"Error sending pending contract signed email: {err}")

        if instances:
            Annex.objects.bulk_update(instances, ["sent"])

        logger.info(f"Sent {len(instances)} pending emails")
