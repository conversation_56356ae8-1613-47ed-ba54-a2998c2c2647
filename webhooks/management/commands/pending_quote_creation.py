import logging

from django.core.management import BaseCommand
from django.db import transaction
from django.db.models import Q

from payments.models import Quote

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Creating pending subscription in Chargebee"""

    def handle(self, *args, **options):
        with transaction.atomic():
            quotes = (
                Quote.objects.select_for_update()
                .filter(
                    Q(status__in=[Quote.EMPTY, Quote.FAILED]),
                    Q(line_items__isnull=False),
                    Q(contract__isnull=False),
                    Q(contract__user__isnull=False),
                    Q(contract__user__created_in_chargebee=True),
                    Q(contract__user__registration_country__isnull=False),
                    Q(contract__contract_document_id__isnull=False),
                    ~Q(contract__country="FR"),  # disable temporarily
                )
                .exclude(
                    chargebee_remote_id__exact="",
                    line_items__exact="",
                )
            )

            for quote in quotes:
                quote.create_in_chargebee()
