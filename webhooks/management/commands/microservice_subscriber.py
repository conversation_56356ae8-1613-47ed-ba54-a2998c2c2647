import logging

from django.conf import settings
from django.core.management import BaseCommand

from webhooks.callbacks.microservices import Callback
from webhooks.management.commands.subscriber_mixin import SubscriberMixin

logger = logging.getLogger(__name__)


class Command(BaseCommand, SubscriberMixin):
    topic = settings.MICROSERVICES_TOPIC
    callback = Callback()
    name = "microservices"

    def handle(self, *args, **options):
        self.run_command(*args, **options)
