from django import forms
from django.db.models import OuterRef, Q, Subquery
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from core.filters import ChainedListFilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import Dict<PERSON>ield
from intranet.models import Calendar, CalendarConfig


class CalendarFilter(
    FilterListMixin,
    filters.FilterSet,
):
    calendar_name = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="calendar_name",
        method="filter_by_calendar_name",
        help_text=_("Bring calendars by name"),
    )

    city_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="city_id",
        method="filter_by_city_id",
        help_text=_("Bring calendars from a specific city"),
    )

    user_id = ListFilter(
        field=DictField(
            key_field=forms.Char<PERSON>ield(),
            value_field=forms.CharField(),
        ),
        field_name="user_id",
        method="filter_by_user_id",
        help_text=_("Bring calendars by user email"),
    )

    rider_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="rider_id",
        method="filter_by_rider_id",
        help_text=_("Bring calendars by rider email"),
    )

    created_on_remote = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="created_on_remote",
        method="filter_by_created_on_remote",
        help_text=_("Bring calendars that are created on remote"),
    )

    no_owner = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="no_owner",
        method="filter_by_no_owner",
        help_text=_("Filter only calendars without ownership"),
    )

    owner_email = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="owner_email",
        method="filter_by_owner_email",
        help_text=_("Filter only calendars which belong to a given user"),
    )

    class Meta:
        model = Calendar
        fields = (
            "calendar_name",
            "city_id",
            "user_id",
            "rider_id",
            "created_on_remote",
            "no_owner",
            "owner_email",
        )

    def filter_by_calendar_name(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_city_id(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_user_id(self, queryset, name, value):
        return self.filter(queryset, "calendarconfig__user_id", value)

    def filter_by_rider_id(self, queryset, name, value):
        return self.filter(queryset, "calendar_rider_config__rider_id", value)

    def filter_by_created_on_remote(self, queryset, name, value):
        value = self.get_value_at_index(0, value=value)
        if value is not None:
            queryset = queryset.filter(calendar_id__isnull=not value)
        return queryset.distinct()

    def filter_by_no_owner(self, queryset, name, value):
        value = self.get_value_at_index(0, value=value)
        if value is not None:
            subquery = CalendarConfig.objects.filter(calendar_id=OuterRef("id"), user=self.request.user)
            queryset = queryset.filter(Q(owner__isnull=True) & ~Q(id__in=Subquery(subquery.values("calendar_id")[:1])))

        return queryset.distinct()

    def filter_by_owner_email(self, queryset, name, value):
        return self.filter(queryset, "owner__email", value)
