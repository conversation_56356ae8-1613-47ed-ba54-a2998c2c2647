from django import forms
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from core.filters import ChainedList<PERSON>ilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import Dict<PERSON>ield
from intranet.models import ResidenceGroup


class ResidenceGroupFilter(
    FilterListMixin,
    filters.FilterSet,
):
    normalized_name = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="normalized_name",
        method="filter_by_normalized_name",
        help_text=_("Filter residence groups by name"),
    )

    created_at = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="created_at",
        method="filter_by_created_at",
        help_text=_("Filter residence groups by created at date"),
    )

    class Meta:
        model = ResidenceGroup
        fields = (
            "normalized_name",
            "created_at",
        )

    def filter_by_normalized_name(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_created_at(self, queryset, name, value):
        return self.filter(queryset, "created_at__date", value)
