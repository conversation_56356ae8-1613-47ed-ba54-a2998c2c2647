from django import forms
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from contracts.models import Service
from core.filters import ChainedListFilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import DictField


class ServiceFilter(
    FilterListMixin,
    filters.FilterSet,
):
    type = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="type",
        method="filter_by_type",
        help_text=_("Bring services by type"),
    )

    contract_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="contract_id",
        method="filter_by_contract_id",
        help_text=_("Bring services from a specific contract"),
    )

    city_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="city_id",
        method="filter_by_city_id",
        help_text=_("Bring services from a specific city"),
    )

    event_date = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="event_date",
        method="filter_by_event_date",
        help_text=_("Bring services by event date"),
    )

    created_by = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="created_by",
        method="filter_by_created_by",
        help_text=_("Bring services by a given rider"),
    )

    rider_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="rider_id",
        method="filter_by_rider_id",
        help_text=_("Bring services by riders email"),
    )

    submitted_at = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="submitted_at",
        method="filter_by_submitted_at",
        help_text=_("Bring services by created at date"),
    )

    updated_at = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="updated_at",
        method="filter_by_updated_at",
        help_text=_("Bring services by updated at date"),
    )

    status = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="status",
        method="filter_by_status",
        help_text=_("Bring services by status"),
    )

    warehouse_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="warehouse_id",
        method="filter_by_warehouse_id",
        help_text=_("Bring services from specific warehouses"),
    )

    calendar_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="calendar_id",
        method="filter_by_calendar_id",
        help_text=_("Bring services from specific calendars"),
    )

    with_event = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="with_event",
        method="filter_by_with_event",
        help_text=_("Bring services with or without events"),
    )

    residence_group_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="residence_group_id",
        method="filter_by_residence_group_id",
        help_text=_("Bring services from specific residences groups"),
    )

    residence_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="residence_id",
        method="filter_by_residence_id",
        help_text=_("Bring services from specific residences"),
    )

    class Meta:
        model = Service
        fields = (
            "type",
            "contract_id",
            "city_id",
            "event_date",
            "created_by",
            "rider_id",
            "submitted_at",
            "updated_at",
            "status",
            "warehouse_id",
            "calendar_id",
            "with_event",
            "residence_group_id",
            "residence_id",
        )

    def filter_by_type(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_contract_id(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_created_by(self, queryset, name, value):
        return self.filter(queryset, "created_by_id", value)

    def filter_by_city_id(self, queryset, name, value):
        return self.filter(queryset, "event__calendar__city_id", value)

    def filter_by_event_date(self, queryset, name, value):
        return self.filter(queryset, "event__start_time__date", value)

    def filter_by_rider_id(self, queryset, name, value):
        return self.filter(queryset, "submitted_by_id", value)

    def filter_by_submitted_at(self, queryset, name, value):
        return self.filter(queryset, "submitted_at__date", value)

    def filter_by_updated_at(self, queryset, name, value):
        return self.filter(queryset, "updated_at__date", value)

    def filter_by_status(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_warehouse_id(self, queryset, name, value):
        return self.filter(queryset, "chosen_warehouse_id", value)

    def filter_by_calendar_id(self, queryset, name, value):
        return self.filter(queryset, "event__calendar__id", value)

    def filter_by_with_event(self, queryset, name, value):
        value = self.get_value_at_index(0, value=value)
        queryset = queryset.filter(~Q(event__isnull=value))
        return queryset.distinct()

    def filter_by_residence_group_id(self, queryset, name, value):
        return self.filter(queryset, "residence__group_id", value)

    def filter_by_residence_id(self, queryset, name, value):
        return self.filter(queryset, name, value)
