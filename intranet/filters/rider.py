from django import forms
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from core.filters import ChainedList<PERSON>ilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import DictField
from riders.models import RiderProxy


class RiderFilter(
    FilterListMixin,
    filters.FilterSet,
):
    """Basic filters against user lookups using the searchbar"""

    name = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="name",
        method="filter_by_name",
        help_text=_("Bring riders by first name or last name"),
    )

    email = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="email",
        method="filter_by_email",
        help_text=_("Bring riders by email"),
    )

    only_active = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="only_active",
        method="filter_by_only_active",
        help_text=_("Bring only active riders"),
    )

    date_joined = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="date_joined",
        method="filter_by_date_joined",
        help_text=_("Bring riders by date joined"),
    )

    city_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="city_id",
        method="filter_by_city_id",
        help_text=_("Bring riders from a specific city"),
    )

    warehouse_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="warehouse_id",
        method="filter_by_warehouse_id",
        help_text=_("Bring riders from specific warehouses"),
    )

    on_leave = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="on_leave",
        method="filter_by_on_leave",
        help_text=_("Bring not disabled riders"),
    )

    calendar_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="calendar_id",
        method="filter_by_calendar_id",
        help_text=_("Bring riders associated with a specific calendar"),
    )

    class Meta:
        model = RiderProxy
        fields = ("name", "email", "only_active", "date_joined", "city_id", "warehouse_id", "on_leave", "calendar_id")

    def filter_by_name(self, queryset, name, value):
        return self.filter(queryset, "first_name", value) | self.filter(queryset, "last_name", value)

    def filter_by_email(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_only_active(self, queryset, name, value):
        queryset = queryset.filter(date_leave__isnull=True)
        return self.filter(queryset, "is_active", value)

    def filter_by_date_joined(self, queryset, name, value):
        return self.filter(queryset, "date_joined__date", value)

    def filter_by_city_id(self, queryset, name, value):
        return self.filter(queryset, "warehouseconfiguration__warehouse__city_id", value)

    def filter_by_warehouse_id(self, queryset, name, value):
        return self.filter(queryset, "warehouseconfiguration__warehouse_id", value)

    def filter_by_on_leave(self, queryset, name, value):
        value = self.get_value_at_index(0, value=value)
        queryset = queryset.filter(date_leave__isnull=not value)
        return queryset.distinct()

    def filter_by_calendar_id(self, queryset, name, value):
        return self.filter(queryset, "calendarriderconfig__calendar_id", value)
