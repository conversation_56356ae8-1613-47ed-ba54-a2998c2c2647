from django import forms
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from core.filters import ListFilter

User = get_user_model()


class UserFilter(filters.FilterSet):
    """Basic filters against user lookups using the searchbar"""

    by_name = filters.CharFilter(field_name="by_name", method="filter_by_name")
    by_phone = filters.Char<PERSON>ilter(field_name="by_phone", method="filter_by_phone")
    by_email = filters.CharFilter(field_name="by_email", method="filter_by_email")
    by_crm_id = filters.CharFilter(field_name="by_crm_id", method="filter_by_crm_id")
    by_all = filters.CharFilter(field_name="by_all", method="filter_by_all")
    by_all_only_intranet = filters.Char<PERSON>ilter(field_name="by_all_only_intranet", method="filter_by_all_only_intranet")
    by_teammates = filters.Bo<PERSON>anFilter(
        field_name="by_teammates",
        method="filter_by_team_mates",
        help_text=_("Filter by another backoffice allowed users"),
    )
    exclude_users = ListFilter(
        field=forms.UUIDField(),
        field_name="exclude_users",
        method="filter_exclude_users",
        help_text=_("Exclude user from query"),
    )

    class Meta:
        model = User
        fields = ("by_name", "by_phone", "by_email", "by_crm_id", "by_all", "by_teammates", "exclude_users")

    def filter_by_name(self, queryset, name, value):
        if value:
            queryset = queryset.filter(Q(first_name__icontains=value) | Q(last_name__icontains=value))
        return queryset.distinct().exclude(is_staff=True)

    def filter_by_phone(self, queryset, name, value):
        if value:
            queryset = queryset.filter(phone__isnull=False, phone__icontains=value)
        return queryset.distinct().exclude(is_staff=True)

    def filter_by_email(self, queryset, name, value):
        if value:
            queryset = queryset.filter(email__isnull=False, email__icontains=value)
        return queryset.distinct().exclude(is_staff=True)

    def filter_by_crm_id(self, queryset, name, value):
        if value:
            queryset = queryset.filter(crm_internal_id__isnull=False, crm_internal_id__exact=value)
        return queryset.distinct().exclude(is_staff=False)

    def filter_by_all(self, queryset, name, value):
        if value:
            queryset = queryset.filter(
                (Q(first_name__icontains=value) | Q(last_name__icontains=value))
                | (Q(phone__isnull=False) & Q(phone__icontains=value))
                | (Q(crm_internal_id__isnull=False) & Q(crm_internal_id__exact=value))
                | (Q(email__isnull=False) & (Q(email__icontains=value)))
            )
        return queryset.distinct().exclude(is_staff=True)

    def filter_by_team_mates(self, queryset, name, value):
        if value:
            queryset = queryset.filter(backoffice_allowed=True, is_active=True)

        return queryset.distinct()

    def filter_exclude_users(self, queryset, name, value):
        if value:
            queryset = queryset.exclude(id__in=value)

        return queryset.distinct()

    def filter_by_all_only_intranet(self, queryset, name, value):
        if value:
            queryset = queryset.filter(
                (Q(first_name__icontains=value) | Q(last_name__icontains=value))
                | (Q(email__isnull=False) & (Q(email__icontains=value)))
            )
        return queryset.distinct()
