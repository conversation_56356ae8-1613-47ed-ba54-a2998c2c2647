from django import forms
from django.db.models import Exists, OuterRef
from django_filters import rest_framework as filters

from core.filters import <PERSON>Filter
from intranet.models import CalendarConfig, Event


class EventFilter(filters.FilterSet):
    """Basic filters against user lookups using the searchbar"""

    calendars = ListFilter(
        field=forms.UUIDField(), field_name="calendars", method="filter_by_calendars", required=False
    )
    start_time = filters.DateTimeFilter(field_name="start_time", method="filter_by_start_time", required=False)
    end_time = filters.DateTimeFilter(field_name="end_time", method="filter_by_end_time", required=False)
    start_date = filters.DateTimeFilter(field_name="start_time", method="filter_by_start_date", required=False)
    end_date = filters.DateTimeFilter(field_name="end_time", method="filter_by_end_date", required=False)
    range = filters.IsoDateTimeFromToRangeFilter(field_name="range", method="filter_by_range", required=False)
    location = filters.BooleanFilter(field_name="location", method="filter_by_location", required=False)

    class Meta:
        model = Event
        fields = ("calendars", "start_time", "end_time", "range", "location")

    def filter_by_calendars(self, queryset, name, value):
        if value:
            queryset = queryset.filter(calendar__in=value)

            hidden_holidays = CalendarConfig.objects.filter(
                calendar=OuterRef("calendar"),
                user=self.request.user,
                display_holidays=False,
            )

            queryset = queryset.annotate(
                hide_holidays=Exists(hidden_holidays),
            ).exclude(hide_holidays=True, is_holiday=True)

        return queryset

    def filter_by_start_time(self, queryset, name, value):
        if value:
            queryset = queryset.filter(start_time__gte=value)
        return queryset

    def filter_by_end_time(self, queryset, name, value):
        if value:
            queryset = queryset.filter(end_time__lte=value)

        return queryset

    def filter_by_start_date(self, queryset, name, value):
        if value:
            queryset = queryset.filter(start_time__date__gte=value)
        return queryset

    def filter_by_end_date(self, queryset, name, value):
        if value:
            queryset = queryset.filter(end_time__date__lte=value)

        return queryset

    def filter_by_range(self, queryset, name, value):
        if value:
            queryset = queryset.filter(start_time__lte=value.stop, end_time__gte=value.start)
        return queryset

    def filter_by_location(self, queryset, name, value):
        if value:
            queryset = queryset.filter(service__latitude__isnull=not value, service__longitude__isnull=not value)
        return queryset
