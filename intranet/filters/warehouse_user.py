from django import forms
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from core.filters import ChainedList<PERSON>ilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import DictField
from intranet.models import WarehouseUser


class WarehouseUserFilter(
    FilterListMixin,
    filters.FilterSet,
):
    """Basic filters against user lookups using the searchbar"""

    warehouse_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="warehouse_id",
        method="filter_by_warehouse_id",
        help_text=_("Bring warehouses from specific warehouse users"),
    )

    email = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="email",
        method="filter_by_email",
        help_text=_("Bring warehouse users by email"),
    )

    date_joined = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="date_joined",
        method="filter_by_date_joined",
        help_text=_("Bring warehouse users by created at date"),
    )

    class Meta:
        model = WarehouseUser
        fields = (
            "warehouse_id",
            "email",
            "date_joined",
        )

    def filter_by_warehouse_id(self, queryset, name, value):
        return self.filter(queryset, "warehouseuserconfiguration__warehouse_id", value)

    def filter_by_email(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_date_joined(self, queryset, name, value):
        return self.filter(queryset, "date_joined__date", value)
