from django import forms
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from core.filters import ChainedListFilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import DictField
from intranet.models import Residence


class ResidenceFilter(
    FilterListMixin,
    filters.FilterSet,
):
    """Filters for Residence model"""

    normalized_name = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="normalized_name",
        method="filter_by_normalized_name",
        help_text=_("Filter residences by name"),
    )

    group_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="group_id",
        method="filter_by_group_id",
        help_text=_("Filter residences by group"),
    )

    city_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="city_id",
        method="filter_by_city_id",
        help_text=_("Filter residences by city"),
    )

    created_at = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="created_at",
        method="filter_by_created_at",
        help_text=_("Filter residences by created at date"),
    )

    country = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="city__country",
        method="filter_by_country",
        help_text=_("Filter residences by country"),
    )

    group_name = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="group_name",
        method="filter_by_group_name",
        help_text=_("Filter residences by group"),
    )

    class Meta:
        model = Residence
        fields = (
            "normalized_name",
            "group_id",
            "city_id",
            "created_at",
            "country",
            "group_name",
        )

    def filter_by_normalized_name(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_group_id(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_city_id(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_created_at(self, queryset, name, value):
        return self.filter(queryset, "created_at__date", value)

    def filter_by_country(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_group_name(self, queryset, name, value):
        return self.filter(queryset, "group__normalized_name", value)
