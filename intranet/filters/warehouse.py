from django import forms
from django.db.models import Prefetch, Q
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters

from contracts.models import Warehouse, WarehouseConfiguration
from core.filters import ChainedListFilter as ListFilter
from core.filters import FilterListMixin
from core.forms.fields import DictField
from intranet.models import WarehouseUserConfiguration


class WarehouseFilter(
    FilterListMixin,
    filters.FilterSet,
):
    normalized_name = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="normalized_name",
        method="filter_by_normalized_name",
        help_text=_("Bring warehouses by name"),
    )

    city_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.UUIDField(),
        ),
        field_name="city_id",
        method="filter_by_city_id",
        help_text=_("Bring warehouses from a specific city"),
    )

    created_at = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.DateField(),
        ),
        field_name="created_at",
        method="filter_by_created_at",
        help_text=_("Bring warehouses by created at date"),
    )

    provider_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="provider_id",
        method="filter_by_provider_id",
        help_text=_("Bring warehouses by providers email"),
    )

    rider_id = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.CharField(),
        ),
        field_name="rider_id",
        method="filter_by_rider_id",
        help_text=_("Bring warehouses by riders email"),
    )

    only_active = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="only_active",
        method="filter_by_only_active",
        help_text=_("Bring current warehouses only"),
    )

    allows_warehouse_differences = ListFilter(
        field=DictField(
            key_field=forms.CharField(),
            value_field=forms.NullBooleanField(),
        ),
        field_name="allows_warehouse_differences",
        method="filter_by_allows_warehouse_differences",
        help_text=_("Bring warehouses which allows warehouse differences only"),
    )

    # Normal filters to include additional contents
    include_relationships = filters.BooleanFilter(
        field_name="include_relationships",
        method="filter_by_include_relationships",
        help_text=_("Bring warehouses with related FKs: calendars and riders"),
    )

    include_providers = filters.BooleanFilter(
        field_name="include_providers",
        method="filter_by_include_providers",
        help_text=_("Bring providers with related FKs: user"),
    )

    include_providers_default = filters.BooleanFilter(
        field_name="include_providers_default",
        method="filter_by_include_providers_default",
        help_text=_("Bring providers filtering by default field with related FKs: user"),
    )

    class Meta:
        model = Warehouse
        fields = (
            "normalized_name",
            "city_id",
            "created_at",
            "include_relationships",
            "include_providers",
            "include_providers_default",
            "only_active",
            "allows_warehouse_differences",
        )

    def filter_by_normalized_name(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_city_id(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_created_at(self, queryset, name, value):
        return self.filter(queryset, "created_at__date", value)

    def filter_by_provider_id(self, queryset, name, value):
        return self.filter(queryset, "warehouseuserconfiguration__user_id", value)

    def filter_by_rider_id(self, queryset, name, value):
        return self.filter(queryset, "warehouseconfiguration__rider_id", value)

    def filter_by_only_active(self, queryset, name, value):
        value = self.get_value_at_index(0, value=value)
        queryset = queryset.filter(~Q(deactivated=value))
        return queryset.distinct()

    def filter_by_allows_warehouse_differences(self, queryset, name, value):
        return self.filter(queryset, name, value)

    def filter_by_include_relationships(self, queryset, name, value):
        if value:
            queryset = queryset.prefetch_related(
                Prefetch(
                    "warehouseconfiguration_set",
                    queryset=WarehouseConfiguration.objects.all().select_related("rider"),
                    to_attr="riders",
                ),
            )

        return queryset.distinct()

    def filter_by_include_providers(self, queryset, name, value):
        if value:
            include_providers_default = self.form.cleaned_data.get("include_providers_default")
            warehouse_user_configuration_qs = WarehouseUserConfiguration.objects.all()

            # Ensure the value is a valid boolean value before filtering
            if isinstance(include_providers_default, bool):
                warehouse_user_configuration_qs = warehouse_user_configuration_qs.filter(
                    default=include_providers_default
                )

            queryset = queryset.prefetch_related(
                Prefetch(
                    "warehouseuserconfiguration_set",
                    queryset=warehouse_user_configuration_qs.select_related("user"),
                    to_attr="warehouseuserconfiguration_data",
                )
            )

        return queryset.distinct()

    def filter_by_include_providers_default(self, queryset, name, value):
        # This is a "no-op" method just to prevent the error.
        # We actually handle the filtering logic inside filter_by_include_providers.
        # This is mandatory to avoid django.core.exceptions.FieldError
        return queryset
