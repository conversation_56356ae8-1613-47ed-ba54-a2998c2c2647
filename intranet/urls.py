from django.urls import path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from core.routers import BulkRout<PERSON>
from intranet.views.budget_estimation import BudgetEstimationViewSet
from intranet.views.calendar import IntranetCalendarViewSet
from intranet.views.calendar_config import CalendarConfigViewSet
from intranet.views.calendar_scheduler import (CalendarEventMutationAPIView,
                                               CalendarFullSyncAPIView,
                                               CalendarPartialSyncAPIView,
                                               CalendarSyncAPIView)
from intranet.views.chargebee import IntranetChargebeeViewSet
from intranet.views.city import IntranetCityViewSet
from intranet.views.contracts import ContractViewSet
from intranet.views.delivery import DeliveryViewSet
from intranet.views.events import IntranetEventViewSet
from intranet.views.global_settings import GlobalSettingsViewSet
from intranet.views.history import (BudgetEstimationHistoryViewSet,
                                    DeliveryRequestHistoryViewSet)
from intranet.views.hubspot import (HubspotContractViewSet, HubspotUserViewSet,
                                    IntranetHubspotMetadataViewSet)
from intranet.views.inventory import InventoryViewSet
from intranet.views.payment_reminder import IntranetPaymentReminderViewSet
from intranet.views.residence_groups import IntranetResidenceGroupViewSet
from intranet.views.residences import ResidenceViewSet
from intranet.views.reviews import ReviewViewSet
from intranet.views.rider import IntranetRiderViewSet
from intranet.views.rider_configuration import \
    IntranetRiderConfigurationViewSet
from intranet.views.service import IntranetServiceViewSet
from intranet.views.task import IntranetTaskViewSet
from intranet.views.team import IntranetTeamViewSet
from intranet.views.user import IntranetSupportUserViewSet, IntranetUserViewSet
from intranet.views.warehouse import IntranetWarehouseViewSet
from intranet.views.warehouse_differences import WarehouseDifferenceViewSet
from intranet.views.warehouse_movement import IntranetWarehouseMovementViewSet
from intranet.views.warehouse_user import IntranetWarehouseUserViewSet
from users.views import ActivateAccountView

router = DefaultRouter()
router.register("ui-budget-estimation", BudgetEstimationViewSet, basename="budget_estimation")
router.register("ui-users", IntranetUserViewSet, basename="users")
router.register("ui-support-users", IntranetSupportUserViewSet, basename="support-users")
router.register("ui-contracts", ContractViewSet, basename="contracts")
router.register("ui-delivery", DeliveryViewSet, basename="delivery")
router.register("ui-inventory", InventoryViewSet, basename="inventory")
router.register("ui-users-hs", HubspotUserViewSet, basename="users_hs")
router.register("ui-contracts-hs", HubspotContractViewSet, basename="contracts_hs")
router.register("ui-delivery-update-history", DeliveryRequestHistoryViewSet, basename="delivery-history")
router.register(
    "ui-budget-estimation-update-history", BudgetEstimationHistoryViewSet, basename="budget-estimation-history"
)
router.register("ui-warehouse-difference", WarehouseDifferenceViewSet, basename="warehouse-differences")
router.register("ui-reminders", IntranetPaymentReminderViewSet, basename="reminders")
router.register("ui-cities", IntranetCityViewSet, basename="city")
router.register("ui-residence-groups", IntranetResidenceGroupViewSet, basename="residence-groups")
router.register("ui-residences", ResidenceViewSet, basename="residences")

# riders app endpoints
router.register("ui-events", IntranetEventViewSet, basename="events")
router.register("ui-teams", IntranetTeamViewSet, basename="teams")
router.register("ui-calendars", IntranetCalendarViewSet, basename="calendars")
router.register("ui-riders", IntranetRiderViewSet, basename="riders")
router.register("ui-riders-configuration", IntranetRiderConfigurationViewSet, basename="riders-configuration")
router.register("ui-hubspot", IntranetHubspotMetadataViewSet, basename="hubspot")
router.register("ui-tasks", IntranetTaskViewSet, basename="tasks")
router.register("ui-chargebee", IntranetChargebeeViewSet, basename="chargebee")
router.register("ui-global-settings", GlobalSettingsViewSet, basename="global-settings")
router.register("ui-calendar-configs", CalendarConfigViewSet, basename="calendar-configs")

bulk_router = BulkRouter()
bulk_router.register("ui-warehouses", IntranetWarehouseViewSet, basename="warehouses")
bulk_router.register("ui-placements", IntranetWarehouseMovementViewSet, basename="placements")
bulk_router.register("ui-reviews", ReviewViewSet, basename="reviews")
bulk_router.register("ui-warehouse-users", IntranetWarehouseUserViewSet, basename="warehouse-users")
bulk_router.register("ui-services", IntranetServiceViewSet, basename="services")

# Override internal auth urlpatterns with user viewset methods
urlpatterns = (
    [
        path("ui-activate-account/", ActivateAccountView.as_view(), name="activate-intranet-account"),
        path("ui-calendar-full-sync/", CalendarFullSyncAPIView.as_view(), name="google-calendar-full-sync"),
        path("ui-calendar-fetch-calendars/", CalendarSyncAPIView.as_view(), name="google-calendar-fetch-calendars"),
        path("ui-calendar-partial-sync/", CalendarPartialSyncAPIView.as_view(), name="google-calendar-partial-sync"),
        path("ui-calendar-events/", CalendarEventMutationAPIView.as_view(), name="google-calendar-events"),
    ]
    + router.urls
    + bulk_router.urls
)

# Needed to add namespace in backoffice_api urls.py
app_name = "intranet"
