import uuid

from django.db import models
from django.utils import timezone


class BlankSlot(models.Model):
    BLANK_SLOT = "blank_slot"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    additional_info = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(default=timezone.now)

    # Internal for holiday events
    is_for_holiday = models.BooleanField(default=False)

    # Foreign keys
    created_by = models.ForeignKey(
        "users.User", null=True, blank=True, on_delete=models.SET_NULL, related_name="blank_slot_created_by"
    )

    @property
    def body(self):
        return self.additional_info

    @property
    def type(self):
        return self.BLANK_SLOT
