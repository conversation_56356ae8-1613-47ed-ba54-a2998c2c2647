from allauth.account.models import EmailAddress
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.db.models.signals import post_save
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _

from core.emails.message import EmailMessage
from intranet.receivers import send_activation_email_to_user

User = get_user_model()


class UserProxy(User):
    """Proxy class to add new methods only for intranet app"""

    class Meta:
        proxy = True
        verbose_name = _("User")

    @property
    def activation_url(self):
        """Create an url to activate the account when new user is added into db with backoffice_allowed=True"""
        import urllib.parse

        # Encode url
        encoded_id = urlsafe_base64_encode(force_bytes(self.pk))
        token = default_token_generator.make_token(self)
        return f"{settings.INTRANET_URL}/activate/{encoded_id}/{token}/?email={urllib.parse.quote(self.email)}"

    def send_activation_email(self):
        """Send the activation email to users"""

        # Create the email address to check if is verified
        EmailAddress.objects.get_or_create(user=self, email__iexact=self.email, defaults={"email": self.email})

        # Activate locale from user
        activate("ES")
        message = EmailMessage(
            subject=_("{prefix} Activate your account to get access in the Backoffice portal").format(
                prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX,
            ),
            to=[
                self.email,
            ],
            sender="ES",
            template="intranet_account_activation_email",
            context={"user_name": self.first_name, "url": self.activation_url},
            attachments=None,
        )

        # Send email
        return message.send() > 0


post_save.connect(send_activation_email_to_user, sender=UserProxy)
