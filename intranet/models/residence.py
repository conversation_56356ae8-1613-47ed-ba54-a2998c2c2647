import uuid

from django.contrib.postgres.indexes import GinIndex
from django.db import models
from django.utils.translation import gettext_lazy as _

from core.managers import RelatedManager
from core.mixins import RelatedManagerModelMixin


class ResidenceGroup(models.Model, RelatedManagerModelMixin):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=150, null=True, blank=True, help_text=_("Residence group name"))

    canonical_name = models.CharField(
        max_length=150, null=False, blank=False, help_text=_("Group canonical name"), unique=True
    )

    normalized_name = models.CharField(
        max_length=150,
        null=True,
        unique=True,
        help_text=_("Used to lookup the name"),
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            GinIndex(fields=("normalized_name",)),
        ]

    # Custom manager to add helper queries
    objects = RelatedManager()

    def __str__(self):
        return self.name or "-"


class Residence(models.Model):
    """Transition model prev to refactor the concept of bases and warehouses"""

    id = models.UUI<PERSON>ield(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=150, null=True, blank=True, help_text=_("Residence visible name"))
    address = models.CharField(max_length=200, null=True, blank=True, help_text=_("Residence address"))

    normalized_name = models.CharField(
        max_length=150,
        null=True,
        blank=True,
        help_text=_("Used to lookup the name"),
    )

    # Foreign key
    group = models.ForeignKey("intranet.ResidenceGroup", null=True, blank=True, on_delete=models.SET_NULL)
    city = models.ForeignKey("intranet.City", null=True, blank=True, on_delete=models.SET_NULL)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            GinIndex(fields=("normalized_name",)),
        ]

    def __str__(self):
        return self.name or "-"
