import logging
import uuid

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from network_clients.base.base import ClientError

from core.external_services import warehouse_difference_client

logger = logging.getLogger(__name__)


def _now():
    """Avoid serialization error when running django makemigrations"""
    return timezone.now().date()


class WarehouseDifference(models.Model):
    """Model to handle discrepancies between warehouse data and our data"""

    PENDING = "pending"
    RUNNING = "running"
    FAILED = "failed"
    SUCCESS = "success"

    STATUS_CHOICES = (
        (PENDING, _("Pending")),
        (RUNNING, _("Running")),
        (FAILED, _("Failed")),
        (SUCCESS, _("Success")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    date = models.DateField(default=_now)
    status = models.Char<PERSON>ield(max_length=100, choices=STATUS_CHOICES, default=PENDING, null=True, blank=True)
    processed_file = models.CharField(max_length=300, null=True, blank=True)
    error_reason = models.TextField(null=True, blank=True)

    # internal
    created_at = models.DateTimeField(default=timezone.now)

    # Resumes
    result = models.FloatField(
        null=True, blank=True, help_text=_("Warehouse difference between warehouse and our space")
    )
    warehouse_space = models.FloatField(null=True, blank=True, help_text=_("Sum of warehouse space"))
    our_space = models.FloatField(null=True, blank=True, help_text=_("Sum of our space"))

    # Foreign keys
    warehouse = models.ForeignKey("contracts.Warehouse", null=True, blank=False, on_delete=models.SET_NULL)

    class Meta:
        unique_together = ("date", "warehouse")

    def __str__(self):
        return f"{self.warehouse_space} - {self.date.strftime('%Y-%m-%d')}"

    def perform_difference(self):
        """Build the task to perform the difference asynchronously"""
        self.status = self.RUNNING

        try:
            warehouse_difference_client.run_warehouse_difference(
                warehouse_model_id=self.pk,
                date=self.date,
                warehouse_id=self.warehouse.warehouse_differences_id,
            )
        except ClientError as err:
            logger.error(f"Error performing warehouse differences for '{self.pk}' asynchronously due to: {err}")
            self.status = self.FAILED
        except Exception as err:
            logger.error(f"Error performing warehouse differences for {self.pk} due to: {err}")
            self.status = self.FAILED

        # Update status
        self.save(update_fields=["status"])
