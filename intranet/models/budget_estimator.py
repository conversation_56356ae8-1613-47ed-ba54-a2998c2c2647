from django.contrib.postgres.fields import Array<PERSON>ield
from django.core.exceptions import ValidationError
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_countries.fields import Country<PERSON>ield

from core.external_services import hubspot_async_client
from core.fields import RawJ<PERSON><PERSON><PERSON>
from core.utils import PlanEstimator
from intranet.models.base import BaseIntranetModel


class BudgetEstimation(BaseIntranetModel):
    """Model to keep the budget estimation for given user"""

    raw_data_with_preserved_order = RawJSONField(
        null=True, blank=True, encoder=DjangoJSONEncoder, help_text=_("Attribute to keep the whole view data")
    )
    total = models.DecimalField(default=0, decimal_places=2, max_digits=10)
    inventory_list = ArrayField(
        models.CharField(max_length=200),
        null=True,
        blank=True,
        help_text=_("Inventory resume data to upload in hubspot"),
    )
    published = models.BooleanField(default=False, help_text=_("If this budget estimation is in hubspot"))

    # Legacy estimation
    is_legacy = models.BooleanField(
        default=False, help_text=_("If this budget estimation is before Mexico refactoring")
    )

    # Foreign Keys
    contract = models.OneToOneField("contracts.contract", null=True, blank=True, on_delete=models.SET_NULL)
    created_by = models.ForeignKey("users.user", null=True, blank=True, on_delete=models.SET_NULL)
    site = CountryField(default="ES", help_text=_("Site where user is allowed to be from intranet"))

    def __str__(self):
        return f"Budget estimation for {self.contract}"

    def publish_in_hubspot(self):
        """Publish this budget estimation in hubspot"""

        if not self.contract:
            raise ValidationError(_("In order to publish the estimation in Hubspot, a valid contract is needed"))

        plan = PlanEstimator.round_the_nearest_plan(self.total)
        hired_space = PlanEstimator.get_hubspot_key_from_plan(plan)

        hubspot_async_client.update_deal(
            deal_id=self.contract.contract_document_id,
            properties={"inventory": "\n".join(self.inventory_list), "hired_space": hired_space},
        )

        # If everything went well, update the published flag
        if not self.published:
            self.published = True
            self.save(update_fields=["published"])
