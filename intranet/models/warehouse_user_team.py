import uuid

from django.db import models
from django.utils import timezone


class WarehouseUserTeam(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Internal
    date_added = models.DateTimeField(default=timezone.now)

    # Foreign keys
    warehouse_user = models.ForeignKey("intranet.WarehouseUser", null=True, on_delete=models.CASCADE)
    service = models.ForeignKey("contracts.Service", null=True, on_delete=models.CASCADE)

    class Meta:
        unique_together = [["warehouse_user", "service"]]
