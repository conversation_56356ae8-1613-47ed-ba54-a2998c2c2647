import datetime
import uuid

import pytz
from django.contrib.postgres.indexes import GinIndex
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _


class Calendar(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    calendar_name = models.CharField(max_length=100, null=True, blank=True, help_text=_("Calendar name"))
    calendar_id = models.CharField(max_length=100, null=True, blank=True, help_text=_("Calendar id on google calendar"))
    color = models.CharField(
        max_length=100, null=True, blank=True, help_text=_("UI color to display this calendar in hexadecimal")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    timezone = models.CharField(max_length=100, null=True, blank=True, default="Europe/Madrid")

    # Foreign keys
    owner = models.ForeignKey("users.User", blank=True, null=True, on_delete=models.CASCADE)
    city = models.ForeignKey("intranet.City", null=True, blank=True, on_delete=models.SET_NULL)

    def __str__(self):
        return self.calendar_name or "-"

    @property
    def city_name(self):
        return self.city.name if self.city else None

    @property
    def created_on_remote(self):
        return bool(self.calendar_id)


class BaseEvent(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    calendar_event_id = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_all_day = models.BooleanField(default=False)

    # Foreign keys
    user = models.ForeignKey("users.User", null=True, on_delete=models.SET_NULL, help_text=_("Created by the user"))
    calendar = models.ForeignKey(
        "intranet.Calendar", null=True, on_delete=models.CASCADE, help_text=_("Calendar linked to this event")
    )

    class Meta:
        abstract = True


class Event(BaseEvent):
    """Model to save a calendar event"""

    title = models.TextField(null=True, blank=True)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)

    color = models.CharField(max_length=100, null=True, blank=True, help_text=_("Used to identify the kind of service"))
    is_holiday = models.BooleanField(default=False)

    # Foreign keys
    service = models.ForeignKey("contracts.Service", null=True, on_delete=models.SET_NULL)
    task = models.ForeignKey("intranet.Task", null=True, on_delete=models.SET_NULL)
    blank_slot = models.ForeignKey("intranet.BlankSlot", null=True, on_delete=models.CASCADE)
    booked_event = models.ForeignKey(
        "self", null=True, on_delete=models.SET_NULL, help_text=_("Blocked slot for a given service")
    )

    class Meta:
        ordering = ("-start_time",)
        indexes = [
            models.Index(fields=("start_time", "end_time"), name="filtering_time_index"),
            GinIndex(fields=("title",)),
        ]

    def format_remote_calendar_event(self, timezone):
        start_date = self.start_time.astimezone(tz=pytz.timezone(timezone))
        end_date = self.end_time.astimezone(tz=pytz.timezone(timezone))
        dates = {
            "start": {"dateTime": start_date.isoformat(), "timeZone": timezone},
            "end": {"dateTime": end_date.isoformat(), "timeZone": timezone},
        }

        if self.is_all_day:
            # End dates are exclusive, so we need to add 1 day to the end
            end_date += datetime.timedelta(days=1)
            dates = {
                "start": {"date": start_date.strftime("%Y-%m-%d"), "timeZone": timezone},
                "end": {"date": end_date.strftime("%Y-%m-%d"), "timeZone": timezone},
            }

        return dates

    @property
    def safe_color(self):
        if self.blank_slot:
            return self.calendar.color

        return self.color

    @property
    def city(self):
        return self.calendar.city_name if self.calendar else None

    @property
    def is_created_on_remote_calendar(self):
        return bool(self.calendar_event_id)

    def delete_related(self):
        """Delete related FK safely if any, avoiding conditional checks on upper invocations"""
        with transaction.atomic():
            if self.service:
                self.service.delete()

            if self.task:
                self.task.delete()

            if self.blank_slot:
                self.blank_slot.delete()

            if self.booked_event:
                self.booked_event.delete()

    @property
    def parent_event(self):
        """Get the parent event in case of this event is a 'booked event' for a given service"""
        return self.event_set.first()


class RecurrentEvent(BaseEvent):
    """Model to save a recurrent event"""

    rrule = models.TextField(blank=True, null=True)


class CalendarConfig(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    visible = models.BooleanField(default=True)
    display_holidays = models.BooleanField(default=True)

    # Foreign keys
    calendar = models.ForeignKey(
        "intranet.Calendar", null=True, on_delete=models.CASCADE, related_name="calendarconfig"
    )
    user = models.ForeignKey("users.User", null=True, on_delete=models.CASCADE)

    class Meta:
        unique_together = ("calendar", "user")


class CalendarRiderConfig(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    default = models.BooleanField(
        default=False, help_text=_("If this rider must be shown as default in the event creation")
    )

    # Foreign keys
    calendar = models.ForeignKey(
        "intranet.Calendar", null=True, on_delete=models.CASCADE, related_name="calendar_rider_config"
    )
    rider = models.ForeignKey("riders.RiderProxy", null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        unique_together = ("calendar", "rider")
