import base64
import logging

from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _

from core.emails.message import EmailMessage
from core.encoders import CustomDjangoJSONEncoder
from core.utils import DeliveryRequestTemplate
from intranet.models.base import BaseIntranetModel
from intranet.templates.generic import GenericProcessor
from intranet.templates.logwin import LogwinProcessor

logger = logging.getLogger(__name__)


class DeliveryRequest(BaseIntranetModel):
    """Model to keep the budget estimation for given user"""

    TEMPLATES = {
        "default": DeliveryRequestTemplate(
            template_file=f"{settings.INTRANET_TEMPLATES_DIR}/warehouse.xlsx", processor=GenericProcessor()
        ),
        "logwin": DeliveryRequestTemplate(
            template_file=f"{settings.INTRANET_TEMPLATES_DIR}/warehouse-logwin.xlsx", processor=LogwinProcessor()
        ),
    }

    EMPTY = "empty"
    PARTIAL_DELIVERY = "partial_delivery"
    FINAL_DELIVERY = "final_delivery"
    USER_DELIVERY = "user_delivery"

    DELIVERY_CHOICES = (
        (EMPTY, _("Empty")),
        (PARTIAL_DELIVERY, _("Partial delivery")),
        (FINAL_DELIVERY, _("Final delivery")),
        (USER_DELIVERY, _("User requested delivery")),
    )

    raw_data = models.JSONField(
        null=True, blank=True, encoder=CustomDjangoJSONEncoder, help_text=_("Attribute to keep the whole view data")
    )
    type = models.CharField(max_length=100, choices=DELIVERY_CHOICES, default=EMPTY, null=True, blank=True)
    delivery_date = models.DateField(null=True, blank=True)
    email_sent_at = models.DateTimeField(null=True, blank=True)

    # Foreign Keys
    contract = models.ForeignKey("contracts.contract", null=True, blank=True, on_delete=models.SET_NULL)
    created_by = models.ForeignKey("users.user", null=True, blank=True, on_delete=models.SET_NULL)
    customer_delivery = models.ForeignKey("contracts.delivery", null=True, blank=True, on_delete=models.SET_NULL)

    def __str__(self):
        return f"Delivery Request for {self.contract}"

    class Meta:
        ordering = ("-created_at",)

    @property
    def load_template_metadata(self):
        """Get the right template according to contract warehouse id"""
        warehouse_canonical_name = self.contract.warehouse.canonical_name if self.contract.warehouse else ""
        return self.TEMPLATES.get(str(warehouse_canonical_name)) or self.TEMPLATES.get("default")

    @property
    def data_into_excel_format(self):
        """Prepare excel file in order to work with it"""

        if (
            not self.contract
            or (self.contract and not self.contract.warehouse)
            or (self.contract and not self.contract.user)
        ):
            return _("Precondition failed to build the excel"), False

        if not self.raw_data:
            return _("Empty data to build the excel file"), False

        if not self.delivery_date:
            return _("Empty delivery date"), False

        # Metadata
        user_name = self.contract.signed_user_name
        contract_internal_id = self.contract.contract_document_id
        odoo_number = self.contract.odoo_number

        template_metadata = self.load_template_metadata
        virtual_workbook = template_metadata.processor(
            template_metadata.template_file,
            contract_internal_id=contract_internal_id,
            odoo_number=odoo_number,
            user_name=user_name,
            delivery_date=self.delivery_date,
            raw_data=self.raw_data,
        )

        return virtual_workbook, True

    def send_email(self, additional_message=None):
        """Send email to warehouse given the delivered items"""
        from intranet.models import WarehouseUserConfiguration

        if not self.contract or (self.contract and not self.contract.warehouse):
            return _("Precondition failed to build the excel"), False

        if not self.contract.signed_user_name and self.contract.warehouse.canonical_name == "logwin":
            return _("User name is required to send the inventory email to Logwin warehouse"), False

        if not self.contract.warehouse.city:
            return _("Warehouse does not have a valid city and it is mandatory to set up the sender on emails"), False

        sender_email = list(
            WarehouseUserConfiguration.objects.select_related("user")
            .filter(should_be_notified=True, warehouse=self.contract.warehouse)
            .values_list("user__email", flat=True)
        )
        if not sender_email:
            return (
                _("Delivery email skipped temporarily because warehouse is not defined: {model}").format(model=self.pk),
                False,
            )

        filled_in_excel, success = self.data_into_excel_format
        if not success or not self.raw_data:
            return _("There was an error building the excel to send it to the warehouse"), False

        attachments = (
            [{
                "content_type": "application/vnd.ms-excel",
                "filename": "warehouse_movement.xlsx",
                "encoded_file": base64.b64encode(filled_in_excel.getvalue()).decode("utf-8"),
            }]
            if filled_in_excel
            else None
        )

        # Activate localeness from user
        sender = self.contract.warehouse.city.country

        activate(sender)
        message = EmailMessage(
            subject=_("{request} - {contract_id}").format(
                contract_id=self.contract.contract_id,
                request=self.get_type_display(),
            ),
            to=sender_email,
            sender=sender,
            template="warehouse_excel_email",
            cc=settings.SUPPORT_TEAM_ATC_EMAILS,
            bcc=[
                "<EMAIL>",
            ],
            context={
                "extra_message": additional_message or "",
                "type": self.get_type_display().lower(),
                "date": self.delivery_date,
            },
            attachments=attachments,
        )

        # Update email sent date
        email_sent = message.send() > 0
        if email_sent:
            self.email_sent_at = timezone.now()
            self.save(update_fields=["email_sent_at"])

        # Send email
        return "OK", email_sent

    def send_cancellation_email(self):
        """Send a cancellation email to the warehouse"""
        from intranet.models import WarehouseUserConfiguration

        if not self.contract or (self.contract and not self.contract.warehouse):
            return _("Precondition failed to send the cancellation email"), False

        if not self.contract.warehouse.city:
            return _("Warehouse does not have a valid city and it is mandatory to set up the sender on emails"), False

        sender_email = list(
            WarehouseUserConfiguration.objects.select_related("user")
            .filter(should_be_notified=True, warehouse=self.contract.warehouse)
            .values_list("user__email", flat=True)
        )
        if not sender_email:
            return (
                _(
                    "Cancellation of delivery email skipped temporarily because warehouse is not defined: {model}"
                ).format(model=self.pk),
                False,
            )

        # Activate localeness from user
        sender = self.contract.warehouse.city.country

        activate(sender)
        message = EmailMessage(
            subject=_("{request} - {contract_id} - Canceled").format(
                contract_id=self.contract.contract_id,
                request=self.get_type_display(),
            ),
            to=sender_email,
            sender=sender,
            template="warehouse_excel_cancel_email",
            cc=settings.SUPPORT_TEAM_ATC_EMAILS,
            bcc=[
                "<EMAIL>",
            ],
            context=None,
            attachments=None,
        )

        # Send email
        return "OK", message.send() > 0
