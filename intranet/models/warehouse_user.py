import uuid

from django.contrib.postgres.indexes import GinIndex
from django.core.validators import validate_email
from django.db import models
from django.utils.translation import gettext_lazy as _


class WarehouseUser(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(
        _("Email address"), unique=True, null=True, validators=[validate_email]
    )  # Add validator to check valid emails
    first_name = models.CharField(max_length=200, blank=True, null=True)
    last_name = models.CharField(max_length=200, blank=True, null=True)

    # Internal
    date_joined = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [GinIndex(fields=("email",))]

    def __str__(self):
        return f"{self.email}"

    @property
    def get_full_name(self):
        return f"{self.first_name or ''} {self.last_name or ''}".strip()


class WarehouseUserConfiguration(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Internal
    default = models.BooleanField(
        default=False, help_text=_("Check this if the warehouse user must be showed as default on events")
    )
    should_be_notified = models.BooleanField(
        default=False, help_text=_("If this user must be notified when sending the moreapp email")
    )

    created_at = models.DateTimeField(auto_now_add=True)

    # Foreign keys
    user = models.ForeignKey("intranet.WarehouseUser", null=True, blank=True, on_delete=models.CASCADE)
    warehouse = models.ForeignKey("contracts.Warehouse", null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["user", "warehouse"], name="unique_warehouse_user_configuration")
        ]

    def __str__(self):
        return f"{self.warehouse.name if self.warehouse else ''}"
