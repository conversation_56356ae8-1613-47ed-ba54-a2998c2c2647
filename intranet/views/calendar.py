import logging

from django.conf import settings
from django.db import transaction
from django.db.models import (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Exists, F, OuterRef,
                              Prefetch, Q, When)
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from googleapiclient.errors import HttpError
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from core.filters import DefaultWithNullOrderingFilter, DjangoFilterBackend
from core.serializers import EmptySerializer
from core.utils import get_openapi_error_response, get_openapi_response
from intranet.core.google_calendar_api.base import CalendarRateLimitError
from intranet.core.google_calendar_api.wrappers import \
    GoogleCalendarSingleWrapper
from intranet.filters.calendars import CalendarFilter
from intranet.models import Calendar, CalendarConfig, CalendarRiderConfig
from intranet.paginations import CalendarPagination
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.calendar import (
    IntranetCalendarConcreteSerializer, IntranetCalendarCreateSerializer,
    IntranetCalendarDeleteSerializer,
    IntranetCalendarDisplayHolidaysSerializer,
    IntranetCalendarPartialUpdateSerializer, IntranetCalendarRiderSerializer,
    IntranetCalendarRidersSerializer, IntranetCalendarSerializer,
    IntranetCalendarUserSerializer, IntranetCalendarVisibilitySerializer)
from riders.models import RiderProxy

logger = logging.getLogger(__name__)


class IntranetCalendarViewSet(viewsets.ModelViewSet):
    """Intranet user endpoints"""

    serializer_class = IntranetCalendarSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = Calendar.objects.all()
    filter_backends = (DjangoFilterBackend, DefaultWithNullOrderingFilter)
    filterset_class = CalendarFilter
    pagination_class = CalendarPagination
    ordering_fields = ["calendar_name"]
    ordering = [
        "calendar_name",
    ]
    http_method_names = ["get", "post", "patch", "delete"]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return IntranetCalendarConcreteSerializer
        elif self.action == "create":
            return IntranetCalendarCreateSerializer
        elif self.action == "partial_update":
            return IntranetCalendarPartialUpdateSerializer
        return self.serializer_class

    def get_serializer_context(self):
        """Override serializer context to have authenticated user available on serializers"""
        context = super().get_serializer_context()
        context.update({"request": self.request})
        return context

    def get_queryset(self):
        queryset = super().get_queryset()

        if self.action == "list":
            queryset = queryset.prefetch_related(
                Prefetch(
                    "calendarconfig",
                    queryset=CalendarConfig.objects.select_related("user"),
                    to_attr="users_data",
                ),
                Prefetch(
                    "calendar_rider_config",
                    queryset=CalendarRiderConfig.objects.select_related("rider"),
                    to_attr="riders_data",
                ),
            )

        return queryset.select_related("city", "owner")

    @swagger_auto_schema(
        operation_summary="Delete Calendar",
        query_serializer=IntranetCalendarDeleteSerializer,
        responses={
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    def destroy(self, request, *args, **kwargs):
        qp_serializer = IntranetCalendarDeleteSerializer(data=request.query_params)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        delete_on_remote = qp_serializer.validated_data["delete_on_remote"]

        calendar = self.get_object()

        if delete_on_remote and calendar.created_on_remote:
            try:
                event_wrapper = GoogleCalendarSingleWrapper(
                    impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL
                )
                event_wrapper.delete(calendar_id=calendar.calendar_id, quota_user=self.request.user)
            except (CalendarRateLimitError, HttpError):
                return Response(
                    {"non_field_errors": _("Remote calendar could not be deleted")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Create calendar on remote source",
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="create-calendar", detail=True)
    def create_calendar(self, request, pk=None):
        calendar = self.get_object()

        if calendar.calendar_id:
            return Response(
                {"non_field_errors": _("Calendar is already created")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            event_wrapper = GoogleCalendarSingleWrapper(impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL)
            response = event_wrapper.create(
                body={
                    "summary": calendar.calendar_name,
                    "timeZone": calendar.timezone,
                },
                quota_user=self.request.user,
            )

            # Update the event
            calendar.calendar_id = response["id"]
            calendar.save(update_fields=["calendar_id"])
        except (CalendarRateLimitError, HttpError) as error:
            logger.error(f"Error creating the calendar in the remote source {error}")
            return Response(
                {"non_field_errors": _("Error creating the calendar on remote source")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(_("OK"), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Share calendar with the teammates",
        operation_description="Share and drop users from shared calendar if needed",
        request_body=IntranetCalendarUserSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="share-calendar", detail=True)
    def share_calendar(self, request, pk=None):
        calendar = self.get_object()

        qp_serializer = IntranetCalendarUserSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        configuration_ids = qp_serializer.validated_data["configurations"]
        user_ids = qp_serializer.validated_data["users"]
        configs = [CalendarConfig(calendar=calendar, user_id=user) for user in user_ids]

        # Ensure we have data before opening a transaction
        if configs or configuration_ids:
            with transaction.atomic():
                if configs:
                    CalendarConfig.objects.bulk_create(configs, ignore_conflicts=True)

                if configuration_ids:
                    CalendarConfig.objects.filter(id__in=configuration_ids).delete()

        return Response(_("OK"), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Set riders as default for this calendar",
        operation_description="Set or unset riders as default for this calendar",
        request_body=IntranetCalendarUserSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="link-riders", detail=True)
    def link_riders(self, request, pk=None):
        calendar = self.get_object()

        qp_serializer = IntranetCalendarRiderSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        configuration_ids = qp_serializer.validated_data["configurations"]
        rider_ids = qp_serializer.validated_data["riders"]
        defaults = qp_serializer.validated_data["defaults"]
        configs = [CalendarRiderConfig(calendar=calendar, rider_id=rider) for rider in rider_ids]

        # Ensure we have data before opening a transaction
        if configs or configuration_ids or defaults:
            with transaction.atomic():
                if configs:
                    CalendarRiderConfig.objects.bulk_create(configs, ignore_conflicts=True)

                if defaults:
                    CalendarRiderConfig.objects.update(
                        default=Case(
                            *[
                                When(Q(rider_id=rider["id"]) & Q(calendar_id=calendar.id), then=rider["default"])
                                for rider in defaults
                            ],
                            default=F("default"),
                            output_field=BooleanField(),
                        )
                    )

                if configuration_ids:
                    CalendarRiderConfig.objects.filter(calendar=calendar, rider_id__in=configuration_ids).delete()

        return Response(_("OK"), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Change visibility for the calendar",
        request_body=IntranetCalendarVisibilitySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="visibility", detail=True)
    def visibility(self, request, pk=None):
        calendar = self.get_object()

        qp_serializer = IntranetCalendarVisibilitySerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        visibility = qp_serializer.validated_data["visibility"]
        update_count = CalendarConfig.objects.filter(calendar=calendar, user_id=self.request.user).update(
            visible=visibility
        )

        # Default responses
        response = {"data": _("OK"), "status": status.HTTP_200_OK}

        if update_count == 0:
            response = {
                "data": {"non_field_errors": _("Visibility could not be changed")},
                "status": status.HTTP_400_BAD_REQUEST,
            }

        return Response(**response)

    @swagger_auto_schema(
        operation_summary="Get available riders for this calendar",
        responses={
            status.HTTP_200_OK: IntranetCalendarRidersSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(IntranetCalendarRidersSerializer, "Error"),
        },
    )
    @action(methods=["get"], url_path="riders", detail=True)
    def riders(self, request, pk=None):
        calendar = self.get_object()

        excluded_riders = self.request.query_params.getlist("exclude") or []

        # Subquery to check if ANY warehouse configuration for this rider has default=True
        has_default_config = CalendarRiderConfig.objects.filter(calendar=calendar, rider=OuterRef("id"), default=True)

        riders = (
            RiderProxy.objects.filter(calendarriderconfig__calendar=calendar)
            .annotate(
                default=Exists(has_default_config),
                excluded=Case(
                    *[When(id=value, then=True) for value in excluded_riders],
                    output_field=BooleanField(),
                ),
            )
            .filter((Q(excluded=True) & Q(is_active=False)) | (Q(excluded__isnull=True) & Q(is_active=True)))
            .distinct("id")
        )

        return Response(IntranetCalendarRidersSerializer(riders, many=True).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Change holidays visibility for the calendar",
        request_body=IntranetCalendarDisplayHolidaysSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="display-holidays", detail=True)
    def display_holidays(self, request, pk=None):
        calendar = self.get_object()

        qp_serializer = IntranetCalendarDisplayHolidaysSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        display_holidays = qp_serializer.validated_data["display_holidays"]
        update_count = CalendarConfig.objects.filter(calendar=calendar, user_id=self.request.user).update(
            display_holidays=display_holidays
        )

        # Default responses
        response = {"data": _("OK"), "status": status.HTTP_200_OK}

        if update_count == 0:
            response = {
                "data": {"non_field_errors": _("Display holidays could not be changed")},
                "status": status.HTTP_400_BAD_REQUEST,
            }

        return Response(**response)
