import logging

from django.contrib.auth import get_user_model
from django.db.models import Prefetch
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from contracts.models import Annex, Box, Contract, Moving
from contracts.utils import \
    translate_hubspot_billing_cycle_into_internal_billing_cycle
from core.mixins import RestrictQuerysetBasedOnGroupMixin
from core.utils import get_openapi_error_response
from intranet.models import DeliveryRequest
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.contract import ContractConcreteSerializer
from intranet.serializers.hubspot import HubspotResponseSerializer
from intranet.serializers.user import IntranetUserSerializer
from webhooks.clients.hubspot import HubspotClientException, hubspot_client

User = get_user_model()

logger = logging.getLogger(__name__)


class HubspotUserViewSet(viewsets.ModelViewSet, RestrictQuerysetBasedOnGroupMixin):
    """User viewset to handle hubspot origin requests thru intranet portal"""

    serializer_class = IntranetUserSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = User.objects.all()
    lookup_url_kwarg = "crm_internal_id"
    lookup_field = "crm_internal_id__iexact"
    http_method_names = ["get"]

    # Restrict data based on these flags
    user_attr = "group_list"

    def get_queryset(self):
        queryset = super().get_queryset()

        # Restrict data to return to the user based on `restrict_on_group` flag
        queryset = self.restrict_queryset_based_on_group(
            queryset, filter_by="registration_country", user=self.request.user
        )

        return queryset

    @swagger_auto_schema(auto_schema=None)
    def list(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)


class HubspotContractViewSet(viewsets.ModelViewSet, RestrictQuerysetBasedOnGroupMixin):
    """Contract viewset to handle hubspot origin requests thru intranet portal"""

    serializer_class = ContractConcreteSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = Contract.objects.all()
    lookup_url_kwarg = "contract_document_id"
    lookup_field = "contract_document_id__iexact"
    http_method_names = ["get"]

    # Restrict data based on these flags
    user_attr = "group_list"

    def get_queryset(self):
        queryset = super().get_queryset()

        # Restrict data to return to the user based on `restrict_on_group` flag
        queryset = (
            self.restrict_queryset_based_on_group(
                queryset, filter_by="user__registration_country", user=self.request.user
            )
            .prefetch_related(
                Prefetch(
                    "annex_set",
                    queryset=Annex.objects.all().select_related("warehouse").order_by("-signed_date").distinct(),
                    to_attr="annex_list",
                ),
                Prefetch(
                    "box_set",
                    queryset=Box.objects.all().order_by("-signed_date").distinct(),
                    to_attr="box_list",
                ),
                Prefetch(
                    "moving_set",
                    queryset=Moving.objects.all().order_by("-signed_date").distinct(),
                    to_attr="moving_list",
                ),
                Prefetch(
                    "deliveryrequest_set",
                    queryset=DeliveryRequest.objects.all().distinct(),
                    to_attr="deliveryrequest_list",
                ),
            )
            .select_related("budgetestimation", "user", "warehouse", "warehouse__city")
        )

        return queryset.distinct()

    @swagger_auto_schema(auto_schema=None)
    def list(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)


class IntranetHubspotMetadataViewSet(viewsets.ViewSet):
    """Retrieve hubspot metadata given an ID"""

    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )

    @swagger_auto_schema(
        operation_summary="Retrieve deal metadata from hubspot",
        responses={
            status.HTTP_200_OK: HubspotResponseSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["get"], url_path="deal", detail=True)
    def deal(self, request, pk=None):
        try:
            hubspot_deal = hubspot_client.get_deal_by_id(
                pk,
                properties=["address", "date_pick_up", "time_slot", "hired_space", "inventory", "permanencia"],
            )

            response = {
                "address": hubspot_deal.properties.get("address"),
                "date_pick_up": hubspot_deal.properties.get("date_pick_up"),
                "time_slot": hubspot_deal.properties.get("time_slot"),
                "hired_space": hubspot_deal.properties.get("hired_space"),
                "inventory": hubspot_deal.properties.get("inventory"),
                "billing_cycle": translate_hubspot_billing_cycle_into_internal_billing_cycle(
                    hubspot_deal.properties.get("permanencia")
                ),
            }

            try:
                hubspot_contact_id = hubspot_deal.associations["contacts"].results[0].id
                hubspot_contact = hubspot_client.get_contact_by_id(
                    hubspot_contact_id, properties=["email", "phone", "firstname", "lastname"]
                )
                response.update(**{
                    "email": hubspot_contact.properties.get("email"),
                    "phone": hubspot_contact.properties.get("phone"),
                    "firstname": hubspot_contact.properties.get("firstname"),
                    "lastname": hubspot_contact.properties.get("lastname"),
                })

            except TypeError:
                pass

            return Response(HubspotResponseSerializer(response).data, status=status.HTTP_200_OK)
        except HubspotClientException as error:
            logger.error(f"Error fetching hubspot metadata: {error}")
            return Response({"non_field_errors": _("Error fetching hubspot data")}, status=status.HTTP_400_BAD_REQUEST)
