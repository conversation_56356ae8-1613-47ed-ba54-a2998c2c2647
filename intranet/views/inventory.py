from django.contrib.auth import get_user_model
from django.db.models import F, Prefetch
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, viewsets
from rest_framework.exceptions import MethodNotAllowed

from contracts.models import Item
from core.filters import DjangoFilter<PERSON>ackend
from core.mixins import RestrictQuerysetBasedOnGroupMixin
from core.paginations import InventoryPagination
from intranet.filters.inventory import InventoryFilter
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.inventory import IntranetInventorySerializer

User = get_user_model()


class InventoryViewSet(viewsets.ModelViewSet, RestrictQuerysetBasedOnGroupMixin):
    """Inventory endpoints to get private data"""

    serializer_class = IntranetInventorySerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = Item.objects.all()
    filter_backends = (DjangoFilterBackend,)
    pagination_class = InventoryPagination
    filterset_class = InventoryFilter
    http_method_names = [
        "get",
    ]

    # Restrict data based on these flags
    user_attr = "group_list"

    def get_queryset(self):
        queryset = super().get_queryset()
        contract_id = self.request.query_params.get("contract_id", None)

        # Perform a join at this level
        queryset = queryset.select_related("contract").prefetch_related(
            Prefetch(
                "contract__user",
                queryset=User.objects.all().only("id", "email").distinct(),
            )
        )

        # Restrict data to return to the user based on `restrict_on_group` flag
        queryset = self.restrict_queryset_based_on_group(
            queryset,
            filter_by="contract__user__registration_country|contract__site__in",
            user=self.request.user,
        )

        if contract_id:
            return (
                queryset.filter(contract=contract_id)
                .distinct()
                .order_by(F("delivery_date").desc(nulls_first=True), "-tag_id")
            )

        # By default, return empty data if we don't have a valid contract id, because is mandatory to filter data
        queryset = queryset.distinct().order_by("-delivery_date", "-tag_id")
        return queryset

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)
