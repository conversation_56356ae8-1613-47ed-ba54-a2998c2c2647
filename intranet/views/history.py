from rest_framework import permissions, viewsets

from intranet.models import BudgetEstimation, DeliveryRequest
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.budget_estimation import \
    BudgetEstimationPartialUpdateSerializer
from intranet.serializers.delivery import \
    DeliveryHistoryPartialUpdateSerializer


class DeliveryRequestHistoryViewSet(viewsets.ModelViewSet):
    """Endpoint to update specific content on history records for delivery requests"""

    serializer_class = DeliveryHistoryPartialUpdateSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = DeliveryRequest.history.all()
    http_method_names = ["patch"]


class BudgetEstimationHistoryViewSet(viewsets.ModelViewSet):
    """Endpoint to update specific content on history records for budget estimations"""

    serializer_class = BudgetEstimationPartialUpdateSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = BudgetEstimation.history.all()
    http_method_names = ["patch"]
