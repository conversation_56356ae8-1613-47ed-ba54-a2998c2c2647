import json

from django.db import transaction
from django.db.models import <PERSON><PERSON>an<PERSON><PERSON>, Case, F, Prefetch, When
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from core.filters import CaseInsensitiveOrderingFilter, DjangoFilterBackend
from core.mixins import PaginationMixin
from core.utils import get_openapi_error_response
from core.views import ChunkAPIMixin
from intranet.filters.warehouse_user import WarehouseUserFilter
from intranet.models import WarehouseUser, WarehouseUserConfiguration
from intranet.paginations import WarehouseUserPagination
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.warehouse_user import (
    IntranetWarehouseUserConfigurationSerializer,
    IntranetWarehouseUserLinkWarehousesSerializer,
    WarehouseUserCreateSerializer, WarehouseUserListSerializer,
    WarehouseUserSerializer)


class IntranetWarehouseUserViewSet(PaginationMixin, ChunkAPIMixin, viewsets.ModelViewSet):
    serializer_class = WarehouseUserSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = WarehouseUser.objects.all()
    filter_backends = (DjangoFilterBackend, CaseInsensitiveOrderingFilter)
    pagination_class = WarehouseUserPagination
    filterset_class = WarehouseUserFilter
    ordering = ["-date_joined", "email"]
    http_method_names = ["post", "get", "patch", "delete"]

    def get_serializer_class(self):
        if self.action == "list":
            return WarehouseUserListSerializer
        elif self.action in [
            "create",
            "partially_update",
        ]:
            return WarehouseUserCreateSerializer

        return super().get_serializer_class()

    def get_queryset(self):
        queryset = super().get_queryset()

        if self.action in ["list", "partial_update"]:
            queryset = queryset.prefetch_related(
                Prefetch(
                    "warehouseuserconfiguration_set",
                    queryset=WarehouseUserConfiguration.objects.all().select_related(
                        "user", "warehouse", "warehouse__city"
                    ),
                    to_attr="warehouseuserconfiguration_data",
                )
            )

        return queryset

    @swagger_auto_schema(
        operation_summary="Link warehouses to user",
        request_body=IntranetWarehouseUserLinkWarehousesSerializer,
        responses={
            status.HTTP_200_OK: IntranetWarehouseUserConfigurationSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                IntranetWarehouseUserConfigurationSerializer, "Error"
            ),
        },
    )
    @action(methods=["post"], url_path="link-warehouses", detail=True)
    def link_warehouses(self, request, pk=None):
        warehouse_user = self.get_object()

        qp_serializer = IntranetWarehouseUserLinkWarehousesSerializer(data=json.loads(request.body.decode("utf-8")))
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        validated_data = qp_serializer.validated_data
        added_warehouses = validated_data["added_warehouses"]
        removed_warehouses = validated_data["removed_warehouses"]
        updated_warehouses = validated_data["updated_warehouses"]

        with transaction.atomic():
            if added_warehouses:
                WarehouseUserConfiguration.objects.bulk_create(
                    [WarehouseUserConfiguration(user=warehouse_user, **warehouse) for warehouse in added_warehouses]
                )

            if removed_warehouses:
                warehouse_user.warehouseuserconfiguration_set.filter(warehouse_id__in=removed_warehouses).delete()

            if updated_warehouses:
                default_cases = []
                should_be_notified_cases = []
                for warehouse in updated_warehouses:
                    default_cases.append(
                        When(warehouse_id=warehouse["warehouse_id"], then=warehouse["default"]),
                    )
                    should_be_notified_cases.append(
                        When(warehouse_id=warehouse["warehouse_id"], then=warehouse["should_be_notified"]),
                    )

                warehouse_user.warehouseuserconfiguration_set.update(
                    default=Case(
                        *default_cases,
                        default=F("default"),
                        output_field=BooleanField(),
                    ),
                    should_be_notified=Case(
                        *should_be_notified_cases,
                        default=F("should_be_notified"),
                        output_field=BooleanField(),
                    ),
                )

        # Optimize related query to avoid many lookups against the database
        warehouse_user.warehouse_configuration = WarehouseUserConfiguration.objects.filter(
            user=warehouse_user
        ).select_related("warehouse", "warehouse__city")

        return Response(
            IntranetWarehouseUserConfigurationSerializer(warehouse_user.warehouse_configuration, many=True).data,
            status=status.HTTP_200_OK,
        )
