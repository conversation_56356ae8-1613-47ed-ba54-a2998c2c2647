import logging

from rest_framework import permissions, viewsets

from core.filters import DefaultWithNull<PERSON><PERSON>ring<PERSON>ilter, DjangoFilterBackend
from intranet.models import CalendarConfig
from intranet.paginations import CalendarConfigPagination
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.calendar_config import CalendarConfigSerializer

logger = logging.getLogger(__name__)


class CalendarConfigViewSet(viewsets.ModelViewSet):
    """Calendar configuration endpoints for intranet"""

    serializer_class = CalendarConfigSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = CalendarConfig.objects.all()
    filter_backends = (
        DjangoFilterBackend,
        DefaultWithNullOrderingFilter,
    )
    pagination_class = CalendarConfigPagination
    ordering_fields = ["calendar__calendar_name"]
    ordering = [
        "calendar__calendar_name",
    ]
    http_method_names = ["get"]

    def get_queryset(self):
        return super().get_queryset().filter(user=self.request.user).select_related("calendar", "calendar__city")
