import logging

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db.models import Prefetch
from django.http import HttpResponse
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from core.mixins import RestrictQuerysetBasedOnGroupMixin
from core.utils import get_openapi_error_response, get_openapi_response
from intranet.models.delivery import DeliveryRequest
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.delivery import (DeliveryChangesSerializer,
                                           DeliveryCreateSerializer,
                                           DeliveryEmailMessage,
                                           DeliveryPartialUpdateSerializer,
                                           DeliveryRetrieveSerializer,
                                           DeliverySerializer)
from intranet.views.base import IntranetViewSet

logger = logging.getLogger(__name__)

User = get_user_model()


class DeliveryViewSet(IntranetViewSet, RestrictQuerysetBasedOnGroupMixin):
    """Deliveries endpoints"""

    serializer_class = DeliverySerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = DeliveryRequest.objects.all()
    http_method_names = ["get", "post", "patch", "delete"]
    history_serializer_class = DeliveryChangesSerializer

    # Restrict data based on these flags
    user_attr = "group_list"

    def get_queryset(self):
        queryset = (
            super()
            .get_queryset()
            .select_related("customer_delivery", "contract")
            .prefetch_related(
                Prefetch(
                    "contract__user",
                    queryset=User.objects.all().only("id", "email").distinct(),
                )
            )
        )

        # Restrict data to return to the user based on `restrict_on_group` flag
        queryset = self.restrict_queryset_based_on_group(
            queryset, filter_by="contract__user__registration_country", user=self.request.user
        )

        return queryset

    def get_serializer_class(self):
        if self.action == "create":
            return DeliveryCreateSerializer
        elif self.action == "partial_update":
            return DeliveryPartialUpdateSerializer
        elif self.action == "retrieve":
            return DeliveryRetrieveSerializer
        return super().get_serializer_class()

    @swagger_auto_schema(auto_schema=None)
    def list(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(
        operation_summary="Delete the delivery request.",
        operation_description="""
              Delete the delivery request and email the warehouse if it is already sent to 'cancel' the delivery
           """,
        responses={
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    def destroy(self, request, *args, **kwargs):
        """On delete delivery, email the warehouse if it is already sent to 'cancel' the delivery"""
        delivery_request = self.get_object()

        try:
            if delivery_request.email_sent_at:
                message, email_sent = delivery_request.send_cancellation_email()
                if not email_sent:
                    return Response({"non_field_errors": message}, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError as error:
            logger.warning(
                f"Error sending cancellation email for delivery request ({delivery_request.pk}) to the warehouse:"
                f" {error.message}"
            )
            return Response({"non_field_errors": error.message}, status=status.HTTP_400_BAD_REQUEST)

        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Send the email to the warehouse",
        operation_description="""
               Send an email for the delivery request to the warehouse
           """,
        request_body=DeliveryEmailMessage,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Send email to the warehouse"),
        },
    )
    @action(methods=["post"], url_path="send-email", detail=True)
    def send_email_to_warehouse(self, request, pk=None):
        """Send the email for the delivery request to the warehouse"""
        delivery_request = self.get_object()

        serializer = DeliveryEmailMessage(delivery_request, data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        response = serializer.validated_data

        try:
            message, email_sent = delivery_request.send_email(response.get("message"))
        except ValidationError as error:
            logger.warning(
                f"Error sending email for delivery request ({delivery_request.pk}) to the warehouse: {error.message}"
            )
            return Response({"non_field_errors": error.message}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            message if email_sent else {"non_field_errors": message},
            status=status.HTTP_200_OK if email_sent else status.HTTP_400_BAD_REQUEST,
        )

    @swagger_auto_schema(
        operation_summary="Download the excel file",
        operation_description="""
                   Download the excel file from this delivery request
               """,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Download the excel file"),
        },
    )
    @action(methods=["get"], url_path="download-excel", detail=True)
    def download_excel(self, request, pk=None):
        """Build the excel file and download it"""

        delivery_request = self.get_object()
        response, is_successful = delivery_request.data_into_excel_format

        if is_successful:
            response = HttpResponse(response.getvalue(), content_type="application/vnd.ms-excel")
            filename = f"Delivery request - {delivery_request.type}.xls"
            response["Content-Disposition"] = f"attachment; filename={filename}"
            return response

        return Response({"non_field_errors": response}, status=status.HTTP_400_BAD_REQUEST)
