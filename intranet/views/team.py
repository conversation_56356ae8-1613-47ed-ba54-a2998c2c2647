import logging

from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import F
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from firebase_admin.exceptions import FirebaseError
from network_clients.base.base import Client<PERSON>rror
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from contracts.models import Service
from core.external_services import asynchronous_client
from core.notifications import NotificationException
from core.serializers import EmptySerializer
from core.utils import get_openapi_error_response, get_openapi_response
from intranet.models import Task
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.team import (IntranetRouteSerializer,
                                       IntranetTeamSerializer)
from riders.models import RiderProxy, Team

logger = logging.getLogger(__name__)


class IntranetTeamViewSet(viewsets.ModelViewSet):
    """Intranet user endpoints"""

    serializer_class = IntranetTeamSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = Team.objects.all()
    http_method_names = ["post"]

    #
    def get_queryset(self):
        queryset = super().get_queryset().select_related("rider", "service")
        return queryset

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(
        operation_summary="Create a route",
        request_body=IntranetRouteSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="create-route", detail=False)
    def create_route(self, request, pk=None):
        qp_serializer = IntranetRouteSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        services = qp_serializer.validated_data["services"]
        tasks = qp_serializer.validated_data["tasks"]
        riders = qp_serializer.validated_data["riders"]
        update_all_services = qp_serializer.validated_data["update_all_services"]
        update_all_riders = qp_serializer.validated_data["update_all_riders"]
        date = qp_serializer.validated_data["date"]
        city = qp_serializer.validated_data["city"]
        calendar_id = qp_serializer.validated_data["calendar_id"]

        if update_all_services:
            filters = {"event__start_time__date": date, "event__calendar__city": city}
            if calendar_id:
                filters["event__calendar_id"] = calendar_id

            services = (
                Service.objects.filter(**filters)
                .annotate(service_id=F("id"))
                .values_list("service_id", flat=True)
                .distinct("service_id")
            )

            tasks = (
                Task.objects.filter(**filters)
                .annotate(task_id=F("id"))
                .values_list("task_id", flat=True)
                .distinct("task_id")
            )

        if update_all_riders:
            riders = (
                RiderProxy.objects.filter(warehouseconfiguration__warehouse__city=city, is_active=True)
                .annotate(rider_id=F("id"))
                .values_list("rider_id", flat=True)
                .distinct("rider_id")
            )

        service_ids, task_ids, teams = [], [], []
        for service_id in services:
            service_ids.append(service_id)
            teams.extend([Team(service_id=service_id, rider_id=rider_id) for rider_id in riders])

        for task_id in tasks:
            task_ids.append(task_id)
            teams.extend([Team(task_id=task_id, rider_id=rider_id) for rider_id in riders])

        with transaction.atomic():
            Team.objects.filter(service_id__in=service_ids).delete()
            Team.objects.filter(task_id__in=task_ids).delete()

            if teams:
                Team.objects.bulk_create(teams)

        try:
            asynchronous_client.update_remote_calendar_events(
                services=service_ids,
                tasks=task_ids,
                request_user=self.request.user.email,
                in_seconds=5,
            )
        except ClientError as err:
            logger.error(f"Error updating calendar events due to: {err}")
        except Exception as err:
            logger.error(f"Unknown error updating calendar events due to: {err}")

        return Response(_("OK"), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Notify to rider",
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="notify", detail=True)
    def notify_rider(self, request, pk=None):
        team = self.get_object()

        # TODO: add with the new approach

        # try:
        #     team.send_push_notification()
        # except ValidationError as error:
        #     return Response({"non_field_errors": error}, status=status.HTTP_400_BAD_REQUEST)
        # except FirebaseError as error:
        #     return Response(
        #         {
        #             "non_field_errors": _("Error sending push notification to rider with error code {error}").format(
        #                 error=error.code
        #             )
        #         },
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )
        # except ValueError:
        #     return Response(
        #         {"non_field_errors": _("Wrong token provided or missing configuration")},
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )
        # except NotificationException as error:
        #     return Response(
        #         {"non_field_errors": error},
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )
        #
        return Response(_("OK"), status=status.HTTP_200_OK)
