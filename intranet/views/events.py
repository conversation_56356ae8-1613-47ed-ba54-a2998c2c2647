import logging
import traceback

import redis.exceptions
from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from django.db.models import Case, <PERSON>r<PERSON><PERSON>, F, Prefetch, Value, When
from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from googleapiclient.errors import HttpError
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from contracts.models import Service
from core.colors import get_color_id, is_same_color
from core.filters import DefaultWithNullOrderingFilter, DjangoFilterBackend
from core.serializers import EmptySerializer
from core.utils import get_openapi_error_response, get_openapi_response
from intranet.core.google_calendar_api.base import CalendarRateLimitError
from intranet.core.google_calendar_api.wrappers import \
    GoogleCalendarEventWrapper
from intranet.filters.event import EventFilter
from intranet.models import Event
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.event import (EventConcreteSerializer,
                                        EventCreateSerializer,
                                        EventDeleteSerializer,
                                        EventMapSerializer, EventSerializer,
                                        EventUpdateSerializer)
from riders.models import Team
from webhooks.clients.hubspot import HubspotClientException, hubspot_client

logger = logging.getLogger(__name__)


class IntranetEventViewSet(viewsets.ModelViewSet):
    """Intranet user endpoints"""

    serializer_class = EventSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = Event.objects.all()
    filter_backends = (DjangoFilterBackend, DefaultWithNullOrderingFilter)
    filterset_class = EventFilter
    http_method_names = ["get", "post", "patch", "delete"]

    def get_serializer_class(self):
        is_retrieve_services_for_map = self.request.query_params.get("map")

        if self.action == "retrieve":
            return EventConcreteSerializer
        elif self.action == "create":
            return EventCreateSerializer
        elif self.action == "partial_update":
            return EventUpdateSerializer

        if self.action == "list" and is_retrieve_services_for_map:
            return EventMapSerializer
        return self.serializer_class

    def filter_queryset(self, queryset):
        """We need display filter only on list request, otherwise we get error on /id/ requests about app is required"""
        if self.action != "list":
            self.filterset_class = None

        return super().filter_queryset(queryset)

    def get_queryset(self):
        is_retrieve_services_for_map = self.request.query_params.get("map")

        queryset = (
            super()
            .get_queryset()
            .select_related(
                "service",
                "calendar",
                "user",
                "task",
                "service__warehouse",
                "service__warehouse__city",
                "blank_slot__created_by",
                "calendar__city",
            )
            .prefetch_related(
                Prefetch(
                    "task__team_set",
                    queryset=Team.objects.all().select_related("rider").distinct(),
                    to_attr="rider_team",
                ),
            )
        )

        # Exclude tasks for map view
        if is_retrieve_services_for_map:
            queryset = queryset.filter(task__isnull=True)

        return queryset

    @swagger_auto_schema(
        operation_summary="List Ui-Events",
        responses={
            status.HTTP_200_OK: EventSerializer(many=True),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(EventSerializer, "Error"),
        },
    )
    def list(self, request, *args, **kwargs):
        """Override this method to avoid performance issues due to serialization"""
        is_retrieve_services_for_map = self.request.query_params.get("map")
        queryset = self.get_queryset()

        values = (
            "id",
            "start_time",
            "end_time",
            "calendar__id",
            "calendar__color",
            "title",
            "service__space",
            "service__address",
            "service__relocation_address",
            "service__first_name",
            "service__last_name",
            "service__billing_cycle",
            "color",
            "service__type",
            "task__id",
            "is_all_day",
            "updated_at",
            "calendar_event_id",
            "service__start_time_slot",
            "service__end_time_slot",
            "blank_slot__id",
            "service__residence__group__name",
            "is_holiday",
        )

        if is_retrieve_services_for_map:
            queryset = queryset.annotate(
                type=Case(
                    *[
                        When(service__type=value[0], then=Value(force_str(value[1])))
                        for value in Service.SERVICE_TYPE_CHOICES
                    ],
                    output_field=CharField(),
                ),
                billing_cycle=Case(
                    *[
                        When(service__billing_cycle=value[0], then=Value(force_str(value[1])))
                        for value in Service.BILLING_CYCLE_CHOICES
                    ],
                    output_field=CharField(),
                ),
            )
            values = (
                "id",
                "start_time",
                "end_time",
                "color",
                "calendar__id",
                "calendar__color",
                "calendar__calendar_name",
                "service__type",
                "service__address",
                "service__space",
                "service__relocation_address",
                "service__latitude",
                "service__longitude",
                "type",
            )

        # Select specific fields
        queryset = self.filter_queryset(queryset).values(*values)

        return Response(self.get_serializer(queryset, many=True).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Delete Ui-Event",
        query_serializer=EventDeleteSerializer,
        responses={
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    def destroy(self, request, *args, **kwargs):
        qp_serializer = EventDeleteSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        delete_on_remote = qp_serializer.validated_data["delete_on_remote"]
        delete_related_service = qp_serializer.validated_data["delete_related_service"]

        event = self.get_object()

        # Make hubspot call only if the event has linked a service
        if event.service:
            # Unselect previously selected items
            event.service.item_set.update(service=None)

            try:
                site = event.calendar.city.country
                hubspot_client.update_deal_by_id(
                    event.service.contract_id, properties={"dealstage": settings.HUBSPOT_BOOKING_CANCELED.get(site)}
                )
            except HubspotClientException:
                pass

        event_wrapper = GoogleCalendarEventWrapper(impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL)
        if event.calendar_event_id:
            try:
                if delete_on_remote:
                    event_wrapper.delete(
                        calendar_id=event.calendar.calendar_id,
                        eventId=event.calendar_event_id,
                        quota_user=self.request.user,
                    )

                    if event.booked_event and event.booked_event.calendar_event_id:
                        event_wrapper.delete(
                            calendar_id=event.booked_event.calendar.calendar_id,
                            eventId=event.booked_event.calendar_event_id,
                            quota_user=self.request.user,
                        )

                else:
                    body = event_wrapper.format_body(
                        summary=f"[DELETED ON INTRANET - BACKUP ONLY] {event.title}",
                    )
                    event_wrapper.patch(
                        calendar_id=event.calendar.calendar_id,
                        eventId=event.calendar_event_id,
                        body=body,
                        quota_user=self.request.user,
                    )

                    if event.booked_event and event.booked_event.calendar_event_id:
                        event_wrapper.patch(
                            calendar_id=event.booked_event.calendar.calendar_id,
                            eventId=event.booked_event.calendar_event_id,
                            body=body,
                            quota_user=self.request.user,
                        )
            except (CalendarRateLimitError, HttpError):
                return Response(
                    {"non_field_errors": _("Remote calendar event could not be deleted")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        instance = super().destroy(request, *args, **kwargs)
        if delete_related_service:
            event.delete_related()

        return instance

    @swagger_auto_schema(
        operation_summary="Create remote event",
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Event created"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="create-event", detail=True)
    def create_event(self, request, pk=None):
        event = self.get_object()
        service_or_task_or_blank_slot = event.service or event.task or event.blank_slot
        is_service_or_task = event.service or event.task

        if event.calendar_event_id:
            return Response(
                {"non_field_errors": _("Event is already created in remote calendar")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not service_or_task_or_blank_slot:
            return Response(
                {"non_field_errors": _("Event doesn't have any service, task or blank_slot linked to it")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            with cache.lock(
                key="create-event",
                timeout=settings.REDIS_LOCK_MAX_TIMEOUT,
                blocking_timeout=settings.REDIS_LOCK_MAX_BACKGROUND_TIMEOUT,
            ):
                try:
                    emails = []

                    if is_service_or_task:
                        emails = service_or_task_or_blank_slot.team_set.annotate(email=F("rider__email")).values(
                            "email"
                        )

                    timezone = event.calendar.timezone
                    event_wrapper = GoogleCalendarEventWrapper(
                        impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL
                    )
                    dates = event.format_remote_calendar_event(timezone)
                    color = event.color
                    body = event_wrapper.format_body(
                        summary=event.title,
                        description=service_or_task_or_blank_slot.body,
                        attendees=list(emails),
                        colorId=get_color_id(color) if not is_same_color(color, event.calendar.color) else None,
                        **dates,
                    )
                    response = event_wrapper.create(
                        calendarId=event.calendar.calendar_id,
                        body=body,
                        quota_user=self.request.user,
                        sendUpdates="all",
                    )

                    # Update the event
                    event.calendar_event_id = response["id"]
                    booked_event_calendar_event_id = None

                    # Ensure the booked event is also created
                    if event.booked_event and not event.booked_event.calendar_event_id:
                        timezone = event.booked_event.calendar.timezone
                        dates = event.booked_event.format_remote_calendar_event(timezone)
                        body = event_wrapper.format_body(
                            summary=event.booked_event.title,
                            description=event.booked_event.blank_slot.body,
                            attendees=None,
                            colorId=None,
                            **dates,
                        )
                        response = event_wrapper.create(
                            calendarId=event.booked_event.calendar.calendar_id,
                            body=body,
                            quota_user=self.request.user,
                            sendUpdates="all",
                        )

                        booked_event_calendar_event_id = response["id"]

                    with transaction.atomic():
                        event.save(update_fields=["calendar_event_id"])

                        if booked_event_calendar_event_id:
                            event.booked_event.calendar_event_id = booked_event_calendar_event_id
                            event.booked_event.save(update_fields=["calendar_event_id"])

                except (CalendarRateLimitError, HttpError) as error:
                    logger.error(f"Error creating the event in the remote calendar {error}")
                    return Response(
                        {"non_field_errors": _("Error creating the event on remote calendar")},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
        except redis.exceptions.LockError:
            return Response(
                {"non_field_errors": _("Concurrent operation. Try again later.")}, status=status.HTTP_400_BAD_REQUEST
            )
        except redis.exceptions.ConnectionError:
            logger.error(f"Error connecting to redis: {traceback.print_exc(chain=False)}")
            return Response(
                {"non_field_errors": _("Error processing the request. Try again later.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        return Response(_("Event created"), status=status.HTTP_200_OK)
