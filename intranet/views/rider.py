import json
import logging

from allauth.account.models import EmailAddress
from django.db import transaction
from django.db.models import BooleanField, Case, F, Prefetch, Q, When
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from contracts.models import WarehouseConfiguration
from core.filters import CaseInsensitiveOrderingFilter, DjangoFilterBackend
from core.serializers import EmptySerializer
from core.utils import get_openapi_error_response, get_openapi_response
from intranet.filters.rider import RiderFilter
from intranet.models import CalendarRiderConfig
from intranet.paginations import IntranetRiderPagination
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.rider import (
    IntranetRiderCalendarRidersConfigurationSerializer,
    IntranetRiderCalendarSerializer, IntranetRiderCreateSerializer,
    IntranetRiderLinkWarehousesSerializer,
    IntranetRiderPartialUpdateSerializer, IntranetRiderRouteSerializer,
    IntranetRiderSerializer, IntranetRiderWarehouseConfigurationSerializer,
    IntranetRiderWarehouseResponseSerializer, IntranetRiderWarehouseSerializer)
from riders.exceptions import RemoteResourcesError
from riders.models import RiderProxy

logger = logging.getLogger(__name__)


class IntranetRiderViewSet(viewsets.ModelViewSet):
    """Intranet user endpoints"""

    serializer_class = IntranetRiderSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = RiderProxy.objects.all()
    filter_backends = (DjangoFilterBackend, CaseInsensitiveOrderingFilter)
    filterset_class = RiderFilter
    pagination_class = IntranetRiderPagination
    ordering_fields = ["-date_joined"]
    ordering = ["-date_joined"]
    http_method_names = ["get", "post", "patch", "delete"]

    def get_serializer_class(self):
        routes = self.request.query_params.get("routes")

        if self.action == "create":
            return IntranetRiderCreateSerializer
        elif self.action == "partial_update":
            return IntranetRiderPartialUpdateSerializer
        elif self.action == "list" and routes:
            return IntranetRiderRouteSerializer

        return self.serializer_class

    def get_queryset(self):
        queryset = super().get_queryset()
        routes = self.request.query_params.get("routes")
        calendar = self.request.query_params.get("calendar")

        if self.action == "list" and not routes:
            calendar_rider_config_filter = {}
            if calendar:
                calendar_rider_config_filter["calendar_id"] = calendar

            queryset = queryset.prefetch_related(
                Prefetch(
                    "warehouseconfiguration_set",
                    queryset=WarehouseConfiguration.objects.all().select_related("warehouse"),
                    to_attr="warehouses",
                ),
                Prefetch(
                    "emailaddress_set", queryset=EmailAddress.objects.filter(verified=True), to_attr="emailaddress_attr"
                ),
                Prefetch(
                    "calendarriderconfig_set",
                    queryset=CalendarRiderConfig.objects.filter(**calendar_rider_config_filter).select_related(
                        "calendar"
                    ),
                    to_attr="configurations",
                ),
            )

        if self.action == "activation_email":
            queryset = queryset.prefetch_related(
                Prefetch(
                    "emailaddress_set", queryset=EmailAddress.objects.filter(verified=True), to_attr="emailaddress_attr"
                ),
            )

        return queryset.select_related("riderconfiguration")

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(
        operation_id="destroy", operation_summary="Delete riders with related keys in the remote source"
    )
    def destroy(self, request, *args, **kwargs):
        rider = self.get_object()

        # Ensure this rider has no finished services or tasks
        if rider.has_finished_service_or_task:
            return Response(
                {
                    "non_field_errors": _(
                        "The rider cannot be deleted because it already has finished services or tasks. Please,"
                        " deactivate it instead"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Remove remote keys first
        rider_configuration = rider.riderconfiguration

        try:
            rider_configuration.delete_remote_keys()
        except RemoteResourcesError as err:
            return Response({"non_field_errors": err}, status=status.HTTP_400_BAD_REQUEST)

        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Read warehouses to which belongs the rider",
        responses={
            status.HTTP_200_OK: IntranetRiderWarehouseSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(IntranetRiderWarehouseSerializer, "Error"),
        },
    )
    @action(methods=["get"], url_path="warehouses", detail=True)
    def warehouses(self, request, pk=None):
        rider = self.get_object()

        return Response(
            IntranetRiderWarehouseSerializer(rider.warehouseconfiguration_set, many=True).data,
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Link warehouse to rider",
        request_body=IntranetRiderLinkWarehousesSerializer,
        responses={
            status.HTTP_200_OK: IntranetRiderWarehouseResponseSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(IntranetRiderWarehouseResponseSerializer, "Error"),
        },
    )
    @action(methods=["patch"], url_path="link-warehouses", detail=True)
    def link_warehouses(self, request, pk=None):
        rider = self.get_object()

        qp_serializer = IntranetRiderLinkWarehousesSerializer(data=json.loads(request.body.decode("utf-8")))
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        validated_data = qp_serializer.validated_data
        linked_warehouses = validated_data["linked_warehouses"]
        dropped_warehouses = validated_data["dropped_warehouses"]

        with transaction.atomic():
            if linked_warehouses:
                WarehouseConfiguration.objects.bulk_create(
                    [WarehouseConfiguration(rider=rider, **warehouse) for warehouse in linked_warehouses]
                )

            if dropped_warehouses:
                rider.warehouseconfiguration_set.filter(warehouse_id__in=dropped_warehouses).delete()

        # Optimize related query to avoid many lookups against the database
        rider.warehouses = WarehouseConfiguration.objects.filter(rider=rider).select_related("warehouse")
        return Response(
            IntranetRiderWarehouseConfigurationSerializer(rider.warehouses, many=True).data, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Send activation email to rider",
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "KO"),
        },
    )
    @action(methods=["post"], url_path="send-activation-email", detail=True)
    def activation_email(self, request, pk=None):
        rider = self.get_object()

        if rider.is_account_verified:
            return Response({"non_field_errors": _("Account already activated")}, status=status.HTTP_400_BAD_REQUEST)

        email_sent = rider.send_activation_email()
        response = {"data": _("OK"), "status": status.HTTP_200_OK}

        if not email_sent:
            response = {"data": _("KO"), "status": status.HTTP_400_BAD_REQUEST}

        return Response(**response)

    @swagger_auto_schema(
        operation_summary="Set riders as default for this calendar",
        operation_description="Set or unset riders as default for this calendar",
        request_body=IntranetRiderCalendarSerializer,
        responses={
            status.HTTP_200_OK: IntranetRiderCalendarRidersConfigurationSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Error"),
        },
    )
    @action(methods=["post"], url_path="link-calendars", detail=True)
    def link_calendars(self, request, pk=None):
        rider = self.get_object()

        qp_serializer = IntranetRiderCalendarSerializer(data=request.data, context={"rider": rider})
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        configuration_ids = qp_serializer.validated_data["configurations"]
        calendar_ids = qp_serializer.validated_data["calendars"]
        defaults = qp_serializer.validated_data["defaults"]
        configs = [CalendarRiderConfig(calendar_id=calendar, rider=rider) for calendar in calendar_ids]

        # Ensure we have data before opening a transaction
        if configs or configuration_ids or defaults:
            with transaction.atomic():
                if configs:
                    CalendarRiderConfig.objects.bulk_create(configs, ignore_conflicts=True)

                if defaults:
                    CalendarRiderConfig.objects.update(
                        default=Case(
                            *[
                                When(Q(calendar_id=calendar["id"]) & Q(rider_id=rider.id), then=calendar["default"])
                                for calendar in defaults
                            ],
                            default=F("default"),
                            output_field=BooleanField(),
                        )
                    )

                if configuration_ids:
                    CalendarRiderConfig.objects.filter(rider=rider, calendar_id__in=configuration_ids).delete()

        # Optimize related query to avoid many lookups against the database
        rider.calendars = CalendarRiderConfig.objects.filter(rider=rider).select_related("calendar")
        return Response(
            IntranetRiderCalendarRidersConfigurationSerializer(rider.calendars, many=True).data,
            status=status.HTTP_200_OK,
        )
