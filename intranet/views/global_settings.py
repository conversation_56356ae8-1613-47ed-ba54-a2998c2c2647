import logging

from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, viewsets
from rest_framework.exceptions import MethodNotAllowed

from core.filters import DjangoFilterBackend
from intranet.models import GlobalSettings
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.global_settings import (
    GlobalSettingsSerializer, GlobalSettingsUpdateSerializer)

logger = logging.getLogger(__name__)


class GlobalSettingsViewSet(viewsets.ModelViewSet):
    """Intranet user endpoints"""

    serializer_class = GlobalSettingsSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = GlobalSettings.objects.all()
    filter_backends = (DjangoFilterBackend,)
    http_method_names = ["get", "patch"]

    def get_serializer_class(self):
        if self.action == "partial_update":
            return GlobalSettingsUpdateSerializer
        return self.serializer_class

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)
