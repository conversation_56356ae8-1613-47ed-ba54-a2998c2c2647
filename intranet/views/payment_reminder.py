import logging

from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, viewsets
from rest_framework.exceptions import MethodNotAllowed

from core.filters import DefaultWithNullOrderingFilter, DjangoFilterBackend
from intranet.models import PaymentReminder
from intranet.paginations import PaymentReminderPagination
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.payment_reminder import \
    IntranetPaymentReminderSerializer

logger = logging.getLogger(__name__)


class IntranetPaymentReminderViewSet(viewsets.ModelViewSet):
    """Intranet payment reminder endpoints"""

    serializer_class = IntranetPaymentReminderSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = PaymentReminder.objects.all()
    filter_backends = (DjangoFilterBackend, DefaultWithNullOrderingFilter)
    ordering_fields = ["created_at"]
    ordering = [
        "-created_at",
    ]
    pagination_class = PaymentReminderPagination
    http_method_names = ["get"]

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)
