import logging

from django.db.models import Prefetch
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from core.filters import DefaultWithNullOrderingFilter, DjangoFilterBackend
from core.utils import get_openapi_error_response
from intranet.filters.task import TaskFilter
from intranet.models import Event, Task
from intranet.paginations import TaskPagination
from intranet.permissions import IsBackofficeAllowedUser
from intranet.serializers.task import IntranetTaskSerializer
from riders.models import Team

logger = logging.getLogger(__name__)


class IntranetTaskViewSet(viewsets.ModelViewSet):
    """Intranet task endpoints"""

    serializer_class = IntranetTaskSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        IsBackofficeAllowedUser,
    )
    queryset = Task.objects.all()
    filter_backends = (DjangoFilterBackend, DefaultWithNullOrderingFilter)
    filterset_class = TaskFilter
    ordering_fields = ["created_at"]
    ordering = [
        "-created_at",
    ]
    pagination_class = TaskPagination
    http_method_names = ["get", "delete"]

    def get_queryset(self):
        queryset = (
            super()
            .get_queryset()
            .select_related("created_by")
            .prefetch_related(
                Prefetch(
                    "event_set",
                    queryset=Event.objects.all().select_related("calendar", "calendar__city").distinct(),
                    to_attr="event_data",
                ),
                Prefetch(
                    "team_set",
                    queryset=Team.objects.all().select_related("rider").distinct(),
                    to_attr="rider_team",
                ),
            )
        )
        return queryset.distinct()

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    @method_decorator(cache_page(60 * 60 * 24))  # 1 day of cache
    @swagger_auto_schema(
        operation_summary="Fetch tasks statuses [C]",
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="List of task statuses",
                schema=openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_STRING)),
            ),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Status"),
        },
    )
    @action(methods=["get"], url_path="statuses", detail=False)
    def statuses(self, request, pk=None):
        return Response([key for key, _ in Task.STATUS_CHOICES], status=status.HTTP_200_OK)
