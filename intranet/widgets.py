from django.contrib.admin import widgets
from django.core.exceptions import ValidationError
from django.urls import NoReverseMatch, reverse
from django.utils.text import Truncator


class IntranetUserRawIdWidget(widgets.ForeignKeyRawIdWidget):
    def get_context(self, name, value, attrs):
        context = super().get_context(name, value, attrs)
        related_url = context["related_url"]

        if related_url:
            related_url = related_url.replace("/users/user/", "/intranet/userproxy/")

        context["related_url"] = related_url
        return context

    def url_parameters(self):
        res = super().url_parameters()
        res["backoffice_allowed__exact"] = 1
        return res

    def label_and_url_for_value(self, value):
        key = self.rel.get_related_field().name
        try:
            obj = self.rel.model._default_manager.using(self.db).get(**{key: value})
        except (ValueError, self.rel.model.DoesNotExist, ValidationError):
            return "", ""

        try:
            url = reverse(
                "%s:%s_%s_change"
                % (
                    self.admin_site.name,
                    obj._meta.app_label,
                    "userproxy",
                ),
                args=(obj.pk,),
            )
        except NoReverseMatch:
            url = ""  # Admin not registered for target model.

        return Truncator(obj).words(14), url
