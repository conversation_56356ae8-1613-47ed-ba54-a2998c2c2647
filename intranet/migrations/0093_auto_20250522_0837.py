# Generated by Django 3.2.25 on 2025-05-22 08:37

from django.db import migrations


def set_holiday_event(apps, schema_editor):
    BlankSlot = apps.get_model("intranet", "BlankSlot")

    _ = BlankSlot.objects.get_or_create(is_for_holiday=True, defaults={"additional_info": "Festivo"})


class Migration(migrations.Migration):
    dependencies = [
        ("intranet", "0092_auto_20250522_0836"),
    ]

    operations = [
        migrations.RunPython(set_holiday_event, reverse_code=migrations.RunPython.noop),
    ]
