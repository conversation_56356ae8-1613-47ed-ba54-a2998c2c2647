# Generated by Django 3.2.25 on 2024-11-28 09:53

from django.db import migrations, transaction

WAREHOUSE_USERS = {
    "1001": [
        "<EMAIL>",
    ],
    "1002": [
        "<EMAIL>",
        "<EMAIL>",
        "<PERSON>@logwin-logistics.com",
        "<EMAIL>",
    ],
    "1003": None,
    "1004": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "1005": [  # Olias
        "<EMAIL>",
    ],
    "1021": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "1031": None,  # Valencia - Nuestro
    "1032": [
        "<EMAIL>",
    ],
    "1041": [
        "<EMAIL>",
        "<EMAIL>",
    ],
    "1042": [
        "<EMAIL>",
    ],
    "1051": None,
    "1052": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "1053": [
        "<EMAIL>",
    ],
    "1070": [
        "<EMAIL>",
    ],
    "1071": [
        "<EMAIL>",
    ],
    "1072": [
        "<EMAIL>",
    ],
    "1073": [
        "<EMAIL>",
    ],
    "1074": [
        "<EMAIL>",
    ],
    "2001": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "2002": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "2010": [  # Formerly - Pantoja
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "2011": None,
    "3001": None,
    "4000": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "5000": [
        "<EMAIL>",
    ],
}


def set_warehouse_users(apps, schema_editor):
    Warehouse = apps.get_model("contracts", "Warehouse")
    WarehouseUser = apps.get_model("intranet", "WarehouseUser")
    WarehouseUserConfiguration = apps.get_model("intranet", "WarehouseUserConfiguration")

    warehouses = {
        warehouse.get("warehouse_id"): warehouse.get("id")
        for warehouse in Warehouse.objects.values("warehouse_id", "id").order_by("-warehouse_id")
    }
    warehouse_users = {}
    warehouse_users_configuration = []

    for warehouse_id, emails in WAREHOUSE_USERS.items():
        for email in emails or []:
            if email not in warehouse_users:
                warehouse_users[email] = WarehouseUser(
                    email=email.lower(),
                )

            warehouse_pk = warehouses.get(warehouse_id)
            if warehouse_pk:
                warehouse_user_configuration = WarehouseUserConfiguration(
                    should_be_notified=True, user=warehouse_users[email], warehouse_id=warehouse_pk
                )

                warehouse_users_configuration.append(warehouse_user_configuration)

    with transaction.atomic():
        if warehouse_users:
            WarehouseUser.objects.bulk_create(warehouse_users.values())

        if warehouse_users_configuration:
            WarehouseUserConfiguration.objects.bulk_create(warehouse_users_configuration)


def unset_warehouse_users(apps, schema_editor):
    WarehouseUser = apps.get_model("intranet", "WarehouseUser")
    WarehouseUserConfiguration = apps.get_model("intranet", "WarehouseUserConfiguration")

    with transaction.atomic():
        WarehouseUser.objects.all().delete()
        WarehouseUserConfiguration.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ("intranet", "0058_auto_20241121_0957"),
    ]

    operations = [
        migrations.RunPython(set_warehouse_users, reverse_code=unset_warehouse_users),
    ]
