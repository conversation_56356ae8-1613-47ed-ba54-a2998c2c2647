# Generated by Django 3.2.13 on 2022-04-18 16:56

import uuid

import django.contrib.postgres.fields
import django.core.serializers.json
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("contracts", "0028_auto_20220224_1411"),
        ("intranet", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalBudgetEstimation",
            fields=[
                ("id", models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "raw_data",
                    models.JSONField(
                        blank=True,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Attribute to keep the whole view data",
                        null=True,
                    ),
                ),
                ("total", models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                (
                    "inventory_list",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=200),
                        blank=True,
                        help_text="Inventory resume data to upload in hubspot",
                        null=True,
                        size=None,
                    ),
                ),
                ("published", models.BooleanField(default=False, help_text="If this budget estimation is in hubspot")),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("history_id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField()),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")], max_length=1),
                ),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="contracts.contract",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical budget estimation",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
