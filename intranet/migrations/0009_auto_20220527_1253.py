# Generated by Django 3.2.13 on 2022-05-27 10:53

from django.db import migrations, models

import users.managers


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0015_user_backoffice_allowed"),
        ("intranet", "0008_auto_20220512_2034"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProxy",
            fields=[],
            options={
                "verbose_name": "Intranet user",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("users.user",),
            managers=[
                ("objects", users.managers.UserManager()),
            ],
        ),
        migrations.AddField(
            model_name="historicalbudgetestimation",
            name="title",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="historicaldeliveryrequest",
            name="title",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
