# Generated by Django 3.2.25 on 2025-04-07 11:22

import uuid

import django.contrib.postgres.indexes
import django.db.models.deletion
from django.db import migrations, models

import core.mixins


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0083_warehousemovement_version"),
    ]

    operations = [
        migrations.CreateModel(
            name="Residence",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.Char<PERSON>ield(blank=True, help_text="Residence visible name", max_length=150, null=True)),
                ("address", models.CharField(blank=True, help_text="Residence address", max_length=200, null=True)),
                (
                    "normalized_name",
                    models.CharField(blank=True, help_text="Used to lookup the name", max_length=150, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="ResidenceGroup",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, help_text="Residence group name", max_length=150, null=True)),
                ("canonical_name", models.CharField(help_text="Group canonical name", max_length=150, unique=True)),
                (
                    "normalized_name",
                    models.CharField(help_text="Used to lookup the name", max_length=150, null=True, unique=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            bases=(models.Model, core.mixins.RelatedManagerModelMixin),
        ),
        migrations.AddIndex(
            model_name="residencegroup",
            index=django.contrib.postgres.indexes.GinIndex(
                fields=["normalized_name"], name="intranet_re_normali_462697_gin"
            ),
        ),
        migrations.AddField(
            model_name="residence",
            name="city",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="intranet.city"
            ),
        ),
        migrations.AddField(
            model_name="residence",
            name="group",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="intranet.residencegroup"
            ),
        ),
        migrations.AddIndex(
            model_name="residence",
            index=django.contrib.postgres.indexes.GinIndex(
                fields=["normalized_name"], name="intranet_re_normali_63b38d_gin"
            ),
        ),
    ]
