# Generated by Django 3.2.25 on 2024-10-23 09:52

from django.db import migrations
from django.db.models import F


def populate_backup_field(apps, schema_editor):
    Calendar = apps.get_model("intranet", "Calendar")
    Calendar.objects.update(city_backup=F("city"))


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0048_calendar_city_backup"),
    ]

    operations = [
        migrations.RunPython(populate_backup_field, reverse_code=migrations.RunPython.noop),
    ]
