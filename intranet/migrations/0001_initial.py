# Generated by Django 3.2.12 on 2022-04-08 19:37

import uuid

import django.contrib.postgres.fields
import django.core.serializers.json
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0008_auto_20220418_1036"),
        ("contracts", "0028_auto_20220224_1411"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BudgetEstimation",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "raw_data",
                    models.JSONField(
                        blank=True,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Attribute to keep the whole view data",
                        null=True,
                    ),
                ),
                ("total", models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                (
                    "inventory_list",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=200),
                        blank=True,
                        help_text="Inventory resume data to upload in hubspot",
                        null=True,
                        size=None,
                    ),
                ),
                ("published", models.BooleanField(default=False, help_text="If this budget estimation is in hubspot")),
                ("updated_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.contract"
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="owner_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
