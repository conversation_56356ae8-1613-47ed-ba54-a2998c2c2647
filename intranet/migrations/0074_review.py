# Generated by Django 3.2.25 on 2025-02-17 09:18

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0073_alter_event_booked_event"),
    ]

    operations = [
        migrations.CreateModel(
            name="Review",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "system",
                    models.CharField(choices=[("google", "Google"), ("trustpilot", "Trustpilot")], max_length=100),
                ),
                (
                    "link",
                    models.CharField(
                        blank=True,
                        help_text="Review link to send by email on contract signing",
                        max_length=300,
                        null=True,
                    ),
                ),
                ("enabled", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "city",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="intranet.city"
                    ),
                ),
            ],
            options={
                "unique_together": {("system", "city")},
            },
        ),
    ]
