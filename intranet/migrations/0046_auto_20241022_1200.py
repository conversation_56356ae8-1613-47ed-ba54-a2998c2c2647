# Generated by Django 3.2.25 on 2024-10-22 12:00

from django.db import migrations

from core.utils import normalize_string

TRANSLATED = {
    "porto": "oporto",
    "milano": "milan",
}


def add_warehouse_building(apps, schema_editor):
    Warehouse = apps.get_model("contracts", "Warehouse")
    City = apps.get_model("intranet", "City")

    cities = {city.canonical_name: city.id for city in City.objects.all()}

    warehouses = []
    for warehouse in Warehouse.objects.all():
        canonical_name = normalize_string(warehouse.name, replacer="_")
        canonical_city_name = normalize_string(warehouse.get_warehouse_city_display(), replacer="_")
        canonical_city_name = TRANSLATED.get(canonical_city_name) or canonical_city_name
        city_id = cities.get(canonical_city_name)

        warehouses.append(Warehouse(id=warehouse.id, canonical_name=canonical_name, city_id=city_id))

    if warehouses:
        Warehouse.objects.bulk_update(warehouses, fields=["canonical_name", "city_id"])


class Migration(migrations.Migration):
    dependencies = [
        ("intranet", "0045_auto_20241022_1151"),
    ]

    operations = [
        migrations.RunPython(add_warehouse_building, reverse_code=migrations.RunPython.noop),
    ]
