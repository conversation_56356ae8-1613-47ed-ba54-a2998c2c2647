# Generated by Django 3.2.25 on 2024-10-09 12:53

import uuid

import django_countries.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0042_auto_20240508_1820"),
    ]

    operations = [
        migrations.CreateModel(
            name="City",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, help_text="City visible name", max_length=150, null=True)),
                ("canonical_name", models.CharField(help_text="City canonical name", max_length=150, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("country", django_countries.fields.CountryField(max_length=2)),
            ],
        ),
    ]
