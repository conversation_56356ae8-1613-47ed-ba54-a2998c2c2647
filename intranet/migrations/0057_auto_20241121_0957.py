# Generated by Django 3.2.25 on 2024-11-21 09:57

import uuid

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0083_auto_20241121_0850"),
        ("intranet", "0056_auto_20241120_1314"),
    ]

    operations = [
        migrations.CreateModel(
            name="WarehouseUser",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "email",
                    models.EmailField(
                        max_length=254,
                        null=True,
                        unique=True,
                        validators=[django.core.validators.EmailValidator()],
                        verbose_name="Email address",
                    ),
                ),
                ("first_name", models.CharField(blank=True, max_length=200, null=True)),
                ("last_name", models.CharField(blank=True, max_length=200, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="WarehouseUserConfiguration",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "default",
                    models.BooleanField(
                        default=False, help_text="Check this if the warehouse user must be showed as default on events"
                    ),
                ),
                (
                    "should_be_notified",
                    models.BooleanField(
                        default=False, help_text="If this user must be notified when sending the moreapp email"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="intranet.warehouseuser"
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.warehouse"
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="warehouseuserconfiguration",
            constraint=models.UniqueConstraint(
                fields=("user", "warehouse"), name="unique_warehouse_user_configuration"
            ),
        ),
    ]
