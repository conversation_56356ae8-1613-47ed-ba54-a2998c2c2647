# Generated by Django 3.2.25 on 2024-11-20 13:14

from django.db import migrations
from django.db.models import Case, Char<PERSON>ield, Value, When

CONTRACT_REVIEW_LINKS = {
    "barcelona": "https://g.page/r/CYSnrtb2jLUBEAE/review",
    "malaga": "https://g.page/r/CeIwjGsedfGdEAE/review",
    "valencia": "https://g.page/r/CQrCR-ItEBjkEAE/review",
    "sevilla": "https://g.page/r/CfgbSdD11TPUEAE/review",
    "madrid": "https://g.page/r/CW8JASR_fXG0EAg/review",
    "paris": "https://g.page/r/CTuvBi0IvyMKEAE/review",
    "lisboa": "https://g.page/r/CYdUOl-o7dqJEAE/review",
    "oporto": "https://g.page/r/CYdUOl-o7dqJEAE/review",
    "milan": "https://g.page/r/CW8JASR_fXG0EAg/review",
    "queretaro": "https://g.page/r/Ce4SY6GWCaSZEB0/review",
    "granada": "https://g.page/r/CW8JASR_fXG0EAg/review",
    "salamanca": "https://g.page/r/CW8JASR_fXG0EAg/review",
    "bilbao": "https://g.page/r/CW8JASR_fXG0EAg/review",
    "san_sebastian": "https://g.page/r/CW8JASR_fXG0EAg/review",
    "pamplona": "https://g.page/r/CW8JASR_fXG0EAg/review",
}


def set_city(apps, schema_editor):
    City = apps.get_model("intranet", "City")

    cities = {city.canonical_name: CONTRACT_REVIEW_LINKS.get(city.canonical_name) for city in City.objects.all()}
    case_expression = Case(
        *[When(canonical_name=key, then=Value(value)) for key, value in cities.items()], output_field=CharField()
    )
    City.objects.update(review_link=case_expression)


def unset_city(apps, schema_editor):
    City = apps.get_model("intranet", "City")

    City.objects.update(review_link=None)


class Migration(migrations.Migration):
    dependencies = [
        ("intranet", "0055_city_review_link"),
    ]

    operations = [
        migrations.RunPython(set_city, reverse_code=unset_city),
    ]
