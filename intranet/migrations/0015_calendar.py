# Generated by Django 3.2.15 on 2023-02-07 08:43

import uuid

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0014_event_recurrentevent"),
    ]

    operations = [
        migrations.CreateModel(
            name="Calendar",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("calendar_name", models.CharField(blank=True, help_text="City id", max_length=100, null=True)),
                (
                    "city",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("unknown", "Unknown"),
                            ("ES-M", "Madrid"),
                            ("PT-P", "Porto"),
                            ("ES-S", "Sevilla"),
                            ("ES-B", "Barcelona"),
                            ("ES-ML", "Malaga"),
                            ("ES-V", "Valencia"),
                            ("IT-M", "Milano"),
                            ("FR-P", "Paris"),
                            ("PT-L", "Lisboa"),
                        ],
                        default="unknown",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "calendar_id",
                    models.CharField(blank=True, help_text="Calendar id on google calendar", max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("warehouse", models.ManyToManyField(blank=True, to="contracts.Warehouse")),
            ],
        ),
    ]
