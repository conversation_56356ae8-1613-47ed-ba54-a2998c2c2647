# Generated by Django 3.2.25 on 2025-02-20 14:07

import django.contrib.postgres.indexes
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0078_alter_warehousedifference_options"),
        ("contracts", "0096_auto_20250220_1321"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="warehouseuser",
            index=django.contrib.postgres.indexes.GinIndex(fields=["email"], name="intranet_wa_email_d1290d_gin"),
        ),
    ]
