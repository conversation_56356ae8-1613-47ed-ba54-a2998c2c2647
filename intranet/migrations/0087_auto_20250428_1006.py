# Generated by Django 3.2.25 on 2025-04-28 10:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0086_calendarriderconfig_default"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="globalsettings",
            name="student_pack_delivery_space_mapping",
            field=models.FloatField(
                blank=True,
                choices=[
                    (0.5, "0.5"),
                    (1, "1"),
                    (2, "2"),
                    (3, "3"),
                    (4, "4"),
                    (5, "5"),
                    (6, "6"),
                    (7.5, "7.5"),
                    (9, "9"),
                    (12, "12"),
                    (15, "15"),
                    (20, "20"),
                    (25, "25"),
                    (30, "30"),
                    (35, "35"),
                    (40, "40"),
                    (45, "45"),
                    (50, "50"),
                    (-1, "50+"),
                ],
                null=True,
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="globalsettings",
            name="student_pack_pickup_space_mapping",
            field=models.FloatField(
                blank=True,
                choices=[
                    (0.5, "0.5"),
                    (1, "1"),
                    (2, "2"),
                    (3, "3"),
                    (4, "4"),
                    (5, "5"),
                    (6, "6"),
                    (7.5, "7.5"),
                    (9, "9"),
                    (12, "12"),
                    (15, "15"),
                    (20, "20"),
                    (25, "25"),
                    (30, "30"),
                    (35, "35"),
                    (40, "40"),
                    (45, "45"),
                    (50, "50"),
                    (-1, "50+"),
                ],
                null=True,
            ),
        ),
    ]
