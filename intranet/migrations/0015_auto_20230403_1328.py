# Generated by Django 3.2.18 on 2023-04-03 11:28

import django.core.serializers.json
from django.db import migrations

import core.fields


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0014_auto_20230327_1238"),
    ]

    operations = [
        migrations.AddField(
            model_name="budgetestimation",
            name="raw_data_with_preserved_order",
            field=core.fields.RawJSONField(
                blank=True,
                encoder=django.core.serializers.json.DjangoJSONEncoder,
                help_text="Attribute to keep the whole view data",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="historicalbudgetestimation",
            name="raw_data_with_preserved_order",
            field=core.fields.RawJSONField(
                blank=True,
                encoder=django.core.serializers.json.DjangoJSONEncoder,
                help_text="Attribute to keep the whole view data",
                null=True,
            ),
        ),
    ]
