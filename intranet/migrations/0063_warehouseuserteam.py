# Generated by Django 3.2.25 on 2024-12-12 16:01

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0093_auto_20241210_1554"),
        ("intranet", "0062_warehouseuser_date_joined"),
    ]

    operations = [
        migrations.CreateModel(
            name="WarehouseUserTeam",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("date_added", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "service",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.service"),
                ),
                (
                    "warehouse_user",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to="intranet.warehouseuser"
                    ),
                ),
            ],
            options={
                "unique_together": {("warehouse_user", "service")},
            },
        ),
    ]
