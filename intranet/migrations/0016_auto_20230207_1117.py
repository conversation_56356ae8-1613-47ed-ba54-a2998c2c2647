# Generated by Django 3.2.15 on 2023-02-07 10:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0015_calendar"),
    ]

    operations = [
        migrations.AddField(
            model_name="event",
            name="calendar",
            field=models.ForeignKey(
                help_text="Calendar linked to this event",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="intranet.calendar",
            ),
        ),
        migrations.AddField(
            model_name="recurrentevent",
            name="calendar",
            field=models.ForeignKey(
                help_text="Calendar linked to this event",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="intranet.calendar",
            ),
        ),
    ]
