# Generated by Django 3.2.25 on 2025-01-20 09:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("riders", "0016_auto_20241203_1205"),
        ("intranet", "0065_auto_20241223_1304"),
    ]

    operations = [
        migrations.AlterField(
            model_name="warehousemovement",
            name="intranet_user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="intranet_user",
                to="intranet.userproxy",
            ),
        ),
        migrations.AlterField(
            model_name="warehousemovement",
            name="rider",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="riders.riderproxy"
            ),
        ),
        migrations.AlterField(
            model_name="warehousemovement",
            name="warehouse_movement",
            field=models.ForeignKey(
                blank=True,
                help_text="If this FK is set means this is an old movement which is replaced by a new movement",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="intranet.warehousemovement",
            ),
        ),
    ]
