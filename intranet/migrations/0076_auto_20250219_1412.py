# Generated by Django 3.2.25 on 2025-02-19 14:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0094_service_billing_cycle_notes"),
        ("intranet", "0075_auto_20250217_0918"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="warehousedifference",
            options={"ordering": ("-date", "warehouse")},
        ),
        migrations.RenameField(
            model_name="warehousedifference",
            old_name="warehouse_id",
            new_name="renamed_warehouse_id",
        ),
        migrations.AddField(
            model_name="warehousedifference",
            name="warehouse",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.warehouse"),
        ),
        migrations.AlterUniqueTogether(
            name="warehousedifference",
            unique_together={("date", "warehouse")},
        ),
    ]
