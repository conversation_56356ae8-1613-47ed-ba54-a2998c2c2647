# Generated by Django 3.2.25 on 2025-02-25 13:59

from django.db import migrations, models

from core.utils import normalize_string


def update_cities(apps, schema_editor):
    City = apps.get_model("intranet", "City")
    cities = [
        City(id=city.id, normalized_name=normalize_string(city.name)) for city in City.objects.all().only("id", "name")
    ]

    if cities:
        City.objects.bulk_update(cities, fields=["normalized_name"])


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0079_warehouseuser_intranet_wa_email_d1290d_gin"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="city",
            name="review_link",
        ),
        migrations.AddField(
            model_name="city",
            name="normalized_name",
            field=models.CharField(help_text="Used to lookup the name", max_length=150, null=True, unique=True),
        ),
        migrations.RunPython(update_cities, reverse_code=migrations.RunPython.noop),
    ]
