# Generated by Django 3.2.23 on 2023-12-19 17:31

import uuid

import django.core.serializers.json
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("intranet", "0028_remove_calendar_warehouse"),
    ]

    operations = [
        migrations.CreateModel(
            name="Task",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("additional_info", models.TextField(blank=True, null=True)),
                (
                    "raw_data",
                    models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, null=True),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("submitted_at", models.DateTimeField(help_text="Date of submission for the task", null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="task_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "submitted_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Rider who has submitted this service",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="task_submitted_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="event",
            name="task",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="intranet.task"),
        ),
    ]
