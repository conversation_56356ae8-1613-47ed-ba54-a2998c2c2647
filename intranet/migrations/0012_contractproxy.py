# Generated by Django 3.2.13 on 2022-06-24 11:08

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0030_contract_signed_user_name"),
        ("intranet", "0011_warehousedifference_created_at"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContractProxy",
            fields=[],
            options={
                "verbose_name": "Intranet contract",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("contracts.contract",),
        ),
    ]
