# Generated by Django 3.2.25 on 2024-05-08 18:20

from django.db import migrations


def create_default_global_settings(apps, schema_editor):
    GlobalSettings = apps.get_model("intranet", "GlobalSettings")
    GlobalSettings.objects.get_or_create(
        student_pack_billing_cycle="2024-10-02",
        student_pack_space_in_pdf=2,
    )


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0041_globalsettings"),
    ]

    operations = [
        migrations.RunPython(create_default_global_settings, reverse_code=migrations.RunPython.noop),
    ]
