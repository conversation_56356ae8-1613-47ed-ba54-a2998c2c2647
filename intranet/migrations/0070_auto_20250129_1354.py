# Generated by Django 3.2.25 on 2025-01-29 13:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0069_auto_20250129_1346"),
    ]

    operations = [
        migrations.AddField(
            model_name="warehousemovement",
            name="parent_warehouse_movement",
            field=models.ForeignKey(
                blank=True,
                help_text="If this FK is set means this is an old entry which is replaced by a new entry",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="parent_warehouse_movement_related",
                to="intranet.warehousemovement",
            ),
        ),
        migrations.AddField(
            model_name="warehousemovement",
            name="root_warehouse_movement",
            field=models.ForeignKey(
                blank=True,
                help_text="FK to improve lookups setting the most up-to-date entry related with this object",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="root_warehouse_movement_related",
                to="intranet.warehousemovement",
            ),
        ),
    ]
