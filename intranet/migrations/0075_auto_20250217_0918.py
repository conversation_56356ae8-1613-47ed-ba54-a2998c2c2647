# Generated by Django 3.2.25 on 2025-02-17 09:18

from django.db import migrations


def set_review(apps, schema_editor):
    City = apps.get_model("intranet", "City")
    Review = apps.get_model("intranet", "Review")

    cities = City.objects.all().values("id", "review_link")

    reviews = []
    for city in cities:
        reviews.extend([
            Review(system="google", link=city.get("review_link"), city_id=city.get("id")),
            Review(
                system="trustpilot",
                link="<EMAIL>",
                city_id=city.get("id"),
                enabled=False,
            ),
        ])

    if reviews:
        Review.objects.bulk_create(reviews)


class Migration(migrations.Migration):

    dependencies = [
        ("intranet", "0074_review"),
    ]

    operations = [
        migrations.RunPython(set_review, reverse_code=migrations.RunPython.noop),
    ]
