from operator import itemgetter

from intranet.templates import BaseTemplateProcessor


class GenericProcessor(BaseTemplateProcessor):
    def build(self, sheet, **data):
        """Build the excel file for generic warehouses (i.e. all except logwin)"""
        contract_internal_id, odoo_number, user_name, delivery_date, raw_data = itemgetter(
            "contract_internal_id", "odoo_number", "user_name", "delivery_date", "raw_data"
        )(data)

        user_name = user_name or ""

        # Fill in the contract and customer metadata
        rows = [[13, 4, contract_internal_id], [14, 4, odoo_number], [17, 4, user_name]]

        self.populate_cells(rows, sheet)

        # Fill in the inventory data
        for index, data in enumerate(raw_data):
            tag_id = data.get("tag_id")
            description = data.get("description")
            index = 21 + index

            rows = [
                [index, 2, delivery_date.strftime("%d/%m/%Y")],
                [index, 3, self.sanitize_tag(tag_id)],
                [index, 4, description],
                [index, 5, user_name or ""],
                [index, 6, contract_internal_id or odoo_number],
            ]

            self.populate_cells(rows, sheet)
