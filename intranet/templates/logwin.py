from operator import itemgetter

from openpyxl.styles import Alignment, Font

from intranet.templates import BaseTemplateProcessor


class LogwinProcessor(BaseTemplateProcessor):
    def build(self, sheet, **data):
        """Build the excel file for generic warehouses (i.e. all except logwin)"""
        contract_internal_id, odoo_number, user_name, delivery_date, raw_data = itemgetter(
            "contract_internal_id", "odoo_number", "user_name", "delivery_date", "raw_data"
        )(data)

        contract_id = contract_internal_id or odoo_number

        # Fill in the inventory data
        for index, data in enumerate(raw_data):
            tag_id = data.get("tag_id")
            index = 2 + index

            rows = [
                [index, 1, "BX2BX"],
                [index, 2, f"{contract_id}{delivery_date.strftime('%d%m%y')}"],
                [index, 4, index - 1],
                [index, 5, self.sanitize_tag(tag_id)],
                [index, 6, 1],
                [index, 7, "CT4"],
                [index, 12, f"{contract_id}{user_name or ''}"],
                [index, 16, "RECOGE CLIENTE"],
                [index, 17, 3],
                [index, 18, 28830],
                [index, 19, "MADRID"],
                [index, 20, 724],
                [index, 21, delivery_date.strftime("%Y%m%d")],
                [index, 23, delivery_date.strftime("%Y%m%d")],
            ]

            self.populate_cells(
                rows,
                sheet,
                ("font", Font(underline="single", sz=8, name="Arial", b=False)),
                ("alignment", Alignment(horizontal="left")),
            )
