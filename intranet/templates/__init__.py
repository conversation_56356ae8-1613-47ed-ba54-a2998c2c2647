from io import BytesIO

from openpyxl import load_workbook

from core.utils import sanitize


class BaseTemplateProcessor:
    def build(self, sheet, **data):
        raise NotImplementedError("Not implemented yet!")

    def populate_cells(self, cell_info: list, sheet, *extras):
        """Helper method to fill in cells in the excel file"""
        for cell in cell_info:
            cell = sheet.cell(*cell)

            # Set things like style or alignment among others
            for extra in extras:
                setattr(cell, f"{extra[0]}", extra[1])

    def sanitize_tag(self, tag_id):
        """Remove non-printable tags before adding to excel"""
        return sanitize(tag_id)

    def __call__(self, template_path: str, **data):
        """Run this template processor to build the excel file"""

        workbook = load_workbook(template_path)

        # Build the excel file
        self.build(sheet=workbook.active, **data)

        virtual_workbook = BytesIO()
        workbook.save(virtual_workbook)

        return virtual_workbook
