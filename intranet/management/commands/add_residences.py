import logging
from io import BytesIO, StringIO

import pandas as pd
from django.conf import settings
from django.core.management import BaseCommand
from django.utils import timezone
from google.cloud import storage

from core.utils import normalize_string
from intranet.models import City, Residence, ResidenceGroup

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Add residence entries in Residence and ResidenceGroup models"
    bucket = "box2box-migration-files"
    now = timezone.now()

    def download_from_remote_storage(self):
        """Fetch from cloud storage csv file"""

        storage_client = storage.Client.from_service_account_json(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT,
        )

        bucket = storage_client.bucket(self.bucket)
        blob = bucket.blob("residences.csv")

        fp = BytesIO()
        blob.download_to_file(fp)
        return StringIO(fp.getvalue().decode("utf-8"))

    def handle(self, *args, **options):
        logger.info("Populating database with residences...")

        # Delete entries from database
        Residence.objects.all().delete()
        ResidenceGroup.objects.all().delete()

        # Try to download from remote storage first
        file = self.download_from_remote_storage()

        # Read the CSV file
        df = pd.read_csv(file, sep=",")

        # Clean string columns to remove trailing spaces or newlines
        string_columns = [
            "Cadena",
            "Residencia",
            "Calle",
            "Número",
            "Código postal",
            "Ciudad",
            "Corresponde a la ciudad",
        ]
        for col in string_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # Replace empty values in Cadena column with 'Default'
        df["Cadena"] = df["Cadena"].replace(["", "nan", "None"], "Default")
        df["Cadena"].fillna("Default", inplace=True)

        # Create address column by concatenating Calle, Número, Código postal, Ciudad
        df["address"] = df.apply(
            lambda row: f"{row['Calle']}, {row['Número']}, {row['Código postal']}, {row['Ciudad']}".strip(), axis=1
        )

        # Get unique groups
        unique_groups = df["Cadena"].apply(lambda x: normalize_string(x).capitalize()).unique()

        # Create ResidenceGroup entries
        residence_groups = []
        group_mapping = {}

        for group_name in unique_groups:
            normalized_name = normalize_string(group_name).strip()
            canonical_name = normalize_string(group_name, replacer="_").strip()

            residence_group = ResidenceGroup(
                name=group_name, normalized_name=normalized_name, canonical_name=canonical_name
            )
            residence_groups.append(residence_group)
            group_mapping[normalized_name] = residence_group

        # Get all cities for fast lookup
        cities = {normalize_string(city.name): city.id for city in City.objects.all()}

        # Create Residence entries
        residences = []
        for _, row in df.iterrows():
            group = group_mapping.get(normalize_string(row["Cadena"]))
            city_id = cities.get(normalize_string(row["Corresponde a la ciudad"]))

            if group and city_id:
                # Get the residence name and normalize it
                residence_name = row["Residencia"]
                normalized_name = normalize_string(residence_name).strip()

                residence = Residence(
                    name=residence_name,
                    address=row["address"],
                    group_id=group.id,
                    city_id=city_id,
                    normalized_name=normalized_name,
                )
                residences.append(residence)

        # Bulk create ResidenceGroup entries
        if residence_groups:
            ResidenceGroup.objects.bulk_create(residence_groups, ignore_conflicts=True)

        # Bulk create Residence entries
        if residences:
            Residence.objects.bulk_create(residences, ignore_conflicts=True)
