from django.db import IntegrityError
from django.utils.translation import gettext_lazy as _
from django_countries.serializer_fields import CountryField
from rest_framework import serializers

from core.mixins import PartialUpdateMixin
from core.serializers import ModelSerializer
from intranet.models import City, Residence, ResidenceGroup


class IntranetResidenceGroupSerializer(ModelSerializer):
    class Meta:
        model = ResidenceGroup
        fields = (
            "id",
            "name",
            "created_at",
        )


class IntranetResidenceCitySerializer(ModelSerializer):
    country = CountryField(country_dict=True)

    class Meta:
        model = City
        fields = (
            "id",
            "name",
            "country",
        )


class IntranetResidenceSerializer(ModelSerializer):
    group = IntranetResidenceGroupSerializer(read_only=True)
    city = IntranetResidenceCitySerializer(read_only=True)

    class Meta:
        model = Residence
        fields = (
            "id",
            "name",
            "address",
            "group",
            "city",
            "created_at",
        )


class IntranetResidenceCreateSerializer(ModelSerializer):
    group_id = serializers.UUIDField(required=True, write_only=True)
    city_id = serializers.UUIDField(required=True, write_only=True)
    group = IntranetResidenceGroupSerializer(read_only=True)
    city = IntranetResidenceCitySerializer(read_only=True)

    class Meta:
        model = Residence
        fields = (
            "id",
            "name",
            "address",
            "group",
            "city",
            "group_id",
            "city_id",
            "created_at",
        )
        extra_kwargs = {
            "created_at": {"read_only": True},
        }

    def create(self, validated_data):
        try:
            return super().create(validated_data)
        except IntegrityError as error:
            raise serializers.ValidationError({"name": _("The residence already exists")}) from error


class IntranetResidencePartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    group_id = serializers.UUIDField(required=False, write_only=True)
    city_id = serializers.UUIDField(required=False, write_only=True)
    group = IntranetResidenceGroupSerializer(read_only=True)
    city = IntranetResidenceCitySerializer(read_only=True)

    class Meta:
        model = Residence
        fields = (
            "id",
            "name",
            "address",
            "group",
            "city",
            "group_id",
            "city_id",
            "created_at",
        )
        extra_kwargs = {
            "created_at": {"read_only": True},
        }

    def update(self, instance, validated_data):
        try:
            return super().update(instance, validated_data)
        except IntegrityError as error:
            raise serializers.ValidationError({"name": _("The residence already exists")}) from error
