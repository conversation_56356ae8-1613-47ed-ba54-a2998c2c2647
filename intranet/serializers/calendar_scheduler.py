from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.mixins import StringErrorsMixin


class CalendarEventCreateSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to create an event in google calendar"""

    calendar_id = serializers.CharField(required=True, allow_null=True, allow_blank=True)
    summary = serializers.CharField(required=True, allow_null=False, allow_blank=False, help_text=_("Title"))
    start_date = serializers.DateTimeField(required=False, allow_null=True)
    end_date = serializers.DateTimeField(required=False, allow_null=True)
    timezone = serializers.CharField(default="Europe/Madrid")
    is_all_day = serializers.BooleanField(default=False)
    description = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    attendees = serializers.ListSerializer(
        child=serializers.Char<PERSON>ield(required=True, allow_null=False, allow_blank=False),
        required=False,
        allow_null=True,
        allow_empty=True,
    )


class CalendarEventPartialUpdateSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to update an event in google calendar"""

    calendar_id = serializers.CharField(required=True, allow_null=True, allow_blank=True)
    event_id = serializers.CharField(required=True, allow_null=True, allow_blank=True)
    body = serializers.JSONField(required=True, allow_null=False)


class CalendarEventDeleteSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to delete an event in google calendar"""

    calendar_id = serializers.CharField(required=True, allow_null=True, allow_blank=True)
    event_id = serializers.CharField(required=True, allow_null=True, allow_blank=True)


class CalendarEventListBodySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to handle body to fetch events from specific calendars"""

    id = serializers.CharField(required=True, allow_null=False, allow_blank=False)


class CalendarEventListQueryParamsSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to handle query params to fetch events from specific calendars"""

    start_date = serializers.DateTimeField(required=True, allow_null=False)
    end_date = serializers.DateTimeField(required=True, allow_null=False)


class CalendarEventListSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to list events from google calendar"""

    id = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    category = serializers.CharField(default="time")
    title = serializers.CharField(required=True, allow_null=True, allow_blank=True, source="summary")
    calendarId = serializers.CharField(required=True, allow_null=False, allow_blank=False, source="calendar_id")
    raw = serializers.CharField(required=False, allow_null=False, allow_blank=False, source="description")
    start = serializers.DateTimeField(required=False, allow_null=False)
    end = serializers.DateTimeField(required=False, allow_null=False)
    attendees = serializers.ListSerializer(
        child=serializers.CharField(allow_null=True, allow_blank=True),
        required=False,
        allow_null=True,
        allow_empty=True,
    )
    status = serializers.CharField(required=True, allow_null=False, allow_blank=True)
    isAllDay = serializers.BooleanField(default=False, allow_null=False)


class CalendarEventErrorListSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to list events from google calendar"""

    error = serializers.CharField(required=True, allow_null=True, allow_blank=True)


class CalendarEventPartialSyncSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to handle partial sync configurations"""

    is_partial = serializers.BooleanField(default=True)
    events = CalendarEventListSerializer(many=True)


class CalendarListSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to list every google calendar linked to main account"""

    id = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    name = serializers.CharField(required=True, allow_blank=False, allow_null=False, source="summary")
    selected = serializers.BooleanField(default=False)
    color = serializers.CharField(required=True, allow_blank=False, allow_null=False, source="foregroundColor")
    bgColor = serializers.CharField(required=True, allow_null=False, allow_blank=False, source="backgroundColor")
    borderColor = serializers.CharField(required=True, allow_null=False, allow_blank=False, source="backgroundColor")
    dragBgColor = serializers.CharField(required=True, allow_null=False, allow_blank=False, source="backgroundColor")
