from django.contrib.auth import get_user_model
from rest_framework import serializers

from core.fields import <PERSON><PERSON><PERSON><PERSON>
from core.serializers import ModelSerializer

User = get_user_model()


class IntranetUserSerializer(ModelSerializer):
    """Basic serializer to get user data"""

    full_name = <PERSON><PERSON><PERSON><PERSON>(source="get_full_name", read_only=True, on_null="-")
    email = CharField(read_only=True, on_null="-")
    phone = CharField(read_only=True, on_null="-")
    groups = serializers.ListSerializer(child=serializers.Char<PERSON>ield(source="name"), read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "phone",
            "full_name",
            "first_name",
            "last_name",
            "crm_internal_id",
            "chargebee_customer_id",
            "chargebee_full_url",
            "hubspot_full_url",
            "groups",
        )
        ref_name = "IntranetUser"
