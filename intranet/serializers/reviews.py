from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator, URLValidator
from django.db import IntegrityError, transaction
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.serializers import ModelSerializer
from intranet.models import City
from intranet.models.reviews import Review, System


class ReviewCitySerializer(ModelSerializer):
    class Meta:
        model = City
        fields = (
            "id",
            "name",
        )


class ReviewSerializer(ModelSerializer):
    city = ReviewCitySerializer(read_only=True)
    cut_off = serializers.DateTimeField(required=True, allow_null=True, write_only=True)

    class Meta:
        model = Review
        fields = ["id", "link", "enabled", "system", "created_at", "city", "cut_off"]
        extra_kwargs = {"system": {"read_only": True}}


class ReviewSystemSerializer(StringErrorsMixin, serializers.Serializer):
    name = serializers.CharField(required=True)


class ReviewCreateSerializer(ModelSerializer):
    city = ReviewCitySerializer(read_only=True)
    city_id = serializers.UUIDField(required=True, write_only=True)

    class Meta:
        model = Review
        fields = ["id", "link", "enabled", "system", "created_at", "city", "city_id"]
        extra_kwargs = {
            "created_at": {"read_only": True},
        }

    def validate(self, attrs):
        system = attrs.get("system")
        link = attrs.get("link")

        if system == System.TRUSTPILOT:
            validator = EmailValidator()
            try:
                validator(link)
            except ValidationError as error:
                raise serializers.ValidationError({"link": _("Invalid email")}) from error
        else:
            validator = URLValidator()
            try:
                validator(link)
            except ValidationError as error:
                raise serializers.ValidationError({"link": _("Invalid URL")}) from error

        return attrs

    def create(self, validated_data):
        try:
            with transaction.atomic():
                instance = super().create(validated_data)

                if instance.enabled:
                    self.Meta.model.objects.select_for_update().filter(~Q(id=instance.id)).update(enabled=False)

                return instance
        except IntegrityError as error:
            raise serializers.ValidationError({
                "non_field_errors": _("The system ({system}) already exists in the selected city").format(
                    system=validated_data["system"]
                )
            }) from error


class ReviewBulkSerializer(StringErrorsMixin, serializers.Serializer):
    """Bulk delete with list serializer"""

    cut_off = serializers.DateTimeField(required=True, allow_null=True)


class ReviewPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    city = ReviewCitySerializer(read_only=True)

    class Meta:
        model = Review
        fields = ["id", "link", "enabled", "system", "created_at", "city"]
        extra_kwargs = {"created_at": {"read_only": True}, "system": {"read_only": True}, "city": {"read_only": True}}

    def update(self, instance, validated_data):
        enabled = validated_data.get("enabled")

        if enabled:
            self.Meta.model.objects.filter(~Q(id=instance.id) & Q(city=instance.city)).update(enabled=False)

        return super().update(instance, validated_data)
