import logging
import traceback

from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_serializer_method
from googleapiclient.errors import HttpError
from network_clients.base.base import ClientError
from rest_framework import serializers

from core.external_services import asynchronous_client
from core.fields import Char<PERSON>ield
from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.serializers import ModelSerializer
from intranet.core.google_calendar_api.base import CalendarRateLimitError
from intranet.core.google_calendar_api.wrappers import (
    GoogleCalendarListWrapper, GoogleCalendarSingleWrapper)
from intranet.models import Calendar, CalendarConfig, CalendarRiderConfig
from riders.models import RiderProxy

User = get_user_model()

logger = logging.getLogger(__name__)


class IntranetCalendarUserMetadataSerializer(ModelSerializer):
    id = serializers.UUIDField()
    full_name = CharField(source="get_full_name", read_only=True, on_null="-")

    class Meta:
        model = User
        fields = ("id", "email", "full_name")
        ref_name = "IntranetCalendarInternalUserSerializer"


class IntranetCalendarConfigurationSerializer(ModelSerializer):
    user = IntranetCalendarUserMetadataSerializer(read_only=True)

    class Meta:
        model = CalendarConfig
        fields = ["id", "visible", "user"]


class IntranetCalendarRidersConfigurationSerializer(ModelSerializer):
    rider = IntranetCalendarUserMetadataSerializer(read_only=True)

    class Meta:
        model = CalendarRiderConfig
        fields = ["id", "default", "rider"]


class IntranetCalendarSerializer(ModelSerializer):
    configuration = IntranetCalendarConfigurationSerializer(source="users_data", many=True)
    owner = IntranetCalendarUserMetadataSerializer(read_only=True)
    created_on_remote = serializers.BooleanField(read_only=True)
    city_name = serializers.CharField(read_only=True)
    riders_configuration = IntranetCalendarRidersConfigurationSerializer(source="riders_data", many=True)

    class Meta:
        model = Calendar
        fields = (
            "id",
            "calendar_name",
            "city_id",
            "calendar_id",
            "configuration",
            "color",
            "owner",
            "timezone",
            "created_on_remote",
            "city_name",
            "riders_configuration",
        )


class IntranetCalendarConcreteSerializer(ModelSerializer):
    shares = IntranetCalendarConfigurationSerializer(many=True, source="calendarconfig")
    owner = IntranetCalendarUserMetadataSerializer(read_only=True)

    class Meta:
        model = Calendar
        fields = ["id", "calendar_name", "city", "calendar_id", "shares", "owner", "created_on_remote"]


class IntranetCalendarCreateSerializer(ModelSerializer):
    configuration = IntranetCalendarConfigurationSerializer(many=True, source="calendarconfig", read_only=True)
    visible = serializers.SerializerMethodField()
    created_on_remote = serializers.BooleanField(read_only=True)
    city_name = serializers.CharField(read_only=True)
    owner = IntranetCalendarUserMetadataSerializer(read_only=True)

    class Meta:
        model = Calendar
        fields = [
            "id",
            "calendar_name",
            "city",
            "calendar_id",
            "color",
            "visible",
            "timezone",
            "configuration",
            "created_on_remote",
            "city_name",
            "owner",
        ]
        extra_kwargs = {"calendar_id": {"read_only": True}}

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField)
    def get_visible(self, obj):
        calendar_config = obj.calendarconfig.filter(user=self.context["request"].user).first()
        if calendar_config:
            return calendar_config.visible
        return False

    def create(self, validated_data):
        validated_data["owner"] = self.context["request"].user
        with transaction.atomic():
            instance = super().create(validated_data)

            CalendarConfig.objects.create(calendar=instance, user=self.context["request"].user)

        # Create the remote calendar
        try:
            # Finally, create the google calendar event
            event_wrapper = GoogleCalendarSingleWrapper(impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL)
            response = event_wrapper.create(
                body={
                    "summary": instance.calendar_name,
                    "timeZone": instance.timezone,
                },
                quota_user=self.context["request"].user,
            )

            # Update the color
            event_wrapper = GoogleCalendarListWrapper(impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL)
            event_wrapper.patch(response["id"], body={"backgroundColor": instance.color}, colorRgbFormat=True)

            # Update the event
            instance.calendar_id = response["id"]
            instance.save(update_fields=["calendar_id"])
        except (CalendarRateLimitError, HttpError):
            logger.error(f"Error creating google calendar {traceback.print_exc(chain=False)}")

        return instance


class IntranetCalendarPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    city_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Calendar
        fields = [
            "id",
            "calendar_name",
            "city",
            "city_id",
            "city_name",
            "color",
            "timezone",
        ]

    @swagger_serializer_method(serializer_or_field=serializers.CharField)
    def get_city_name(self, obj):
        return obj.city.name if obj.city else None

    def update(self, instance, validated_data):
        color = validated_data.get("color")
        calendar_name = validated_data.get("calendar_name")
        current_calendar_name = instance.calendar_name
        current_city = instance.city
        instance = super().update(instance, validated_data)

        if color:
            try:
                event_wrapper = GoogleCalendarSingleWrapper(
                    impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL
                )

                if not instance.created_on_remote:
                    response = event_wrapper.create(
                        body={
                            "summary": instance.calendar_name,
                            "timeZone": instance.timezone,
                        },
                        quota_user=self.context["request"].user,
                    )

                    # Update the event
                    instance.calendar_id = response["id"]
                    instance.save(update_fields=["calendar_id"])
                else:
                    if calendar_name and calendar_name != current_calendar_name:
                        event_wrapper.patch(instance.calendar_id, body={"summary": instance.calendar_name})

                event_wrapper = GoogleCalendarListWrapper(
                    impersonated_email=settings.GOOGLE_CALENDAR_IMPERSONATED_EMAIL
                )
                event_wrapper.patch(instance.calendar_id, body={"backgroundColor": instance.color}, colorRgbFormat=True)
            except (CalendarRateLimitError, HttpError):
                logger.error(f"Error updating google calendar {traceback.print_exc(chain=False)}")

        if current_city != instance.city:
            try:
                asynchronous_client.update_holidays_on_calendars(
                    city_id=None,
                    calendar_id=instance.id,
                    request_user=self.context["request"].user.email,
                    in_seconds=5,
                )
            except ClientError as err:
                logger.error(f"Error updating holidays on calendars due to: {err}")
            except Exception as err:
                logger.error(f"Unknown error updating holidays on calendars due to: {err}")

        return instance

    # def exclude_fields_in_many_to_many(self, instance, attribute):
    #     """Override this method from M2MMixin to exclude fields from relationship when updating instance"""
    #     if attribute == "warehouse":
    #         return [Q(id__in=instance.warehouse.values("id"))]


class IntranetCalendarDeleteSerializer(StringErrorsMixin, serializers.Serializer):
    delete_on_remote = serializers.BooleanField(default=False)


class IntranetCalendarUserSerializer(StringErrorsMixin, serializers.Serializer):
    configurations = serializers.ListSerializer(
        child=serializers.UUIDField(allow_null=False), required=True, allow_empty=True, allow_null=True
    )
    users = serializers.ListSerializer(child=serializers.UUIDField(allow_null=False), required=True)

    def validate(self, attrs):
        ids = attrs.get("users")
        allowed_users = User.objects.filter(id__in=ids, backoffice_allowed=True, is_active=True).count()

        if len(ids) != allowed_users:
            raise serializers.ValidationError({
                "ids": _("You are trying to share this calendar with non allowed users"),
            })

        return attrs


class IntranetRiderDefaultSerializer(StringErrorsMixin, serializers.Serializer):
    id = serializers.UUIDField(allow_null=False, required=True)
    default = serializers.BooleanField(default=False)


class IntranetCalendarRiderSerializer(StringErrorsMixin, serializers.Serializer):
    configurations = serializers.ListSerializer(
        child=serializers.UUIDField(allow_null=False), required=True, allow_empty=True, allow_null=True
    )
    riders = serializers.ListSerializer(child=serializers.UUIDField(allow_null=False), required=True)
    defaults = IntranetRiderDefaultSerializer(many=True, allow_null=False, required=True)

    def validate(self, attrs):
        ids = attrs.get("riders")
        defaults = attrs.get("defaults")
        default_ids = [default["id"] for default in defaults]

        ids = set(ids + default_ids)
        allowed_riders = RiderProxy.objects.filter(id__in=ids, date_leave__isnull=True, is_active=True).count()

        if len(ids) != allowed_riders:
            raise serializers.ValidationError({
                "ids": _(
                    "You are trying to share this calendar or set as default users on this calendar with non allowed"
                    " users"
                ),
            })

        return attrs


class IntranetCalendarVisibilitySerializer(StringErrorsMixin, serializers.Serializer):
    visibility = serializers.BooleanField(required=True)


class IntranetCalendarRidersSerializer(ModelSerializer):
    full_name = CharField(source="get_full_name", read_only=True, on_null="-")
    avatar = serializers.SerializerMethodField()
    is_default = serializers.SerializerMethodField(read_only=True)
    excluded = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = RiderProxy
        fields = ("id", "email", "full_name", "avatar", "is_default", "excluded")

    @swagger_serializer_method(serializer_or_field=serializers.CharField)
    def get_avatar(self, obj):
        first_name = obj.first_name[0] if obj.first_name else "U"
        last_name = obj.last_name[0] if obj.last_name else ""

        return f"{first_name}{last_name}"

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField)
    def get_is_default(self, obj):
        return obj.default

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField)
    def get_excluded(self, obj):
        return obj.excluded or False


class IntranetCalendarDisplayHolidaysSerializer(StringErrorsMixin, serializers.Serializer):
    display_holidays = serializers.BooleanField(required=True)
