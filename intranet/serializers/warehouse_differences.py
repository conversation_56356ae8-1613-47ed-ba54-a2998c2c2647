from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from contracts.models import Warehouse
from core.serializers import ModelSerializer
from intranet.models import City, WarehouseDifference


class WarehouseDifferenceCitySerializer(ModelSerializer):
    class Meta:
        model = City
        fields = (
            "id",
            "name",
        )


class WarehouseDifferenceWarehouseSerializer(ModelSerializer):
    city = WarehouseDifferenceCitySerializer(read_only=True)

    class Meta:
        model = Warehouse
        fields = (
            "id",
            "name",
            "city",
        )


class WarehouseDifferenceSerializer(ModelSerializer):
    """Basic serializer to get warehouse differences"""

    warehouse = WarehouseDifferenceWarehouseSerializer(read_only=True)

    class Meta:
        model = WarehouseDifference
        fields = "__all__"


class WarehouseDifferenceCreateSerializer(ModelSerializer):
    """Serializer to create a warehouse difference"""

    date = serializers.DateField(required=True)
    warehouse = WarehouseDifferenceWarehouseSerializer(read_only=True)
    warehouse_id = serializers.UUIDField(required=True, write_only=True)

    class Meta:
        model = WarehouseDifference
        fields = "__all__"
        extra_kwargs = {
            "status": {"read_only": True},
            "result": {"read_only": True},
            "processed_file": {"read_only": True},
            "error_reason": {"read_only": True},
            "warehouse_space": {"read_only": True},
            "our_space": {"read_only": True},
            "id": {"read_only": True},
        }

    def validate(self, attrs):
        warehouse_id = attrs["warehouse_id"]

        if self.Meta.model.objects.filter(warehouse_id=warehouse_id, status=self.Meta.model.RUNNING).exists():
            raise serializers.ValidationError({
                "non_field_errors": _(
                    "There's already another process to check discrepancies between warehouse and us. Please,"
                    " wait if it is running or choose another date. "
                )
            })

        return attrs

    def create(self, validated_data):
        """Create a task to perform the warehouse difference asynchronously once model is created"""
        instance = super().create(validated_data)
        instance.perform_difference()
        return instance
