from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from contracts.models import Item
from core.serializers import ModelSerializer


class IntranetInventorySerializer(ModelSerializer):
    contract_document_id = serializers.CharField(source="contract.contract_document_id")
    is_used_in_service = serializers.SerializerMethodField()

    class Meta:
        model = Item
        fields = (
            "id",
            "tag_id",
            "description",
            "delivery_date",
            "pick_up_date",
            "remote_dir",
            "contract_id",
            "contract_document_id",
            "is_used_in_service",
        )
        ref_name = "IntranetInventorySerializer"

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField)
    def get_is_used_in_service(self, obj):
        service_id = self.context["request"].query_params.get("service_id", None)
        return obj.service_id and service_id != str(obj.service_id)
