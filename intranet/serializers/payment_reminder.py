from django.conf import settings
from django.contrib.auth import get_user_model
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from core.serializers import ModelSerializer
from intranet.models import PaymentReminder

User = get_user_model()


class IntranetPaymentReminderSerializer(ModelSerializer):
    status = serializers.SerializerMethodField(read_only=True)
    deal_url = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PaymentReminder
        fields = [
            "id",
            "email",
            "first_name",
            "address",
            "date",
            "time_slot",
            "created_at",
            "sent_at",
            "status",
            "deal_id",
            "deal_url",
        ]

    @swagger_serializer_method(serializer_or_field=serializers.BooleanField)
    def get_status(self, obj):
        return obj.sent_at is not None

    @swagger_serializer_method(serializer_or_field=serializers.CharField)
    def get_deal_url(self, obj):

        return settings.HUBSPOT_URL_DOMAIN.format(model="deal", id=obj.deal_id) if obj.deal_id else None
