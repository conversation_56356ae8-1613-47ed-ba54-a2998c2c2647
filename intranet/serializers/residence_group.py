from django.db import IntegrityError
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.mixins import PartialUpdateMixin
from core.serializers import ModelSerializer
from core.utils import normalize_string
from intranet.models import ResidenceGroup


class IntranetResidenceGroupSerializer(ModelSerializer):
    class Meta:
        model = ResidenceGroup
        fields = (
            "id",
            "name",
            "created_at",
        )


class IntranetResidenceGroupCreateSerializer(ModelSerializer):
    class Meta:
        model = ResidenceGroup
        fields = (
            "id",
            "name",
            "created_at",
        )
        extra_kwargs = {
            "created_at": {"read_only": True},
        }

    def create(self, validated_data):
        name = validated_data.get("name")
        validated_data["canonical_name"] = normalize_string(name, replacer="_")
        validated_data["normalized_name"] = normalize_string(name)

        try:
            return super().create(validated_data)
        except IntegrityError as error:
            raise serializers.ValidationError({"name": _("The residence group already exists")}) from error


class IntranetResidenceGroupPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    class Meta:
        model = ResidenceGroup
        fields = (
            "id",
            "name",
            "created_at",
        )
        extra_kwargs = {
            "created_at": {"read_only": True},
        }

    def update(self, instance, validated_data):
        name = validated_data.get("name")
        validated_data["canonical_name"] = normalize_string(name, replacer="_")
        validated_data["normalized_name"] = normalize_string(name)

        try:
            return super().update(instance, validated_data)
        except IntegrityError as error:
            raise serializers.ValidationError({"name": _("The residence group already exists")}) from error
