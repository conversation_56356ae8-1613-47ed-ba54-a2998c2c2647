from rest_framework import serializers

from core.mixins import StringErrorsMixin


class HubspotResponseSerializer(StringErrorsMixin, serializers.Serializer):
    address = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    date_pick_up = serializers.DateField(read_only=True, allow_null=True)
    time_slot = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    hired_space = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    email = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    phone = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    firstname = serializers.Char<PERSON><PERSON>(read_only=True, allow_null=True)
    lastname = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    inventory = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
    billing_cycle = serializers.Char<PERSON>ield(read_only=True, allow_null=True)
