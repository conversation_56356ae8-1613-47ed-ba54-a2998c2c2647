from django.contrib.auth import get_user_model
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from core.fields import <PERSON><PERSON><PERSON><PERSON>
from core.serializers import ModelSerializer
from intranet.models import Calendar, City, Event, Task
from riders.models import Team

User = get_user_model()


class IntranetTaskUserSerializer(ModelSerializer):
    full_name = CharField(source="get_full_name", read_only=True, on_null="-")

    class Meta:
        model = User
        fields = ("id", "email", "full_name")


class IntranetTeamSerializer(ModelSerializer):
    rider = IntranetTaskUserSerializer(read_only=True)
    metadata = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Team
        fields = ("id", "rider", "sent_at", "metadata")

    @swagger_serializer_method(serializer_or_field=serializers.Serializer)
    def get_metadata(self, obj):
        return (obj.raw_data or {}).get("raw_data", {}).get("data", {})


class TaskEventSerializer(ModelSerializer):
    class Meta:
        model = Event
        fields = ("id", "start_time", "end_time")


class IntranetTaskCitySerializer(ModelSerializer):
    class Meta:
        model = City
        fields = ("id", "name", "created_at")


class IntranetTaskCalendarSerializer(ModelSerializer):
    city = IntranetTaskCitySerializer(read_only=True)

    class Meta:
        model = Calendar
        fields = ("id", "calendar_name", "city")


class IntranetTaskSerializer(ModelSerializer):
    created_by = IntranetTaskUserSerializer(allow_null=True, read_only=True)
    title = serializers.SerializerMethodField(read_only=True)
    calendar = serializers.SerializerMethodField(read_only=True)
    riders = serializers.SerializerMethodField(read_only=True)
    status = serializers.SerializerMethodField(read_only=True)
    event = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Task
        fields = [
            "id",
            "title",
            "created_at",
            "created_by",
            "calendar",
            "riders",
            "status",
            "event",
        ]

    @swagger_serializer_method(serializer_or_field=serializers.CharField)
    def get_title(self, obj):
        try:
            return obj.event_data[0].title
        except (IndexError, AttributeError):
            return None

    @swagger_serializer_method(serializer_or_field=serializers.CharField)
    def get_calendar(self, obj):
        try:
            return IntranetTaskCalendarSerializer(obj.event_data[0].calendar).data
        except (IndexError, AttributeError):
            return None

    @swagger_serializer_method(serializer_or_field=IntranetTaskUserSerializer)
    def get_riders(self, obj):
        return IntranetTeamSerializer(obj.rider_team, allow_null=True, many=True).data

    @swagger_serializer_method(serializer_or_field=serializers.CharField)
    def get_status(self, obj):
        rider_team_list = obj.rider_team or []
        task_finished_count = len([True for rider in rider_team_list if rider.sent_at is not None])
        rider_list_count = len(rider_team_list)

        if task_finished_count > 0 and task_finished_count == rider_list_count:
            return Task.COMPLETED

        if task_finished_count > 0 and task_finished_count != rider_list_count:
            return Task.PARTIAL

        return Task.PENDING

    @swagger_serializer_method(serializer_or_field=TaskEventSerializer)
    def get_event(self, obj):
        try:
            return TaskEventSerializer(obj.event_data[0]).data
        except (IndexError, AttributeError):
            return None
