from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django_countries.serializers import CountryFieldMixin
from rest_framework import serializers

from contracts.models import Contract, Service
from core.mixins import PartialUpdateMixin
from core.serializers import ModelSerializer
from intranet.models import BudgetEstimation
from intranet.serializers.history.base_history import BaseHistorySerializer


class BudgetEstimationSerializer(CountryFieldMixin, ModelSerializer):
    """Basic serializer to get budget estimations data"""

    class Meta:
        model = BudgetEstimation
        fields = "__all__"


class BudgetEstimationCreateSerializer(ModelSerializer):
    """Serializer to create budget estimations"""

    contract_document_id = serializers.CharField(
        required=False, allow_null=True, allow_blank=True, help_text=_("Fill in only if contract is null")
    )
    contract = serializers.PrimaryKeyRelatedField(
        required=False,
        allow_null=True,
        queryset=Contract.objects.all(),
        help_text=_("If contract_document_id is filled in, it will have priority over this attribute"),
    )

    class Meta:
        model = BudgetEstimation
        fields = "__all__"
        extra_kwargs = {
            "published": {"read_only": True},
            "created_at": {"read_only": True},
            "inventory_list": {"allow_null": False, "required": True},
        }

    def validate(self, attrs):
        contract_document_id = attrs.get("contract_document_id")
        contract = attrs.get("contract")

        if not contract_document_id and not contract:
            raise serializers.ValidationError(
                {"non_field_errors": _("A valid contract is needed in order to create a budget estimation")}
            )

        return attrs

    def create(self, validated_data):
        # Create contract if we have a contract_document_id instead
        contract_document_id = validated_data.pop("contract_document_id", None)
        contract = validated_data.pop("contract", None)
        site = validated_data.get("site", None)
        if contract_document_id:
            contract, _ = Contract.objects.get_or_create(
                contract_document_id=contract_document_id, defaults={"site": site}
            )

        validated_data["contract"] = contract
        validated_data["created_by"] = self.context["request"].user

        return super().create(validated_data)


class BudgetEstimationUpdateSerializer(ModelSerializer):
    """Serializer to update attributes for budget estimation"""

    service = serializers.UUIDField(required=False)

    class Meta:
        model = BudgetEstimation
        exclude = ["created_by"]
        extra_kwargs = {
            "published": {"read_only": True},
            "created_at": {"read_only": True},
            "contract": {"read_only": True},
            "inventory_list": {"allow_null": False, "required": True},
        }

    def update(self, instance, validated_data):
        """Override method to handle published in hubspot transparently"""
        validated_data["published"] = False
        validated_data["is_legacy"] = False

        service = validated_data.get("service")
        with transaction.atomic():
            service = Service.objects.select_for_update().filter(id=service).first()
            if service:
                inventory_list = "\n".join(validated_data.get("inventory_list", []) or [])
                service.inventory_list = inventory_list
                service.save(update_fields=["inventory_list"])

            return super().update(instance, validated_data)


class BudgetEstimationChangesSerializer(BaseHistorySerializer):
    class Meta(BaseHistorySerializer.Meta):
        model = BudgetEstimation
        fields = BaseHistorySerializer.Meta.fields + [
            "total",
            "inventory_list",
            "published",
            "created_at",
            "raw_data_with_preserved_order",
            "is_legacy",
            "site",
        ]


class BudgetEstimationPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    """Serializer to handle partial updates on history record"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields:
            if field != "title":
                self.fields[field].read_only = True

    class Meta:
        model = BudgetEstimation.history.model
        fields = "__all__"
