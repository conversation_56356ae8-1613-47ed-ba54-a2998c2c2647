from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from contracts.models import Contract, Warehouse
from core.fields import Cha<PERSON><PERSON>ield
from core.mixins import PartialUpdateVersionedMixin, StringErrorsMixin
from core.serializers import ModelSerializer
from intranet.models import City, WarehouseMovement
from payments.models import Subscription

User = get_user_model()


class WarehouseMovementUserSerializer(ModelSerializer):
    full_name = Char<PERSON><PERSON>(source="get_full_name", read_only=True, on_null="-")
    is_intranet_user = serializers.BooleanField(default=False)

    class Meta:
        model = User
        fields = ("id", "email", "full_name", "is_intranet_user")


class WarehouseMovementCitySerializer(ModelSerializer):
    class Meta:
        model = City
        fields = (
            "id",
            "name",
        )


class WarehouseMovementWarehouseSerializer(ModelSerializer):
    city = WarehouseMovementCitySerializer(read_only=True)

    class Meta:
        model = Warehouse
        fields = ("id", "name", "city", "tracking_inventory")
        ref_name = "IntranetWarehouseMovementWarehouseSerializer"


class WarehouseMovementContractSerializer(ModelSerializer):
    class Meta:
        model = Contract
        fields = ("id", "contract_document_id")
        ref_name = "IntranetWarehouseMovementContractSerializer"


class WarehouseMovementSerializer(ModelSerializer):
    """Basic serializer to get warehouse differences"""

    warehouse = WarehouseMovementWarehouseSerializer(read_only=True)
    contract = WarehouseMovementContractSerializer(read_only=True)
    user = serializers.SerializerMethodField()
    active_contract = serializers.SerializerMethodField()
    remote_dir = serializers.CharField(source="safe_remote_dir", read_only=True)

    class Meta:
        model = WarehouseMovement
        fields = (
            "id",
            "location",
            "pallet",
            "floor",
            "sent_at",
            "user",
            "warehouse",
            "remote_dir",
            "contract",
            "is_outdated",
            "active_contract",
            "version",
        )

    def get_user(self, obj):
        if obj.intranet_user:
            obj.intranet_user.is_intranet_user = True
            return WarehouseMovementUserSerializer(obj.intranet_user).data

        if obj.rider:
            return WarehouseMovementUserSerializer(obj.rider).data

        return None

    @swagger_serializer_method(serializers.BooleanField)
    def get_active_contract(self, obj):
        return obj.status == Subscription.ACTIVE


class WarehouseMovementBulkDestroySerializer(StringErrorsMixin, serializers.Serializer):
    """Bulk delete with list serializer"""

    cut_off = serializers.DateTimeField(required=True, allow_null=True)
    include_tracking = serializers.BooleanField(required=False, allow_null=False)


class WarehouseMovementCreateSerializer(ModelSerializer):
    warehouse = WarehouseMovementWarehouseSerializer(read_only=True)
    contract = WarehouseMovementContractSerializer(read_only=True)
    user = WarehouseMovementUserSerializer(read_only=True, source="intranet_user")
    contract_id = serializers.UUIDField(required=True, write_only=True)
    warehouse_id = serializers.UUIDField(required=True, write_only=True)
    has_photos = serializers.BooleanField(default=False, write_only=True)

    class Meta:
        model = WarehouseMovement
        fields = (
            "id",
            "location",
            "pallet",
            "floor",
            "sent_at",
            "internal_id",
            "user",
            "warehouse",
            "remote_dir",
            "contract",
            "contract_id",
            "warehouse_id",
            "has_photos",
            "is_outdated",
            "version",
        )
        extra_kwargs = {
            "sent_at": {"read_only": True},
            "remote_dir": {"read_only": True},
            "is_outdated": {"read_only": True},
            "internal_id": {"write_only": True},
            "version": {"read_only": True},
        }

    def validate(self, attrs):
        has_photos = attrs.pop("has_photos", False)
        contract_id = attrs.get(
            "contract_id",
        )
        warehouse_id = attrs.get(
            "warehouse_id",
        )

        contract = Contract.objects.filter(id=contract_id).first()
        if not contract:
            raise serializers.ValidationError({"contract_id": _("Invalid contract")})

        warehouse = Warehouse.objects.filter(id=warehouse_id).first()
        if not warehouse:
            raise serializers.ValidationError({"warehouse_id": _("Invalid warehouse")})

        attrs["contract_id"] = contract.id
        attrs["warehouse_id"] = warehouse.id

        if has_photos:
            temp_warehouse_movement = WarehouseMovement(**attrs)
            attrs["remote_dir"] = temp_warehouse_movement.safe_remote_dir

        attrs["sent_at"] = timezone.now()
        attrs["intranet_user"] = self.context["user"]
        return attrs

    def create(self, validated_data):
        instance = super().create(validated_data)
        instance.intranet_user.is_intranet_user = True

        return instance


class WarehouseMovementRelocationSerializer(WarehouseMovementSerializer):
    warehouse_id = serializers.UUIDField(write_only=True, required=True)
    version = serializers.IntegerField(required=True)

    class Meta(WarehouseMovementSerializer.Meta):
        model = WarehouseMovementSerializer.Meta.model
        fields = WarehouseMovementSerializer.Meta.fields + ("warehouse_id",)

        ref_name = "IntranetWarehouseMovementRelocationSerializer"

    def validate(self, attrs):
        warehouse_id = attrs.get("warehouse_id")

        if not Warehouse.objects.filter(id=warehouse_id).exists():
            raise serializers.ValidationError({"warehouse": _("Warehouse does not exist")})

        return attrs


class WarehouseMovementPartialUpdateSerializer(
    StringErrorsMixin, PartialUpdateVersionedMixin, serializers.ModelSerializer
):
    warehouse = WarehouseMovementWarehouseSerializer(read_only=True)
    contract = WarehouseMovementContractSerializer(read_only=True)
    user = serializers.SerializerMethodField()
    contract_id = serializers.UUIDField(required=True, write_only=True)
    warehouse_id = serializers.UUIDField(required=True, write_only=True)
    version = serializers.IntegerField(required=True)

    class Meta:
        model = WarehouseMovement
        fields = (
            "id",
            "location",
            "pallet",
            "floor",
            "sent_at",
            "internal_id",
            "user",
            "warehouse",
            "remote_dir",
            "contract",
            "contract_id",
            "warehouse_id",
            "is_outdated",
            "version",
        )
        extra_kwargs = {
            "sent_at": {"read_only": True},
            "is_outdated": {"read_only": True},
            "internal_id": {"write_only": True},
        }

    def get_user(self, obj):
        if obj.intranet_user:
            obj.intranet_user.is_intranet_user = True
            return WarehouseMovementUserSerializer(obj.intranet_user).data

        if obj.rider:
            return WarehouseMovementUserSerializer(obj.rider).data

        return None

    def validate(self, attrs):
        attrs["intranet_user"] = self.context["user"]
        return attrs

    def to_representation(self, instance):
        instance = super().to_representation(instance)
        instance["remote_dir"] = instance.get("safe_remote_dir")
        return instance
