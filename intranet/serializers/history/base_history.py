from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.serializers import ModelSerializer

User = get_user_model()


class HistoryUserSerializer(ModelSerializer):
    """Serializer to get translated the user id"""

    name = serializers.CharField(source="get_full_name", read_only=True)

    class Meta:
        model = User
        fields = ("id", "email", "name")


class BaseHistorySerializer(ModelSerializer):
    """Custom base serializer to handle common data"""

    id = serializers.UUIDField(source="pk", read_only=True)
    change_updated = serializers.DateTimeField(source="history_date", read_only=True)
    change_type = serializers.ChoiceField(
        source="history_type",
        read_only=True,
        choices=["+", "-", "~"],
        help_text=_("* +: create\n* -: delete\n* ~: update"),
    )
    change_made_by_user = HistoryUserSerializer(source="history_user", read_only=True)
    title = serializers.Char<PERSON>ield(read_only=True)

    class Meta:
        fields = ["id", "change_updated", "change_type", "change_made_by_user", "title"]
        abstract = True

        # Workaround to avoid read_only fields displaying in redoc for POST endpoints:
        # @see https://github.com/axnsan12/drf-yasg/issues/239
        ref_name = None
