from rest_framework import serializers

from contracts.models import Item
from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.serializers import ModelSerializer
from intranet.models.delivery import DeliveryRequest
from intranet.serializers.history.base_history import BaseHistorySerializer


class DeliverySerializer(ModelSerializer):
    """Serializer to get a list of deliveries made"""

    class Meta:
        model = DeliveryRequest
        fields = ("id", "type")


class DataSerializer(ModelSerializer):
    """Serializer to parse raw data on creation"""

    id = serializers.UUIDField(required=True, allow_null=False)

    class Meta:
        model = Item
        fields = ("id", "tag_id", "description", "delivery_date", "pick_up_date", "remote_dir")


class DeliveryCreateSerializer(ModelSerializer):
    """Serializer to create a delivery request"""

    raw_data = DataSerializer(many=True)
    type = serializers.ChoiceField(
        allow_null=False,
        allow_blank=False,
        choices=[DeliveryRequest.PARTIAL_DELIVERY, DeliveryRequest.FINAL_DELIVERY, DeliveryRequest.USER_DELIVERY],
    )

    class Meta:
        model = DeliveryRequest
        fields = ("id", "raw_data", "delivery_date", "type", "contract", "created_by")
        extra_kwargs = {
            "created_by": {"read_only": True},
            "contract": {"allow_null": False, "required": True},
            "delivery_date": {"allow_null": False, "required": True},
        }

    def create(self, validated_data):
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)


class DeliveryPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    """Serializer to update partials from model"""

    raw_data = DataSerializer(many=True)
    type = serializers.ChoiceField(
        allow_null=False,
        allow_blank=False,
        choices=[DeliveryRequest.PARTIAL_DELIVERY, DeliveryRequest.FINAL_DELIVERY, DeliveryRequest.USER_DELIVERY],
    )

    class Meta:
        model = DeliveryRequest
        fields = ("id", "raw_data", "delivery_date", "type", "email_sent_at")
        extra_kwargs = {"delivery_date": {"allow_null": False, "required": True}}


class DeliveryChangesSerializer(BaseHistorySerializer):
    class Meta(BaseHistorySerializer.Meta):
        model = DeliveryRequest
        fields = BaseHistorySerializer.Meta.fields + [
            "type",
            "raw_data",
            "delivery_date",
            "created_at",
            "email_sent_at",
        ]


class DeliveryEmailMessage(StringErrorsMixin, serializers.Serializer):
    """Serializer to pass additional message into email body"""

    message = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class DeliveryRetrieveSerializer(ModelSerializer):
    """Serializer to get a specific delivery request"""

    furniture_assembly = serializers.BooleanField(source="customer_delivery.furniture_assembly", allow_null=True)
    access = serializers.CharField(source="customer_delivery.access", allow_null=True)
    floor = serializers.CharField(source="customer_delivery.floor", allow_null=True)

    class Meta:
        model = DeliveryRequest
        fields = (
            "id",
            "raw_data",
            "type",
            "delivery_date",
            "email_sent_at",
            "contract",
            "furniture_assembly",
            "access",
            "floor",
        )


class DeliveryHistoryPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    """Serializer to handle partial updates on history record"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields:
            if field != "title":
                self.fields[field].read_only = True

    class Meta:
        model = DeliveryRequest.history.model
        fields = "__all__"
