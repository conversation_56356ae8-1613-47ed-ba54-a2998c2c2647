from datetime import datetime

from django.conf import settings
from django.core.cache import cache

from intranet.core.google_calendar_api.base import BaseCalendar
from intranet.core.google_calendar_api.mixins import (
    GoogleCalendarCreateMixin, GoogleCalendarDeleteMixin,
    GoogleCalendarListMixin, GoogleCalendarPatchMixin)


class GoogleCalendarCalendarWrapper(BaseCalendar, GoogleCalendarListMixin):
    service_call = "calendarList"

    def populate_request_params(self, page_token, sync_token, max_results: int = 50, **kwargs):
        self._request_params = {"pageToken": page_token, "syncToken": sync_token, "maxResults": max_results}


class BaseGoogleCalendarEventWrapper(BaseCalendar, GoogleCalendarListMixin):
    service_call = "events"

    def populate_request_params(
        self,
        calendar_id: str,
        max_results: int = 50,
        time_min: datetime = None,
        time_max: datetime = None,
        page_token: str = None,
        sync_token: str = None,
        **kwargs,
    ):
        request_params = {
            "calendarId": calendar_id,
            "maxResults": max_results,
            "syncToken": sync_token,
            "pageToken": page_token,
            "singleEvents": True,
            **kwargs,
        }

        if time_min:
            request_params["timeMin"] = time_min.isoformat() + "Z"

        if time_max:
            request_params["timeMax"] = time_max.isoformat() + "Z"

        # Update params
        self._request_params = request_params

    def sanitize_params(self, **request_params):
        """Override base method to handle syncToken and fullSyncs on token error"""
        sync_token = request_params.get("syncToken")

        request_params_copy = request_params.copy()
        if sync_token:
            request_params_copy.pop("timeMin", None)
            request_params_copy.pop("timeMax", None)

        return request_params_copy


class GoogleCalendarEventWrapper(
    BaseGoogleCalendarEventWrapper, GoogleCalendarCreateMixin, GoogleCalendarPatchMixin, GoogleCalendarDeleteMixin
):
    """Override base calendar wrapper to add creation and patching data into remote calendar"""

    def format_body(self, summary=None, description=None, start=None, end=None, attendees=None, **kwargs):
        body = {}

        if summary:
            body["summary"] = summary

        if description:
            body["description"] = description

        if start:
            body["start"] = start

        if end:
            body["end"] = end

        if attendees is not None:
            body["attendees"] = attendees

        body.update(**kwargs)
        return body


class GoogleCalendarEventIncrementalWrapper(BaseGoogleCalendarEventWrapper):
    """Perform an incremental sync from new data"""

    def populate_request_params(
        self,
        calendar_id: str,
        max_results: int = 50,
        time_min: datetime = None,
        time_max: datetime = None,
        page_token: str = None,
        **kwargs,
    ):
        # Get sync_token from cache. Set " " as default sync token to populate run_on_error attribute on run method
        sync_token = cache.get(f"{calendar_id}-{settings.GOOGLE_SYNC_TOKEN_PREFIX}") or " "

        super().populate_request_params(calendar_id, max_results, time_min, time_max, page_token, sync_token, **kwargs)

    def run(self, method="list", **request_params):
        """Override run method to handle transparently tokens"""

        response = super().run(method, **request_params)

        # Set cached key
        cache.set(
            f"{request_params.get('calendarId')}-{settings.GOOGLE_SYNC_TOKEN_PREFIX}", response.get("nextSyncToken")
        )

        return response


class GoogleCalendarSingleWrapper(
    BaseCalendar, GoogleCalendarCreateMixin, GoogleCalendarPatchMixin, GoogleCalendarDeleteMixin
):
    service_call = "calendars"


class GoogleCalendarListWrapper(BaseCalendar, GoogleCalendarPatchMixin):
    service_call = "calendarList"
