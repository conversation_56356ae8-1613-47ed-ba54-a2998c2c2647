class GoogleCalendarListMixin:
    def list(self, quota_user=None, num_retries=0) -> list:
        """Retrieve a list of items from google calendar"""
        response_list = []

        while True:
            response = self.run(method="list", **self._request_params, num_retries=num_retries)

            self._request_params.update(**{"pageToken": response.get("pageToken", None), "quotaUser": quota_user})
            response_list.extend(response.get("items", []))
            # response_list.extend(self.parser_response_list(response.get("items", []), self._request_params))

            if not self._request_params["pageToken"]:
                break

        return response_list


class GoogleCalendarCreateMixin:
    def create(self, body: dict, quota_user=None, num_retries=0, **kwargs):
        """Create a google calendar item"""

        return self.run(method="insert", body=body, quotaUser=quota_user, num_retries=num_retries, **kwargs)


class GoogleCalendarPatchMixin:
    def patch(self, calendar_id: str, body: dict, quota_user=None, num_retries=0, **kwargs):
        """Update a google calendar item"""

        return self.run(
            method="patch", calendarId=calendar_id, body=body, quotaUser=quota_user, num_retries=num_retries, **kwargs
        )


class GoogleCalendarDeleteMixin:
    def delete(self, calendar_id: str, quota_user=None, num_retries=0, **kwargs):
        """Delete a google calendar item"""

        return self.run(
            method="delete", calendarId=calendar_id, quotaUser=quota_user, num_retries=num_retries, **kwargs
        )
