from django.conf import settings
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


class CalendarRateLimitError(Exception):
    pass


class BaseCalendar:
    SCOPES = ["https://www.googleapis.com/auth/calendar"]
    service_call = None
    run_on_error = False

    def __init__(self, impersonated_email):
        self.credentials = service_account.Credentials.from_service_account_file(
            settings.GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR, scopes=self.SCOPES
        )
        self.credentials = self.credentials.with_subject(impersonated_email)
        self.service = build("calendar", "v3", credentials=self.credentials)
        self._request_params = {}

    def populate_request_params(self, *args, **kwargs) -> dict:
        raise NotImplementedError("Not implemented yet!")

    def sanitize_params(self, **request_params):
        """Helper method to handle request params preprocessing on list call against calendar api"""
        return request_params

    def run(self, num_retries, method="list", **request_params):
        """This method is intended to be used with one GoogleCalendarMixin"""

        try:
            response = getattr(getattr(self.service, self.service_call)(), method)(
                **self.sanitize_params(**request_params)
            ).execute(num_retries=num_retries)
        except HttpError as error:
            if error.status_code in [403, 429]:
                raise CalendarRateLimitError() from error

            if error.status_code != 410:
                raise error
            else:
                # if reason is deleted, do nothing
                # @see https://developers.google.com/calendar/api/guides/errors?hl=es-419#410_gone
                try:
                    reason = error.error_details[0].get("reason")
                    if reason == "deleted":
                        return
                except (IndexError, AttributeError):
                    pass

            # Set internal flag for debugging purposes
            self.run_on_error = True

            # A 410 status code, "Gone", indicates that the sync token is invalid, so drop it from requesst
            request_params.pop("syncToken", None)

            # Include num_retries
            request_params["num_retries"] = num_retries
            return self.run(**request_params)

        return response


class BatchedCalendar(BaseCalendar):
    def __init__(self, impersonated_email):
        """Generic wrapper to handle batch requests against google calendar"""
        self._batch = None

        super().__init__(impersonated_email)

    def __enter__(self):
        self._batch = self.service.new_batch_http_request()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self._batch.execute()

    def add_operation(self, resource, operation, callback, **kwargs):
        """Add operation to given batch"""

        resource = getattr(self.service, resource)()
        operation = getattr(resource, operation)

        self._batch.add(operation(**kwargs), callback=callback)
