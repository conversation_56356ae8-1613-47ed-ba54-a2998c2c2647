from collections import OrderedDict

from rest_framework.response import Response

from core.paginations import (BaseCursorPagination, BasePagination,
                              BulkPaginationMixin)


class UserPagination(BasePagination):
    pass


class ContractPagination(BasePagination):
    pass


class WarehouseDifferencePagination(BulkPaginationMixin, BasePagination):
    page_size = 24
    date_field = "created_at"


class ServicePagination(BasePagination):
    page_size = 24


class CalendarPagination(BasePagination):
    pass


class TaskPagination(BasePagination):
    page_size = 24


class PaymentReminderPagination(BasePagination):
    pass


class WarehouseMovementPagination(BulkPaginationMixin, BasePagination):
    page_size = 24
    date_field = "sent_at"


class WarehouseMovementTrackingPagination(BaseCursorPagination):
    page_size = 24

    def get_paginated_response(self, data):
        ordered_dict = OrderedDict()

        if isinstance(self, BasePagination):
            ordered_dict.update(count=self.page.paginator.count, total_pages=self.page.paginator.num_pages)

        ordered_dict.update(
            next=self.get_next_link(),
            previous=self.get_previous_link(),
            results=data,
        )

        return Response(ordered_dict)


class ReviewPagination(BulkPaginationMixin, BasePagination):
    page_size = 24
    date_field = "created_at"


class WarehousePagination(BulkPaginationMixin, BasePagination):
    page_size = 24
    date_field = "created_at"


class CityPagination(BasePagination):
    page_size = 24


class WarehouseUserPagination(BulkPaginationMixin, BasePagination):
    page_size = 24
    date_field = "date_joined"


class IntranetRiderPagination(BasePagination):
    page_size = 24


class ResidenceGroupPagination(BasePagination):
    page_size = 24


class ResidencePagination(BasePagination):
    page_size = 24


class CalendarConfigPagination(BasePagination):
    page_size = 24
