import importlib
import itertools

from django.apps import apps
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Prefetch
from django.db.models.functions import Cast


class BusinessSocialAccountManager(models.Manager):
    """Manager to simplify calls to this method in order to get owner organization social account tokens"""

    def owner(self):
        return self.filter(business__organization__membership__role__name="owner")

    def owner_facebook_social_account(self):
        return self.filter(
            social_account__provider="facebook", business__organization__membership__role__name="owner"
        ).first()

    def owner_google_social_account(self):
        return self.filter(
            social_account__provider="google", business__organization__membership__role__name="owner"
        ).first()


class BusinessManager(models.Manager):
    """Manager to get related campaigns linked to business"""

    def campaigns_with_related_objects_as_list(self, business_id):
        """Retrieve every campaign linked to this business as list instead of queryset"""
        from campaigns.models import Campaign

        campaigns = []
        for model_class in apps.get_app_config("campaigns").get_models():
            # We are interested in campaign subclasses
            if issubclass(model_class, Campaign):
                query_filter = {f"{model_class.__social_network__}_business_account__business__id": business_id}
                adset_model_class = getattr(
                    importlib.import_module("campaigns.models"), f"{model_class.__social_network__.capitalize()}Adset"
                )
                ad_model_class = getattr(
                    importlib.import_module("campaigns.models"), f"{model_class.__social_network__.capitalize()}Ad"
                )
                queryset = (
                    model_class.objects.filter(**query_filter)
                    .prefetch_related(
                        Prefetch(
                            f"{model_class.__social_network__}adset_set",
                            queryset=adset_model_class.objects.all()
                            .only("id")
                            .distinct()
                            .prefetch_related(
                                Prefetch(
                                    f"{model_class.__social_network__}adset__{model_class.__social_network__}ad_set",
                                    queryset=ad_model_class.objects.all().only("id").distinct(),
                                )
                            ),
                        )
                    )
                    .distinct()
                    .annotate(
                        campaign_id=Cast("id", output_field=CharField()),
                        adset_id=Cast(f"{model_class.__social_network__}adset__id", output_field=CharField()),
                        ad_id=Cast(
                            f"{model_class.__social_network__}adset__{model_class.__social_network__}ad__id",
                            output_field=CharField(),
                        ),
                    )
                    .values_list(
                        "campaign_id",
                        "adset_id",
                        "ad_id",
                    )
                )

                campaigns.extend(list(set(itertools.chain(*queryset))))

        return campaigns
