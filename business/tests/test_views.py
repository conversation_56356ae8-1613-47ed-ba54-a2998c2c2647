from datetime import timed<PERSON><PERSON>
from unittest.mock import PropertyMock, patch

import pytest
from allauth.socialaccount.models import SocialAccount, SocialApp, SocialToken
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from facebook_business.exceptions import FacebookRequestError
from pytest import mark
from rest_framework import status
from rest_framework.test import APIClient

from business.models import (Business, BusinessSocialAccount,
                             FacebookBusinessAccount, Report)
from campaigns.models import FacebookCampaign
from organizations.models import Group, Membership, Organization

pytestmark = mark.django_db
User = get_user_model()


@pytest.fixture
def client():
    client = APIClient()
    yield client


@pytest.fixture(autouse=True)
def user_one():
    yield User.objects.create_user(
        first_name="calimero", last_name="molon", email="<EMAIL>", password="passy"
    )


@pytest.fixture(autouse=True)
def user_two():
    yield User.objects.create_user(
        first_name="calimero", last_name="nomolon", email="<EMAIL>", password="passy"
    )


@pytest.fixture(autouse=True)
def user_superuser():
    yield User.objects.create_superuser(first_name="super", last_name="user", email="<EMAIL>", password="passy")


@pytest.fixture(autouse=True)
def social_app_facebook():
    yield SocialApp.objects.create(provider="facebook", name="test app", secret="123")


@pytest.fixture(autouse=True)
def facebook_social_account(user_one):
    yield SocialAccount.objects.create(user=user_one, provider="facebook", uid=*********987)


@pytest.fixture(autouse=True)
def social_token_facebook(social_app_facebook, facebook_social_account):
    yield SocialToken.objects.create(app=social_app_facebook, account=facebook_social_account, token="123")


@pytest.fixture(autouse=True)
def organization():
    yield Organization.objects.create(
        name="Test Organization",
        type_organization="business",
    )


@pytest.fixture(autouse=True)
def membership(organization, user_one):
    yield Membership.objects.create(organization=organization, user=user_one, role=Group.objects.get(name="owner"))


@pytest.fixture(autouse=True)
def business(organization):
    yield Business.objects.create(
        name="Test business",
        organization=organization,
    )


@pytest.fixture(autouse=True)
def business_without_social_account(organization):
    yield Business.objects.create(
        name="Test business Two",
        organization=organization,
    )


@pytest.fixture(autouse=True)
def business_without_business_account(organization):
    yield Business.objects.create(
        name="Test business w/o business account",
        organization=organization,
    )


@pytest.fixture(autouse=True)
def facebook_business_account(business):
    yield FacebookBusinessAccount.objects.create(page_id=*********, ad_account_id="act_*********", business=business)


@pytest.fixture(autouse=True)
def facebook_business_account_for_business_without_social_account(business_without_social_account):
    yield FacebookBusinessAccount.objects.create(
        page_id=*********, ad_account_id="act_*********", business=business_without_social_account
    )


@pytest.fixture(autouse=True)
def business_social_account_facebook(business, user_one, facebook_social_account):
    yield BusinessSocialAccount.objects.create(
        business=business, user=user_one, social_account=facebook_social_account, uid=facebook_social_account.uid
    )


@pytest.fixture
def facebook_request_error():
    yield FacebookRequestError(
        message="error",
        request_context={},
        http_status="200",
        http_headers={},
        body={"error": {"error_data": {"blame_field_specs": ""}, "error_user_msg": "error message"}},
    )


@pytest.fixture
def campaign(facebook_business_account):
    yield FacebookCampaign.objects.create(
        name="FacebookCampaign Test",
        objective=FacebookCampaign.CONVERSIONS,
        facebook_business_account=facebook_business_account,
        conversion_event_type=FacebookCampaign.PURCHASE,
        pixel_id="********",
        budget=10000,
        ad_account_id="act_*********",
    )


@pytest.fixture
def create_report(campaign, business):
    yield Report.objects.create(
        name="Report Test", campaign=campaign, business=business, date_added="2021-03-15T09:31:26.755849+01:00"
    )


class TestBusinessViewSet:
    def test_get_businesses(self, client):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.get("/business/")
        response_json = response.json()

        assert response.status_code == 200
        assert response_json["count"] == 3

    def test_get_businesses_no_membership(self, client):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.get("/business/")
        response_json = response.json()

        assert response.status_code == 200
        assert response_json["count"] == 0

    def test_link_business_account_ok(self, client, business_without_business_account):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.post(
            f"/business/{business_without_business_account.pk}/link_business_account/",
            data={"facebook_business_account": {"page_id": "**********"}},
        )

        response_json = response.json()
        assert response.status_code == 200
        assert response_json == {"status": _("Business account linked successfully")}

    def test_link_business_account_ko(self, client, business):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.post(
            f"/business/{business.pk}/link_business_account/",
            data={"facebook_business_account": {"page_id": "**********"}},
        )

        response_json = response.json()
        assert response.status_code == 400
        assert response_json == {"non_field_errors": _("Business already have a linked facebook business account")}

    def test_link_socialaccount_to_business_ok(self, client, business_without_social_account, facebook_social_account):
        with patch("users.auth.models.SocialAccount.facebook_pages", new_callable=PropertyMock) as mock_pages:
            mock_pages.return_value = {
                business_without_social_account.facebook_business_account.page_id: {
                    "category": "category",
                    "id": "id",
                    "name": "name",
                    "picture": "url",
                    "can_advertise": True,
                }
            }
            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "passy"},
            )
            access_token = response.json()["access_token"]
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

            response = client.post(
                f"/business/{business_without_social_account.pk}/link_social_account/",
                data={"social_account": str(facebook_social_account.pk)},
            )

            response_json = response.json()
            assert response.status_code == 200
            assert response_json == {"status": _("Social account linked successfully")}

    def test_link_socialaccount_to_business_already_linked_ko(self, client, business, facebook_social_account):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.post(
            f"/business/{business.pk}/link_social_account/", data={"social_account": str(facebook_social_account.pk)}
        )

        response_json = response.json()
        assert response.status_code == 400
        assert response_json == {"non_field_errors": _("Social account already linked")}

    def test_local_campaigns_ok(self, client, business):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.get(f"/business/{business.pk}/campaigns/")

        response_json = response.json()
        assert response.status_code == 200
        assert response_json == {"facebook": []}

    def test_facebook_remote_campaigns_ok(self, client, business, user_one):
        with patch("business.models.business.Business.facebook_remote_campaigns") as mock_remote_campaigns:
            mock_remote_campaigns.return_value = [
                {
                    "remote_id": "*********",
                    "name": "Fake campaign",
                    "objective": "LINK_CLICKS",
                    "imported": True,
                    "currency": "EUR",
                    "facebook_business_account": business.facebook_business_account.pk,
                }
            ]
            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "passy"},
            )
            access_token = response.json()["access_token"]
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

            response = client.get(f"/business/{business.pk}/facebook-remote-campaigns/")

            response_json = response.json()
            assert response.status_code == 200
            assert response_json == [
                {
                    "remote_id": "*********",
                    "name": "Fake campaign",
                    "objective": "LINK_CLICKS",
                    "imported": True,
                    "creation_status": "in_process",
                    "currency": "EUR",
                    "pixel_id": None,
                    "conversion_event_type": None,
                    "facebook_business_account": str(business.facebook_business_account.pk),
                }
            ]
            mock_remote_campaigns.assert_called_once_with(limit=5, user=user_one)

    def test_facebook_remote_campaigns_bad_query_parameter(self, client, business, user_one):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.get(f"/business/{business.pk}/facebook-remote-campaigns/?limit=-1")

        response_json = response.json()
        assert response.status_code == 400
        assert response_json == {"limit": _("Ensure this value is greater than or equal to 0.")}

    def test_facebook_remote_campaigns_facebook_request_error_ko(
        self, client, business, user_one, facebook_request_error
    ):
        with patch("business.models.business.Business.facebook_remote_campaigns") as mock_remote_campaigns:
            mock_remote_campaigns.side_effect = facebook_request_error
            response = client.post(
                "/auth/login/",
                data={"email": "<EMAIL>", "password": "passy"},
            )
            access_token = response.json()["access_token"]
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

            response = client.get(f"/business/{business.pk}/facebook-remote-campaigns/")

            response_json = response.json()
            assert response.status_code == 400
            assert response_json == {"details": _("Error importing campaign from Facebook. Please, try again later")}
            mock_remote_campaigns.assert_called_once_with(limit=5, user=user_one)

    def test_facebook_remote_campaigns_validation_error_ko(self, client, business, user_one, facebook_request_error):
        with patch("business.models.business.Business.facebook_remote_campaigns") as mock_remote_campaigns:
            with patch("business.views.logger") as mock_logger:
                mock_remote_campaigns.side_effect = ValidationError("Error")
                response = client.post(
                    "/auth/login/",
                    data={"email": "<EMAIL>", "password": "passy"},
                )
                access_token = response.json()["access_token"]
                client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

                response = client.get(f"/business/{business.pk}/facebook-remote-campaigns/")

                response_json = response.json()
                assert response.status_code == 400
                assert response_json == {"non_field_errors": _("Error")}
                mock_remote_campaigns.assert_called_once_with(limit=5, user=user_one)
                mock_logger.error.assert_called_once_with("Error importing facebook campaigns: Error")

    def test_swap_business_account_ok(self, client, business):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.post(
            f"/business/{business.pk}/change-business-account/", data={"facebook_business_account": {"page_id": 1010}}
        )
        response_json = response.json()
        assert response.status_code == 200
        assert response_json == "OK"

    def test_swap_business_account_ko(self, client, business):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        response = client.post(
            f"/business/{business.pk}/change-business-account/", data={"business_account_no_exists": {"page_id": 1010}}
        )
        response_json = response.json()
        assert response.status_code == 400
        assert response_json == {"non_field_errors": _("Empty business account to change")}


class TestReportsViewSet:
    def test_create_report(self, client, user_one, business, campaign):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(
            path="/reports/", data={"name": "Report Test", "campaign": campaign.id, "business": business.id}
        )
        fake_response = {
            "business": str(business.id),
            "campaign": campaign.name,
            "id": response.json()["id"],
            "name": "Report Test",
        }
        assert response.status_code == status.HTTP_201_CREATED
        assert response.json() == fake_response

    def test_create_report_ko(self, client, user_one, business, campaign):
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path="/reports/", data={"name": "Report Test", "business": business.id})
        fake_response = {"campaign": "This field is required."}
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == fake_response

    def test_update_metrics_report(self, client, user_one, create_report, business, campaign):
        report = create_report
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.patch(
            path=f"/reports/{report.id}/",
            data={
                "metrics": [
                    "post_engagement",
                    "impressions",
                    "reach",
                    "total_shared_posts",
                    "results",
                    "cost",
                    "spend",
                    "total_clicks",
                ]
            },
        )
        fake_response = {
            "id": str(report.id),
            "name": "Report Test",
            "business": str(business.id),
            "campaign": {"id": str(campaign.id), "name": campaign.name},
            "metric_ordering": None,
            "metrics": [
                "post_engagement",
                "impressions",
                "reach",
                "total_shared_posts",
                "results",
                "cost",
                "spend",
                "total_clicks",
            ],
            "objective": "CONVERSIONS",
        }
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == fake_response

    def test_delete_report(self, client, user_one, create_report):
        report = create_report
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.delete(path=f"/reports/{report.id}/")
        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_retrieve_report(self, client, user_one, create_report, campaign, business):
        report = create_report
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]
        since = timezone.now() - timedelta(seconds=90)
        until = timezone.now() + timedelta(seconds=90)
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.get(path=f"/reports/{report.id}/?since={since}&until={until}")
        fake_reponse = {
            "id": str(report.id),
            "name": "Report Test",
            "objective": "CONVERSIONS",
            "date_added": str(report.date_added),
            "campaign": {"id": str(campaign.id), "name": campaign.name},
            "metrics": [
                "total_purchase_conversion_value",
                "impressions",
                "reach",
                "total_post_save",
                "results",
                "cost",
                "spend",
                "total_clicks",
            ],
            "metric_ordering": None,
            "metric_data": None,
            "currency": None,
        }
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == fake_reponse

    def test_download_report(self, client, user_one, create_report, campaign, business):
        report = create_report
        response = client.post(
            "/auth/login/",
            data={"email": "<EMAIL>", "password": "passy"},
        )
        access_token = response.json()["access_token"]

        post_data = {"charts": [{"title": "Gasto", "image": ""}]}
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path=f"/reports/{report.id}/download/", data=post_data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "image" in response.json()["charts"][0]

        post_data = {"charts": [{"title": "Gasto", "image": "Nah"}]}
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path=f"/reports/{report.id}/download/", data=post_data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "image" in response.json()["charts"][0]

        post_data = {"charts": [{"title": "Gasto", "image": 123}]}
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path=f"/reports/{report.id}/download/", data=post_data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "image" in response.json()["charts"][0]

        post_data = {
            "charts": [
                {
                    "title": "Gasto",
                    "image": "data:image/bmp;base64,Qk2KoAAAAAAAAIoAAAB8AAAAAAQAAAoAAAABACAAAwAAAACgAABgDwAAYA8AAAAAAAAAAAAAAAD/AAD/AAD/AAAAAAAA/0JHUnOAwvUoYLgeFSCF6wFAMzMTgGZmJkBmZgagmZkJPArXAyRcjzIAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAA/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////w==",
                }
            ]
        }
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = client.post(path=f"/reports/{report.id}/download/", data=post_data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "image" in response.json()["charts"][0]
