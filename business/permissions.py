from rest_framework import permissions

from organizations.models import Membership


class BusinessPermissions(permissions.BasePermission):
    def get_organization(self, data):
        if isinstance(data, list) and len(data) > 0:
            return data[0].get("organization")

        return data.get("organization")

    def has_permission(self, request, view):
        if request.user.is_superuser:
            return True

        if request.method == "POST":
            organization = self.get_organization(request.data)
            if not (
                organization and Membership.objects.filter(organization_id=organization, user_id=request.user).exists()
            ):
                return False

        return True
