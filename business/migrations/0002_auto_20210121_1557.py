# Generated by Django 3.1.5 on 2021-01-21 15:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("organizations", "0004_auto_20210121_1015"),
        ("socialaccount", "0003_extra_data_default_dict"),
        ("business", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="business",
            name="organization",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="organizations.organization"
            ),
        ),
        migrations.CreateModel(
            name="BusinessSocialAccount",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "business",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="business.business"),
                ),
                (
                    "social_account",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.SET_NULL, to="socialaccount.socialaccount"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "uid",
                    models.CharField(max_length=255, null=True),
                ),
            ],
            options={
                "unique_together": {("business", "user", "social_account")},
            },
        ),
    ]
