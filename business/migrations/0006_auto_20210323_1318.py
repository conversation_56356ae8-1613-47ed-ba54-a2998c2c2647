# Generated by Django 3.1.7 on 2021-03-23 12:18

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0005_auto_20210309_1845"),
    ]

    operations = [
        migrations.CreateModel(
            name="FacebookAdAccount",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("account_id", models.CharField(max_length=200, null=True)),
                ("currency", models.CharField(max_length=3, null=True)),
                ("created_time", models.DateTimeField(auto_now_add=True, null=True)),
                ("is_personal", models.PositiveIntegerField(null=True)),
            ],
        ),
        migrations.RemoveField(
            model_name="facebookbusinessaccount",
            name="ad_account_id",
        ),
        migrations.AddField(
            model_name="facebookbusinessaccount",
            name="ad_account",
            field=models.OneToOneField(
                null=True, on_delete=django.db.models.deletion.CASCADE, to="business.facebookadaccount"
            ),
        ),
    ]
