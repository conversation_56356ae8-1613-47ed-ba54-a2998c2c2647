# Generated by Django 3.1.5 on 2021-01-21 14:28

import uuid

import django.core.serializers.json
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Business",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("date_added", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "cms",
                    models.JSONField(
                        blank=True,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="This is a JSONField, not a String",
                        null=True,
                    ),
                ),
                (
                    "sales_to",
                    models.CharField(
                        blank=True,
                        choices=[("b2b", "B2B"), ("b2c", "B2C"), ("b2b_b2c", "B2B-B2C")],
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "business_type",
                    models.CharField(
                        blank=True,
                        choices=[("online", "Online"), ("offline", "Offline"), ("mixed", "Mixed")],
                        max_length=200,
                        null=True,
                    ),
                ),
                ("website", models.URLField(blank=True, null=True)),
                (
                    "website_activity",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("ecommerce", "Ecommerce"),
                            ("lead_generation", "Lead generation"),
                            ("signup", "Signup"),
                            ("informative", "Informative"),
                        ],
                        max_length=200,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "businesses",
                "ordering": ("-date_added",),
            },
        ),
        migrations.CreateModel(
            name="FacebookBusinessAccount",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("date_added", models.DateTimeField(auto_now_add=True)),
                ("page_id", models.CharField(max_length=200)),
                ("ad_account_id", models.CharField(max_length=200)),
                (
                    "business",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="business.business"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
