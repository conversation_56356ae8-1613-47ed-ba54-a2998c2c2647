# Create your models here.
import logging
import time
import uuid

from allauth.socialaccount.models import SocialAccount
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models, transaction
from django.db.models import F, JSONField, Q
from django.db.models.signals import pre_delete
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from business.managers import BusinessManager, BusinessSocialAccountManager
from business.receivers import delete_comments_and_notifications
from core.external_services.facebook.client import FacebookClient
from core.external_services.facebook.importer import FacebookCampaignImporter
from core.external_services.google.client import (GoogleAdsClient,
                                                  GoogleQueryWrapper)
from core.external_services.google.helpers import safe_attribute_getter
from core.external_services.google.importer import GoogleCampaignImporter
from core.external_services.microservices.base import ClientError
from core.external_services.microservices.hiperion import \
    HiperionAPIServiceClient
from core.utils import cached_model_property
from organizations.models import Organization

User = get_user_model()

logger = logging.getLogger(__name__)


class Business(models.Model):
    B2B = "b2b"
    B2C = "b2c"
    B2B2C = "b2b_b2c"

    SALES_TO_CHOICES = (
        (B2B, _("B2B")),
        (B2C, _("B2C")),
        (B2B2C, _("B2B-B2C")),
    )

    ONLINE = "online"
    OFFLINE = "offline"
    MIXED = "mixed"

    BUSINESS_TYPE_CHOICES = (
        (ONLINE, _("Online")),
        (OFFLINE, _("Offline")),
        (MIXED, _("Mixed")),
    )

    ECOMMERCE = "ecommerce"
    LEAD_GENERATION = "lead_generation"
    SIGNUP = "signup"
    INFORMATIVE = "informative"

    WEBSITE_ACTIVITY_CHOICES = (
        (ECOMMERCE, _("Ecommerce")),
        (LEAD_GENERATION, _("Lead generation")),
        (SIGNUP, _("Signup")),
        (INFORMATIVE, _("Informative")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, null=True, blank=True)
    date_added = models.DateTimeField(default=timezone.now)
    organization = models.ForeignKey(Organization, null=True, blank=True, on_delete=models.CASCADE)

    # Business configuration
    cms = JSONField(blank=True, null=True, encoder=DjangoJSONEncoder, help_text=_("This is a JSONField, not a String"))
    sales_to = models.CharField(max_length=200, choices=SALES_TO_CHOICES, blank=True, null=True)
    business_type = models.CharField(max_length=200, choices=BUSINESS_TYPE_CHOICES, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    website_activity = models.CharField(max_length=200, choices=WEBSITE_ACTIVITY_CHOICES, blank=True, null=True)

    objects = BusinessManager()

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "businesses"
        ordering = ("-date_added",)
        indexes = [models.Index(fields=("name",), name="filtering_business_index")]

    @property
    def facebook_business_account(self):
        return self.facebookbusinessaccount_set.first()

    @property
    def google_business_account(self):
        return self.googlebusinessaccount_set.first()

    def get_tokens(self, social_network=None, only_owner_tokens=False):
        """Get linked tokens to this business"""
        annotations = {
            "google": F("social_account__socialtoken__token_secret"),
            "social_account__provider": F("social_account__provider"),
            "facebook": F("social_account__socialtoken__token"),
        }

        filter_query = {"social_account__socialtoken__expires_at__isnull": True}
        if social_network == "google":
            annotations.pop("facebook")
            filter_query.update(**{"social_account__provider": "google"})
        elif social_network == "facebook":
            annotations.pop("google")
            filter_query.update(**{"social_account__provider": "facebook"})

        if only_owner_tokens:
            return (
                self.businesssocialaccount_set.filter(
                    business__organization__membership__user=F("user"),
                    business__organization__membership__role__name="owner",
                    **filter_query,
                )
                .annotate(**annotations)
                .values(*annotations.keys())
            )

        return self.businesssocialaccount_set.filter(**filter_query).annotate(**annotations).values(*annotations.keys())

    @cached_model_property(cache_time_in_seconds=900, is_property=False)  # Cache for 15 min
    def facebook_remote_campaigns(self, user, limit=None):
        """Fetch from facebook every campaign which belongs to this business"""
        from campaigns.models import FacebookCampaign

        # Get an access token from business silo
        access_token = (
            self.get_tokens(social_network="facebook")
            .filter(Q(user=user) & Q(social_account__provider="facebook") | Q(social_account__provider="facebook"))
            .first()
        )
        if not access_token:
            raise ValidationError(_("Business has not any valid token linked"))

        access_token = access_token["facebook"]
        facebook_client = FacebookClient(sandbox_or_production=settings.ENVIRONMENT == "PROD")
        ad_account_metadata = facebook_client.get_ad_account_metadata(
            access_token=access_token,
            ad_account_id=self.facebook_business_account.ad_account.ad_account_id,
            reraise=True,
        )

        # Get current facebook campaigns linked to this business to avoid import an already imported campaign
        imported_campaigns = (
            FacebookCampaign.objects.filter(facebook_business_account__business=self)
            .exclude(remote_id__isnull=True)
            .values_list("remote_id", flat=True)
        )

        campaign_filters = [
            {"field": "campaign.effective_status", "operator": "IN", "value": ["ACTIVE", "PAUSED"]},
            {
                "field": "campaign.objective",
                "operator": "IN",
                "value": [
                    FacebookCampaign.POST_ENGAGEMENT,
                    FacebookCampaign.LEAD_GENERATION,
                    FacebookCampaign.LINK_CLICKS,
                    FacebookCampaign.CONVERSIONS,
                    FacebookCampaign.REACH,
                    FacebookCampaign.VIDEO_VIEWS,
                    FacebookCampaign.BRAND_AWARENESS,
                    FacebookCampaign.PAGE_LIKES,
                ],
            },
            {"field": "campaign.id", "operator": "NOT_IN", "value": list(imported_campaigns)},
            {
                "field": "campaign.created_time",
                "operator": "GREATER_THAN",
                "value": time.mktime(
                    (timezone.now() - relativedelta(months=37)).replace(tzinfo=timezone.utc).timetuple()
                ),
            },
        ]

        # Get campaigns from ad account
        campaigns = facebook_client.get_campaigns_from_ad_account(
            access_token=access_token,
            ad_account_id=ad_account_metadata.get("id"),
            campaign_filters=campaign_filters,
            reraise=True,
        )

        return [
            data
            for data in FacebookCampaignImporter(
                campaigns,
                max_number_of_results=limit,
                metadata={
                    "page_id": self.facebook_business_account.page_id,
                    "facebook_business_account": self.facebook_business_account.id,
                    "currency": ad_account_metadata.get("currency"),
                },
            )
        ]

    @cached_model_property(cache_time_in_seconds=900, is_property=False)  # Cache for 15 min
    def google_remote_campaigns(self, user, limit=None):
        """Fetch from facebook every campaign which belongs to this business"""
        from campaigns.models import GoogleCampaign

        # Get an access token from business silo
        refresh_token = (
            self.get_tokens(social_network="google")
            .filter(Q(user=user) & Q(social_account__provider="google") | Q(social_account__provider="google"))
            .first()
        )
        if not refresh_token:
            raise ValidationError(_("Business has not any valid token linked"))

        refresh_token = refresh_token["google"]
        google_client = GoogleAdsClient(production=settings.ENVIRONMENT == "PROD")

        # Get account metadata for currency among other fields
        account_metadata = google_client.get_account_metadata_from_resource(
            refresh_token=refresh_token,
            resource_id=self.google_business_account.customer_id,
            reraise=True,
        )

        # Get current facebook campaigns linked to this business to avoid import an already imported campaign
        imported_campaigns = (
            GoogleCampaign.objects.filter(google_business_account__business=self)
            .exclude(remote_id__isnull=True)
            .values_list("remote_id", flat=True)
        )

        where_clause = ["campaign.status IN ('ENABLED', 'PAUSED')", "campaign.advertising_channel_type = 'SEARCH'"]

        if imported_campaigns:
            where_clause.append(
                f"campaign.id NOT IN {GoogleQueryWrapper.sanitize_where_clause(tuple(imported_campaigns))}"
            )

        # Wrap google query and build at once
        google_query = GoogleQueryWrapper(
            select_clause=[
                "campaign.id",
                "campaign.resource_name",
                "campaign.name",
                "campaign.status",
                "campaign.advertising_channel_type",
                "campaign.advertising_channel_sub_type",
                "campaign.bidding_strategy_type",
                "campaign.network_settings.target_google_search",
                "campaign.network_settings.target_search_network",
                "campaign.network_settings.target_content_network",
                "campaign.network_settings.target_partner_search_network",
                "campaign_budget.id",
                "campaign_budget.resource_name",
                "campaign_budget.name",
                "campaign_budget.period",
                "campaign_budget.delivery_method",
                "campaign_budget.type",
                "campaign_budget.explicitly_shared",
                "campaign_budget.amount_micros",
                "campaign_budget.total_amount_micros",
                "campaign.ad_serving_optimization_status",
                "campaign.dynamic_search_ads_setting.domain_name",
                "campaign.dynamic_search_ads_setting.feeds",
                "campaign.dynamic_search_ads_setting.language_code",
                "campaign.dynamic_search_ads_setting.use_supplied_urls_only",
                "campaign.frequency_caps",
                "campaign.selective_optimization.conversion_actions",
                "campaign.optimization_goal_setting.optimization_goal_types",
                "campaign.payment_mode",
                "campaign.excluded_parent_asset_field_types",
                "campaign.targeting_setting.target_restrictions",
                "campaign.start_date",
                "campaign.end_date",
            ],
            from_clause="campaign",
            where_clause=where_clause,
            order_clause="campaign.start_date",
            limit_clause=limit,
        )

        # Get campaigns from customer id
        campaigns = google_client.get_campaigns_from_account(
            customer_id=self.google_business_account.customer_id,
            refresh_token=refresh_token,
            query=google_query,
            reraise=True,
        )

        return [
            data
            for data in GoogleCampaignImporter(
                campaigns,
                metadata={
                    "google_business_account": self.google_business_account.id,
                    "currency": safe_attribute_getter(account_metadata, "currency_code"),
                },
            )
        ]

    def swap_business_accounts(self, facebook_business_account=None, google_business_account=None):
        """Change business accounts linked to business in one-click"""

        with transaction.atomic():
            if facebook_business_account:
                self.facebookbusinessaccount_set.select_for_update().update(**facebook_business_account)

            if google_business_account:
                self.googlebusinessaccount_set.select_for_update().update(**google_business_account)

    @cached_model_property(cache_time_in_seconds=3600, is_property=False)  # Cache for 1h
    def get_business_insights(
        self, since, until, fields, order_by, order, limit, insight_type, page=None, objective=None
    ):
        """Retrieve business insights from hiperion database"""
        try:
            hiperion_client = HiperionAPIServiceClient()
            response = hiperion_client.get_business_insights(
                ad_account_id=self.facebook_business_account.ad_account.account_id,
                objective=objective,
                date_start=since,
                date_end=until,
                fields=fields,
                order_by=order_by,
                order=order,
                limit=limit,
                page=page,
                insight_type=insight_type,
            )
            return response
        except ClientError as e:
            logger.error("Error getting metrics for report {id}:: {reason}".format(id=self.pk, reason=e))
            return None


pre_delete.connect(delete_comments_and_notifications, sender=Business)


class BusinessSocialAccount(models.Model):
    """Link to this business a given social account (with token)"""

    business = models.ForeignKey(Business, null=True, on_delete=models.CASCADE)
    user = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    social_account = models.ForeignKey(SocialAccount, null=True, on_delete=models.SET_NULL)
    uid = models.CharField(max_length=255, null=True)

    objects = BusinessSocialAccountManager()

    class Meta:
        # Restrict, per user, one social_account type
        unique_together = ("business", "user", "social_account")

    @property
    def token(self):
        return self.social_account.socialtoken_set.first().token


class BusinessAccount(models.Model):
    __business_account_type__ = None

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    business = models.ForeignKey(Business, null=True, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name

    @property
    def business_account_type(self):
        return self.__business_account_type__

    @property
    def ads_policy_link(self):
        return f"{settings.POLICIES_URL}?acc={self.pk}"

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        # Set name for this business account if none is provided
        if not self.name and self.business:
            self.name = self.business.name

        return super(BusinessAccount, self).save(force_insert, force_update, using, update_fields)


class FacebookAdAccount(models.Model):
    """Model to save facebook ad account"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, null=True, blank=True)
    account_id = models.CharField(max_length=200, null=True, blank=False)
    currency = models.CharField(max_length=3, null=True, blank=False)
    created_time = models.DateTimeField(null=True, blank=True, auto_now_add=True)
    is_personal = models.PositiveIntegerField(null=True)

    def __str__(self):
        return self.name

    @property
    def ad_account_id(self):
        return f"act_{self.account_id}"


class FacebookBusinessAccount(BusinessAccount):
    __business_account_type__ = "facebook"

    page_id = models.CharField(max_length=200)
    ad_account = models.OneToOneField(FacebookAdAccount, null=True, on_delete=models.SET_NULL)
    pixel_id = models.CharField(max_length=200, null=True, blank=True)
    pixel_name = models.CharField(max_length=200, null=True, blank=True)

    def facebook_page_data(self):
        """Get facebook page metadata and cache it"""
        access_token = self.tokens.first()

        if not access_token:
            return {
                "connected_instagram_account": {
                    "username": None,
                    "id": None,
                },
                "category": None,
                "categories_id": None,
                "name": None,
                "link": None,
                "picture": None,
            }

        access_token = access_token["facebook"]
        facebook_client = FacebookClient(sandbox_or_production=settings.ENVIRONMENT == "PROD")

        def _instagram_information():
            @cached_model_property(cache_time_in_seconds=2592000, is_property=False, attr="business_id")  # 1 month
            def instagram_information(self, *args, **kwargs):
                metadata = (
                    facebook_client.get_connected_instagram_account(page_id=self.page_id, access_token=access_token)
                    or {}
                )
                return {
                    "connected_instagram_account": {
                        "username": metadata.get("connected_instagram_account", {}).get("username", None),
                        "id": metadata.get("connected_instagram_account", {}).get("id", None),
                    }
                }

            return instagram_information(self)

        def _category_information():
            @cached_model_property(cache_time_in_seconds=********, is_property=False, attr="business_id")  # 6 months
            def category_information(self, *args, **kwargs):
                metadata = facebook_client.get_categories(page_id=self.page_id, access_token=access_token) or {}
                return {
                    "category": metadata.get("category", None),
                    "categories_id": [category_id["id"] for category_id in metadata.get("category_list", [])],
                }

            return category_information(self)

        def _metadata():
            @cached_model_property(cache_time_in_seconds=******** * 2, is_property=False, attr="business_id")  # 1 year
            def metadata(self, *args, **kwargs):
                metadata_ = facebook_client.get_metadata(page_id=self.page_id, access_token=access_token) or {}
                return {"name": metadata_.get("name", None), "link": metadata_.get("link", None)}

            return metadata(self)

        def _picture():
            @cached_model_property(cache_time_in_seconds=345600, is_property=False, attr="business_id")  # 4 days
            def picture(self, *args, **kwargs):
                metadata = facebook_client.get_picture(page_id=self.page_id, access_token=access_token) or [{}]
                a = {
                    "picture": metadata[0].get("url", None),
                }
                return a

            return picture(self)

        return {**_instagram_information(), **_category_information(), **_metadata(), **_picture()}

    @property
    def tokens(self):
        """Get linked facebook tokens to this business"""
        return self.business.get_tokens(social_network="facebook").filter(social_account__provider="facebook")

    @property
    def leadgen_tos_accepted(self):
        """Get leadgen tos from Facebook page to be able to do LEAD_GENERATION campaigns"""
        access_token = self.tokens.first()
        if not access_token:
            return None

        access_token = access_token["facebook"]
        facebook_client = FacebookClient(sandbox_or_production=settings.ENVIRONMENT == "PROD")
        return facebook_client.get_lead_get_tos(page_id=self.page_id, access_token=access_token)


class GoogleBusinessAccount(BusinessAccount):
    __business_account_type__ = "google"

    customer_id = models.CharField(max_length=200, help_text=_("Google calls account-id as customer-id"))

    @property
    def tokens(self):
        """Get linked facebook tokens to this business"""
        return self.business.get_tokens(social_network="google").filter(social_account__provider="google")
