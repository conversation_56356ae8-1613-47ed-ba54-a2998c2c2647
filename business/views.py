# Create your views here.
import logging

import pdfkit
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.storage import FileSystemStorage
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils.decorators import method_decorator
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from facebook_business.exceptions import FacebookRequestError
from google.ads.googleads.errors import GoogleAdsException
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from business.pagination import BusinessPagination, ReportPagination
from business.serializers import (
    AddSocialAccountToBusinessSerializer, BusinessBulkCreationSerializer,
    BusinessInsightsQuerySerializer, BusinessInsightsResponseSerializer,
    BusinessInsightsSerializer, BusinessPartialUpdateSerializer,
    BusinessQuerySetSerializer, BusinessSerializer,
    CampaignsInBusinessSerializer,
    FacebookImportedCampaignsFromBusinessSerializer,
    GoogleImportedCampaignsFromBusinessSerializer,
    ImportedCampaignsQuerySerializer, LinkBusinessAccountToBusinessSerializer,
    ReportBusinessQuerySetSerializer,
    ReportBusinessRetrieveQueryParametersSerializer, ReportCreateSerializer,
    ReportPatchMetricsSerializer, ReportRetrieveSerializer,
    ReportsDownloadSerializer, ReportSerializer, SwapBusinessAccountSerializer)
from campaigns.models import FacebookCampaign, GoogleCampaign
from core.filters import CaseInsensitiveOrderingFilter, SearchFilter
from core.mixins import BulkCreateModelMixin
from core.utils import (get_openapi_cache_header, get_openapi_error_response,
                        get_openapi_response)

from .models import Business, Report

logger = logging.getLogger(__name__)


@method_decorator(name="create", decorator=swagger_auto_schema(query_serializer=BusinessQuerySetSerializer))
@method_decorator(name="partial_update", decorator=swagger_auto_schema(query_serializer=BusinessQuerySetSerializer))
class BusinessViewSet(BulkCreateModelMixin, viewsets.ModelViewSet):
    serializer_class = BusinessSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Business.objects.all()
    filter_backends = (SearchFilter, CaseInsensitiveOrderingFilter)
    pagination_class = BusinessPagination
    http_method_names = ["get", "post", "patch", "delete"]

    # filter_backends config
    search_fields = ("name",)
    ordering_fields = (
        "name",
        "date_added",
    )

    def get_queryset(self):
        queryset = super(BusinessViewSet, self).get_queryset()

        filter_queryset_params = {"organization__membership__user": self.request.user}
        organization_id = self.request.query_params.get("organization", None)

        if organization_id:
            filter_queryset_params["organization_id"] = organization_id

        # We must to restrict business to given organization
        return queryset.filter(**filter_queryset_params)

    def get_serializer_class(self):
        if self.action == "create":
            return BusinessBulkCreationSerializer
        elif self.action == "partial_update":
            return BusinessPartialUpdateSerializer
        return self.serializer_class

    @swagger_auto_schema(
        operation_summary="Read business [C]",
        operation_description=(
            "Get data from all businesses. "
            "This is a cached endpoint for **facebook_page_data** response. "
            "Specifically, **facebook_page_data** can be split in: \n"
            "- **instagram**: 1 month of cache at most.\n"
            "- **category**:  6 months of cache at most.\n"
            "- **metadata**: 1 year of cache at most.\n"
            "- **picture**: 1 year of cache at most.\n"
        ),
        query_serializer=BusinessQuerySetSerializer,
        manual_parameters=[
            get_openapi_cache_header(
                "X-API-cache-eviction",
                description="""
        Evict cached data from redis. In this version, it will evict **facebook_page_data** as a whole.
        """,
            )
        ],
    )
    def retrieve(self, request, *args, **kwargs):
        return super(BusinessViewSet, self).retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="List business [C]",
        operation_description=(
            "Get data from all businesses. "
            "This is a cached endpoint for **facebook_page_data** response. "
            "Specifically, **facebook_page_data** can be split in: \n"
            "- **instagram**: 1 month of cache at most.\n"
            "- **category**:  6 months of cache at most.\n"
            "- **metadata**: 1 year of cache at most.\n"
            "- **picture**: 1 year of cache at most.\n"
        ),
        query_serializer=BusinessQuerySetSerializer,
        manual_parameters=[
            get_openapi_cache_header(
                "X-API-cache-eviction",
                description="""
        Evict cached data from redis. In this version, it will evict **facebook_page_data** as a whole.
        """,
            )
        ],
    )
    def list(self, request, *args, **kwargs):
        return super(BusinessViewSet, self).list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Link business account to business",
        operation_description="Link concrete business accounts to business",
        request_body=LinkBusinessAccountToBusinessSerializer,
        query_serializer=BusinessQuerySetSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Business account linked successfully"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(LinkBusinessAccountToBusinessSerializer),
        },
    )
    @action(methods=["post"], detail=True, url_path="link_business_account")
    def link_business_account(self, request, pk=None):
        """Add a new business account to existing business"""
        business = self.get_object()
        serializer = LinkBusinessAccountToBusinessSerializer(business, data=request.data, context={"request": request})
        if serializer.is_valid():
            serializer.save()
            return Response({"status": _("Business account linked successfully")})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Add social account to business",
        operation_description="Add an existing social accounts to business",
        request_body=AddSocialAccountToBusinessSerializer,
        query_serializer=BusinessQuerySetSerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Social account linked successfully"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(AddSocialAccountToBusinessSerializer),
        },
    )
    @action(methods=["post"], detail=True, url_path="link_social_account")
    def link_socialaccount_to_business(self, request, pk=None):
        """Link socialaccount to this business"""
        business = self.get_object()
        serializer = AddSocialAccountToBusinessSerializer(business, data=request.data, context={"request": request})

        if serializer.is_valid():
            serializer.save()
            return Response({"status": _("Social account linked successfully")})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Get campaigns from business [C]",
        operation_description=(
            "Get all campaigns linked to business. This is a cached endpoint for "
            "**traffic_light_status** response and will hold the response during 8 hours. This "
            "endpoint **doesn't** allow the custom header to evict the cached entry."
        ),
        query_serializer=BusinessQuerySetSerializer,
        responses={
            status.HTTP_200_OK: CampaignsInBusinessSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(CampaignsInBusinessSerializer),
        },
    )
    @action(methods=["get"], detail=True, url_path="campaigns")
    def local_campaigns(self, request, pk=None):
        """Get all campaigns linked to business"""

        business = self.get_object()

        serializer = CampaignsInBusinessSerializer(
            {
                "facebook": FacebookCampaign.objects.filter(facebook_business_account__business=business).order_by(
                    "-created_at"
                ),
                "google": GoogleCampaign.objects.filter(google_business_account__business=business).order_by(
                    "-created_at"
                ),
            }
        )
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_summary="Get Facebook campaigns to import them [C]",
        operation_description=(
            "Get all remote campaigns with the **same** ad account and page id"
            " as linked in the business. This endpoint **caches** the response during 15 minutes."
        ),
        query_serializer=ImportedCampaignsQuerySerializer,
        manual_parameters=[
            get_openapi_cache_header(
                "X-API-cache-eviction",
                description="""
            Evict cached data from redis.
            """,
            )
        ],
        responses={
            status.HTTP_200_OK: FacebookImportedCampaignsFromBusinessSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(FacebookImportedCampaignsFromBusinessSerializer),
        },
    )
    @action(methods=["get"], detail=True, url_path="facebook-remote-campaigns")
    def facebook_remote_campaigns(self, request, pk=None):
        """Fetch all campaigns what belong to this business from facebook"""

        business = self.get_object()

        serializer = ImportedCampaignsQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            response = business.facebook_remote_campaigns(limit=serializer.validated_data["limit"], user=request.user)
        except FacebookRequestError as e:
            return Response(
                data=e.body()
                if e.revoked
                else {"details": _("Error importing campaign from Facebook. Please, try again later")},
                status=status.HTTP_424_FAILED_DEPENDENCY if e.revoked else status.HTTP_400_BAD_REQUEST,
            )
        except ValidationError as e:
            logger.error(f"Error importing facebook campaigns: {e.message}")
            return Response({"non_field_errors": e.message}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            FacebookImportedCampaignsFromBusinessSerializer(response, many=True).data, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Change business account linked in business",
        operation_description=(
            "Replace an existing business account in business by another one, maintaining every resource linked to "
            "it. **This method has lots of side effect, thus it will be well-though when is in use by client, "
            "and temporarily will be discouraged and marked as deprecated.**\n\n"
            "[Deprecation date: 7/7/21]"
        ),
        query_serializer=BusinessQuerySetSerializer,
        request_body=SwapBusinessAccountSerializer,
        deprecated=True,
        responses={
            status.HTTP_200_OK: get_openapi_response("OK"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(SwapBusinessAccountSerializer),
        },
    )
    @action(methods=["post"], detail=True, url_path="change-business-account")
    def swap_business_account(self, request, pk=None):
        """Change specific business account linked in the business by another one"""
        business = self.get_object()

        serializer = SwapBusinessAccountSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        business.swap_business_accounts(**serializer.validated_data)

        return Response("OK", status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Get Business Insights [C]",
        operation_description=(
            "Get business insights for ads or audiences.  This endpoint **caches** the response during 60 minutes."
        ),
        query_serializer=BusinessInsightsQuerySerializer,
        request_body=BusinessInsightsSerializer,
        responses={
            status.HTTP_200_OK: BusinessInsightsResponseSerializer(allow_null=True),
            status.HTTP_204_NO_CONTENT: get_openapi_response("No data"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(BusinessInsightsSerializer),
        },
    )
    @action(methods=["post"], detail=True, url_path="insights")
    def business_insights(self, request, pk=None):
        """Retrieve from hiperion business insights"""
        business = self.get_object()

        serializer = BusinessInsightsSerializer(data=request.data, instance=business)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        qp_serializer = BusinessInsightsQuerySerializer(data=request.query_params)
        if not qp_serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        response = business.get_business_insights(**serializer.validated_data, **qp_serializer.validated_data)
        return Response(response, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Get Google Ads campaigns to import them [C]",
        operation_description=(
            "Get all remote campaigns from the customer_id account"
            " as linked in the business. This endpoint **caches** the response during 15 minutes."
        ),
        query_serializer=ImportedCampaignsQuerySerializer,
        manual_parameters=[
            get_openapi_cache_header(
                "X-API-cache-eviction",
                description="""
            Evict cached data from redis.
            """,
            )
        ],
        responses={
            status.HTTP_200_OK: GoogleImportedCampaignsFromBusinessSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(GoogleImportedCampaignsFromBusinessSerializer),
        },
    )
    @action(methods=["get"], detail=True, url_path="google-remote-campaigns")
    def google_remote_campaigns(self, request, pk=None):
        """Fetch all campaigns what belong to this business from google ads"""

        business = self.get_object()

        serializer = ImportedCampaignsQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            response = business.google_remote_campaigns(limit=serializer.validated_data["limit"], user=request.user)
        except GoogleAdsException as e:
            return Response(
                data={
                    "error": e.error,
                    "request_id": e.request_id,
                }
                if e.revoked
                else {"details": _("Error importing campaign from Google. Please, try again later")},
                status=status.HTTP_424_FAILED_DEPENDENCY if e.revoked else status.HTTP_400_BAD_REQUEST,
            )
        except ValidationError as e:
            logger.error(f"Error importing google campaigns: {e.message}")
            return Response({"non_field_errors": e.message}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            GoogleImportedCampaignsFromBusinessSerializer(response, many=True).data, status=status.HTTP_200_OK
        )


@method_decorator(
    name="retrieve", decorator=swagger_auto_schema(query_serializer=ReportBusinessRetrieveQueryParametersSerializer)
)
@method_decorator(name="list", decorator=swagger_auto_schema(query_serializer=ReportBusinessQuerySetSerializer))
@method_decorator(name="create", decorator=swagger_auto_schema(query_serializer=ReportBusinessQuerySetSerializer))
@method_decorator(
    name="partial_update", decorator=swagger_auto_schema(query_serializer=ReportBusinessQuerySetSerializer)
)
@method_decorator(name="destroy", decorator=swagger_auto_schema(query_serializer=ReportBusinessQuerySetSerializer))
class ReportViewSet(viewsets.ModelViewSet):
    serializer_class = ReportSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Report.objects.all()
    pagination_class = ReportPagination
    http_method_names = ["get", "post", "patch", "delete"]

    def get_queryset(self):
        queryset = super(ReportViewSet, self).get_queryset()

        filter_queryset_params = {"business__organization__membership__user": self.request.user}
        business_id = self.request.query_params.get("business", None)

        if business_id:
            filter_queryset_params["business_id"] = business_id

        # We must to restrict report to given business
        return queryset.filter(**filter_queryset_params)

    def get_serializer_class(self):
        if self.action == "create":
            return ReportCreateSerializer
        elif self.action == "partial_update":
            return ReportPatchMetricsSerializer
        elif self.action == "retrieve":
            return ReportRetrieveSerializer
        return self.serializer_class

    @swagger_auto_schema(
        operation_summary="Download reports",
        operation_description="Download PDF reports",
        request_body=ReportsDownloadSerializer,
        query_serializer=ReportBusinessQuerySetSerializer,
        responses={
            status.HTTP_200_OK: "File download",
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(ReportsDownloadSerializer),
        },
    )
    @action(methods=["post"], detail=True, url_path="download")
    def reports_download(self, request, pk):
        # Validate body
        serializer = ReportsDownloadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        report = self.get_object()
        context = serializer.validated_data
        context["campaign"] = report.campaign
        context["business"] = report.business

        html = render_to_string("reports/download_pdfs/reports.html", context)

        fs = FileSystemStorage()
        filename = f"{fs.location}/{report.pk}.pdf"
        pdfkit.from_string(html, filename, options=settings.PDFKIT_OPTIONS)
        with fs.open(filename) as pdf:
            response = HttpResponse(pdf, content_type="application/pdf")
            pdf_filename = f"Adtuo-{slugify(report.name)}-reports.pdf"
            response["Content-Disposition"] = f"attachment; filename={pdf_filename};filename*=UTF-8''{pdf_filename}"
            fs.delete(filename)
            return response
