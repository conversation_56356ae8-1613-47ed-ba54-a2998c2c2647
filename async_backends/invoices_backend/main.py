import base64
import json
import logging
import os
import traceback

import firebase_admin
from firebase_admin import credentials, firestore
from tenacity import retry, stop_after_attempt, wait_random

logger = logging.getLogger(__name__)

# Environmental variables to set the config
PROJECT_ID = os.getenv("PROJECT_ID")


# @see https://www.timvink.nl/google-cloud-functions/
# @see https://stackoverflow.com/questions/52129628/python-google-cloud-function-connection-reset-by-peer
@retry(stop=stop_after_attempt(3), wait=wait_random(min=1, max=4), reraise=True)
def get_cloud_service():
    # Use the application default credentials
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(
        cred,
        {
            "projectId": PROJECT_ID,
        },
    )
    return firestore.client()


try:
    # Initialize firestore
    db = get_cloud_service()
except Exception as err:
    logger.error(
        f"Error initializing firestore client :: {err} - {traceback.print_exc(chain=False)}"
    )
    db = None


def entrypoint(event, context):
    """Background Cloud Function to be triggered by Pub/Sub.
    Args:
         event (dict):  The dictionary with data specific to this type of
                        event. The `@type` field maps to
                         `type.googleapis.com/google.pubsub.v1.PubsubMessage`.
                        The `data` field maps to the PubsubMessage data
                        in a base64-encoded string. The `attributes` field maps
                        to the PubsubMessage attributes if any is present.
         context (google.cloud.functions.Context): Metadata of triggering event
                        including `event_id` which maps to the PubsubMessage
                        messageId, `timestamp` which maps to the PubsubMessage
                        publishTime, `event_type` which maps to
                        `google.pubsub.topic.publish`, and `resource` which is
                        a dictionary that describes the service API endpoint
                        pubsub.googleapis.com, the triggering topic's name, and
                        the triggering event type
                        `type.googleapis.com/google.pubsub.v1.PubsubMessage`.
    Returns:
        None. The output is written to Cloud Logging.
    """

    payload_data = base64.b64decode(event["data"]).decode("utf-8")
    payload_data = json.loads(payload_data)

    try:
        # Save this message into firestore to process later
        invoice_id = payload_data.get("invoice_id")
        invoice_user_email = payload_data.get("invoice_user_email")
        invoice_total = payload_data.get("total")

        if db is not None:
            invoice_ref = db.collection("invoice_historic").document(invoice_id)
            invoice_ref.set(
                {"user_email": invoice_user_email, "total": invoice_total}, merge=True
            )
            return "OK", 200
    except Exception as err:
        logger.error(f"Unknown error running hubspot call : {err}")

    # In error: retry later with exponential backoff
    return "KO", 400
