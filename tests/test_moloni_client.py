import datetime
import json
from unittest import mock
from unittest.mock import PropertyMock, patch

import pytest

from network_clients.base.base import ClientError
from network_clients.moloni.client import MoloniClient


@pytest.fixture
def client():
    return MoloniClient(
        client_id="client_id",
        client_secret="client_secret",
        username="username",
        password="pass",
    )


def mock_requests_factory(response_stub, status_code=200):
    return mock.Mock(
        **{
            "json.return_value": json.loads(response_stub)
            if isinstance(response_stub, str)
            else response_stub,
            "text.return_value": response_stub,
            "status_code": status_code,
            "ok": status_code == 200,
        }
    )


class TestMoloniClient:
    def test_handle_token_ok(self):
        moloni_client = MoloniClient(
            client_id="client_id",
            client_secret="client_secret",
            username="username",
            password="pass",
        )
        assert moloni_client.tokens.access_token is None
        assert moloni_client.tokens.refresh_token is None
        assert moloni_client.tokens.access_expires_in is None
        assert moloni_client.tokens.refresh_expires_in is None

        # Call tokens and check we have data
        with patch("network_clients.moloni.client.HTTPClient.request") as mock_request:
            with patch(
                "network_clients.moloni.client.datetime", spec=datetime
            ) as mock_datetime:
                mock_datetime.datetime.now.return_value = datetime.datetime(
                    2022, 3, 23, 0, 0, 0
                )
                mock_datetime.timedelta = datetime.timedelta
                mock_request.return_value = mock_requests_factory(
                    {
                        "access_token": "access_token",
                        "expires_in": 3600,
                        "token_type": "bearer",
                        "scope": None,
                        "refresh_token": "refresh_token",
                    }
                )

                # Make call
                _ = moloni_client.token

        assert moloni_client.tokens.access_token == "access_token"
        assert moloni_client.tokens.refresh_token == "refresh_token"
        assert moloni_client.tokens.access_expires_in == datetime.datetime(
            2022, 3, 23
        ) + datetime.timedelta(minutes=55)
        assert moloni_client.tokens.refresh_expires_in == datetime.datetime(
            2022, 3, 23
        ) + datetime.timedelta(days=13)

    def test_handle_token_renew_ok(self):
        moloni_client = MoloniClient(
            client_id="client_id",
            client_secret="client_secret",
            username="username",
            password="pass",
        )

        with patch.object(moloni_client, "authenticate") as mock_moloni_authenticate:
            with patch.object(moloni_client, "renew_token") as mock_moloni_renew:
                with patch(
                    "network_clients.moloni.client.datetime", spec=datetime
                ) as mock_datetime:
                    mock_datetime.datetime.now.return_value = datetime.datetime(
                        2022, 3, 23, 0, 0, 0
                    )
                    mock_datetime.timedelta = datetime.timedelta
                    mock_moloni_authenticate.return_value = mock_requests_factory(
                        {
                            "access_token": "access_token",
                            "expires_in": 3600,
                            "token_type": "bearer",
                            "scope": None,
                            "refresh_token": "refresh_token",
                        }
                    )
                    mock_moloni_renew.return_value = mock_requests_factory(
                        {
                            "access_token": "access_token",
                            "expires_in": 3600,
                            "token_type": "bearer",
                            "scope": None,
                            "refresh_token": "refresh_token",
                        }
                    )

                    _ = moloni_client.token
                    assert mock_moloni_authenticate.called

                    # Check now that token is renewed and not authenticated
                    mock_datetime.datetime.now.return_value = datetime.datetime(
                        2022, 3, 24, 0, 0, 0
                    )
                    _ = moloni_client.token
                    mock_moloni_renew.assert_called_once_with(
                        moloni_client.tokens.refresh_token
                    )

    def test_authenticate_ok(self, client):
        with patch("network_clients.moloni.client.HTTPClient.request") as mock_request:
            mock_request.return_value = mock_requests_factory(
                {
                    "access_token": "access_token",
                    "expires_in": 3600,
                    "token_type": "bearer",
                    "scope": None,
                    "refresh_token": "refresh_token",
                }
            )

            response = client.authenticate()

            assert response.json() == {
                "access_token": "access_token",
                "expires_in": 3600,
                "token_type": "bearer",
                "scope": None,
                "refresh_token": "refresh_token",
            }
            mock_request.assert_called_once_with(
                endpoint="https://api.moloni.pt/v1/grant/?grant_type=password&client_id=client_id&client_secret=client_secret&username=username&password=pass",
                method="GET",
            )

    def test_renew_token_ok(self, client):
        with patch("network_clients.moloni.client.HTTPClient.request") as mock_request:
            mock_request.return_value = mock_requests_factory(
                {
                    "access_token": "access_token",
                    "expires_in": 3600,
                    "token_type": "bearer",
                    "scope": None,
                    "refresh_token": "refresh_token",
                }
            )

            response = client.renew_token("refresh_token")

            assert response.json() == {
                "access_token": "access_token",
                "expires_in": 3600,
                "token_type": "bearer",
                "scope": None,
                "refresh_token": "refresh_token",
            }
            mock_request.assert_called_once_with(
                endpoint="https://api.moloni.pt/v1/grant/?grant_type=refresh_token&client_id=client_id&client_secret=client_secret&refresh_token=refresh_token",
                method="GET",
            )

    def test_retrieve_customer_by_id_ok(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = {
                    "email": "",
                    "website": "",
                    "fax": None,
                    "customer_id": 62325855,
                    "visibility_id": 1,
                    "salesman_id": 0,
                    "number": "9999",
                    "name": "Consumidor Final",
                    "vat": "*********",
                    "address": "Desconhecido",
                    "city": "Desconhecido",
                    "zip_code": "0000-000",
                    "country_id": 1,
                    "phone": "",
                    "contact_name": "",
                    "contact_email": "",
                    "contact_phone": "",
                    "notes": "",
                    "discount": 0,
                    "credit_limit": 0,
                    "qty_copies_document": 2,
                    "maturity_date_id": 1400722,
                    "payment_day": 0,
                    "field_notes": "",
                    "language_id": 1,
                    "payment_method_id": 1574236,
                    "delivery_method_id": 1608485,
                    "price_class_id": 0,
                    "document_set_id": 0,
                    "exemption_reason": "",
                    "country": {
                        "iso_3166_1": "pt",
                        "country_id": 1,
                        "name": "Portugal",
                    },
                    "maturity_date": {
                        "maturity_date_id": 1400722,
                        "name": "Pronto Pagamento",
                        "days": 0,
                        "associated_discount": 0,
                    },
                    "payment_method": {"payment_method_id": 1574236, "name": "Cheque"},
                    "delivery_method": {
                        "delivery_method_id": 1608485,
                        "name": "Correio",
                    },
                    "alternate_addresses": [
                        {
                            "email": "",
                            "fax": "",
                            "address_id": 1853220,
                            "customer_id": 62325855,
                            "designation": "Sandra Ferreira",
                            "code": "Sandra",
                            "address": "Rua do Aleixo, Nº 234",
                            "city": "Portimão",
                            "zip_code": "8000-345",
                            "country_id": 1,
                            "phone": "",
                            "contact_name": "",
                        }
                    ],
                    "associated_taxes": [],
                    "copies": [
                        {"customer_id": 62325855, "document_type_id": 1, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 2, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 3, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 4, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 5, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 6, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 7, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 8, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 9, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 10, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 11, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 12, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 13, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 14, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 15, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 16, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 17, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 18, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 19, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 20, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 21, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 22, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 23, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 24, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 25, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 26, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 27, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 28, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 29, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 30, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 31, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 32, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 33, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 34, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 35, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 36, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 37, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 47, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 74, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 75, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 76, "copies": 2},
                        {"customer_id": 62325855, "document_type_id": 77, "copies": 2},
                    ],
                }
                mock_request.return_value = mock_requests_factory(mock_response)

                response = client.retrieve_customer_by_id(
                    company_id=5, customer_id=62325855
                )
                assert response == mock_response
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/customers/getOne/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "customer_id": 62325855}',
                    headers={"Content-Type": "application/json"},
                )

    def test_retrieve_customer_by_number_ok(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = [
                    {
                        "email": "",
                        "website": "",
                        "fax": None,
                        "customer_id": 62325855,
                        "visibility_id": 1,
                        "salesman_id": 0,
                        "number": "9999",
                        "name": "Consumidor Final",
                        "vat": "*********",
                        "address": "Desconhecido",
                        "city": "Desconhecido",
                        "zip_code": "0000-000",
                        "country_id": 1,
                        "phone": "",
                        "contact_name": "",
                        "contact_email": "",
                        "contact_phone": "",
                        "notes": "",
                        "discount": 0,
                        "credit_limit": 0,
                        "qty_copies_document": 2,
                        "maturity_date_id": 1400722,
                        "payment_day": 0,
                        "field_notes": "",
                        "language_id": 1,
                        "payment_method_id": 1574236,
                        "delivery_method_id": 1608485,
                        "price_class_id": 0,
                        "document_set_id": 0,
                        "exemption_reason": "",
                        "country": {
                            "iso_3166_1": "pt",
                            "country_id": 1,
                            "name": "Portugal",
                        },
                        "maturity_date": {
                            "maturity_date_id": 1400722,
                            "name": "Pronto Pagamento",
                            "days": 0,
                            "associated_discount": 0,
                        },
                        "payment_method": {
                            "payment_method_id": 1574236,
                            "name": "Cheque",
                        },
                        "delivery_method": {
                            "delivery_method_id": 1608485,
                            "name": "Correio",
                        },
                        "alternate_addresses": [
                            {
                                "email": "",
                                "fax": "",
                                "address_id": 1853220,
                                "customer_id": 62325855,
                                "designation": "Sandra Ferreira",
                                "code": "Sandra",
                                "address": "Rua do Aleixo, Nº 234",
                                "city": "Portimão",
                                "zip_code": "8000-345",
                                "country_id": 1,
                                "phone": "",
                                "contact_name": "",
                            }
                        ],
                        "associated_taxes": [],
                        "copies": [
                            {
                                "customer_id": 62325855,
                                "document_type_id": 1,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 2,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 3,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 4,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 5,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 6,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 7,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 8,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 9,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 10,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 11,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 12,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 13,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 14,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 15,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 16,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 17,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 18,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 19,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 20,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 21,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 22,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 23,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 24,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 25,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 26,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 27,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 28,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 29,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 30,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 31,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 32,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 33,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 34,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 35,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 36,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 37,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 47,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 74,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 75,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 76,
                                "copies": 2,
                            },
                            {
                                "customer_id": 62325855,
                                "document_type_id": 77,
                                "copies": 2,
                            },
                        ],
                    }
                ]
                mock_request.return_value = mock_requests_factory(mock_response)

                response = client.retrieve_customer_by_number(
                    company_id=5, number=62325855, qty=90, offset=123
                )
                assert response == mock_response
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/customers/getByNumber/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "number": 62325855, "qty": 50, "offset": 123}',
                    headers={"Content-Type": "application/json"},
                )

    def test_retrieve_next_free_number_ok(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = [{"number": 1324}]
                mock_request.return_value = mock_requests_factory(mock_response)

                response = client.retrieve_next_free_number(company_id=5)
                assert response == mock_response
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/customers/getNextNumber/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5}',
                    headers={"Content-Type": "application/json"},
                )

    def test_create_customer_ok(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = {"valid": 1, "customer_id": 62343554}
                mock_request.return_value = mock_requests_factory(mock_response)

                response = client.create_customer(
                    company_id=5,
                    vat="*********",
                    number="9995",
                    name="Cristiano Ronaldo",
                    language_id=1,
                    address="Rua del Obrador",
                    city="Lisboa",
                )

                assert response == 62343554
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/customers/insert/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "vat": "*********", "number": "9995", "name": "Cristiano Ronaldo", "language_id": 1, "zip_code": null, "city": "Lisboa", "country_id": 1, "salesman_id": 0, "maturity_date_id": 0, "payment_day": 0, "discount": 0, "credit_limit": 0, "payment_method_id": 0, "delivery_method_id": 0, "address": "Rua del Obrador"}',
                    headers={"Content-Type": "application/json"},
                )

    def test_create_customer_ko(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = None
                mock_request.return_value = mock_requests_factory(mock_response)

                with pytest.raises(ClientError):
                    _ = client.create_customer(
                        company_id=5,
                        vat="*********",
                        number="9995",
                        name="Cristiano Ronaldo",
                        language_id=1,
                        address="Rua del Obrador",
                        city="Lisboa",
                    )

                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/customers/insert/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "vat": "*********", "number": "9995", "name": "Cristiano Ronaldo", "language_id": 1, "zip_code": null, "city": "Lisboa", "country_id": 1, "salesman_id": 0, "maturity_date_id": 0, "payment_day": 0, "discount": 0, "credit_limit": 0, "payment_method_id": 0, "delivery_method_id": 0, "address": "Rua del Obrador"}',
                    headers={"Content-Type": "application/json"},
                )

    def test_create_invoice_ok(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = {"valid": 1, "document_id": *********}
                mock_request.return_value = mock_requests_factory(mock_response)

                response = client.create_invoice(
                    company_id=5,
                    date=datetime.datetime(2022, 3, 25).date(),
                    expiration_date=datetime.datetime(2022, 3, 25).date(),
                    document_set_id=518531,
                    customer_id=62328655,
                    products=[
                        {
                            "product_id": *********,
                            "name": "AMD",
                            "qty": 1,
                            "price": 10,
                            "taxes": [{"tax_id": 2441999}],
                        }
                    ],
                )

                assert response == *********
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/invoices/insert/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "date": "2022-03-25", "expiration_date": "2022-03-25", "document_set_id": 518531, "customer_id": 62328655, "products": [{"product_id": *********, "name": "AMD", "qty": 1, "price": 10, "taxes": [{"tax_id": 2441999}]}]}',
                    headers={"Content-Type": "application/json"},
                )

    def test_create_invoice_ko(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = None
                mock_request.return_value = mock_requests_factory(mock_response)

                with pytest.raises(ClientError):
                    _ = client.create_invoice(
                        company_id=5,
                        date=datetime.datetime(2022, 3, 25).date(),
                        expiration_date=datetime.datetime(2022, 3, 25).date(),
                        document_set_id=518531,
                        customer_id=62328655,
                        products=[
                            {
                                "product_id": *********,
                                "name": "AMD",
                                "qty": 1,
                                "price": 10,
                                "taxes": [{"tax_id": 2441999}],
                            }
                        ],
                    )

                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/invoices/insert/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "date": "2022-03-25", "expiration_date": "2022-03-25", "document_set_id": 518531, "customer_id": 62328655, "products": [{"product_id": *********, "name": "AMD", "qty": 1, "price": 10, "taxes": [{"tax_id": 2441999}]}]}',
                    headers={"Content-Type": "application/json"},
                )

    def test_get_invoices(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_request.return_value = mock_requests_factory(
                    [
                        {
                            "terminal_id": 0,
                            "lastmodified": "2022-04-11T13:28:51+0100",
                            "lastmodifiedby": "Sander",
                            "document_id": *********,
                            "company_id": 161640,
                            "document_type_id": 1,
                            "customer_id": 59510808,
                            "supplier_id": 0,
                            "salesman_id": 0,
                            "document_set_id": 343384,
                            "document_set_name": "M",
                            "number": 820,
                            "date": "2022-04-09T00:00:00+0100",
                            "expiration_date": "2022-05-09T00:00:00+0100",
                            "maturity_date_days": None,
                            "maturity_date_id": 0,
                            "maturity_date_name": "",
                            "your_reference": None,
                            "our_reference": "",
                            "entity_number": "665",
                            "entity_name": "RUTE MARIA FREITAS DE AZEVEDO",
                            "entity_vat": "*********",
                            "entity_address": "Desconhecido",
                            "entity_city": "Desconhecido",
                            "entity_zip_code": "0000-000",
                            "entity_country": "Portugal",
                            "entity_country_id": 1,
                            "financial_discount": 0,
                            "gross_value": 31.71,
                            "comercial_discount_value": 0,
                            "financial_discount_value": 0,
                            "taxes_value": 7.29,
                            "deduction_value": 0,
                            "net_value": 39,
                            "reconciled_value": 0,
                            "status": 1,
                            "transport_code": "",
                            "transport_code_set_by": 0,
                            "global_guide": 0,
                            "exchange_currency_id": 0,
                            "exchange_total_value": 0,
                            "exchange_rate": 0,
                            "document_type": {"document_type_id": 1, "saft_code": "FT"},
                            "document_set": {"document_set_id": 343384, "name": "M"},
                            "associated_documents": [],
                            "reverse_associated_documents": [],
                            "document_calc_method": {
                                "document_id": *********,
                                "calc_method_id": 2,
                            },
                        }
                    ],
                    200,
                )

                result = []
                number_of_tries = 0
                calls = []
                for invoice_batch in client.get_invoices(company_id=161640):
                    result.append(invoice_batch)
                    number_of_tries += 1
                    calls.extend(mock_request.return_value.mock_calls)

                    if number_of_tries < 5:
                        mock_request.return_value = mock_requests_factory(
                            [
                                {
                                    "terminal_id": 0,
                                    "lastmodified": "2022-04-11T13:28:51+0100",
                                    "lastmodifiedby": "Sander",
                                    "document_id": *********,
                                    "company_id": 161640,
                                    "document_type_id": 1,
                                    "customer_id": 59510808,
                                    "supplier_id": 0,
                                    "salesman_id": 0,
                                    "document_set_id": 343384,
                                    "document_set_name": "M",
                                    "number": 820,
                                    "date": "2022-04-09T00:00:00+0100",
                                    "expiration_date": "2022-05-09T00:00:00+0100",
                                    "maturity_date_days": None,
                                    "maturity_date_id": 0,
                                    "maturity_date_name": "",
                                    "your_reference": None,
                                    "our_reference": "",
                                    "entity_number": "665",
                                    "entity_name": "RUTE MARIA FREITAS DE AZEVEDO",
                                    "entity_vat": "*********",
                                    "entity_address": "Desconhecido",
                                    "entity_city": "Desconhecido",
                                    "entity_zip_code": "0000-000",
                                    "entity_country": "Portugal",
                                    "entity_country_id": 1,
                                    "financial_discount": 0,
                                    "gross_value": 31.71,
                                    "comercial_discount_value": 0,
                                    "financial_discount_value": 0,
                                    "taxes_value": 7.29,
                                    "deduction_value": 0,
                                    "net_value": 39,
                                    "reconciled_value": 0,
                                    "status": 1,
                                    "transport_code": "",
                                    "transport_code_set_by": 0,
                                    "global_guide": 0,
                                    "exchange_currency_id": 0,
                                    "exchange_total_value": 0,
                                    "exchange_rate": 0,
                                    "document_type": {
                                        "document_type_id": 1,
                                        "saft_code": "FT",
                                    },
                                    "document_set": {
                                        "document_set_id": 343384,
                                        "name": "M",
                                    },
                                    "associated_documents": [],
                                    "reverse_associated_documents": [],
                                    "document_calc_method": {
                                        "document_id": *********,
                                        "calc_method_id": 2,
                                    },
                                }
                            ],
                            200,
                        )
                    else:
                        mock_request.return_value = mock_requests_factory([], 200)

                for call in calls:
                    assert call == call.json()

                assert len(result) == 5

    def test_get_invoice(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_request.return_value = mock_requests_factory(
                    [
                        {
                            "terminal_id": 0,
                            "lastmodified": "2022-04-11T13:28:51+0100",
                            "lastmodifiedby": "Sander",
                            "document_id": *********,
                            "company_id": 161640,
                            "document_type_id": 1,
                            "customer_id": 59510808,
                            "supplier_id": 0,
                            "salesman_id": 0,
                            "document_set_id": 343384,
                            "document_set_name": "M",
                            "number": 820,
                            "date": "2022-04-09T00:00:00+0100",
                            "expiration_date": "2022-05-09T00:00:00+0100",
                            "maturity_date_days": None,
                            "maturity_date_id": 0,
                            "maturity_date_name": "",
                            "your_reference": None,
                            "our_reference": "",
                            "entity_number": "665",
                            "entity_name": "RUTE MARIA FREITAS DE AZEVEDO",
                            "entity_vat": "*********",
                            "entity_address": "Desconhecido",
                            "entity_city": "Desconhecido",
                            "entity_zip_code": "0000-000",
                            "entity_country": "Portugal",
                            "entity_country_id": 1,
                            "financial_discount": 0,
                            "gross_value": 31.71,
                            "comercial_discount_value": 0,
                            "financial_discount_value": 0,
                            "taxes_value": 7.29,
                            "deduction_value": 0,
                            "net_value": 39,
                            "reconciled_value": 0,
                            "status": 1,
                            "transport_code": "",
                            "transport_code_set_by": 0,
                            "global_guide": 0,
                            "exchange_currency_id": 0,
                            "exchange_total_value": 0,
                            "exchange_rate": 0,
                            "document_type": {"document_type_id": 1, "saft_code": "FT"},
                            "document_set": {"document_set_id": 343384, "name": "M"},
                            "associated_documents": [],
                            "reverse_associated_documents": [],
                            "document_calc_method": {
                                "document_id": *********,
                                "calc_method_id": 2,
                            },
                        }
                    ],
                    200,
                )
                client.get_invoice(161640, status=0)
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/invoices/getOne/?access_token=access_token&json=true",
                    method="POST",
                    data=json.dumps({"company_id": 161640, "status": 0}),
                    headers={"Content-Type": "application/json"},
                )

    def test_update_invoice(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_request.return_value(
                    {
                        "valid": 1,
                        "mb_reference": {
                            "entity": "1234",
                            "reference": "1234",
                            "value": "1",
                        },
                    }
                )

                client.update_invoice(161640, 88890987)
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/invoices/update/?access_token=access_token&json=true",
                    method="POST",
                    data=json.dumps({"company_id": 161640, "document_id": 88890987}),
                    headers={"Content-Type": "application/json"},
                )

    def test_create_credit_note_ok(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = {"valid": 1, "document_id": *********}
                mock_request.return_value = mock_requests_factory(mock_response)

                response = client.create_credit_note(
                    company_id=5,
                    date=datetime.datetime(2022, 3, 25).date(),
                    expiration_date=datetime.datetime(2022, 3, 25).date(),
                    document_set_id=518531,
                    customer_id=62328655,
                    associated_documents=[
                        {"associated_id": 1234444, "value": 0}  # invoice[document_id]
                    ],
                    products=[
                        {
                            "product_id": *********,
                            "name": "AMD",
                            "qty": 1,
                            "price": 10,
                            "taxes": [{"tax_id": 2441999}],
                        }
                    ],
                )

                assert response == *********
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/debitNotes/insert/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "date": "2022-03-25", "expiration_date": "2022-03-25", "document_set_id": 518531, "customer_id": 62328655, "associated_documents": [{"associated_id": 1234444, "value": 0}], "products": [{"product_id": *********, "name": "AMD", "qty": 1, "price": 10, "taxes": [{"tax_id": 2441999}]}]}',
                    headers={"Content-Type": "application/json"},
                )

    def test_create_credit_note_ko(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_response = None
                mock_request.return_value = mock_requests_factory(mock_response)

                with pytest.raises(ClientError):
                    _ = client.create_credit_note(
                        company_id=5,
                        date=datetime.datetime(2022, 3, 25).date(),
                        expiration_date=datetime.datetime(2022, 3, 25).date(),
                        associated_documents=[
                            {
                                "associated_id": 1234444,  # invoice[document_id]
                                "value": 0,
                            }
                        ],
                        document_set_id=518531,
                        customer_id=62328655,
                        products=[
                            {
                                "product_id": *********,
                                "name": "AMD",
                                "qty": 1,
                                "price": 10,
                                "taxes": [{"tax_id": 2441999}],
                            }
                        ],
                    )

                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/debitNotes/insert/?access_token=access_token&json=true",
                    method="POST",
                    data='{"company_id": 5, "date": "2022-03-25", "expiration_date": "2022-03-25", "document_set_id": 518531, "customer_id": 62328655, "associated_documents": [{"associated_id": 1234444, "value": 0}], "products": [{"product_id": *********, "name": "AMD", "qty": 1, "price": 10, "taxes": [{"tax_id": 2441999}]}]}',
                    headers={"Content-Type": "application/json"},
                )

    def test_get_credit_notes(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_request.return_value = mock_requests_factory(
                    [
                        {
                            "associated_documents": [{"associated_id": *********}],
                            "comercial_discount_value": 0,
                            "company_id": 5,
                            "customer_id": 63989846,
                            "date": "2022-05-25T00:00:00+0100",
                            "deduction_value": 0,
                            "document_calc_method": {
                                "calc_method_id": 2,
                                "document_id": *********,
                            },
                            "document_id": *********,
                            "document_set": {
                                "document_set_id": 526402,
                                "name": "M2022",
                            },
                            "document_set_id": 526402,
                            "document_set_name": "M2022",
                            "document_type": {"document_type_id": 4, "saft_code": "ND"},
                            "document_type_id": 4,
                            "entity_address": "Peterburi Tee 92E",
                            "entity_city": "Tallinn",
                            "entity_country": "Estónia",
                            "entity_country_id": 72,
                            "entity_name": "Infotark As",
                            "entity_number": "100126554",
                            "entity_vat": "100126554",
                            "entity_zip_code": "13816",
                            "exchange_currency_id": 0,
                            "exchange_rate": 0,
                            "exchange_total_value": 0,
                            "expiration_date": "2022-05-28T00:00:00+0100",
                            "financial_discount": 0,
                            "financial_discount_value": 0,
                            "global_guide": 0,
                            "gross_value": 15,
                            "lastmodified": "2022-05-03T20:03:10+0100",
                            "lastmodifiedby": "Sander",
                            "maturity_date_days": None,
                            "maturity_date_id": 0,
                            "maturity_date_name": "",
                            "net_value": 18.45,
                            "number": -1,
                            "our_reference": "",
                            "reconciled_value": 0,
                            "reverse_associated_documents": [],
                            "salesman_id": 0,
                            "status": 0,
                            "supplier_id": 0,
                            "taxes_value": 3.45,
                            "terminal_id": 0,
                            "transport_code": "",
                            "transport_code_set_by": 0,
                            "your_reference": None,
                        }
                    ],
                    200,
                )

                result = []
                number_of_tries = 0
                calls = []
                for invoice_batch in client.get_credit_notes(company_id=161640):
                    result.append(invoice_batch)
                    number_of_tries += 1
                    calls.extend(mock_request.return_value.mock_calls)

                    if number_of_tries < 5:
                        mock_request.return_value = mock_requests_factory(
                            [
                                {
                                    "associated_documents": [
                                        {"associated_id": *********}
                                    ],
                                    "comercial_discount_value": 0,
                                    "company_id": 5,
                                    "customer_id": 63989846,
                                    "date": "2022-05-25T00:00:00+0100",
                                    "deduction_value": 0,
                                    "document_calc_method": {
                                        "calc_method_id": 2,
                                        "document_id": *********,
                                    },
                                    "document_id": *********,
                                    "document_set": {
                                        "document_set_id": 526402,
                                        "name": "M2022",
                                    },
                                    "document_set_id": 526402,
                                    "document_set_name": "M2022",
                                    "document_type": {
                                        "document_type_id": 4,
                                        "saft_code": "ND",
                                    },
                                    "document_type_id": 4,
                                    "entity_address": "Peterburi Tee 92E",
                                    "entity_city": "Tallinn",
                                    "entity_country": "Estónia",
                                    "entity_country_id": 72,
                                    "entity_name": "Infotark As",
                                    "entity_number": "100126554",
                                    "entity_vat": "100126554",
                                    "entity_zip_code": "13816",
                                    "exchange_currency_id": 0,
                                    "exchange_rate": 0,
                                    "exchange_total_value": 0,
                                    "expiration_date": "2022-05-28T00:00:00+0100",
                                    "financial_discount": 0,
                                    "financial_discount_value": 0,
                                    "global_guide": 0,
                                    "gross_value": 15,
                                    "lastmodified": "2022-05-03T20:03:10+0100",
                                    "lastmodifiedby": "Sander",
                                    "maturity_date_days": None,
                                    "maturity_date_id": 0,
                                    "maturity_date_name": "",
                                    "net_value": 18.45,
                                    "number": -1,
                                    "our_reference": "",
                                    "reconciled_value": 0,
                                    "reverse_associated_documents": [],
                                    "salesman_id": 0,
                                    "status": 0,
                                    "supplier_id": 0,
                                    "taxes_value": 3.45,
                                    "terminal_id": 0,
                                    "transport_code": "",
                                    "transport_code_set_by": 0,
                                    "your_reference": None,
                                }
                            ],
                            200,
                        )
                    else:
                        mock_request.return_value = mock_requests_factory([], 200)

                for call in calls:
                    assert call == call.json()

                assert len(result) == 5

    def test_update_credit_note(self, client):
        with patch.object(
            MoloniClient, "token", new_callable=PropertyMock
        ) as mock_token:
            mock_token.return_value = "access_token"
            with patch(
                "network_clients.moloni.client.HTTPClient.request"
            ) as mock_request:
                mock_request.return_value(
                    {
                        "valid": 1,
                        "mb_reference": {
                            "entity": "1234",
                            "reference": "1234",
                            "value": "1",
                        },
                    }
                )

                client.update_credit_note(161640, 88890987)
                mock_request.assert_called_once_with(
                    endpoint="https://api.moloni.pt/v1/debitNotes/update/?access_token=access_token&json=true",
                    method="POST",
                    data=json.dumps({"company_id": 161640, "document_id": 88890987}),
                    headers={"Content-Type": "application/json"},
                )
