import datetime
from pprint import pprint
from typing import Iterator

import pandas as pd
import requests


class BaseNotionClient:
    NOTION_BASE_URL = "https://api.notion.com/v1"

    def __init__(self, api_key: str):
        self._api_key = api_key

    @property
    def pages(self):
        return NotionPageWrapper(self)

    @property
    def tables(self):
        return NotionTableWrapper(self)

    def call(self,
             method: str,
             path: str,
             body: dict = None,
             version="2022-06-28"):
        response = requests.request(
            method,
            f"{self.NOTION_BASE_URL}{path}",
            json=body,
            headers={
                "Authorization": f"Bearer {self._api_key}",
                "Content-Type": "application/json",
                "Notion-Version": version
            },
        )

        response.raise_for_status()
        return response.json()


class Client(BaseNotionClient):
    pass


class BaseNotionWrapper:
    def __init__(self, client: BaseNotionClient):
        self._client = client


class NotionPageWrapper(BaseNotionWrapper):
    NOTION_PAGE_ENDPOINT = "/pages"

    def create(self, parent: dict, icon: dict, properties: dict, children: list):
        return self._client.call(
            method="POST",
            path=f"{self.NOTION_PAGE_ENDPOINT}/",
            body={
                "parent": parent,
                "icon": icon,
                "properties": properties,
                "children": children,
            }
        )


class NotionTableWrapper(BaseNotionWrapper):
    NOTION_TABLE_ENDPOINT = "/blocks/{block_id}/{extra}"

    def create(self, page_id: str, children: list, path: str = "/"):
        return self._client.call(
            method="PATCH",
            path=f"{self.NOTION_TABLE_ENDPOINT.format(block_id=page_id, extra=path)}",
            body={
                "children": children,
            }
        )


class NotionFacade:
    def __init__(self, api_key: str):
        self._notion_client = Client(api_key=api_key)

    def add_page(self, parent_page_id: str, file_data: dict, title: str, icon: str = "🥬") -> dict:
        children = []
        for key, value in file_data.items():
            children.extend(
                [
                    {
                        "object": "block",
                        "type": "heading_2",
                        "heading_2": {
                            "rich_text": [{"type": "text", "text": {"content": key}}]
                        }
                    },
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [
                                {
                                    "type": "text",
                                    "text": {
                                        "content": "\n".join(value) if isinstance(value, list) else value
                                    }
                                }
                            ]
                        }
                    }
                ]
            )

        # Do the call
        return self._notion_client.pages.create(
            parent={"page_id": parent_page_id},
            icon={"emoji": icon},
            properties={
                "title": {
                    "id": "title",
                    "type": "title",
                    "title": [
                        {
                            "type": "text",
                            "text": {
                                "content": title,
                                "link": None
                            },
                            "annotations": {
                                "bold": False,
                                "italic": False,
                                "strikethrough": False,
                                "underline": False,
                                "code": False,
                                "color": "default"
                            },
                            "plain_text": "This is also not done",
                            "href": None
                        }
                    ]
                }
            },
            children=children
        )

    def create_table(self, parent_page_id: str, file_data: pd.DataFrame, title: str, icon: str = "🖥") -> dict:
        page = self.add_page(parent_page_id=parent_page_id, file_data={
            "Menú - Plan RETO": ""
        }, title=title, icon=icon)

        df = file_data[["Tipo", "Detalle2"]]
        days_of_week = file_data["Menu"].unique().tolist()
        children = [
            {
                "type": "table_row",
                "table_row": {
                    "cells": [[{"type": "text", "annotations": {
                            "bold": True,
                            "italic": False,
                            "strikethrough": False,
                            "underline": False,
                            "code": False,
                            "color": "default"
                        }, "text": {"content": value}}] for value in days_of_week],

                }
            },
        ]
        for index in range(len(days_of_week)):
            sub_df = df[index::len(days_of_week)]
            partial_row = [
                {
                    "type": "table_row",
                    "table_row": {
                        "cells": [[{"type": "text", "annotations": {
                            "bold": True,
                            "italic": True,
                            "strikethrough": False,
                            "underline": False,
                            "code": False,
                            "color": "default"
                        }, "text": {"content": value}}] for value in
                                  sub_df["Tipo"].values.tolist()],
                    }
                },
                {
                    "type": "table_row",
                    "table_row": {
                        "cells":
                            [[{"type": "text", "text": {"content": value}}] for value in
                             sub_df["Detalle2"].values.tolist()],
                    }
                },
            ]

            children.extend(partial_row)

        # Create a table in that page
        body = {
            "object": "block",
            "type": "table",
            "table": {
                "table_width": len(days_of_week),
                "has_column_header": True,
                "has_row_header": False,
                "children": children
            }
        }

        # Do the call
        return self._notion_client.tables.create(
            page_id=page["id"],
            children=[body],
            path="children/"
        )


if __name__ == '__main__':
    # Open a dataframe for testing purposes only.
    df = pd.read_csv("/home/<USER>/Descargas/informedf_parsed.csv", sep=",")
    df.fillna("", inplace=True)

    # Menu plan reto
    df2 = pd.read_json("/home/<USER>/Descargas/menu.json")

    notion_facade = NotionFacade(api_key="**************************************************")
    parent_page_id = "15e6a088c7a344e395e5599c6bca54d1"

    #### THIS IS WHAT YOU NEED IN ORDER TO CALL THE NOTION API
    try:
        notion_facade.add_page(
            parent_page_id=parent_page_id,
            file_data=df.to_dict("records")[0],
            title=f"DieteticReportMenu | {datetime.datetime.utcnow().strftime('%Y-%m-%d')}",
        )
        notion_facade.create_table(
            parent_page_id="15e6a088c7a344e395e5599c6bca54d1",
            file_data=df2,
            title=f"DieteticMenuPlanReto | {datetime.datetime.utcnow().strftime('%Y-%m-%d')}",
        )
    except requests.exceptions.RequestException as error:
        pprint(error.response.json())
        print("Error calling notion api -> ", error)
