import time
from datetime import datetime
from unittest.mock import patch

import pandas as pd
import pytest
import pytz
from fastapi.testclient import TestClient
from fastapi_jwt_auth import AuthJWT
from network_clients.base.base import ClientError
from starlette import status

import conf
from app.dependencies import FastAPIPubsubClient, FastAPIStorageClient
from app.main import app
from app.parsers.base import WarehouseResponse as WarehouseResponseParser


@pytest.fixture
def client():
    yield TestClient(app)


@pytest.fixture
def emit_auth_token():
    @AuthJWT.load_config
    def get_settings():
        return conf.settings()

    return AuthJWT().create_access_token(subject="test-user")


class TestRoutes:
    def test_perform_warehouse_differences_no_auth(self, client):
        response = client.post(
            "/run/",
            json={"id": "ad74e01a-86c1-43ab-ab63-38246dad43e9", "warehouse_id": 2001, "date": "2022-05-27"},
        )

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_perform_warehouse_differences_pubsub_error(self, client, emit_auth_token):
        with patch("app.routers.warehouse.run_discrepancies") as run_discrepancies:
            with patch("app.routers.warehouse.FastAPIPubsubClient") as publisher_client:
                # Overrides dependency
                app.dependency_overrides[FastAPIPubsubClient] = lambda: publisher_client

                run_discrepancies.return_value = WarehouseResponseParser()
                publisher_client.request.side_effect = ClientError("Error")

                with patch("app.routers.warehouse.logger") as logger:
                    response = client.post(
                        "/run/",
                        json={"id": "ad74e01a-86c1-43ab-ab63-38246dad43e9", "warehouse_id": 2001, "date": "2022-05-27"},
                        headers={"Authorization": f"Bearer {emit_auth_token}"},
                    )

                assert response.status_code == 299
                assert logger.error.called

    def test_perform_warehouse_differences_exception(self, client, emit_auth_token):
        with patch("app.routers.warehouse.run_discrepancies") as run_discrepancies:
            with patch("app.routers.warehouse.FastAPIPubsubClient") as publisher_client:
                with patch("app.routers.warehouse.datetime", spec=datetime) as mock_datetime:
                    mock_datetime.now.return_value = datetime(2022, 5, 10)
                    publisher_client.request.side_effect = None

                    # Overrides dependency
                    app.dependency_overrides[FastAPIPubsubClient] = lambda: publisher_client
                    run_discrepancies.side_effect = Exception("Error")

                    with patch("app.routers.warehouse.logger") as logger:
                        response = client.post(
                            "/run/",
                            json={
                                "id": "ad74e01a-86c1-43ab-ab63-38246dad43e9",
                                "warehouse_id": 2001,
                                "date": "2022-05-27",
                            },
                            headers={"Authorization": f"Bearer {emit_auth_token}"},
                        )

                    assert response.status_code == 200
                    assert logger.error.call_count == 2
                    publisher_client.request.assert_called_once_with(
                        topic="gcf-webhooks-to-backend-microservices",
                        data={
                            "id": "ad74e01a-86c1-43ab-ab63-38246dad43e9",
                            "status": "failed",
                            "processed_file": None,
                            "result": None,
                            "error_reason": "Error",
                            "warehouse_space": None,
                            "our_space": None,
                            "event_type": "warehouse_service",
                            "occurred_at": int(time.mktime(datetime(2022, 5, 10).replace(tzinfo=pytz.utc).timetuple())),
                        },
                    )

    def test_perform_warehouse_differences_ok_failed(self, client, emit_auth_token):
        with patch("app.routers.warehouse.run_discrepancies") as run_discrepancies:
            with patch("app.routers.warehouse.FastAPIPubsubClient") as publisher_client:
                with patch("app.routers.warehouse.FastAPIStorageClient") as storage_client:
                    with patch("app.routers.warehouse.datetime", spec=datetime) as mock_datetime:
                        mock_datetime.now.return_value = datetime(2022, 5, 10)
                        publisher_client.request.side_effect = None

                        # Overrides dependency
                        app.dependency_overrides[FastAPIPubsubClient] = lambda: publisher_client
                        app.dependency_overrides[FastAPIStorageClient] = lambda: storage_client

                        run_discrepancies.return_value = WarehouseResponseParser()

                        response = client.post(
                            "/run/",
                            json={
                                "id": "ad74e01a-86c1-43ab-ab63-38246dad43e9",
                                "warehouse_id": 2001,
                                "date": "2022-05-27",
                            },
                            headers={"Authorization": f"Bearer {emit_auth_token}"},
                        )

                        assert response.status_code == 200
                        publisher_client.request.assert_called_once_with(
                            topic="gcf-webhooks-to-backend-microservices",
                            data={
                                "id": "ad74e01a-86c1-43ab-ab63-38246dad43e9",
                                "status": "failed",
                                "processed_file": None,
                                "result": None,
                                "error_reason": None,
                                "warehouse_space": None,
                                "our_space": None,
                                "event_type": "warehouse_service",
                                "occurred_at": int(
                                    time.mktime(datetime(2022, 5, 10).replace(tzinfo=pytz.utc).timetuple())
                                ),
                            },
                        )

    def test_perform_warehouse_differences_ok_success(self, client, emit_auth_token):
        with patch("app.routers.warehouse.run_discrepancies") as run_discrepancies:
            with patch("app.routers.warehouse.FastAPIPubsubClient") as publisher_client:
                with patch("app.routers.warehouse.FastAPIStorageClient") as storage_client:
                    with patch("app.routers.warehouse.datetime", spec=datetime) as mock_datetime:
                        mock_datetime.now.return_value = datetime(2022, 5, 10)
                        publisher_client.request.side_effect = None
                        storage_client.upload_file.side_effect = None

                        # Overrides dependency
                        app.dependency_overrides[FastAPIPubsubClient] = lambda: publisher_client
                        app.dependency_overrides[FastAPIStorageClient] = lambda: storage_client

                        run_discrepancies.return_value = WarehouseResponseParser(data=pd.DataFrame([1, 2]))

                        response = client.post(
                            "/run/",
                            json={
                                "id": "ad74e01a-86c1-43ab-ab63-38246dad43e9",
                                "warehouse_id": 2001,
                                "date": "2022-05-27",
                            },
                            headers={"Authorization": f"Bearer {emit_auth_token}"},
                        )

                        assert response.status_code == 200
                        publisher_client.request.assert_called_once_with(
                            topic="gcf-webhooks-to-backend-microservices",
                            data={
                                "id": "ad74e01a-86c1-43ab-ab63-38246dad43e9",
                                "status": "success",
                                "processed_file": "processed/2001_2022-05.csv",
                                "result": None,
                                "error_reason": None,
                                "warehouse_space": None,
                                "our_space": None,
                                "event_type": "warehouse_service",
                                "occurred_at": int(
                                    time.mktime(datetime(2022, 5, 10).replace(tzinfo=pytz.utc).timetuple())
                                ),
                            },
                        )
