import datetime
from unittest.mock import PropertyMock, call, patch

import pytest
from chargebee.list_result import ListResult

from network_clients.chargebee.client import ChargebeeClient


@pytest.fixture
def client():
    return ChargebeeClient(api_key="fake_key", site="fake-test")


class TestChargebeeClient:
    def test_create_customer_ok(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_customer(
                    user_email="<EMAIL>",
                    user_first_name="Calimero",
                    user_last_name="Molon",
                    preferred_currency_code="EUR",
                    preferred_locale="es-ES",
                    vat_number="A12345678",
                    address="Calle Los alamos",
                    postal_code="28001",
                    city="Madrid",
                    state="Madrid",
                    country="ES",
                    phone="+34600100200",
                )

                mock_chargebee.return_value.Customer.create.assert_called_once_with(
                    {
                        "first_name": "<PERSON>ime<PERSON>",
                        "last_name": "Molon",
                        "email": "<EMAIL>",
                        "locale": "es-ES",
                        "vat_number": "A12345678",
                        "preferred_currency_code": "EUR",
                        "phone": "+34600100200",
                        "billing_address": {
                            "first_name": "Calimero",
                            "last_name": "Molon",
                            "line1": "Calle Los alamos",
                            "city": "Madrid",
                            "state": "Madrid",
                            "zip": "28001",
                            "country": "ES",
                        },
                    },
                    env=mock_env.return_value,
                )

    def test_customer_update(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.update_customer(
                    customer_id="BTM9F0SpmlB7656t",
                    email="<EMAIL>",
                    first_name="Calimero",
                    last_name="Molon",
                    phone="+34600100200",
                    vat_number="A12345678",
                )

                mock_chargebee.return_value.Customer.update.assert_called_once_with(
                    "BTM9F0SpmlB7656t",
                    {
                        "first_name": "Calimero",
                        "last_name": "Molon",
                        "phone": "+34600100200",
                        "vat_number": "A12345678",
                        "email": "<EMAIL>",
                    },
                    env=mock_env.return_value,
                )

    def test_customer_update_no_data(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment"):
                response = client.update_customer(
                    customer_id="BTM9F0SpmlB7656t",
                )

                assert not mock_chargebee.return_value.Customer.update.called
                assert response is None

    def test_update_billing_data(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.update_billing_data(
                    customer_id="BTM9F0SpmlB7656t",
                    user_first_name="Calimero",
                    user_last_name="Molon",
                    vat_number="A12345678",
                    address="Calle Los alamos",
                    postal_code="28001",
                    city="Madrid",
                    state="Madrid",
                    country="ES",
                    company="Business Fake Inc.",
                )

                mock_chargebee.return_value.Customer.update_billing_info.assert_called_once_with(
                    "BTM9F0SpmlB7656t",
                    {
                        "vat_number": "A12345678",
                        "billing_address": {
                            "first_name": "Calimero",
                            "last_name": "Molon",
                            "line1": "Calle Los alamos",
                            "city": "Madrid",
                            "state": "Madrid",
                            "zip": "28001",
                            "country": "ES",
                            "company": "Business Fake Inc.",
                        },
                    },
                    env=mock_env.return_value,
                )

    def test_subscription_create(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_subscription(
                    customer_id="BTM9F0SpmlB7656t",
                    expires_at=**********,
                    contract_id="hubspot_deal_id",
                    subscription_id="198ay3SqMJo1O1WJ",
                    change_option="immediately",
                    subscription_items=[
                        {"item_price_id": "esp1-one-EUR-Monthly", "quantity": 1},
                        {
                            "item_price_id": "esp1-parking-EUR",
                            "quantity": 1,
                            "charge_on_option": "on_event",
                            "charge_on_event": "subscription_activation",
                        },
                    ],
                )

                mock_chargebee.return_value.Quote.create_sub_items_for_customer_quote.assert_called_once_with(
                    "BTM9F0SpmlB7656t",
                    {
                        "change_option": "immediately",
                        "expires_at": **********,
                        "subscription_items": [
                            {"item_price_id": "esp1-one-EUR-Monthly", "quantity": 1},
                            {
                                "item_price_id": "esp1-parking-EUR",
                                "quantity": 1,
                                "charge_on_option": "on_event",
                                "charge_on_event": "subscription_activation",
                            },
                        ],
                        "subscription": {
                            "id": "198ay3SqMJo1O1WJ",
                            "po_number": "hubspot_deal_id",
                        },
                    },
                    env=mock_env.return_value,
                )

    def test_update_quote_for_subscription(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.update_quote_for_subscription(
                    subscription_id="198ay3SqMJo1O1WJ",
                    expires_at=**********,
                    change_option="immediately",
                    subscription_items=[
                        {"item_price_id": "esp1-one-EUR-Monthly", "quantity": 1},
                        {
                            "item_price_id": "esp1-parking-EUR",
                            "quantity": 1,
                            "charge_on_option": "on_event",
                            "charge_on_event": "subscription_activation",
                        },
                    ],
                )

                mock_chargebee.return_value.Quote.update_subscription_quote_for_items.assert_called_once_with(
                    {
                        "change_option": "immediately",
                        "expires_at": **********,
                        "subscription": {"id": "198ay3SqMJo1O1WJ"},
                        "subscription_items": [
                            {"item_price_id": "esp1-one-EUR-Monthly", "quantity": 1},
                            {
                                "item_price_id": "esp1-parking-EUR",
                                "quantity": 1,
                                "charge_on_option": "on_event",
                                "charge_on_event": "subscription_activation",
                            },
                        ],
                    },
                    env=mock_env.return_value,
                )

    def test_create_payment_method_payment_intent(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_payment_method(
                    customer_id="BTM9F0SpmlB7656t",
                    payment_intent="199YwdSqS2wQz2zxHBHndmvyPkI3BZjHqhy3zaT8EbzR5nSE",
                )

                mock_chargebee.return_value.PaymentSource.create_using_token.create_using_payment_intent(
                    {
                        "customer_id": "BTM9F0SpmlB7656t",
                        "replace_primary_payment_source": True,
                        "payment_intent": {
                            "id": "199YwdSqS2wQz2zxHBHndmvyPkI3BZjHqhy3zaT8EbzR5nSE"
                        },
                    },
                    env=mock_env.return_value,
                )

    def test_create_payment_intent(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_payment_intent(
                    customer_id="BTM9F0SpmlB7656t",
                    amount=1234,
                    reference_id="cus_1234/pm_12334",
                    payment_gateway="stripe_gw",
                )

                mock_chargebee.return_value.PaymentIntent.create.assert_called_once_with(
                    {
                        "customer_id": "BTM9F0SpmlB7656t",
                        "payment_method_type": "card",
                        "gateway_account_id": "stripe_gw",
                        "amount": 1234,
                        "currency_code": "EUR",
                        "reference_id": "cus_1234/pm_12334",
                    },
                    env=mock_env.return_value,
                )

    def test_one_time_payment_create(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_one_time_payment(
                    customer_id="BTM9F0SpmlB7656t",
                    expires_at=**********,
                    contract_id="hubspot_deal_id",
                    subscription_id="198ay3SqMJo1O1WJ",
                    change_option="immediately",
                    charge_items=[
                        {
                            "item_price_id": "esp1-parking-EUR",
                            "quantity": 1,
                            "charge_on_option": "on_event",
                            "charge_on_event": "subscription_activation",
                        },
                    ],
                )

                mock_chargebee.return_value.Quote.create_for_charge_items_and_charges.assert_called_once_with(
                    {
                        "customer_id": "BTM9F0SpmlB7656t",
                        "change_option": "immediately",
                        "expires_at": **********,
                        "item_prices": [
                            {
                                "item_price_id": "esp1-parking-EUR",
                                "quantity": 1,
                                "charge_on_option": "on_event",
                                "charge_on_event": "subscription_activation",
                            }
                        ],
                        "po_number": "hubspot_deal_id",
                        "subscription_id": "198ay3SqMJo1O1WJ",
                    },
                    env=mock_env.return_value,
                )

    def test_coupon_create_fixed_amount(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                with patch("uuid.uuid4") as mock_uuid:
                    mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                    client.create_coupon(
                        name="Discount",
                        discount_amount=100,
                    )

                    mock_chargebee.return_value.Coupon.create_for_items.assert_called_once_with(
                        {
                            "id": "ca9090e9fcab4ad0a3c86284dcaadbc1",
                            "name": "Coupon - ca9090e9fcab4ad0a3c86284dcaadbc1",
                            "invoice_name": "Discount",
                            "apply_on": "invoice_amount",
                            "duration_type": "one_time",
                            "max_redemptions": 1,
                            "currency_code": "EUR",
                            "discount_type": "fixed_amount",
                            "discount_amount": 100,
                        },
                        env=mock_env.return_value,
                    )

    def test_coupon_create_percentage(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                with patch("uuid.uuid4") as mock_uuid:
                    mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                    client.create_coupon(
                        name="Discount",
                        discount_percentage=5,
                    )

                    mock_chargebee.return_value.Coupon.create_for_items.assert_called_once_with(
                        {
                            "id": "ca9090e9fcab4ad0a3c86284dcaadbc1",
                            "name": "Coupon - ca9090e9fcab4ad0a3c86284dcaadbc1",
                            "invoice_name": "Discount",
                            "apply_on": "invoice_amount",
                            "duration_type": "one_time",
                            "max_redemptions": 1,
                            "currency_code": "EUR",
                            "discount_type": "percentage",
                            "discount_amount": None,
                        },
                        env=mock_env.return_value,
                    )

    def test_apply_coupon(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                with patch("uuid.uuid4") as mock_uuid:
                    mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                    client.apply_coupon(
                        subscription_id="198ay3SqMJo1O1WJ",
                        coupon_id="744592",
                    )

                    mock_chargebee.return_value.Subscription.update_for_items.assert_called_once_with(
                        "198ay3SqMJo1O1WJ",
                        {"coupon_ids": ["744592"], "end_of_term": True},
                        env=mock_env.return_value,
                    )

    def test_receive_pdf_invoice(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.retrieve_pdf_invoice(
                    invoice_id="50",
                )

                mock_chargebee.return_value.Invoice.pdf.assert_called_once_with(
                    "50", {"disposition_type": "attachment"}, env=mock_env.return_value
                )

    def test_pause_subscription_pause_date(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.pause_subscription(
                    subscription_id="198ay3SqMJo1O1WJ",
                    now=True,
                    pause_date=datetime.datetime(2021, 12, 1),
                )

                mock_chargebee.return_value.Subscription.pause.assert_called_once_with(
                    "198ay3SqMJo1O1WJ",
                    {"pause_option": "specific_date", "pause_date": 1638316800},
                    env=mock_env.return_value,
                )

    def test_pause_subscription_now(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.pause_subscription(
                    subscription_id="198ay3SqMJo1O1WJ",
                    now=True,
                )

                mock_chargebee.return_value.Subscription.pause.assert_called_once_with(
                    "198ay3SqMJo1O1WJ",
                    {"pause_option": "immediately"},
                    env=mock_env.return_value,
                )

    def test_cancel_subscription(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.cancel_subscription(
                    subscription_id="198ay3SqMJo1O1WJ",
                )

                mock_chargebee.return_value.Subscription.cancel_for_items.assert_called_once_with(
                    "198ay3SqMJo1O1WJ", {"end_of_term": True}, env=mock_env.return_value
                )

    def test_create_comment_in_subscription(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_comment_in_subscription(
                    subscription_id="198ay3SqMJo1O1WJ", comment="This is a comment"
                )

                mock_chargebee.return_value.Comment.create.assert_called_once_with(
                    {
                        "entity_id": "198ay3SqMJo1O1WJ",
                        "entity_type": "subscription",
                        "notes": "This is a comment",
                    },
                    env=mock_env.return_value,
                )

    def test_update_subscription(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.update_subscription(
                    subscription_id="198ay3SqMJo1O1WJ", cf_initial_space=4
                )

                mock_chargebee.return_value.Subscription.update_for_items.assert_called_once_with(
                    "198ay3SqMJo1O1WJ",
                    {"cf_initial_space": 4},
                    env=mock_env.return_value,
                )

    def test_list_item_prices(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.list_item_prices(
                    item_price_list=[
                        "esp1-one-EUR-Monthly",
                        "esp1-one-pickup-EUR",
                        "esp1-one-delivery-EUR",
                    ]
                )

                mock_chargebee.return_value.ItemPrice.list.assert_called_once_with(
                    {
                        "id[in]": [
                            "esp1-one-EUR-Monthly",
                            "esp1-one-pickup-EUR",
                            "esp1-one-delivery-EUR",
                        ]
                    },
                    env=mock_env.return_value,
                )

    def test_list_invoices_in_date_range(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                mock_chargebee.return_value.Invoice.list.return_value = ListResult(
                    [
                        {
                            "invoice": {
                                "id": "INV-022022-91",
                                "customer_id": "634401",
                                "subscription_id": "4272902892",
                                "recurring": True,
                                "status": "paid",
                                "price_type": "tax_inclusive",
                                "date": 1646002800,
                                "due_date": 1646002800,
                                "net_term_days": 0,
                                "exchange_rate": 1.0,
                                "total": 28664,
                                "amount_paid": 28664,
                                "amount_adjusted": 0,
                                "write_off_amount": 0,
                                "credits_applied": 0,
                                "amount_due": 0,
                                "paid_at": 1646071702,
                                "updated_at": 1646071702,
                                "resource_version": 1646071702284,
                                "deleted": False,
                                "object": "invoice",
                                "first_invoice": True,
                                "amount_to_collect": 0,
                                "round_off_amount": 0,
                                "new_sales_amount": 28664,
                                "has_advance_charges": False,
                                "currency_code": "EUR",
                                "base_currency_code": "EUR",
                                "generated_at": 1646002800,
                                "is_gifted": False,
                                "term_finalized": True,
                                "channel": "web",
                                "tax": 5359,
                                "line_items": [
                                    {
                                        "id": "li_19ACbBSyl30sM5La",
                                        "date_from": 1646002800,
                                        "date_to": 1648418399,
                                        "unit_amount": 9500,
                                        "quantity": 1,
                                        "amount": 9500,
                                        "pricing_model": "flat_fee",
                                        "is_taxed": True,
                                        "tax_amount": 1776,
                                        "tax_rate": 23.0,
                                        "object": "line_item",
                                        "subscription_id": "4272902892",
                                        "customer_id": "634401",
                                        "description": "Armaz\u00e9m 7.5m\u00b2",
                                        "entity_type": "plan_item_price",
                                        "entity_id": "por1-seven-half-EUR-Monthly",
                                        "discount_amount": 0,
                                        "item_level_discount_amount": 0,
                                    },
                                ],
                                "taxes": [
                                    {
                                        "object": "tax",
                                        "name": "VAT",
                                        "description": "VAT @ 23 %",
                                        "amount": 5359,
                                    }
                                ],
                                "line_item_taxes": [
                                    {
                                        "tax_name": "VAT",
                                        "tax_rate": 23.0,
                                        "tax_juris_type": "country",
                                        "tax_juris_name": "Portugal",
                                        "tax_juris_code": "PT",
                                        "object": "line_item_tax",
                                        "line_item_id": "li_19ACbBSyl30sM5La",
                                        "tax_amount": 1776,
                                        "is_partial_tax_applied": False,
                                        "taxable_amount": 7724,
                                        "is_non_compliance_tax": False,
                                    },
                                ],
                                "sub_total": 28664,
                                "linked_payments": [
                                    {
                                        "txn_id": "txn_19ACbBSyl30um5Lf",
                                        "applied_amount": 28664,
                                        "applied_at": 1646069773,
                                        "txn_status": "success",
                                        "txn_date": 1646069772,
                                        "txn_amount": 28664,
                                    }
                                ],
                                "applied_credits": [],
                                "adjustment_credit_notes": [],
                                "issued_credit_notes": [],
                                "linked_orders": [],
                                "dunning_attempts": [],
                                "billing_address": {
                                    "first_name": "andreia paula agostinho da",
                                    "last_name": "cunha",
                                    "line1": "Rua Do Carvalhido, 78, 3 Drt",
                                    "city": "porto",
                                    "state": "Porto",
                                    "country": "PT",
                                    "zip": "4250-100",
                                    "validation_status": "not_validated",
                                    "object": "billing_address",
                                },
                            }
                        },
                    ],
                    next_offset=1,
                )
                for _ in client.list_invoices_in_date_range(
                    date_from=int(datetime.datetime(2022, 3, 1).timestamp()),
                    date_to=int(datetime.datetime(2022, 3, 31).timestamp()),
                ):
                    mock_chargebee.return_value.Invoice.list.return_value = ListResult(
                        [
                            {
                                "invoice": {
                                    "id": "INV-022022-91",
                                    "customer_id": "634401",
                                    "subscription_id": "4272902892",
                                    "recurring": True,
                                    "status": "paid",
                                    "price_type": "tax_inclusive",
                                    "date": 1646002800,
                                    "due_date": 1646002800,
                                    "net_term_days": 0,
                                    "exchange_rate": 1.0,
                                    "total": 28664,
                                    "amount_paid": 28664,
                                    "amount_adjusted": 0,
                                    "write_off_amount": 0,
                                    "credits_applied": 0,
                                    "amount_due": 0,
                                    "paid_at": 1646071702,
                                    "updated_at": 1646071702,
                                    "resource_version": 1646071702284,
                                    "deleted": False,
                                    "object": "invoice",
                                    "first_invoice": True,
                                    "amount_to_collect": 0,
                                    "round_off_amount": 0,
                                    "new_sales_amount": 28664,
                                    "has_advance_charges": False,
                                    "currency_code": "EUR",
                                    "base_currency_code": "EUR",
                                    "generated_at": 1646002800,
                                    "is_gifted": False,
                                    "term_finalized": True,
                                    "channel": "web",
                                    "tax": 5359,
                                    "line_items": [
                                        {
                                            "id": "li_19ACbBSyl30sM5La",
                                            "date_from": 1646002800,
                                            "date_to": 1648418399,
                                            "unit_amount": 9500,
                                            "quantity": 1,
                                            "amount": 9500,
                                            "pricing_model": "flat_fee",
                                            "is_taxed": True,
                                            "tax_amount": 1776,
                                            "tax_rate": 23.0,
                                            "object": "line_item",
                                            "subscription_id": "4272902892",
                                            "customer_id": "634401",
                                            "description": "Armaz\u00e9m 7.5m\u00b2",
                                            "entity_type": "plan_item_price",
                                            "entity_id": "por1-seven-half-EUR-Monthly",
                                            "discount_amount": 0,
                                            "item_level_discount_amount": 0,
                                        },
                                    ],
                                    "taxes": [
                                        {
                                            "object": "tax",
                                            "name": "VAT",
                                            "description": "VAT @ 23 %",
                                            "amount": 5359,
                                        }
                                    ],
                                    "line_item_taxes": [
                                        {
                                            "tax_name": "VAT",
                                            "tax_rate": 23.0,
                                            "tax_juris_type": "country",
                                            "tax_juris_name": "Portugal",
                                            "tax_juris_code": "PT",
                                            "object": "line_item_tax",
                                            "line_item_id": "li_19ACbBSyl30sM5La",
                                            "tax_amount": 1776,
                                            "is_partial_tax_applied": False,
                                            "taxable_amount": 7724,
                                            "is_non_compliance_tax": False,
                                        },
                                    ],
                                    "sub_total": 28664,
                                    "linked_payments": [
                                        {
                                            "txn_id": "txn_19ACbBSyl30um5Lf",
                                            "applied_amount": 28664,
                                            "applied_at": 1646069773,
                                            "txn_status": "success",
                                            "txn_date": 1646069772,
                                            "txn_amount": 28664,
                                        }
                                    ],
                                    "applied_credits": [],
                                    "adjustment_credit_notes": [],
                                    "issued_credit_notes": [],
                                    "linked_orders": [],
                                    "dunning_attempts": [],
                                    "billing_address": {
                                        "first_name": "andreia paula agostinho da",
                                        "last_name": "cunha",
                                        "line1": "Rua Do Carvalhido, 78, 3 Drt",
                                        "city": "porto",
                                        "state": "Porto",
                                        "country": "PT",
                                        "zip": "4250-100",
                                        "validation_status": "not_validated",
                                        "object": "billing_address",
                                    },
                                }
                            }
                        ],
                        next_offset=None,
                    )
                    pass

                assert mock_chargebee.return_value.Invoice.list.mock_calls == [
                    call(
                        {
                            "date[between]": [1646089200, 1648677600],
                            "limit": 100,
                            "offset": None,
                        },
                        env=mock_env.return_value,
                    ),
                    call(
                        {
                            "date[between]": [1646089200, 1648677600],
                            "limit": 100,
                            "offset": 1,
                        },
                        env=mock_env.return_value,
                    ),
                ]

    def test_remove_coupons(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.remove_coupons(
                    subscription_id="198ay3SqMJo1O1WJ", coupon_ids=["free-month"]
                )

                mock_chargebee.return_value.Subscription.remove_coupons.assert_called_once_with(
                    "198ay3SqMJo1O1WJ",
                    {"coupon_ids": ["free-month"]},
                    env=mock_env.return_value,
                )

    def test_list_credit_notes_in_date_range(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                mock_chargebee.return_value.CreditNote.list.return_value = ListResult(
                    [
                        {
                            "credit_note": {
                                "id": "CN-60",
                                "customer_id": "7376",
                                "subscription_id": "4265975508",
                                "reference_invoice_id": "INV/2022/0146",
                                "type": "refundable",
                                "reason_code": "product_unsatisfactory",
                                "status": "refunded",
                                "date": 1651053477,
                                "price_type": "tax_inclusive",
                                "exchange_rate": 1.0,
                                "total": 2500,
                                "amount_allocated": 0,
                                "amount_refunded": 2500,
                                "amount_available": 0,
                                "refunded_at": 1651053491,
                                "generated_at": 1651053477,
                                "updated_at": 1651053491,
                                "channel": "web",
                                "resource_version": 1651053491216,
                                "deleted": False,
                                "object": "credit_note",
                                "create_reason_code": "Product Unsatisfactory",
                                "currency_code": "EUR",
                                "round_off_amount": 0,
                                "fractional_correction": 0,
                                "base_currency_code": "EUR",
                                "sub_total": 2500,
                                "line_items": [
                                    {
                                        "id": "li_19AChmT4CK88cFU6",
                                        "date_from": 1651010400,
                                        "date_to": 1651096799,
                                        "unit_amount": 2500,
                                        "quantity": 1,
                                        "amount": 2500,
                                        "pricing_model": "flat_fee",
                                        "is_taxed": True,
                                        "tax_amount": 467,
                                        "tax_rate": 23.0,
                                        "object": "line_item",
                                        "subscription_id": "4265975508",
                                        "customer_id": "7376",
                                        "description": "PT 1m\u00b2 Mensual\nInvoicing period: [2022-02-10 -> 2022-03-09]",
                                        "entity_type": "adhoc",
                                        "discount_amount": 0,
                                        "item_level_discount_amount": 0,
                                    },
                                    {
                                        "id": "li_19AChmT4CK88fFU7",
                                        "date_from": 1651010400,
                                        "date_to": 1651096799,
                                        "unit_amount": 0,
                                        "quantity": 1,
                                        "amount": 0,
                                        "pricing_model": "flat_fee",
                                        "is_taxed": True,
                                        "tax_amount": 0,
                                        "tax_rate": 23.0,
                                        "object": "line_item",
                                        "subscription_id": "4265975508",
                                        "customer_id": "7376",
                                        "description": "PT Recolha",
                                        "entity_type": "adhoc",
                                        "discount_amount": 0,
                                        "item_level_discount_amount": 0,
                                    },
                                ],
                                "taxes": [
                                    {
                                        "object": "tax",
                                        "name": "VAT",
                                        "description": "VAT @ 23 %",
                                        "amount": 467,
                                    }
                                ],
                                "line_item_taxes": [
                                    {
                                        "tax_name": "VAT",
                                        "tax_rate": 23.0,
                                        "object": "line_item_tax",
                                        "line_item_id": "li_19AChmT4CK88cFU6",
                                        "tax_amount": 467,
                                        "taxable_amount": 2033,
                                        "is_partial_tax_applied": False,
                                        "is_non_compliance_tax": False,
                                    },
                                    {
                                        "tax_name": "VAT",
                                        "tax_rate": 23.0,
                                        "object": "line_item_tax",
                                        "line_item_id": "li_19AChmT4CK88fFU7",
                                        "tax_amount": 0,
                                        "taxable_amount": 0,
                                        "is_partial_tax_applied": False,
                                        "is_non_compliance_tax": False,
                                    },
                                ],
                                "line_item_discounts": [],
                                "linked_refunds": [
                                    {
                                        "applied_amount": 2500,
                                        "applied_at": 1651053491,
                                        "txn_id": "txn_199YWPT4CKBnJFTf",
                                        "txn_status": "success",
                                        "txn_date": 1651053483,
                                        "txn_amount": 2500,
                                    }
                                ],
                                "allocations": [],
                            }
                        }
                    ],
                    next_offset=1,
                )
                for _ in client.list_credit_notes_in_date_range(
                    date_from=int(datetime.datetime(2022, 3, 1).timestamp()),
                    date_to=int(datetime.datetime(2022, 3, 31).timestamp()),
                ):
                    mock_chargebee.return_value.CreditNote.list.return_value = ListResult(
                        [
                            {
                                "credit_note": {
                                    "id": "CN-61",
                                    "customer_id": "7376",
                                    "subscription_id": "4265975508",
                                    "reference_invoice_id": "INV/2022/0146",
                                    "type": "refundable",
                                    "reason_code": "product_unsatisfactory",
                                    "status": "refunded",
                                    "date": 1651053477,
                                    "price_type": "tax_inclusive",
                                    "exchange_rate": 1.0,
                                    "total": 2500,
                                    "amount_allocated": 0,
                                    "amount_refunded": 2500,
                                    "amount_available": 0,
                                    "refunded_at": 1651053491,
                                    "generated_at": 1651053477,
                                    "updated_at": 1651053491,
                                    "channel": "web",
                                    "resource_version": 1651053491216,
                                    "deleted": False,
                                    "object": "credit_note",
                                    "create_reason_code": "Product Unsatisfactory",
                                    "currency_code": "EUR",
                                    "round_off_amount": 0,
                                    "fractional_correction": 0,
                                    "base_currency_code": "EUR",
                                    "sub_total": 2500,
                                    "line_items": [
                                        {
                                            "id": "li_19AChmT4CK88cFU6",
                                            "date_from": 1651010400,
                                            "date_to": 1651096799,
                                            "unit_amount": 2500,
                                            "quantity": 1,
                                            "amount": 2500,
                                            "pricing_model": "flat_fee",
                                            "is_taxed": True,
                                            "tax_amount": 467,
                                            "tax_rate": 23.0,
                                            "object": "line_item",
                                            "subscription_id": "4265975508",
                                            "customer_id": "7376",
                                            "description": "PT 1m\u00b2 Mensual\nInvoicing period: [2022-02-10 -> 2022-03-09]",
                                            "entity_type": "adhoc",
                                            "discount_amount": 0,
                                            "item_level_discount_amount": 0,
                                        },
                                        {
                                            "id": "li_19AChmT4CK88fFU7",
                                            "date_from": 1651010400,
                                            "date_to": 1651096799,
                                            "unit_amount": 0,
                                            "quantity": 1,
                                            "amount": 0,
                                            "pricing_model": "flat_fee",
                                            "is_taxed": True,
                                            "tax_amount": 0,
                                            "tax_rate": 23.0,
                                            "object": "line_item",
                                            "subscription_id": "4265975508",
                                            "customer_id": "7376",
                                            "description": "PT Recolha",
                                            "entity_type": "adhoc",
                                            "discount_amount": 0,
                                            "item_level_discount_amount": 0,
                                        },
                                    ],
                                    "taxes": [
                                        {
                                            "object": "tax",
                                            "name": "VAT",
                                            "description": "VAT @ 23 %",
                                            "amount": 467,
                                        }
                                    ],
                                    "line_item_taxes": [
                                        {
                                            "tax_name": "VAT",
                                            "tax_rate": 23.0,
                                            "object": "line_item_tax",
                                            "line_item_id": "li_19AChmT4CK88cFU6",
                                            "tax_amount": 467,
                                            "taxable_amount": 2033,
                                            "is_partial_tax_applied": False,
                                            "is_non_compliance_tax": False,
                                        },
                                        {
                                            "tax_name": "VAT",
                                            "tax_rate": 23.0,
                                            "object": "line_item_tax",
                                            "line_item_id": "li_19AChmT4CK88fFU7",
                                            "tax_amount": 0,
                                            "taxable_amount": 0,
                                            "is_partial_tax_applied": False,
                                            "is_non_compliance_tax": False,
                                        },
                                    ],
                                    "line_item_discounts": [],
                                    "linked_refunds": [
                                        {
                                            "applied_amount": 2500,
                                            "applied_at": 1651053491,
                                            "txn_id": "txn_199YWPT4CKBnJFTf",
                                            "txn_status": "success",
                                            "txn_date": 1651053483,
                                            "txn_amount": 2500,
                                        }
                                    ],
                                    "allocations": [],
                                }
                            }
                        ],
                        next_offset=None,
                    )
                    pass

                assert mock_chargebee.return_value.CreditNote.list.mock_calls == [
                    call(
                        {
                            "date[between]": [1646089200, 1648677600],
                            "limit": 100,
                            "offset": None,
                        },
                        env=mock_env.return_value,
                    ),
                    call(
                        {
                            "date[between]": [1646089200, 1648677600],
                            "limit": 100,
                            "offset": 1,
                        },
                        env=mock_env.return_value,
                    ),
                ]

    def test_list_subscriptions_in_date_range(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                mock_chargebee.return_value.Subscription.list.return_value = ListResult(
                    [
                        {
                            "subscription": {
                                "id": "BTLWMPT2K3kGa10X",
                                "billing_period": 1,
                                "billing_period_unit": "month",
                                "remaining_billing_cycles": 0,
                                "customer_id": "237151",
                                "status": "non_renewing",
                                "current_term_start": **********,
                                "current_term_end": 1651874399,
                                "created_at": 1649335517,
                                "started_at": **********,
                                "activated_at": **********,
                                "cancelled_at": 1651874400,
                                "updated_at": 1649335517,
                                "has_scheduled_changes": False,
                                "cancel_schedule_created_at": 1649335517,
                                "channel": "web",
                                "resource_version": 1649335517808,
                                "deleted": False,
                                "object": "subscription",
                                "currency_code": "EUR",
                                "subscription_items": [
                                    {
                                        "item_price_id": "zero-plan-EUR-Monthly",
                                        "item_type": "plan",
                                        "quantity": 1,
                                        "unit_price": 500,
                                        "amount": 500,
                                        "free_quantity": 0,
                                        "billing_cycles": 0,
                                        "object": "subscription_item",
                                    }
                                ],
                                "due_invoices_count": 1,
                                "due_since": **********,
                                "total_dues": 500,
                                "mrr": 0,
                                "cf_stairs": "False",
                                "cf_assembly": "False",
                                "cf_free_partial_pickup": "False",
                                "meta_data": {
                                    "contract_id": 4458390516,
                                    "email": "<EMAIL>",
                                    "contact_id": 237151,
                                    "stage": "initial_payment",
                                },
                            },
                            "customer": {
                                "id": "237151",
                                "first_name": "María",
                                "last_name": "Lucía García",
                                "email": "<EMAIL>",
                                "auto_collection": "on",
                                "net_term_days": 0,
                                "allow_direct_debit": True,
                                "created_at": 1649333673,
                                "taxability": "taxable",
                                "updated_at": 1649335516,
                                "locale": "es-ES",
                                "pii_cleared": "active",
                                "channel": "web",
                                "resource_version": 1649335516397,
                                "deleted": False,
                                "object": "customer",
                                "billing_address": {
                                    "first_name": "María",
                                    "last_name": "Lucía García",
                                    "line1": "calle local",
                                    "city": "Ciudad Real",
                                    "state": "Ciudad Real",
                                    "country": "ES",
                                    "validation_status": "not_validated",
                                    "object": "billing_address",
                                },
                                "promotional_credits": 0,
                                "refundable_credits": 0,
                                "excess_payments": 0,
                                "unbilled_charges": 0,
                                "preferred_currency_code": "EUR",
                                "primary_payment_source_id": "pm_BTLWMPT2K3k4910F",
                                "payment_method": {
                                    "object": "payment_method",
                                    "type": "direct_debit",
                                    "reference_id": "CB_BTLWMPT2K3k3v10E/LHKCWGXR96KXWD82",
                                    "gateway": "adyen",
                                    "gateway_account_id": "gw_BTM9HQSq9oFEiWWD",
                                    "status": "valid",
                                },
                            },
                            "invoice": {
                                "id": "INV-042022-4106",
                                "customer_id": "237151",
                                "subscription_id": "BTLWMPT2K3kGa10X",
                                "recurring": True,
                                "status": "payment_due",
                                "price_type": "tax_inclusive",
                                "date": **********,
                                "due_date": **********,
                                "net_term_days": 0,
                                "exchange_rate": 0.91685,
                                "total": 500,
                                "amount_paid": 0,
                                "amount_adjusted": 0,
                                "write_off_amount": 0,
                                "credits_applied": 0,
                                "amount_due": 500,
                                "updated_at": 1649335517,
                                "resource_version": 1649335517756,
                                "deleted": False,
                                "object": "invoice",
                                "first_invoice": True,
                                "amount_to_collect": 0,
                                "round_off_amount": 0,
                                "new_sales_amount": 500,
                                "has_advance_charges": False,
                                "currency_code": "EUR",
                                "base_currency_code": "USD",
                                "generated_at": **********,
                                "is_gifted": False,
                                "term_finalized": True,
                                "channel": "web",
                                "tax": 87,
                                "line_items": [
                                    {
                                        "id": "li_BTLWMPT2K3kIj10b",
                                        "date_from": **********,
                                        "date_to": 1651874399,
                                        "unit_amount": 500,
                                        "quantity": 1,
                                        "amount": 500,
                                        "pricing_model": "flat_fee",
                                        "is_taxed": True,
                                        "tax_amount": 87,
                                        "tax_rate": 21,
                                        "object": "line_item",
                                        "subscription_id": "BTLWMPT2K3kGa10X",
                                        "customer_id": "237151",
                                        "description": "Pago de señal",
                                        "entity_type": "plan_item_price",
                                        "entity_id": "zero-plan-EUR-Monthly",
                                        "discount_amount": 0,
                                        "item_level_discount_amount": 0,
                                    }
                                ],
                                "taxes": [
                                    {
                                        "object": "tax",
                                        "name": "VAT",
                                        "description": "VAT @ 21%",
                                        "amount": 87,
                                    }
                                ],
                                "line_item_taxes": [
                                    {
                                        "tax_name": "VAT",
                                        "tax_rate": 21,
                                        "tax_juris_type": "country",
                                        "tax_juris_name": "Spain",
                                        "tax_juris_code": "ES",
                                        "object": "line_item_tax",
                                        "line_item_id": "li_BTLWMPT2K3kIj10b",
                                        "tax_amount": 87,
                                        "is_partial_tax_applied": False,
                                        "taxable_amount": 413,
                                        "is_non_compliance_tax": False,
                                    }
                                ],
                                "sub_total": 500,
                                "linked_payments": [
                                    {
                                        "txn_id": "txn_BTLWMPT2K3kLn10g",
                                        "applied_amount": 500,
                                        "applied_at": 1649335517,
                                        "txn_status": "in_progress",
                                        "txn_date": 1649335517,
                                        "txn_amount": 500,
                                    }
                                ],
                                "applied_credits": [],
                                "adjustment_credit_notes": [],
                                "issued_credit_notes": [],
                                "linked_orders": [],
                                "dunning_attempts": [
                                    {
                                        "created_at": 1649335517,
                                        "attempt": 0,
                                        "dunning_type": "direct_debit",
                                        "transaction_id": "txn_BTLWMPT2K3kLn10g",
                                        "txn_status": "in_progress",
                                        "txn_amount": 500,
                                    }
                                ],
                                "billing_address": {
                                    "first_name": "María",
                                    "last_name": "Lucía García",
                                    "line1": "calle local",
                                    "city": "Ciudad Real",
                                    "state": "Ciudad Real",
                                    "country": "ES",
                                    "validation_status": "not_validated",
                                    "object": "billing_address",
                                },
                            },
                        }
                    ],
                    next_offset=1,
                )
                for _ in client.list_subscriptions_in_date_range(
                    date_from=int(datetime.datetime(2022, 3, 1).timestamp()),
                    date_to=int(datetime.datetime(2022, 3, 31).timestamp()),
                ):
                    mock_chargebee.return_value.Subscription.list.return_value = ListResult(
                        [
                            {
                                "subscription": {
                                    "id": "BTLWMPT2K3kGa10X",
                                    "billing_period": 1,
                                    "billing_period_unit": "month",
                                    "remaining_billing_cycles": 0,
                                    "customer_id": "237151",
                                    "status": "non_renewing",
                                    "current_term_start": **********,
                                    "current_term_end": 1651874399,
                                    "created_at": 1649335517,
                                    "started_at": **********,
                                    "activated_at": **********,
                                    "cancelled_at": 1651874400,
                                    "updated_at": 1649335517,
                                    "has_scheduled_changes": False,
                                    "cancel_schedule_created_at": 1649335517,
                                    "channel": "web",
                                    "resource_version": 1649335517808,
                                    "deleted": False,
                                    "object": "subscription",
                                    "currency_code": "EUR",
                                    "subscription_items": [
                                        {
                                            "item_price_id": "zero-plan-EUR-Monthly",
                                            "item_type": "plan",
                                            "quantity": 1,
                                            "unit_price": 500,
                                            "amount": 500,
                                            "free_quantity": 0,
                                            "billing_cycles": 0,
                                            "object": "subscription_item",
                                        }
                                    ],
                                    "due_invoices_count": 1,
                                    "due_since": **********,
                                    "total_dues": 500,
                                    "mrr": 0,
                                    "cf_stairs": "False",
                                    "cf_assembly": "False",
                                    "cf_free_partial_pickup": "False",
                                    "meta_data": {
                                        "contract_id": 4458390516,
                                        "email": "<EMAIL>",
                                        "contact_id": 237151,
                                        "stage": "initial_payment",
                                    },
                                },
                                "customer": {
                                    "id": "237151",
                                    "first_name": "María",
                                    "last_name": "Lucía García",
                                    "email": "<EMAIL>",
                                    "auto_collection": "on",
                                    "net_term_days": 0,
                                    "allow_direct_debit": True,
                                    "created_at": 1649333673,
                                    "taxability": "taxable",
                                    "updated_at": 1649335516,
                                    "locale": "es-ES",
                                    "pii_cleared": "active",
                                    "channel": "web",
                                    "resource_version": 1649335516397,
                                    "deleted": False,
                                    "object": "customer",
                                    "billing_address": {
                                        "first_name": "María",
                                        "last_name": "Lucía García",
                                        "line1": "calle local",
                                        "city": "Ciudad Real",
                                        "state": "Ciudad Real",
                                        "country": "ES",
                                        "validation_status": "not_validated",
                                        "object": "billing_address",
                                    },
                                    "promotional_credits": 0,
                                    "refundable_credits": 0,
                                    "excess_payments": 0,
                                    "unbilled_charges": 0,
                                    "preferred_currency_code": "EUR",
                                    "primary_payment_source_id": "pm_BTLWMPT2K3k4910F",
                                    "payment_method": {
                                        "object": "payment_method",
                                        "type": "direct_debit",
                                        "reference_id": "CB_BTLWMPT2K3k3v10E/LHKCWGXR96KXWD82",
                                        "gateway": "adyen",
                                        "gateway_account_id": "gw_BTM9HQSq9oFEiWWD",
                                        "status": "valid",
                                    },
                                },
                                "invoice": {
                                    "id": "INV-042022-4106",
                                    "customer_id": "237151",
                                    "subscription_id": "BTLWMPT2K3kGa10X",
                                    "recurring": True,
                                    "status": "payment_due",
                                    "price_type": "tax_inclusive",
                                    "date": **********,
                                    "due_date": **********,
                                    "net_term_days": 0,
                                    "exchange_rate": 0.91685,
                                    "total": 500,
                                    "amount_paid": 0,
                                    "amount_adjusted": 0,
                                    "write_off_amount": 0,
                                    "credits_applied": 0,
                                    "amount_due": 500,
                                    "updated_at": 1649335517,
                                    "resource_version": 1649335517756,
                                    "deleted": False,
                                    "object": "invoice",
                                    "first_invoice": True,
                                    "amount_to_collect": 0,
                                    "round_off_amount": 0,
                                    "new_sales_amount": 500,
                                    "has_advance_charges": False,
                                    "currency_code": "EUR",
                                    "base_currency_code": "USD",
                                    "generated_at": **********,
                                    "is_gifted": False,
                                    "term_finalized": True,
                                    "channel": "web",
                                    "tax": 87,
                                    "line_items": [
                                        {
                                            "id": "li_BTLWMPT2K3kIj10b",
                                            "date_from": **********,
                                            "date_to": 1651874399,
                                            "unit_amount": 500,
                                            "quantity": 1,
                                            "amount": 500,
                                            "pricing_model": "flat_fee",
                                            "is_taxed": True,
                                            "tax_amount": 87,
                                            "tax_rate": 21,
                                            "object": "line_item",
                                            "subscription_id": "BTLWMPT2K3kGa10X",
                                            "customer_id": "237151",
                                            "description": "Pago de señal",
                                            "entity_type": "plan_item_price",
                                            "entity_id": "zero-plan-EUR-Monthly",
                                            "discount_amount": 0,
                                            "item_level_discount_amount": 0,
                                        }
                                    ],
                                    "taxes": [
                                        {
                                            "object": "tax",
                                            "name": "VAT",
                                            "description": "VAT @ 21%",
                                            "amount": 87,
                                        }
                                    ],
                                    "line_item_taxes": [
                                        {
                                            "tax_name": "VAT",
                                            "tax_rate": 21,
                                            "tax_juris_type": "country",
                                            "tax_juris_name": "Spain",
                                            "tax_juris_code": "ES",
                                            "object": "line_item_tax",
                                            "line_item_id": "li_BTLWMPT2K3kIj10b",
                                            "tax_amount": 87,
                                            "is_partial_tax_applied": False,
                                            "taxable_amount": 413,
                                            "is_non_compliance_tax": False,
                                        }
                                    ],
                                    "sub_total": 500,
                                    "linked_payments": [
                                        {
                                            "txn_id": "txn_BTLWMPT2K3kLn10g",
                                            "applied_amount": 500,
                                            "applied_at": 1649335517,
                                            "txn_status": "in_progress",
                                            "txn_date": 1649335517,
                                            "txn_amount": 500,
                                        }
                                    ],
                                    "applied_credits": [],
                                    "adjustment_credit_notes": [],
                                    "issued_credit_notes": [],
                                    "linked_orders": [],
                                    "dunning_attempts": [
                                        {
                                            "created_at": 1649335517,
                                            "attempt": 0,
                                            "dunning_type": "direct_debit",
                                            "transaction_id": "txn_BTLWMPT2K3kLn10g",
                                            "txn_status": "in_progress",
                                            "txn_amount": 500,
                                        }
                                    ],
                                    "billing_address": {
                                        "first_name": "María",
                                        "last_name": "Lucía García",
                                        "line1": "calle local",
                                        "city": "Ciudad Real",
                                        "state": "Ciudad Real",
                                        "country": "ES",
                                        "validation_status": "not_validated",
                                        "object": "billing_address",
                                    },
                                },
                            }
                        ],
                        next_offset=None,
                    )
                    pass

                assert mock_chargebee.return_value.Subscription.list.mock_calls == [
                    call(
                        {
                            "date[between]": [1646089200, 1648677600],
                            "limit": 100,
                            "offset": None,
                        },
                        env=mock_env.return_value,
                    ),
                    call(
                        {
                            "date[between]": [1646089200, 1648677600],
                            "limit": 100,
                            "offset": 1,
                        },
                        env=mock_env.return_value,
                    ),
                ]

    def test_list_subscriptions_by_ids(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                mock_chargebee.return_value.Subscription.list.return_value = ListResult(
                    [
                        {
                            "subscription": {
                                "id": "BTLWMPT2K3kGa10Z",
                                "billing_period": 1,
                                "billing_period_unit": "month",
                                "remaining_billing_cycles": 0,
                                "customer_id": "237151",
                                "status": "non_renewing",
                                "current_term_start": **********,
                                "current_term_end": 1651874399,
                                "created_at": 1649335517,
                                "started_at": **********,
                                "activated_at": **********,
                                "cancelled_at": 1651874400,
                                "updated_at": 1649335517,
                                "has_scheduled_changes": False,
                                "cancel_schedule_created_at": 1649335517,
                                "channel": "web",
                                "resource_version": 1649335517808,
                                "deleted": False,
                                "object": "subscription",
                                "currency_code": "EUR",
                                "subscription_items": [
                                    {
                                        "item_price_id": "zero-plan-EUR-Monthly",
                                        "item_type": "plan",
                                        "quantity": 1,
                                        "unit_price": 500,
                                        "amount": 500,
                                        "free_quantity": 0,
                                        "billing_cycles": 0,
                                        "object": "subscription_item",
                                    }
                                ],
                                "due_invoices_count": 1,
                                "due_since": **********,
                                "total_dues": 500,
                                "mrr": 0,
                                "cf_stairs": "False",
                                "cf_assembly": "False",
                                "cf_free_partial_pickup": "False",
                                "meta_data": {
                                    "contract_id": 4458390516,
                                    "email": "<EMAIL>",
                                    "contact_id": 237151,
                                    "stage": "initial_payment",
                                },
                            },
                            "customer": {
                                "id": "237151",
                                "first_name": "María",
                                "last_name": "Lucía García",
                                "email": "<EMAIL>",
                                "auto_collection": "on",
                                "net_term_days": 0,
                                "allow_direct_debit": True,
                                "created_at": 1649333673,
                                "taxability": "taxable",
                                "updated_at": 1649335516,
                                "locale": "es-ES",
                                "pii_cleared": "active",
                                "channel": "web",
                                "resource_version": 1649335516397,
                                "deleted": False,
                                "object": "customer",
                                "billing_address": {
                                    "first_name": "María",
                                    "last_name": "Lucía García",
                                    "line1": "calle local",
                                    "city": "Ciudad Real",
                                    "state": "Ciudad Real",
                                    "country": "ES",
                                    "validation_status": "not_validated",
                                    "object": "billing_address",
                                },
                                "promotional_credits": 0,
                                "refundable_credits": 0,
                                "excess_payments": 0,
                                "unbilled_charges": 0,
                                "preferred_currency_code": "EUR",
                                "primary_payment_source_id": "pm_BTLWMPT2K3k4910F",
                                "payment_method": {
                                    "object": "payment_method",
                                    "type": "direct_debit",
                                    "reference_id": "CB_BTLWMPT2K3k3v10E/LHKCWGXR96KXWD82",
                                    "gateway": "adyen",
                                    "gateway_account_id": "gw_BTM9HQSq9oFEiWWD",
                                    "status": "valid",
                                },
                            },
                            "invoice": {
                                "id": "INV-042022-4106",
                                "customer_id": "237151",
                                "subscription_id": "BTLWMPT2K3kGa10X",
                                "recurring": True,
                                "status": "payment_due",
                                "price_type": "tax_inclusive",
                                "date": **********,
                                "due_date": **********,
                                "net_term_days": 0,
                                "exchange_rate": 0.91685,
                                "total": 500,
                                "amount_paid": 0,
                                "amount_adjusted": 0,
                                "write_off_amount": 0,
                                "credits_applied": 0,
                                "amount_due": 500,
                                "updated_at": 1649335517,
                                "resource_version": 1649335517756,
                                "deleted": False,
                                "object": "invoice",
                                "first_invoice": True,
                                "amount_to_collect": 0,
                                "round_off_amount": 0,
                                "new_sales_amount": 500,
                                "has_advance_charges": False,
                                "currency_code": "EUR",
                                "base_currency_code": "USD",
                                "generated_at": **********,
                                "is_gifted": False,
                                "term_finalized": True,
                                "channel": "web",
                                "tax": 87,
                                "line_items": [
                                    {
                                        "id": "li_BTLWMPT2K3kIj10b",
                                        "date_from": **********,
                                        "date_to": 1651874399,
                                        "unit_amount": 500,
                                        "quantity": 1,
                                        "amount": 500,
                                        "pricing_model": "flat_fee",
                                        "is_taxed": True,
                                        "tax_amount": 87,
                                        "tax_rate": 21,
                                        "object": "line_item",
                                        "subscription_id": "BTLWMPT2K3kGa10X",
                                        "customer_id": "237151",
                                        "description": "Pago de señal",
                                        "entity_type": "plan_item_price",
                                        "entity_id": "zero-plan-EUR-Monthly",
                                        "discount_amount": 0,
                                        "item_level_discount_amount": 0,
                                    }
                                ],
                                "taxes": [
                                    {
                                        "object": "tax",
                                        "name": "VAT",
                                        "description": "VAT @ 21%",
                                        "amount": 87,
                                    }
                                ],
                                "line_item_taxes": [
                                    {
                                        "tax_name": "VAT",
                                        "tax_rate": 21,
                                        "tax_juris_type": "country",
                                        "tax_juris_name": "Spain",
                                        "tax_juris_code": "ES",
                                        "object": "line_item_tax",
                                        "line_item_id": "li_BTLWMPT2K3kIj10b",
                                        "tax_amount": 87,
                                        "is_partial_tax_applied": False,
                                        "taxable_amount": 413,
                                        "is_non_compliance_tax": False,
                                    }
                                ],
                                "sub_total": 500,
                                "linked_payments": [
                                    {
                                        "txn_id": "txn_BTLWMPT2K3kLn10g",
                                        "applied_amount": 500,
                                        "applied_at": 1649335517,
                                        "txn_status": "in_progress",
                                        "txn_date": 1649335517,
                                        "txn_amount": 500,
                                    }
                                ],
                                "applied_credits": [],
                                "adjustment_credit_notes": [],
                                "issued_credit_notes": [],
                                "linked_orders": [],
                                "dunning_attempts": [
                                    {
                                        "created_at": 1649335517,
                                        "attempt": 0,
                                        "dunning_type": "direct_debit",
                                        "transaction_id": "txn_BTLWMPT2K3kLn10g",
                                        "txn_status": "in_progress",
                                        "txn_amount": 500,
                                    }
                                ],
                                "billing_address": {
                                    "first_name": "María",
                                    "last_name": "Lucía García",
                                    "line1": "calle local",
                                    "city": "Ciudad Real",
                                    "state": "Ciudad Real",
                                    "country": "ES",
                                    "validation_status": "not_validated",
                                    "object": "billing_address",
                                },
                            },
                        }
                    ],
                    next_offset=1,
                )
                for _ in client.list_subscriptions_by_ids(
                    ids=["BTLWMPT2K3kGa10Z", "BTLWMPT2K3kGa10X"],
                ):
                    mock_chargebee.return_value.Subscription.list.return_value = ListResult(
                        [
                            {
                                "subscription": {
                                    "id": "BTLWMPT2K3kGa10X",
                                    "billing_period": 1,
                                    "billing_period_unit": "month",
                                    "remaining_billing_cycles": 0,
                                    "customer_id": "237151",
                                    "status": "non_renewing",
                                    "current_term_start": **********,
                                    "current_term_end": 1651874399,
                                    "created_at": 1649335517,
                                    "started_at": **********,
                                    "activated_at": **********,
                                    "cancelled_at": 1651874400,
                                    "updated_at": 1649335517,
                                    "has_scheduled_changes": False,
                                    "cancel_schedule_created_at": 1649335517,
                                    "channel": "web",
                                    "resource_version": 1649335517808,
                                    "deleted": False,
                                    "object": "subscription",
                                    "currency_code": "EUR",
                                    "subscription_items": [
                                        {
                                            "item_price_id": "zero-plan-EUR-Monthly",
                                            "item_type": "plan",
                                            "quantity": 1,
                                            "unit_price": 500,
                                            "amount": 500,
                                            "free_quantity": 0,
                                            "billing_cycles": 0,
                                            "object": "subscription_item",
                                        }
                                    ],
                                    "due_invoices_count": 1,
                                    "due_since": **********,
                                    "total_dues": 500,
                                    "mrr": 0,
                                    "cf_stairs": "False",
                                    "cf_assembly": "False",
                                    "cf_free_partial_pickup": "False",
                                    "meta_data": {
                                        "contract_id": 4458390516,
                                        "email": "<EMAIL>",
                                        "contact_id": 237151,
                                        "stage": "initial_payment",
                                    },
                                },
                                "customer": {
                                    "id": "237151",
                                    "first_name": "María",
                                    "last_name": "Lucía García",
                                    "email": "<EMAIL>",
                                    "auto_collection": "on",
                                    "net_term_days": 0,
                                    "allow_direct_debit": True,
                                    "created_at": 1649333673,
                                    "taxability": "taxable",
                                    "updated_at": 1649335516,
                                    "locale": "es-ES",
                                    "pii_cleared": "active",
                                    "channel": "web",
                                    "resource_version": 1649335516397,
                                    "deleted": False,
                                    "object": "customer",
                                    "billing_address": {
                                        "first_name": "María",
                                        "last_name": "Lucía García",
                                        "line1": "calle local",
                                        "city": "Ciudad Real",
                                        "state": "Ciudad Real",
                                        "country": "ES",
                                        "validation_status": "not_validated",
                                        "object": "billing_address",
                                    },
                                    "promotional_credits": 0,
                                    "refundable_credits": 0,
                                    "excess_payments": 0,
                                    "unbilled_charges": 0,
                                    "preferred_currency_code": "EUR",
                                    "primary_payment_source_id": "pm_BTLWMPT2K3k4910F",
                                    "payment_method": {
                                        "object": "payment_method",
                                        "type": "direct_debit",
                                        "reference_id": "CB_BTLWMPT2K3k3v10E/LHKCWGXR96KXWD82",
                                        "gateway": "adyen",
                                        "gateway_account_id": "gw_BTM9HQSq9oFEiWWD",
                                        "status": "valid",
                                    },
                                },
                                "invoice": {
                                    "id": "INV-042022-4106",
                                    "customer_id": "237151",
                                    "subscription_id": "BTLWMPT2K3kGa10X",
                                    "recurring": True,
                                    "status": "payment_due",
                                    "price_type": "tax_inclusive",
                                    "date": **********,
                                    "due_date": **********,
                                    "net_term_days": 0,
                                    "exchange_rate": 0.91685,
                                    "total": 500,
                                    "amount_paid": 0,
                                    "amount_adjusted": 0,
                                    "write_off_amount": 0,
                                    "credits_applied": 0,
                                    "amount_due": 500,
                                    "updated_at": 1649335517,
                                    "resource_version": 1649335517756,
                                    "deleted": False,
                                    "object": "invoice",
                                    "first_invoice": True,
                                    "amount_to_collect": 0,
                                    "round_off_amount": 0,
                                    "new_sales_amount": 500,
                                    "has_advance_charges": False,
                                    "currency_code": "EUR",
                                    "base_currency_code": "USD",
                                    "generated_at": **********,
                                    "is_gifted": False,
                                    "term_finalized": True,
                                    "channel": "web",
                                    "tax": 87,
                                    "line_items": [
                                        {
                                            "id": "li_BTLWMPT2K3kIj10b",
                                            "date_from": **********,
                                            "date_to": 1651874399,
                                            "unit_amount": 500,
                                            "quantity": 1,
                                            "amount": 500,
                                            "pricing_model": "flat_fee",
                                            "is_taxed": True,
                                            "tax_amount": 87,
                                            "tax_rate": 21,
                                            "object": "line_item",
                                            "subscription_id": "BTLWMPT2K3kGa10X",
                                            "customer_id": "237151",
                                            "description": "Pago de señal",
                                            "entity_type": "plan_item_price",
                                            "entity_id": "zero-plan-EUR-Monthly",
                                            "discount_amount": 0,
                                            "item_level_discount_amount": 0,
                                        }
                                    ],
                                    "taxes": [
                                        {
                                            "object": "tax",
                                            "name": "VAT",
                                            "description": "VAT @ 21%",
                                            "amount": 87,
                                        }
                                    ],
                                    "line_item_taxes": [
                                        {
                                            "tax_name": "VAT",
                                            "tax_rate": 21,
                                            "tax_juris_type": "country",
                                            "tax_juris_name": "Spain",
                                            "tax_juris_code": "ES",
                                            "object": "line_item_tax",
                                            "line_item_id": "li_BTLWMPT2K3kIj10b",
                                            "tax_amount": 87,
                                            "is_partial_tax_applied": False,
                                            "taxable_amount": 413,
                                            "is_non_compliance_tax": False,
                                        }
                                    ],
                                    "sub_total": 500,
                                    "linked_payments": [
                                        {
                                            "txn_id": "txn_BTLWMPT2K3kLn10g",
                                            "applied_amount": 500,
                                            "applied_at": 1649335517,
                                            "txn_status": "in_progress",
                                            "txn_date": 1649335517,
                                            "txn_amount": 500,
                                        }
                                    ],
                                    "applied_credits": [],
                                    "adjustment_credit_notes": [],
                                    "issued_credit_notes": [],
                                    "linked_orders": [],
                                    "dunning_attempts": [
                                        {
                                            "created_at": 1649335517,
                                            "attempt": 0,
                                            "dunning_type": "direct_debit",
                                            "transaction_id": "txn_BTLWMPT2K3kLn10g",
                                            "txn_status": "in_progress",
                                            "txn_amount": 500,
                                        }
                                    ],
                                    "billing_address": {
                                        "first_name": "María",
                                        "last_name": "Lucía García",
                                        "line1": "calle local",
                                        "city": "Ciudad Real",
                                        "state": "Ciudad Real",
                                        "country": "ES",
                                        "validation_status": "not_validated",
                                        "object": "billing_address",
                                    },
                                },
                            }
                        ],
                        next_offset=None,
                    )
                    pass

                assert mock_chargebee.return_value.Subscription.list.mock_calls == [
                    call(
                        {
                            "id[in]": ["BTLWMPT2K3kGa10Z", "BTLWMPT2K3kGa10X"],
                            "limit": 100,
                            "offset": None,
                        },
                        env=mock_env.return_value,
                    ),
                    call(
                        {
                            "id[in]": ["BTLWMPT2K3kGa10Z", "BTLWMPT2K3kGa10X"],
                            "limit": 100,
                            "offset": 1,
                        },
                        env=mock_env.return_value,
                    ),
                ]

    def test_has_pending_invoices(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.has_pending_invoices(
                    subscription_id="198ay3SqMJo1O1WJ",
                )

                mock_chargebee.return_value.Invoice.list.assert_called_once_with(
                    {
                        "limit": 1,
                        "subscription_id[is]": "198ay3SqMJo1O1WJ",
                        "status[is_not]": "paid",
                    },
                    env=mock_env.return_value,
                )

    def test_receive_pdf_credit_note(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.retrieve_pdf_credit_note(
                    credit_note_id="50",
                )

                mock_chargebee.return_value.CreditNote.pdf.assert_called_once_with(
                    "50", {"disposition_type": "attachment"}, env=mock_env.return_value
                )

    def test_customer_assign_payment_role(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.assign_payment_role(
                    customer_id="BTM9F0SpmlB7656t",
                    payment_source_id="pm_199YXFTA9Rcb1ChX",
                )

                mock_chargebee.return_value.Customer.assign_payment_role.assert_called_once_with(
                    "BTM9F0SpmlB7656t",
                    {"payment_source_id": "pm_199YXFTA9Rcb1ChX", "role": "primary"},
                    env=mock_env.return_value,
                )

    def test_get_subscription(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.get_subscription(
                    subscription_id="198ay3SqMJo1O1WJ",
                )

                mock_chargebee.return_value.Subscription.retrieve.assert_called_once_with(
                    "198ay3SqMJo1O1WJ",
                    env=mock_env.return_value,
                )

    def test_get_transaction(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.get_transaction(
                    transaction_id="txn_19ANPFTDsMqxoCIKL",
                )

                mock_chargebee.return_value.Transaction.retrieve.assert_called_once_with(
                    "txn_19ANPFTDsMqxoCIKL",
                    env=mock_env.return_value,
                )

    def test_create_invoice_charge(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_invoice_charge(
                    customer_id="238651",
                    item_prices=[{"item_price_id": "fee-EUR", "unit_price": 100}],
                )

                mock_chargebee.return_value.Invoice.create_for_charge_items_and_charges.assert_called_once_with(
                    {
                        "customer_id": "238651",
                        "item_prices": [
                            {"item_price_id": "fee-EUR", "unit_price": 100}
                        ],
                    },
                    env=mock_env.return_value,
                )

    def test_get_invoice(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.get_invoice(
                    invoice_id="INV-082022-1010",
                )

                mock_chargebee.return_value.Invoice.retrieve.assert_called_once_with(
                    "INV-082022-1010",
                    env=mock_env.return_value,
                )

    def test_change_term_end(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.change_term_end(
                    subscription_id="198ay3SqMJo1O1WJ", term_ends_at=1613413800
                )

                mock_chargebee.return_value.Subscription.change_term_end.assert_called_once_with(
                    "198ay3SqMJo1O1WJ",
                    {"term_ends_at": 1613413800},
                    env=mock_env.return_value,
                )

    def test_create_comment_in_quote(self, client):
        with patch(
            "network_clients.chargebee.client.ChargebeeClient.chargebee_sdk",
            new_callable=PropertyMock,
        ) as mock_chargebee:
            with patch("network_clients.chargebee.client.Environment") as mock_env:
                client.create_comment_in_quote(
                    quote_id="198ay3SqMJo1O1WJ", comment="This is a comment"
                )

                mock_chargebee.return_value.Comment.create.assert_called_once_with(
                    {
                        "entity_id": "198ay3SqMJo1O1WJ",
                        "entity_type": "quote",
                        "notes": "This is a comment",
                    },
                    env=mock_env.return_value,
                )
