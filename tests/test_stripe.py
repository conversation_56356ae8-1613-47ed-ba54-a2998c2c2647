import datetime
from unittest.mock import PropertyMock, patch

import pytest

from network_clients.stripe.stripe import StripeClient, SubscriptionItem


@pytest.fixture
def client():
    return StripeClient(api_key="fake_key")


class TestStripeClient:
    def test_stripe_client_initialized(self):
        stripe_client = StripeClient(api_key=None)
        assert stripe_client.stripe_sdk.api_key is None

        stripe_client = StripeClient(api_key="fake_key")
        assert stripe_client.stripe_sdk.api_key is not None

    def test_create_customer_ok(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.create_customer(
                    user_email="<EMAIL>",
                    user_full_name="Calimero Molon",
                    address="Calle Los alamos",
                    postal_code="28001",
                    city="Madrid",
                    state="Madrid",
                    country="ES",
                )

                mock_stripe.return_value.Customer.create.assert_called_once_with(
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    address={
                        "city": "Madrid",
                        "country": "ES",
                        "line1": "Calle Los alamos",
                        "postal_code": "28001",
                        "state": "Madrid",
                    },
                    name="Calimero Molon",
                    email="<EMAIL>",
                    preferred_locales=["ES"],
                )

    def test_create_customer_with_extra_kwargs(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.create_customer(
                    user_email="<EMAIL>",
                    user_full_name="Calimero Molon",
                    address="Calle Los alamos",
                    postal_code="28001",
                    city="Madrid",
                    state="Madrid",
                    country="ES",
                    phone="600100200",
                )

                mock_stripe.return_value.Customer.create.assert_called_once_with(
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    address={
                        "city": "Madrid",
                        "country": "ES",
                        "line1": "Calle Los alamos",
                        "postal_code": "28001",
                        "state": "Madrid",
                    },
                    name="Calimero Molon",
                    email="<EMAIL>",
                    preferred_locales=["ES"],
                    phone="600100200",
                )

    def test_update_customer(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.update_customer(
                    customer_id="cus_KPaZRf9h3JGDQt",
                    user_full_name="Calimero Molon",
                )

                mock_stripe.return_value.Customer.modify.assert_called_once_with(
                    "cus_KPaZRf9h3JGDQt",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    user_full_name="Calimero Molon",
                )

    def test_get_customer(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            client.get_customer(
                customer_id="cus_KPaZRf9h3JGDQt",
            )

            mock_stripe.return_value.Customer.retrieve.assert_called_once_with(
                "cus_KPaZRf9h3JGDQt",
            )

    def test_delete_customer(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            client.delete_customer(
                customer_id="cus_KPaZRf9h3JGDQt",
            )

            mock_stripe.return_value.Customer.delete.assert_called_once_with(
                "cus_KPaZRf9h3JGDQt",
            )

    def test_create_subscription(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.create_subscription(
                    customer_id="cus_KPaZRf9h3JGDQt",
                    recurring_price_id_items=[
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=1
                        ).__dict__
                    ],
                    one_price_id_items=[
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=3
                        ).__dict__
                    ],
                    cancel_at_period_end=False,
                    tax="txr_1JbiBHF2cafxNCYjwT5huVv5",
                    coupon="Hg38Pwpz",
                    metadata={"service": "5m2"},
                    trial_period_days=30,
                )

                mock_stripe.return_value.Subscription.create.assert_called_once_with(
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    customer="cus_KPaZRf9h3JGDQt",
                    items=[{"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 1}],
                    add_invoice_items=[
                        {"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 3}
                    ],
                    cancel_at_period_end=False,
                    proration_behavior="none",
                    off_session=True,
                    expand=["latest_invoice.payment_intent", "latest_invoice"],
                    default_tax_rates=["txr_1JbiBHF2cafxNCYjwT5huVv5"],
                    coupon="Hg38Pwpz",
                    metadata={"service": "5m2"},
                    trial_period_days=30,
                )

    def test_get_subscription(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            client.get_subscription(
                subscription_id="sub_1Jmh3zF2cafxNCYjBzHxVAmj",
            )

            mock_stripe.return_value.Subscription.retrieve.assert_called_once_with(
                "sub_1Jmh3zF2cafxNCYjBzHxVAmj",
            )

    def test_apply_subscription(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.apply_coupon(
                    subscription_id="sub_1Jmh3zF2cafxNCYjBzHxVAmj", coupon_id="QNfkjI9M"
                )

                mock_stripe.return_value.Subscription.modify.assert_called_once_with(
                    "sub_1Jmh3zF2cafxNCYjBzHxVAmj",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    coupon="QNfkjI9M",
                )

    def test_update_subscription(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.update_subscription(
                    subscription_id="sub_1Jmh3zF2cafxNCYjBzHxVAmj",
                    price_id_items=[
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=2
                        ).__dict__
                    ],
                    billing_cycle_anchor=1634756891,
                    proration_behavior="none",
                )

                mock_stripe.return_value.Subscription.modify.assert_called_once_with(
                    "sub_1Jmh3zF2cafxNCYjBzHxVAmj",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    items=[{"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 2}],
                    billing_cycle_anchor=1634756891,
                    proration_behavior="none",
                )

    def test_cancel_subscription_at_period_end(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            client.cancel_subscription(
                subscription_id="sub_1Jmh3zF2cafxNCYjBzHxVAmj",
            )

            mock_stripe.return_value.Subscription.modify.assert_called_once_with(
                "sub_1Jmh3zF2cafxNCYjBzHxVAmj", cancel_at_period_end=True
            )

    def test_cancel_subscription_now(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            client.cancel_subscription(
                subscription_id="sub_1Jmh3zF2cafxNCYjBzHxVAmj", now=True
            )

            mock_stripe.return_value.Subscription.delete.assert_called_once_with(
                "sub_1Jmh3zF2cafxNCYjBzHxVAmj"
            )

    def test_attach_payment_method(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.attach_payment_method_to_customer(
                    payment_method_id="pm_1JklO0F2cafxNCYjxrG39Zyg",
                    customer_id="cus_KPaZRf9h3JGDQt",
                )

                mock_stripe.return_value.PaymentMethod.attach.assert_called_once_with(
                    "pm_1JklO0F2cafxNCYjxrG39Zyg",
                    customer="cus_KPaZRf9h3JGDQt",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                )

    def test_detach_payment_method(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.detach_payment_method(
                    payment_method_id="pm_1JklO0F2cafxNCYjxrG39Zyg",
                )

                mock_stripe.return_value.PaymentMethod.detach.assert_called_once_with(
                    "pm_1JklO0F2cafxNCYjxrG39Zyg",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                )

    def test_create_quote(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                expires_at = int(datetime.datetime(2021, 10, 20).timestamp() * 1000)
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.create_quote(
                    customer_id="cus_KPaZRf9h3JGDQt",
                    line_items=[
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=1
                        ).__dict__,
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=3
                        ).__dict__,
                    ],
                    tax="txr_1JbiBHF2cafxNCYjwT5huVv5",
                    coupon="Hg38Pwpz",
                    metadata={"service": "5m2"},
                    expires_at=expires_at,
                    finalize_quote_automatically=False,
                )

                mock_stripe.return_value.Quote.create.assert_called_once_with(
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    customer="cus_KPaZRf9h3JGDQt",
                    line_items=[
                        {"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 1},
                        {"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 3},
                    ],
                    expires_at=expires_at,
                    metadata={"service": "5m2"},
                    default_tax_rates=["txr_1JbiBHF2cafxNCYjwT5huVv5"],
                    discounts=[{"coupon": "Hg38Pwpz"}],
                )

    def test_create_quote_finalizing_automatically(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_stripe.return_value.Quote.create.return_value = {
                    "id": "qt_1JnMsWF2cafxNCYjs3B14eVr",
                    "object": "quote",
                    "amount_subtotal": 6300,
                    "amount_total": 6300,
                    "application_fee_amount": None,
                    "application_fee_percent": None,
                    "automatic_tax": {"enabled": False, "status": None},
                    "collection_method": "charge_automatically",
                    "computed": {
                        "recurring": {
                            "amount_subtotal": 2900,
                            "amount_total": 2900,
                            "interval": "month",
                            "interval_count": 1,
                            "total_details": {
                                "amount_discount": 0,
                                "amount_shipping": 0,
                                "amount_tax": 503,
                            },
                        },
                        "upfront": {
                            "amount_subtotal": 6300,
                            "amount_total": 6300,
                            "total_details": {
                                "amount_discount": 0,
                                "amount_shipping": 0,
                                "amount_tax": 1094,
                            },
                        },
                    },
                    "created": 1634905784,
                    "currency": "eur",
                    "customer": "cus_KPaZRf9h3JGDQt",
                    "default_tax_rates": ["txr_1Jbi1IF2cafxNCYjEqREgHlh"],
                    "description": None,
                    "discounts": [],
                    "expires_at": 1637497784,
                    "footer": None,
                    "from_quote": {
                        "is_revision": True,
                        "quote": "qt_1JnMsAF2cafxNCYja0gOihWp",
                    },
                    "header": None,
                    "invoice": "in_1JnMt4F2cafxNCYjiswedI19",
                    "invoice_settings": {"days_until_due": None},
                    "livemode": False,
                    "metadata": {},
                    "number": "QT-50B8D2B7-0003-2",
                    "on_behalf_of": None,
                    "status": "accepted",
                    "status_transitions": {
                        "accepted_at": 1634905818,
                        "canceled_at": None,
                        "finalized_at": 1634905800,
                    },
                    "subscription": "sub_1JnMt4F2cafxNCYjozZ8vXv9",
                    "subscription_data": {
                        "effective_date": None,
                        "trial_period_days": None,
                    },
                    "subscription_schedule": None,
                    "total_details": {
                        "amount_discount": 0,
                        "amount_shipping": 0,
                        "amount_tax": 1094,
                    },
                    "transfer_data": None,
                }
                expires_at = int(datetime.datetime(2021, 10, 20).timestamp() * 1000)
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.create_quote(
                    customer_id="cus_KPaZRf9h3JGDQt",
                    line_items=[
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=1
                        ).__dict__,
                        SubscriptionItem(
                            price="price_1JmhT1F2cafxNCYjKum3poYw", quantity=3
                        ).__dict__,
                    ],
                    tax="txr_1JbiBHF2cafxNCYjwT5huVv5",
                    coupon="Hg38Pwpz",
                    metadata={"service": "5m2"},
                    expires_at=expires_at,
                    finalize_quote_automatically=True,
                )

                mock_stripe.return_value.Quote.create.assert_called_once_with(
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    customer="cus_KPaZRf9h3JGDQt",
                    line_items=[
                        {"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 1},
                        {"price": "price_1JmhT1F2cafxNCYjKum3poYw", "quantity": 3},
                    ],
                    expires_at=expires_at,
                    metadata={"service": "5m2"},
                    default_tax_rates=["txr_1JbiBHF2cafxNCYjwT5huVv5"],
                    discounts=[{"coupon": "Hg38Pwpz"}],
                )

                mock_stripe.return_value.Quote.finalize_quote.assert_called_once_with(
                    "qt_1JnMsWF2cafxNCYjs3B14eVr",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                )

    def test_finalize_quote(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.finalize_quote(
                    quote_id="qt_1JnMsWF2cafxNCYjs3B14eVr",
                )

                mock_stripe.return_value.Quote.finalize_quote.assert_called_once_with(
                    "qt_1JnMsWF2cafxNCYjs3B14eVr",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                )

    def test_create_coupon(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.create_coupon(
                    id="discount_coupon",
                    amount_off=100,
                    percent_off=15,
                    name="coupon_name",
                    currency="eur",
                )

                mock_stripe.return_value.Coupon.create.assert_called_once_with(
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                    duration="once",
                    id="discount_coupon",
                    name="coupon_name",
                    percent_off=None,
                    amount_off=100,
                    currency="eur",
                )

    def test_retrieve_coupon(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                client.retrieve_coupon("coupon_discount")

                mock_stripe.return_value.Coupon.retrieve.assert_called_once_with(
                    "coupon_discount"
                )

    def test_get_customer_taxes(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            mock_stripe.return_value.Customer.list_tax_ids.return_value = {
                "data": [
                    {
                        "country": "ES",
                        "created": 1636540417,
                        "customer": "cus_KWVQUKhDM0ZnZY",
                        "id": "txi_1JuE7ZF2cafxNCYj5FLy47sA",
                        "livemode": False,
                        "object": "tax_id",
                        "type": "eu_vat",
                        "value": "ES06100200R",
                        "verification": {
                            "status": "pending",
                            "verified_address": None,
                            "verified_name": None,
                        },
                    }
                ],
                "has_more": False,
                "object": "list",
                "url": "/v1/customers/cus_KWVQUKhDM0ZnZY/tax_ids",
            }

            customer_tax = client.get_customer_taxes(
                "cus_KWVQUKhDM0ZnZY", only_first=True
            )
            assert customer_tax == {
                "country": "ES",
                "created": 1636540417,
                "customer": "cus_KWVQUKhDM0ZnZY",
                "id": "txi_1JuE7ZF2cafxNCYj5FLy47sA",
                "livemode": False,
                "object": "tax_id",
                "type": "eu_vat",
                "value": "ES06100200R",
                "verification": {
                    "status": "pending",
                    "verified_address": None,
                    "verified_name": None,
                },
            }

            customer_tax = client.get_customer_taxes(
                "cus_KWVQUKhDM0ZnZY", only_first=False
            )
            assert customer_tax == [
                {
                    "country": "ES",
                    "created": 1636540417,
                    "customer": "cus_KWVQUKhDM0ZnZY",
                    "id": "txi_1JuE7ZF2cafxNCYj5FLy47sA",
                    "livemode": False,
                    "object": "tax_id",
                    "type": "eu_vat",
                    "value": "ES06100200R",
                    "verification": {
                        "status": "pending",
                        "verified_address": None,
                        "verified_name": None,
                    },
                }
            ]

    def test_get_customer_taxes_ko(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            mock_stripe.return_value.Customer.list_tax_ids.return_value = {"data": None}

            customer_tax = client.get_customer_taxes("cus_KWVQUKhDM0ZnZY")
            assert customer_tax is None

    def test_update_customer_tax_same(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                mock_stripe.return_value.Customer.list_tax_ids.return_value = {
                    "data": [
                        {
                            "country": "ES",
                            "created": 1636540417,
                            "customer": "cus_KWVQUKhDM0ZnZY",
                            "id": "txi_1JuE7ZF2cafxNCYj5FLy47sA",
                            "livemode": False,
                            "object": "tax_id",
                            "type": "eu_vat",
                            "value": "ES06100200R",
                            "verification": {
                                "status": "pending",
                                "verified_address": None,
                                "verified_name": None,
                            },
                        }
                    ],
                    "has_more": False,
                    "object": "list",
                    "url": "/v1/customers/cus_KWVQUKhDM0ZnZY/tax_ids",
                }

                client.update_customer_tax(
                    "cus_KWVQUKhDM0ZnZY", "eu_vat", "ES06100200R"
                )

                assert not mock_stripe.return_value.Customer.create_tax_id.called

    def test_update_customer_tax(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                mock_stripe.return_value.Customer.list_tax_ids.return_value = {
                    "data": [
                        {
                            "country": "ES",
                            "created": 1636540417,
                            "customer": "cus_KWVQUKhDM0ZnZY",
                            "id": "txi_1JuE7ZF2cafxNCYj5FLy47sA",
                            "livemode": False,
                            "object": "tax_id",
                            "type": "eu_vat",
                            "value": "ES06100200R",
                            "verification": {
                                "status": "pending",
                                "verified_address": None,
                                "verified_name": None,
                            },
                        }
                    ],
                    "has_more": False,
                    "object": "list",
                    "url": "/v1/customers/cus_KWVQUKhDM0ZnZY/tax_ids",
                }

                client.update_customer_tax(
                    "cus_KWVQUKhDM0ZnZY", "eu_vat", "ES06300200R"
                )

                mock_stripe.return_value.Customer.create_tax_id.assert_called_once_with(
                    "cus_KWVQUKhDM0ZnZY",
                    type="eu_vat",
                    value="ES06300200R",
                    idempotency_key="ca9090e9fcab4ad0a3c86284dcaadbc1",
                )

                mock_stripe.return_value.Customer.delete_tax_id.assert_called_once_with(
                    "cus_KWVQUKhDM0ZnZY", "txi_1JuE7ZF2cafxNCYj5FLy47sA"
                )

    def test_delete_customer_tax(self, client):
        with patch(
            "network_clients.stripe.stripe.StripeClient.stripe_sdk",
            new_callable=PropertyMock,
        ) as mock_stripe:
            with patch("uuid.uuid4") as mock_uuid:
                mock_uuid.return_value.hex = "ca9090e9fcab4ad0a3c86284dcaadbc1"
                mock_stripe.return_value.Customer.list_tax_ids.return_value = {
                    "data": [
                        {
                            "country": "ES",
                            "created": 1636540417,
                            "customer": "cus_KWVQUKhDM0ZnZY",
                            "id": "txi_1JuE7ZF2cafxNCYj5FLy47sA",
                            "livemode": False,
                            "object": "tax_id",
                            "type": "eu_vat",
                            "value": "ES06100200R",
                            "verification": {
                                "status": "pending",
                                "verified_address": None,
                                "verified_name": None,
                            },
                        }
                    ],
                    "has_more": False,
                    "object": "list",
                    "url": "/v1/customers/cus_KWVQUKhDM0ZnZY/tax_ids",
                }

                client.delete_customer_tax("cus_KWVQUKhDM0ZnZY")

                mock_stripe.return_value.Customer.delete_tax_id.assert_called_once_with(
                    "cus_KWVQUKhDM0ZnZY", "txi_1JuE7ZF2cafxNCYj5FLy47sA"
                )
