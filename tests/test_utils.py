from unittest.mock import patch

from network_clients.chargebee.client import ChargebeeClient

from app.utils import get_chargebee_instance
from conf import get_settings


class TestUtils:
    def test_get_chargebee_instance(self):
        with patch("app.utils.ChargebeeClient", spec=ChargebeeClient) as client:
            settings = get_settings()
            get_chargebee_instance("ES", settings)

            client.assert_called_once_with(api_key=settings.CHARGEBEE_API_KEY_ES, site=settings.CHARGEBEE_SITE_ES)
