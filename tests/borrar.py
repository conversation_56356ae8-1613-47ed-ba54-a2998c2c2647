from io import BytesIO

import torch
import torchaudio
# if __name__ == '__main__':
    # model, utils = torch.hub.load(repo_or_dir='snakers4/silero-vad',
    #                               model='silero_vad',
    #                               force_reload=False,
    #                               onnx=True)
    #
    # (get_speech_timestamps,
    #  save_audio,
    #  read_audio,
    #  VADIterator,
    #  collect_chunks) = utils
    #
    # with open('/home/<USER>/Documentos/audioSAMPLEMICRO.wav', "rb") as fp:
    #     buffer = BytesIO(fp.read())
    #
    # wav = read_audio(buffer, sampling_rate=16000)
    # speech_timestamps = get_speech_timestamps(wav, model, threshold=0.75, sampling_rate=16000)
    #
    # buffer_save = BytesIO()
    # torchaudio.save(buffer_save,
    #            collect_chunks(speech_timestamps, wav).unsqueeze(0), 16000, bits_per_sample=16, format="wav")
    # print(collect_chunks(speech_timestamps, wav).unsqueeze(0))
    #
    # with open('/home/<USER>/Documentos/audtioSalida.wav', "wb") as fp:
    #     fp.write(buffer_save.getbuffer().tobytes())
    #
    # # wav = read_audio('/home/<USER>/Documentos/audioSAMPLEJEJEJE2.wav', sampling_rate=16000)
    # # speech_timestamps = get_speech_timestamps(wav, model, threshold=0.75, sampling_rate=16000)
    # # save_audio('/home/<USER>/Documentos/only_speech.wav',
    # #            collect_chunks(speech_timestamps, wav), sampling_rate=16000)
    # # print(speech_timestamps)


import pandas as pd
df = pd.read_json("/tmp/2023-09-04 18:26:45.371525.json")
print(df)