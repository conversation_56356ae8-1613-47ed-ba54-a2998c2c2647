import datetime
import json
from unittest import mock
from unittest.mock import patch

import pytest

from network_clients.slack.client import Slack<PERSON>lient


@pytest.fixture
def client():
    return SlackClient()


def mock_requests_factory(response_stub, status_code=200):
    return mock.Mock(
        **{
            "json.return_value": json.loads(response_stub)
            if isinstance(response_stub, str)
            else response_stub,
            "text.return_value": response_stub,
            "status_code": status_code,
            "ok": status_code == 200,
        }
    )


class TestSlackClient:
    def test_notify_lead(self, client):
        """Perform a valid request and retrieve the response"""

        with patch("network_clients.slack.client.HTTPClient.request") as mock_request:
            mock_request.return_value = mock_requests_factory({"msg": "ok"})
            submit_date = datetime.datetime.now().strftime("%d/%m/%Y - %H:%M")

            response = client.notify_lead(
                odoo_url="https://fake.com",
                user_name="Calimero Molon",
                device="Desktop",
                country="Spain",
                city="Murcia",
                submit_date=submit_date,
                email="<EMAIL>",
                phone="+34600100200",
            )

            assert response.status_code == 200
            assert response.json() == {"msg": "ok"}
            mock_request.assert_called_once_with(
                endpoint=client.hook_url,
                method="POST",
                json={
                    "blocks": [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "Nuevo lead:\n*<https://fake.com|Calimero Molon>*",
                            },
                        },
                        {
                            "type": "section",
                            "fields": [
                                {"type": "mrkdwn", "text": "*Dispositivo:*\nDesktop"},
                                {"type": "mrkdwn", "text": f"*Fecha:*\n{submit_date}"},
                                {"type": "mrkdwn", "text": "*País:*\nSpain"},
                                {"type": "mrkdwn", "text": "*Ciudad:*\nMurcia"},
                                {
                                    "type": "mrkdwn",
                                    "text": "*Correo electrónico:*\<EMAIL>",
                                },
                                {"type": "mrkdwn", "text": "*Teléfono:*\n+34600100200"},
                            ],
                        },
                    ]
                },
                headers={"Content-Type": "application/json"},
            )

    def test_notify_pickup_or_deliver_order(self, client):
        """Perform a valid request and retrieve the response"""

        with patch("network_clients.slack.client.HTTPClient.request") as mock_request:
            mock_request.return_value = mock_requests_factory({"msg": "ok"})
            submit_date = datetime.datetime.now().strftime("%d/%m/%Y")

            response = client.notify_pickup_or_deliver_order(
                contract_id="BOX-ES-13455",
                date=submit_date,
                address="Calle Los álamos verdes, 21, 4ºB, Madrid (España)",
                user="Calimero Molón",
                email="<EMAIL>",
                order_type="pick_up",
            )

            assert response.status_code == 200
            assert response.json() == {"msg": "ok"}
            mock_request.assert_called_once_with(
                endpoint=client.hook_url,
                method="POST",
                json={
                    "blocks": [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "Hola equipo :wave:! Hay una nueva solicitud de *pick_up*\n\n *Datos de la solicitud:*",
                            },
                        },
                        {"type": "divider"},
                        {
                            "type": "section",
                            "fields": [
                                {
                                    "type": "mrkdwn",
                                    "text": f">>>*Contrato BOX-ES-13455*\n:man_and_woman_holding_hands: Calimero Molón\n:incoming_envelope: <EMAIL>\n:calendar: {submit_date}\n:truck: Calle Los álamos verdes, 21, 4ºB, Madrid (España)",
                                }
                            ],
                        },
                    ]
                },
                headers={"Content-Type": "application/json"},
            )
