import asyncio
import urllib.parse

import pytest
import pytest_asyncio
from mongomock_motor import AsyncMongoMockClient

import conf
from app.db.base import CRUDMixin
from app.db.mongodb import MongoBackend
from app.models.mongo import MongoResponse


@pytest.fixture(scope="session")
def settings():
    return conf.settings()


@pytest.fixture(scope="session")
def mongo_client(settings):
    mongo_db_uri = f"mongodb://{settings.MONGODB_USER}:{urllib.parse.quote_plus(settings.MONGODB_PASSWORD)}@localhost:27017/moreapp"
    return AsyncMongoMockClient(mongo_db_uri)


@pytest.fixture(scope="session")
def mongo_backend(mongo_client):
    yield MongoBackend(mongo_client)


# FIX: https://github.com/tortoise/tortoise-orm/issues/638
@pytest.fixture(scope="session")
def event_loop():
    return asyncio.get_event_loop()


@pytest_asyncio.fixture(scope="session", autouse=True)
async def populate_mongo_database(mongo_client, settings):
    collection = mongo_client[settings.MONGODB_DATABASE][settings.MONGODB_COLLECTION_1001]
    await collection.insert_many(
        [
            {
                "EnterDate": "2022-07-14",
                "Contract_id": 503390920522,
                "ObjectCode": "B2BQ006122",
                "ObjectDescription": "C14",
                "DeliveryDate": "2022-07-22",
                "Name": "Pepe Botella",
            },
            {
                "EnterDate": "2022-07-14",
                "Contract_id": 503390920522,
                "ObjectCode": "B2BQ006122",
                "ObjectDescription": "C13",
                "DeliveryDate": "2022-07-22",
                "Name": "Pepe Botella2",
            },
            {
                "EnterDate": "2022-07-14",
                "Contract_id": 503390920522,
                "ObjectCode": "B2BQ006122",
                "ObjectDescription": "C12",
                "DeliveryDate": "2022-07-22",
                "Name": "Pepe Botella3",
            },
            {
                "EnterDate": "2022-07-14",
                "Contract_id": 503390920522,
                "ObjectCode": "B2BQ006122",
                "ObjectDescription": "C11",
                "DeliveryDate": None,
                "Name": "Pepe Botella3",
            },
        ]
    )


@pytest.fixture
def crud_mixin():
    return CRUDMixin()


class TestBasedb:
    def test_get_all_inventory(self, crud_mixin):
        with pytest.raises(NotImplementedError):
            crud_mixin.get_all_inventory(1000)


class TestMongo:
    @pytest.mark.asyncio
    async def test_get_all_inventory(self, mongo_backend, settings):
        data = await mongo_backend.get_all_inventory("1001", settings)

        for item in data:
            assert (
                item
                == MongoResponse(
                    id=item.get("id"),
                    ObjectCode=item.get("ObjectCode"),
                    Contract_id=item.get("Contract_id"),
                    ObjectDescription=item.get("ObjectDescription"),
                    EnterDate=item.get("EnterDate"),
                    DeliveryDate=item.get("DeliveryDate"),
                    Name=item.get("Name"),
                ).__dict__
            )

        assert len(data) == 1
