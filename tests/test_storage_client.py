import datetime
from io import By<PERSON><PERSON>
from unittest import mock
from unittest.mock import patch

import pytest
from google.api_core.exceptions import NotFound
from google.pubsub_v1 import Publisher<PERSON><PERSON>

from network_clients.base.base import ClientError
from network_clients.base.storage_client import StorageClient


@pytest.fixture
def client():
    def _client(
        service_account_json="service_account_json",
        google_cloud_project_name="gcloud_project_name",
    ):
        return StorageClient(
            service_account_json=service_account_json,
        )

    return _client


class TestPubsubClient:
    def test_lazy_initialization_ok(self, client):
        """Check that client is initialized lazily and new calls to this method retrieves the same instance"""

        # Init factory fixture
        client = client()

        with patch(
            "network_clients.base.storage_client.storage.Client.from_service_account_json",
            spec=PublisherClient,
        ) as _:
            assert client._client is None
            first_client = client.client
            assert client._client is not None
            second_client = client.client

            assert id(first_client) == id(second_client)

    def test_lazy_initialization_credentials_error(self, client):
        file_error_client = client("service_account_json")
        with pytest.raises(ClientError) as error:
            _ = file_error_client.client

        assert (
            str(error.value)
            == "Wrong `service_account_json` credentials to init the storage client"
        )

        type_error_client = client(None)
        with pytest.raises(ClientError) as error:
            _ = type_error_client.client

        assert str(error.value) == "Wrong `None` credentials to init the storage client"

    def test_upload_file(self, client):
        """Upload a file to remote and check that everything goes well"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        source_file_in_bytes = BytesIO(b"fake")
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            _ = client.upload_file(
                bucket_name=bucket_name,
                file=source_file_in_bytes,
                file_name=destination_blob_name,
                content_type="application/pdf",
            )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.upload_from_string.assert_called_once_with(
            source_file_in_bytes, content_type="application/pdf"
        )

    def test_download_file(self, client):
        """Download a file to remote and check that everything goes well"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            _ = client.download_file(
                bucket_name=bucket_name,
                file_name=destination_blob_name,
            )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.download_to_file.assert_called_once_with(
            mock.ANY
        )

    def test_upload_file_not_found(self, client):
        """Perform a wrong request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        source_file_in_bytes = BytesIO(b"fake")
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.upload_from_string.side_effect = NotFound(
                message="Not found error"
            )

            with pytest.raises(ClientError) as error:
                _ = client.upload_file(
                    bucket_name=bucket_name,
                    file=source_file_in_bytes,
                    file_name=destination_blob_name,
                    content_type="application/pdf",
                )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.upload_from_string.assert_called_once_with(
            source_file_in_bytes, content_type="application/pdf"
        )
        assert str(error.value) == f"Bucket {bucket_name} not found on project"

    def test_download_file_not_found(self, client):
        """Perform a wrong request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.download_to_file.side_effect = NotFound(
                message="Not found error"
            )

            with pytest.raises(ClientError) as error:
                _ = client.download_file(
                    bucket_name=bucket_name,
                    file_name=destination_blob_name,
                )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.download_to_file.assert_called_once_with(
            mock.ANY
        )
        assert str(error.value) == f"Bucket {bucket_name} not found on project"

    def test_download_file_exception(self, client):
        """Perform a wrong request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.download_to_file.side_effect = Exception(
                "Unknown exception raised"
            )

            with pytest.raises(ClientError) as error:
                _ = client.download_file(
                    bucket_name=bucket_name,
                    file_name=destination_blob_name,
                )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.download_to_file.assert_called_once_with(
            mock.ANY
        )
        assert (
            str(error.value)
            == f"Unknown exception downloading object from bucket {bucket_name}: Unknown exception raised"
        )

    def test_general_exception(self, client):
        """Perform a wrong request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        source_file_in_bytes = BytesIO(b"fake")
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.upload_from_string.side_effect = (
                Exception()
            )

            with pytest.raises(ClientError) as error:
                _ = client.upload_file(
                    bucket_name=bucket_name,
                    file=source_file_in_bytes,
                    file_name=destination_blob_name,
                    content_type="application/pdf",
                )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.upload_from_string.assert_called_once_with(
            source_file_in_bytes, content_type="application/pdf"
        )
        assert (
            str(error.value)
            == "Unknown exception uploading object to bucket bucket_name: "
        )

    def test_get_signed_object(self, client):
        """Perform a valid request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        destination_blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            _ = client.get_signed_object(
                bucket_name=bucket_name, blob_name=destination_blob_name
            )

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.generate_signed_url.assert_called_once_with(
            version="v4",
            # This URL is valid for 15 minutes
            expiration=datetime.timedelta(seconds=604800),
            # Allow GET requests using this URL.
            method="GET",
        )

    def test_delete_file(self, client):
        """Perform a valid request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            _ = client.delete_file(bucket_name=bucket_name, blob_name=blob_name)

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.delete.assert_called_once_with()

    def test_delete_file_not_found(self, client):
        """Perform a valid request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.delete.side_effect = NotFound(
                message="Not found error"
            )

            with pytest.raises(ClientError) as error:
                _ = client.delete_file(bucket_name=bucket_name, blob_name=blob_name)

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.delete.assert_called_once_with()

        assert str(error.value) == f"Bucket {bucket_name} not found on project"

    def test_delete_file_exception(self, client):
        """Perform a valid request and retrieve the response"""

        # Init factory fixture
        client = client()

        bucket_name = "bucket_name"
        blob_name = "/file.pdf"
        with patch("network_clients.base.storage_client.storage") as mock_storage:
            mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.delete.side_effect = Exception(
                "Unknown exception raised"
            )

            with pytest.raises(ClientError) as error:
                _ = client.delete_file(bucket_name=bucket_name, blob_name=blob_name)

        mock_storage.Client.from_service_account_json.return_value.bucket.return_value.blob.return_value.delete.assert_called_once_with()

        assert (
            str(error.value)
            == f"Unknown exception deleting object in bucket {bucket_name}: Unknown exception raised"
        )
