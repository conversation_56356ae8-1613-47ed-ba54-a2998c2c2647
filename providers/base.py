import base64
import datetime

import requests
from google import auth
from requests import RequestException


class EmailProvider:
    FROM_EMAIL = "<EMAIL>"

    def send(self, data):
        raise NotImplementedError("Not implemented yet!")


class EmailSenderMixin:
    SENDERS = {
        "ES": "BOX2BOX SPAIN",
        "PT": "BOX2BOX PORTUGAL",
        "FR": "BOX2BOX FRANCE",
        "IT": "BOX2BOX ITALIA",
        "MX": "BOX2BOX MEXICO",
    }
    DEFAULT_SENDER = "BOX2BOX"

    def get_sender_by_name(self, name):
        return self.SENDERS.get(name) or self.DEFAULT_SENDER


class BaseAttachment:
    def __init__(self, storage_client, bucket_name, validness_time=604800):
        self.storage_client = storage_client
        self.bucket_name = bucket_name
        self.validness_time = validness_time

    def _remote_file_as_binary(self, remote_file):
        """Download remote file as binary and keep it into cache"""

        try:
            # Workaround: make explicit credentials to get signed_url
            # https://googleapis.dev/python/storage/latest/blobs.html#google.cloud.storage.blob.Blob.generate_signed_url
            credentials, project_id = auth.default()
            credentials.refresh(auth.transport.requests.Request())

            # Get url file from google cloud storage
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(remote_file)

            remote_url_file = blob.generate_signed_url(
                version="v4",
                # This URL is valid for 15 minutes
                expiration=datetime.timedelta(seconds=self.validness_time),
                # Allow GET requests using this URL.
                method="GET",
                service_account_email=credentials.service_account_email,
                access_token=credentials.token,
            )

            # Download the file as binary blob
            response = requests.request(
                method="GET",
                url=remote_url_file,
            )
            response.raise_for_status()
            return response.content
        except (RequestException, Exception):
            return None

    def _retrieve_document(self, attachment):
        try:
            remote_file = self._remote_file_as_binary(attachment["remote_file"])
            if remote_file:
                remote_file = base64.b64encode(remote_file).decode("utf-8")
        except KeyError:
            remote_file = attachment.get("encoded_file")

        return remote_file

    def format_attachments(self, attachments):
        """Parse attachments to send it to mailjet. Attachment pointers are a list of attachments stored in GCS in order
        to skip the 10MB message size hard limit from pub/sub"""

        raise NotImplementedError("Not implemented yet!")
