#!/bin/bash

DOCKER_IMAGE="eu.gcr.io/box2box-cloud/notifications-dev"

IMGS_COUNT=$(gcloud container images list-tags ${DOCKER_IMAGE} --sort-by=TIMESTAMP --format='get(digest)' | wc -l)
if (( IMGS_COUNT > 10 )); then
  LAST_IMG=$(gcloud container images list-tags ${DOCKER_IMAGE} --limit=1 --sort-by=TIMESTAMP --format='get(digest)')
  gcloud container images delete -q --force-delete-tags "${DOCKER_IMAGE}@${LAST_IMG}"
fi
