name: Staging Hubspot Booking Funnel CI/CD

on:
  push:
    branches:
      - dev
      - master
    paths:
      - 'api_gateway_backends/authorization.py'
      - 'api_gateway_backends/common.py'
      - 'api_gateway_backends/hubspot_funnel.py'
      - 'api_gateway_backends/requirements/hubspot_funnel.txt'
      - '.github/workflows/hubspot_funnel.yml'
      - 'README.md'

jobs:
  dev:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "HUBSPOT_API_KEY: $HUBSPOT_API_KEY" >> .env.yaml
          echo "HUBSPOT_PIPELINE_SPAIN: 'default'" >> .env.yaml
          echo "HUBSPOT_STAGE_SPAIN: '********'" >> .env.yaml
          echo "HUBSPOT_PIPELINE_PORTUGAL: '********'" >> .env.yaml
          echo "HUBSPOT_STAGE_PORTUGAL: '********'" >> .env.yaml
          echo "HUBSPOT_PIPELINE_FRANCE: '********'" >> .env.yaml
          echo "HUBSPOT_STAGE_FRANCE: '********'" >> .env.yaml
          echo "OWNER_ID: '*********'" >> .env.yaml
        env:
          HUBSPOT_API_KEY: ${{ secrets.HUBSPOT_API_KEY_DEV }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p hubspot_funnel_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/hubspot_funnel.py hubspot_funnel_deployment/
          cp -r api_gateway_backends/requirements/hubspot_funnel.txt hubspot_funnel_deployment/
          cp -r .env.yaml hubspot_funnel_deployment/
          cp -r i18n/ hubspot_funnel_deployment/
          cp -r locale/ hubspot_funnel_deployment/
          mv hubspot_funnel_deployment/hubspot_funnel.txt hubspot_funnel_deployment/requirements.txt
          mv hubspot_funnel_deployment/hubspot_funnel.py hubspot_funnel_deployment/main.py

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd hubspot_funnel_deployment/
          gcloud functions deploy hubspot_funnel --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 60 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 4
  prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "HUBSPOT_API_KEY: $HUBSPOT_API_KEY" >> .env.yaml
          echo "HUBSPOT_PIPELINE_SPAIN: 'default'" >> .env.yaml
          echo "HUBSPOT_STAGE_SPAIN: 'qualifiedtobuy'" >> .env.yaml
          echo "HUBSPOT_PIPELINE_PORTUGAL: '********'" >> .env.yaml
          echo "HUBSPOT_STAGE_PORTUGAL: '********'" >> .env.yaml
          echo "HUBSPOT_PIPELINE_FRANCE: '********'" >> .env.yaml
          echo "HUBSPOT_STAGE_FRANCE: '********'" >> .env.yaml
          echo "OWNER_ID: '*********'" >> .env.yaml
        env:
          HUBSPOT_API_KEY: ${{ secrets.HUBSPOT_API_KEY_PROD }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p hubspot_funnel_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/hubspot_funnel.py hubspot_funnel_deployment/
          cp -r api_gateway_backends/requirements/hubspot_funnel.txt hubspot_funnel_deployment/
          cp -r .env.yaml hubspot_funnel_deployment/
          cp -r i18n/ hubspot_funnel_deployment/
          cp -r locale/ hubspot_funnel_deployment/
          mv hubspot_funnel_deployment/hubspot_funnel.txt hubspot_funnel_deployment/requirements.txt
          mv hubspot_funnel_deployment/hubspot_funnel.py hubspot_funnel_deployment/main.py

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd hubspot_funnel_deployment/
          gcloud functions deploy hubspot_funnel_prod --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 120 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 15
