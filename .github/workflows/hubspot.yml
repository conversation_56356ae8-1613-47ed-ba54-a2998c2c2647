name: Staging Hubspot CI/CD

on:
  push:
    branches:
      - dev
      - master
    paths:
      - 'api_gateway_backends/authorization.py'
      - 'api_gateway_backends/common.py'
      - 'api_gateway_backends/hubspot_webhook.py'
      - 'api_gateway_backends/requirements/hubspot.txt'
      - '.github/workflows/hubspot.yml'
      - 'README.md'

jobs:
  dev:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "HUBSPOT_CLIENT_SECRET: $HUBSPOT_CLIENT_SECRET" >> .env.yaml
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "RESERVA_STAGE: '********'" >> .env.yaml
          echo "HUBSPOT_TOPIC: 'gcf-webhooks-to-backend-hubspot'" >> .env.yaml
        env:
          HUBSPOT_CLIENT_SECRET: ${{ secrets.HUBSPOT_CLIENT_SECRET }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p hubspot_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/common.py hubspot_deployment/
          cp -r api_gateway_backends/authorization.py api_gateway_backends/hubspot_webhook.py api_gateway_backends/requirements/hubspot.txt hubspot_deployment/
          cp -r .env.yaml hubspot_deployment/
          mv hubspot_deployment/hubspot.txt hubspot_deployment/requirements.txt
          mv hubspot_deployment/hubspot_webhook.py hubspot_deployment/main.py

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd hubspot_deployment/
          gcloud functions deploy hubspot --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 60 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 4
  prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "HUBSPOT_CLIENT_SECRET: $HUBSPOT_CLIENT_SECRET" >> .env.yaml
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "RESERVA_STAGE: 'decisionmakerboughtin'" >> .env.yaml
          echo "HUBSPOT_TOPIC: 'gcf-webhooks-to-backend-hubspot-prod'" >> .env.yaml
        env:
          HUBSPOT_CLIENT_SECRET: ${{ secrets.HUBSPOT_CLIENT_SECRET_PROD }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p hubspot_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/common.py hubspot_deployment/
          cp -r api_gateway_backends/authorization.py api_gateway_backends/hubspot_webhook.py api_gateway_backends/requirements/hubspot.txt hubspot_deployment/
          cp -r .env.yaml hubspot_deployment/
          mv hubspot_deployment/hubspot.txt hubspot_deployment/requirements.txt
          mv hubspot_deployment/hubspot_webhook.py hubspot_deployment/main.py

      - name: Deploy cloud function in google cloud (prod)
        run: |
          cd hubspot_deployment/
          gcloud functions deploy hubspot-prod --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 512MB --project box2box-cloud --timeout 120 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 25
