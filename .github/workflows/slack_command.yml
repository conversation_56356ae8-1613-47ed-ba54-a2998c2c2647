name: Staging Slack command CI/CD

on:
  push:
    branches:
      - dev
      - master
    paths:
      - 'api_gateway_backends/slack_command.py'
      - 'api_gateway_backends/authorization.py'
      - '.github/workflows/slack_command.yml'
      - 'api_gateway_backends/requirements/slack_command.txt'
      - 'README.md'

jobs:
  dev:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "SLACK_SIGNING_SECRET: $SLACK_SIGNING_SECRET" >> .env.yaml
        env:
          SLACK_SIGNING_SECRET: ${{ secrets.SLACK_SIGNING_SECRET }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p slack_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/authorization.py slack_deployment
          cp api_gateway_backends/slack_command.py api_gateway_backends/requirements/slack_command.txt .env.yaml slack_deployment/
          mv slack_deployment/slack_command.txt slack_deployment/requirements.txt
          mv slack_deployment/slack_command.py slack_deployment/main.py

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd slack_deployment/
          gcloud functions deploy slack-command --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 60 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 4
  prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "SLACK_SIGNING_SECRET: $SLACK_SIGNING_SECRET" >> .env.yaml
        env:
          SLACK_SIGNING_SECRET: ${{ secrets.SLACK_SIGNING_SECRET }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p slack_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/authorization.py slack_deployment
          cp api_gateway_backends/slack_command.py api_gateway_backends/requirements/slack_command.txt .env.yaml slack_deployment/
          mv slack_deployment/slack_command.txt slack_deployment/requirements.txt
          mv slack_deployment/slack_command.py slack_deployment/main.py

      - name: Deploy cloud function in google cloud (prod)
        run: |
          cd slack_deployment/
          gcloud functions deploy slack-command-prod --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 60 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 10
