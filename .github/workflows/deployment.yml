name: Email wrapper over mailjet deployment

on:
  push:
    branches:
      - master
    paths:
      - 'i18n/**.py'
      - 'main.py'
      - 'providers/**.py'
      - '.github/workflows/deployment.yml'
      - 'requirements.txt'
      - '.gcloudignore'

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Create env file
        run: |
          echo "MAILJET_API_KEY: $MAILJET_API_KEY" >> .env.yaml
          echo "MAILJET_API_SECRET: $MAILJET_API_SECRET" >> .env.yaml
          echo "SENDGRID_API_KEY: $SENDGRID_API_KEY" >> .env.yaml
          echo "DOCUMENTATION_BUCKET_NAME: docs-box2box-prod" >> .env.yaml
        env:
          MAILJET_API_KEY: ${{ secrets.MAILJET_API_KEY }}
          MAILJET_API_SECRET: ${{ secrets.MAILJET_API_SECRET }}
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}

      - name: Deploy cf in google cloud
        run: gcloud functions deploy emails --entry-point emails --runtime python310 --trigger-topic mailjet-emails --region europe-west1 --memory 512MB --env-vars-file .env.yaml --project box2box-cloud --service-account <EMAIL>
