name: Production API CI/CD

on:
  push:
    branches:
      - master
    paths-ignore:
#      - '.github/**'
      - 'README.md'
      - 'CHANGELOG.md'

jobs:
  store_git_tag:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set git tag version globally
        shell: bash
        run: |
          expr $(git describe --tags $(git rev-list --tags --max-count=1)) > tag_version.txt

      - name: Upload version in artifact
        uses: actions/upload-artifact@v1
        with:
          name: git_version
          path: tag_version.txt

  build_deploy:
    runs-on: ubuntu-latest
    needs: store_git_tag

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@master
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
          export_default_credentials: true

      - name: Install docker-compose
        run: |
          pip install docker-compose

      - name: Save gcloud service accounts
        run: |
          echo `echo "${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}" | python -m base64 -d` > gcloud-adtuo-cloud.json

      - name: Download git tag version
        uses: actions/download-artifact@v1
        with:
          name: git_version

      - name: Create env file
        run: |
          echo "DJANGO_SETTINGS_MODULE=adtuo_api.settings.prod" >> .env
          echo "SECRET_KEY=$SECRET_KEY" >> .env
          echo "STRIPE_API_KEY=${{ secrets.PROD_STRIPE_API_KEY }}" >> .env
          echo "POSTGRES_HOST=postgres" >> .env
          echo "POSTGRES_PORT=5432" >> .env
          echo "POSTGRES_USER=adtuo" >> .env
          echo "DATABASE_URL=postgres://adtuo:${{ secrets.PROD_POSTGRES_PASSWORD }}@cloudsql:5432/adtuo-saas" >> .env
          echo "MAILGUN_API_KEY=$MAILGUN_API_KEY" >> .env
          echo "ADS_DEVELOPER_TOKEN=$ADS_DEVELOPER_TOKEN" >> .env
          echo "APP_TOKEN=$APP_TOKEN" >> .env
          echo "SANDBOX_TOKEN=$SANDBOX_TOKEN" >> .env
          echo "SENTRY_SDK_DSN=$SENTRY_SDK_DSN" >> .env
          echo "REDIS_SERVER=redis://redis:6379/0" >> .env
          echo "GOOGLE_CLOUD_SERVICE_ACCOUNT=gcloud-adtuo-cloud.json" >> .env
          echo "STRIPE_API_KEY=$STRIPE_API_KEY" >> .env
          echo "STRIPE_TAX_RATE_ID=txr_1JFvQbH0IEn3mssSsk28cM9d" >> .env
          echo "STRIPE_WEBHOOK_KEY=$STRIPE_WEBHOOK_KEY" >> .env
          echo "MASTER_PASSWORD=$MASTER_PASSWORD" >> .env
          echo "SECRET_KEY_FROM_BEACON_CONFIG=$SECRET_KEY_FROM_BEACON_CONFIG" >> .env
          echo "WS_NOTIFICATION_KEY=$WS_NOTIFICATION_KEY" >> .env
          echo "DEBOUNCE_API_KEY=$DEBOUNCE_API_KEY" >> .env
          echo "ENCODING_KEY=$ENCODING_KEY" >> .env
        env:
          ADS_DEVELOPER_TOKEN: ${{ secrets.DEV_ADS_DEVELOPER_TOKEN }}
          APP_TOKEN: ${{ secrets.PROD_APP_TOKEN }}
          POSTGRES_PASSWORD: ${{ secrets.PROD_POSTGRES_PASSWORD }}
          SECRET_KEY: ${{ secrets.PROD_SECRET_KEY }}
          MAILGUN_API_KEY: ${{ secrets.MAILGUN_API_KEY }}
          SANDBOX_TOKEN: ${{ secrets.SANDBOX_TOKEN }}
          SENTRY_SDK_DSN: ${{ secrets.SENTRY_SDK_SDN }}
          STRIPE_API_KEY: ${{ secrets.PROD_STRIPE_API_KEY }}
          STRIPE_WEBHOOK_KEY: ${{ secrets.PROD_STRIPE_WEBHOOK_KEY }}
          MASTER_PASSWORD: ${{ secrets.PROD_MASTER_PASSWORD }}
          SECRET_KEY_FROM_BEACON_CONFIG: ${{ secrets.DEV_SECRET_KEY_FROM_BEACON_CONFIG }}
          WS_NOTIFICATION_KEY: ${{ secrets.PROD_WS_NOTIFICATION_KEY }}
          DEBOUNCE_API_KEY: ${{ secrets.PROD_DEBOUNCE_API_KEY }}
          ENCODING_KEY: ${{ secrets.ENCODING_KEY }}

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Move script to container
        run: |
          chmod +x .github/scripts/remove_images_from_prodapi.sh
          ./.github/scripts/remove_images_from_prodapi.sh

      - name: Add version to container deployment
        run: |
          export VERSION=`cat git_version/tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment_files/prod/docker-compose.yml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment_files/prod/docker-compose-tasks.yml

      - name: Build image
        run: |
          sed -i -e s/\${POSTGRES_PASSWORD}/$POSTGRES_PASSWORD\/g deployment_files/prod/docker-compose.yml
          docker-compose -f deployment_files/prod/docker-compose.yml build --build-arg SSH_PRIVATE_KEY="$SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY"
        env:
          POSTGRES_PASSWORD: ${{ secrets.PROD_POSTGRES_PASSWORD }}
          SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY: ${{ secrets.SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY }}

      - name: Push image
        run: docker-compose -f deployment_files/prod/docker-compose.yml push

      - name: Test connection and deploy api
        run: |
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-preprod --tunnel-through-iap  --project "adtuo-cloud" --strict-host-key-checking=no -- "echo 'ssh connected'"
          gcloud compute scp ./deployment_files/prod/docker-compose.yml --zone "europe-west1-b" adtuo@adtuo-saas-preprod:~/adtuo-saas/ --tunnel-through-iap --project "adtuo-cloud"
          gcloud compute scp ./deployment_files/prod/docker-compose-tasks.yml --zone "europe-west1-b" adtuo@adtuo-saas-preprod:~/adtuo-tasks/ --tunnel-through-iap --project "adtuo-cloud"
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-preprod --tunnel-through-iap --project "adtuo-cloud" -- "cd ~/adtuo-saas/ && sudo docker-compose -f docker-compose.yml pull"
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-preprod --tunnel-through-iap --project "adtuo-cloud" -- "cd ~/adtuo-saas/ && sudo docker-compose -f docker-compose.yml up -d --no-deps"
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-preprod --tunnel-through-iap --project "adtuo-cloud" -- "cd ~/adtuo-tasks/ && sudo docker-compose -f docker-compose-tasks.yml up -d --no-deps"

  collect_static:
    runs-on: ubuntu-latest
    needs: build_deploy

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@master
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "DJANGO_SETTINGS_MODULE=adtuo_api.settings.prod" >> .env
          echo "SECRET_KEY=$SECRET_KEY" >> .env
          echo "STRIPE_API_KEY=${{ secrets.PROD_STRIPE_API_KEY }}" >> .env
          echo "POSTGRES_HOST=postgres" >> .env
          echo "POSTGRES_PORT=5432" >> .env
          echo "POSTGRES_USER=adtuo" >> .env
          echo "DATABASE_URL=postgres://adtuo:${{ secrets.PROD_POSTGRES_PASSWORD }}@127.0.0.1:5432/adtuo-saas" >> .env
          echo "MAILGUN_API_KEY=$MAILGUN_API_KEY" >> .env
          echo "ADS_DEVELOPER_TOKEN=$ADS_DEVELOPER_TOKEN" >> .env
          echo "APP_TOKEN=$APP_TOKEN" >> .env
          echo "SANDBOX_TOKEN=$SANDBOX_TOKEN" >> .env
          echo "SENTRY_SDK_DSN=$SENTRY_SDK_DSN" >> .env
          echo "REDIS_SERVER=redis://redis:6379/0" >> .env
          echo "GOOGLE_CLOUD_SERVICE_ACCOUNT=gcloud-adtuo-cloud.json" >> .env
          echo "GOOGLE_APPLICATION_CREDENTIALS=gcloud-adtuo-cloud.json" >> .env
          echo "STRIPE_API_KEY=$STRIPE_API_KEY" >> .env
          echo "STRIPE_TAX_RATE_ID=txr_1ITWTfH0IEn3mssS16jEssmI" >> .env
          echo "STRIPE_WEBHOOK_KEY=$STRIPE_WEBHOOK_KEY" >> .env
          echo "MASTER_PASSWORD=$MASTER_PASSWORD" >> .env
          echo "SECRET_KEY_FROM_BEACON_CONFIG=$SECRET_KEY_FROM_BEACON_CONFIG" >> .env
          echo "WS_NOTIFICATION_KEY=$WS_NOTIFICATION_KEY" >> .env
          echo "DEBOUNCE_API_KEY=$DEBOUNCE_API_KEY" >> .env
          echo "ENCODING_KEY=$ENCODING_KEY" >> .env
        env:
            ADS_DEVELOPER_TOKEN: ${{ secrets.DEV_ADS_DEVELOPER_TOKEN }}
            APP_TOKEN: ${{ secrets.PROD_APP_TOKEN }}
            POSTGRES_PASSWORD: ${{ secrets.PROD_POSTGRES_PASSWORD }}
            SECRET_KEY: ${{ secrets.PROD_SECRET_KEY }}
            MAILGUN_API_KEY: ${{ secrets.MAILGUN_API_KEY }}
            SANDBOX_TOKEN: ${{ secrets.SANDBOX_TOKEN }}
            SENTRY_SDK_DSN: ${{ secrets.SENTRY_SDK_SDN }}
            STRIPE_API_KEY: ${{ secrets.PROD_STRIPE_API_KEY }}
            STRIPE_WEBHOOK_KEY: ${{ secrets.PROD_STRIPE_WEBHOOK_KEY }}
            MASTER_PASSWORD: ${{ secrets.PROD_MASTER_PASSWORD }}
            SECRET_KEY_FROM_BEACON_CONFIG: ${{ secrets.DEV_SECRET_KEY_FROM_BEACON_CONFIG }}
            WS_NOTIFICATION_KEY: ${{ secrets.PROD_WS_NOTIFICATION_KEY }}
            DEBOUNCE_API_KEY: ${{ secrets.PROD_DEBOUNCE_API_KEY }}
            ENCODING_KEY: ${{ secrets.ENCODING_KEY }}

      - name: Save gcloud service accounts
        run: |
          echo `echo "${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}" | python -m base64 -d` > gcloud-adtuo-cloud.json

      - uses: actions/cache@v1
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('Pipfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Adding SSH access to adtuo-django-email repo
        uses: webfactory/ssh-agent@v0.4.1
        with:
          ssh-private-key: ${{ secrets.SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY }}

      - name: Collect static files
        run: |
          python3 -m venv venv
          source venv/bin/activate
          pip install -U pip pipenv
          pipenv sync --dev
          python manage.py collectstatic --no-input --settings=adtuo_api.settings.prod

      - name: Upload static files
        run: gsutil -m rsync -R staticfiles/ gs://saas_static_resources/static/
