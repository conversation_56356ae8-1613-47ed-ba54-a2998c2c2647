name: Staging API CI/CD

on:
  push:
    branches:
      - master
    paths:
      - 'backend/**'
      - 'deployment/**'
      - 'static/**'
      - 'users/**'
      - 'pipfile'
      - 'README.md'

jobs:
  build_deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Install docker-compose
        run: |
          pip install docker-compose

      - name: Create env file
        run: |
          echo "DJANGO_SETTINGS_MODULE=backend.settings" >> .env
          echo "DATABASE_URL=****************************************************/api-dev" >> .env
          echo "MAILJET_API_KEY=$MAILJET_API_KEY" >> .env
          echo "MAILJET_API_SECRET=$MAILJET_API_SECRET" >> .env
        env:
          POSTGRES_PASSWORD: ${{ secrets.DEV_POSTGRES_PASSWORD }}
          MAILJET_API_KEY: ${{ secrets.MAILJET_API_KEY }}
          MAILJET_API_SECRET: ${{ secrets.MAILJET_API_SECRET }}

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Move script to container
        run: |
          chmod +x .github/scripts/remove_images_from_devapi.sh
          ./.github/scripts/remove_images_from_devapi.sh

      - name: Build image
        run: |
          sed -i -e s/\${POSTGRES_PASSWORD}/$POSTGRES_PASSWORD\/g deployment/test/docker-compose.yml
          docker-compose -f deployment/test/docker-compose.yml build
        env:
          POSTGRES_PASSWORD: ${{ secrets.DEV_POSTGRES_PASSWORD }}

      - name: Push image
        run: docker-compose -f deployment/test/docker-compose.yml push

      - name: Test connection and deploy api
        run: |
          gcloud compute ssh --zone "europe-west1-b" box2box@evento-malaga --tunnel-through-iap --project "box2box-cloud" --strict-host-key-checking=no -- "echo 'ssh connected'"
          gcloud compute scp ./deployment/test/docker-compose.yml --zone "europe-west1-b" box2box@evento-malaga:~/box2box-api/ --tunnel-through-iap --project "box2box-cloud"
          gcloud compute ssh --zone "europe-west1-b" box2box@evento-malaga --tunnel-through-iap --project "box2box-cloud" -- "cd ~/box2box-api/ && sudo docker-compose -f docker-compose.yml pull"
          gcloud compute ssh --zone "europe-west1-b" box2box@evento-malaga --tunnel-through-iap --project "box2box-cloud" -- "cd ~/box2box-api/ && sudo docker-compose -f docker-compose.yml up -d"
