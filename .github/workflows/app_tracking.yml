name: Riders Tracking CI/CD

on:
  push:
    branches:
      - dev
      - master
    paths:
      - 'api_gateway_backends/authorization.py'
      - 'api_gateway_backends/common.py'
      - 'api_gateway_backends/app_common.py'
      - 'api_gateway_backends/app_tracking.py'
      - 'api_gateway_backends/requirements/riders.txt'
      - '.github/workflows/app_tracking.yml'
      - 'README.md'

jobs:
  dev:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Create env file
        run: |
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "RIDERS_TOPIC: 'gcf-webhooks-to-backend'" >> .env.yaml

      - name: Prepare folder to deployment
        run: |
          mkdir -p riders_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/common.py api_gateway_backends/authorization.py api_gateway_backends/app_common.py riders_deployment/
          cp api_gateway_backends/app_tracking.py api_gateway_backends/requirements/riders.txt .env.yaml riders_deployment/
          mv riders_deployment/riders.txt riders_deployment/requirements.txt
          mv riders_deployment/app_tracking.py riders_deployment/main.py

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd riders_deployment/
          gcloud functions deploy tracking --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 60 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 4
  prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Create env file
        run: |
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "RIDERS_TOPIC: 'gcf-webhooks-to-backend-riders-prod'" >> .env.yaml

      - name: Prepare folder to deployment
        run: |
          mkdir -p riders_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/common.py api_gateway_backends/authorization.py api_gateway_backends/app_common.py riders_deployment/
          cp api_gateway_backends/app_tracking.py api_gateway_backends/requirements/riders.txt .env.yaml riders_deployment/
          mv riders_deployment/riders.txt riders_deployment/requirements.txt
          mv riders_deployment/app_tracking.py riders_deployment/main.py

      - name: Deploy cloud function in google cloud (prod)
        run: |
          cd riders_deployment/
          gcloud functions deploy tracking-prod --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 512MB --project box2box-cloud --timeout 120 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 25
