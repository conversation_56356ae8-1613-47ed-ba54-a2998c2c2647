name: Staging API CI/CD

on:
  push:
    branches:
      - dev
    paths-ignore:
      - '.github/**'
      - 'README.md'
      - 'CHANGELOG.md'


jobs:
  build_deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@master
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
          export_default_credentials: true

      - name: Install docker-compose
        run: |
          pip install docker-compose

      - name: Save gcloud service accounts
        run: |
          echo `echo "${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}" | python -m base64 -d` > gcloud-adtuo-cloud.json

      - name: Create env file
        run: |
          echo "DJANGO_SETTINGS_MODULE=adtuo_api.settings.staging" >> .env
          echo "ADS_DEVELOPER_TOKEN=$ADS_DEVELOPER_TOKEN" >> .env
          echo "APP_TOKEN=$APP_TOKEN" >> .env
          echo "DATABASE_URL=*************************************************/adtuo-dev" >> .env
          echo "SECRET_KEY=$SECRET_KEY" >> .env
          echo "MAILGUN_API_KEY=$MAILGUN_API_KEY" >> .env
          echo "SANDBOX_TOKEN=$SANDBOX_TOKEN" >> .env
          echo "SENTRY_SDK_DSN=$SENTRY_SDK_DSN" >> .env
          echo "REDIS_SERVER=redis://redis:6379/0" >> .env
          echo "GOOGLE_CLOUD_SERVICE_ACCOUNT=gcloud-adtuo-cloud.json" >> .env
          echo "STRIPE_API_KEY=$STRIPE_API_KEY" >> .env
          echo "STRIPE_TAX_RATE_ID=txr_1ITWTfH0IEn3mssS16jEssmI" >> .env
          echo "STRIPE_WEBHOOK_KEY=$STRIPE_WEBHOOK_KEY" >> .env
          echo "MASTER_PASSWORD=$MASTER_PASSWORD" >> .env
          echo "SECRET_KEY_FROM_BEACON_CONFIG=$SECRET_KEY_FROM_BEACON_CONFIG" >> .env
          echo "DEBOUNCE_API_KEY=$DEBOUNCE_API_KEY" >> .env
          echo "ENCODING_KEY=$ENCODING_KEY" >> .env
        env:
          ADS_DEVELOPER_TOKEN: ${{ secrets.DEV_ADS_DEVELOPER_TOKEN }}
          APP_TOKEN: ${{ secrets.DEV_APP_TOKEN }}
          POSTGRES_PASSWORD: ${{ secrets.DEV_POSTGRES_PASSWORD }}
          SECRET_KEY: ${{ secrets.DEV_SECRET_KEY }}
          MAILGUN_API_KEY: ${{ secrets.MAILGUN_API_KEY }}
          SANDBOX_TOKEN: ${{ secrets.SANDBOX_TOKEN }}
          SENTRY_SDK_DSN: ${{ secrets.SENTRY_SDK_SDN }}
          STRIPE_API_KEY: ${{ secrets.DEV_STRIPE_API_KEY }}
          STRIPE_WEBHOOK_KEY: ${{ secrets.DEV_STRIPE_WEBHOOK_KEY }}
          MASTER_PASSWORD: ${{ secrets.DEV_MASTER_PASSWORD }}
          SECRET_KEY_FROM_BEACON_CONFIG: ${{ secrets.DEV_SECRET_KEY_FROM_BEACON_CONFIG }}
          DEBOUNCE_API_KEY: ${{ secrets.PROD_DEBOUNCE_API_KEY }}
          ENCODING_KEY: ${{ secrets.ENCODING_KEY }}

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Move script to container
        run: |
          chmod +x .github/scripts/remove_images_from_devapi.sh
          ./.github/scripts/remove_images_from_devapi.sh

      - name: Build image
        run: |
          sed -i -e s/\${POSTGRES_PASSWORD}/$POSTGRES_PASSWORD\/g deployment_files/test/docker-compose.yml
          docker-compose -f deployment_files/test/docker-compose.yml build --build-arg SSH_PRIVATE_KEY="$SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY"
        env:
          POSTGRES_PASSWORD: ${{ secrets.DEV_POSTGRES_PASSWORD }}
          SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY: ${{ secrets.SSH_GITHUB_DJANGO_EMAIL_DEPENDENCY }}

      - name: Push image
        run: docker-compose -f deployment_files/test/docker-compose.yml push

      - name: Test connection and deploy api
        run: |
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-test --tunnel-through-iap --project "adtuo-cloud" --strict-host-key-checking=no -- "echo 'ssh connected'"
          gcloud compute scp ./deployment_files/test/docker-compose.yml --zone "europe-west1-b" adtuo@adtuo-saas-test:~/adtuo-saas/ --tunnel-through-iap --project "adtuo-cloud"
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-test --tunnel-through-iap --project "adtuo-cloud" -- "cd ~/adtuo-saas/ && sudo docker-compose -f docker-compose.yml pull"
          gcloud compute ssh --zone "europe-west1-b" adtuo@adtuo-saas-test --tunnel-through-iap --project "adtuo-cloud" -- "cd ~/adtuo-saas/ && sudo docker-compose -f docker-compose.yml up -d"
