name: Notifications staging deployment

on:
  push:
    branches:
      - doesnotexist
    paths:
      - 'app/**.py'
      - 'tests/**.py'
      - '.github/workflows/dev_deployment.yml'
      - 'deployment/dev/*'

jobs:
  tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Adding SSH access to network_clients repo
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY }}

      - name: Installing packages for testing
        run: |
          python3 -m venv venv
          source venv/bin/activate
          pip install -U virtualenv pip pipenv pytest
          pipenv sync --dev

      - name: Running tests
        run: |
          source venv/bin/activate
          export FASTAPI_ENV="test"
          pytest --junitxml=test-reports/junit.xml


  build_and_deploy:
    needs: tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Install docker-compose
        run: |
          pip install docker-compose

      - name: Create env file
        run: |
          echo "FASTAPI_ENV=dev" >> .env
          echo "ODOO_PASSWORD=$ODOO_PASSWORD" >> .env
          echo "REDOC_USER=<EMAIL>" >> .env
          echo "REDOC_PASSWORD=$REDOC_PASSWORD" >> .env
        env:
          ODOO_PASSWORD: ${{ secrets.ODOO_PASSWORD_DEV }}
          REDOC_PASSWORD: ${{ secrets.REDOC_PASSWORD_DEV }}

      - name: Move script to container
        run: |
          chmod +x .github/scripts/remove_images_from_dev.sh
          ./.github/scripts/remove_images_from_dev.sh

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Build dev image
        run: |
          docker-compose -f deployment/dev/docker-compose.yml build --build-arg SSH_PRIVATE_KEY="$SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY"
        env:
          SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY: ${{ secrets.SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY }}

      - name: Push image
        run: docker-compose -f deployment/dev/docker-compose.yml push

      - name: Deploy notifications microservice in Cloud Run
        run: |
          gcloud run deploy notifications-dev --image=eu.gcr.io/box2box-cloud/notifications-dev --min-instances=0 --max-instances=1 --cpu=1000m --memory=256Mi --platform=managed --region=europe-west1 --port=8000 --timeout=60 --concurrency=4 --project=box2box-cloud --allow-unauthenticated --service-account <EMAIL>
