name: Staging Payment Link Async CI/CD

on:
  push:
    branches:
      - dev
      - master
    paths:
      - 'backends/payment_link_backend/**'
      - '.github/workflows/payment_link_backend.yml'
      - 'README.md'

jobs:
  dev:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "FIRESTORE_PAYMENT_LINK_COLLECTION: 'payment_link_chargebee'" >> .env.yaml

      - name: Prepare folder to deployment
        run: |
          mkdir -p payment_link_backend
          cp -r backends/payment_link_backend/__init__.py payment_link_backend/
          cp -r backends/payment_link_backend/* payment_link_backend/
          cp -r .env.yaml payment_link_backend/

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd payment_link_backend/
          gcloud functions deploy payment-link-generation --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --env-vars-file .env.yaml --service-account <EMAIL>
  prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Create env file
        run: |
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "FIRESTORE_PAYMENT_LINK_COLLECTION: 'payment_link_chargebee_prod'" >> .env.yaml

      - name: Prepare folder to deployment
        run: |
          mkdir -p payment_link_backend
          cp -r backends/payment_link_backend/__init__.py payment_link_backend/
          cp -r backends/payment_link_backend/* payment_link_backend/
          cp -r .env.yaml payment_link_backend/

      - name: Deploy cloud function in google cloud (prod)
        run: |
          cd payment_link_backend/
          gcloud functions deploy payment-link-generation-prod --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 512MB --project box2box-cloud --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 25
