name: Production API CI/CD

on:
  push:
    branches:
      - master
    paths-ignore:
      - 'README.md'
      - 'CHANGELOG.md'
      -  '.github/workflows/dev.yml'


jobs:
  store_git_tag:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set git tag version globally
        shell: bash
        run: |
          expr $(git describe --tags $(git rev-list --tags --max-count=1)) > tag_version.txt

      - name: Upload version in artifact
        uses: actions/upload-artifact@v4
        with:
          name: git_version
          path: tag_version.txt

  build:
    runs-on: ubuntu-22.04
    needs: store_git_tag

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: 3.8

      - name: Download git tag version
        uses: actions/download-artifact@v4
        with:
          name: git_version

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Install docker using buildx
        uses: docker/setup-buildx-action@v3

      - name: Adding SSH access to network_clients repo
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY }}

      - name: Save gcloud service accounts
        run: |
          echo `echo "${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}" | python -m base64 -d` > gcloud-box2box-cloud.json
          echo `echo "${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON_SHEETS }}" | python -m base64 -d` > gcloud-box2box-cloud-sheets.json
          echo `echo "${{ secrets.GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR }}" | python -m base64 -d` > gcloud-box2box-cloud-calendar.json
          echo `echo "${{ secrets.GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT }}" | python -m base64 -d` > gcloud-box2box-cloud-firebase.json

      - name: Create env file
        run: |
          echo "DJANGO_SETTINGS_MODULE=backoffice_api.settings.prod" >> .env
          echo "SECRET_KEY=$SECRET_KEY" >> .env
          echo "POSTGRES_USER=box2box" >> .env
          echo "POSTGRES_PASSWORD=${{ secrets.POSTGRES_KUBERNETES_PASSWORD }}" >> .env
          echo "DATABASE_URL=postgres://box2box:${{ secrets.POSTGRES_KUBERNETES_PASSWORD }}@127.0.0.1:5432/backend" >> .env
          echo "SENTRY_SDK_DSN=$SENTRY_SDK_DSN" >> .env
          echo "REDIS_SERVER=redis://redis:6379/0" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT=gcloud-box2box-cloud.json" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS=gcloud-box2box-cloud-sheets.json" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR=gcloud-box2box-cloud-calendar.json" >> .env
          echo "GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT=gcloud-box2box-cloud-firebase.json" >> .env
          echo "HUBSPOT_CLIENT_SECRET=$HUBSPOT_CLIENT_SECRET" >> .env
          echo "HUBSPOT_REFRESH_TOKEN=$HUBSPOT_REFRESH_TOKEN" >> .env
          echo "MOREAPP_API_KEY=$MOREAPP_API_KEY" >> .env
          echo "CHARGEBEE_SITE_FR=box2boxfr" >> .env
          echo "CHARGEBEE_API_KEY_FR=$CHARGEBEE_API_KEY_FR" >> .env
          echo "CHARGEBEE_SITE_ES=box2box" >> .env
          echo "CHARGEBEE_API_KEY_ES=$CHARGEBEE_API_KEY_ES" >> .env
          echo "CHARGEBEE_SITE_PT=box2boxpt" >> .env
          echo "CHARGEBEE_API_KEY_PT=$CHARGEBEE_API_KEY_PT" >> .env
          echo "CHARGEBEE_SITE_MX=box2boxmx" >> .env
          echo "CHARGEBEE_API_KEY_MX=$CHARGEBEE_API_KEY_MX" >> .env
          echo "MOLONI_CLIENT_SECRET=$MOLONI_CLIENT_SECRET" >> .env
          echo "MOLONI_PASSWORD=$MOLONI_PASSWORD" >> .env
          echo "MONGODB_PASSWORD=$MONGODB_PASSWORD" >> .env
          echo "MASTER_PASSWORD=$MASTER_PASSWORD" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_ES=gw_198LqsSulrptW2Rp" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_PT=gw_BTUTgVSvT0XSW1Bg2" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_FR=gw_BTUTgVSvT1cyO1Biq" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_MX=gw_BTcLXLTfdAAEV6X4" >> .env
          echo "HUBSPOT_API_KEY=$HUBSPOT_API_KEY" >> .env
          echo "FIREBASE_USER_ID=L6kpfKCaVegVa0LR1fwhxj5Zj3Q2" >> .env
        env:
          SECRET_KEY: ${{ secrets.SECRET_KEY_PROD }}
          SENTRY_SDK_DSN: ${{ secrets.SENTRY_SDK_SDN }}
          HUBSPOT_CLIENT_SECRET: ${{ secrets.HUBSPOT_CLIENT_SECRET_PROD }}
          HUBSPOT_REFRESH_TOKEN: ${{ secrets.HUBSPOT_REFRESH_TOKEN_PROD }}
          MOREAPP_API_KEY: ${{ secrets.MOREAPP_API_KEY }}
          CHARGEBEE_API_KEY_FR: ${{ secrets.CHARGEBEE_API_KEY_FR_PROD }}
          CHARGEBEE_API_KEY_ES: ${{ secrets.CHARGEBEE_API_KEY_ES_PROD }}
          CHARGEBEE_API_KEY_PT: ${{ secrets.CHARGEBEE_API_KEY_PT_PROD }}
          CHARGEBEE_API_KEY_MX: ${{ secrets.CHARGEBEE_API_KEY_MX_PROD }}
          MOLONI_CLIENT_SECRET: ${{ secrets.MOLONI_CLIENT_SECRET }}
          MOLONI_PASSWORD: ${{ secrets.MOLONI_PASSWORD }}
          MONGODB_PASSWORD: ${{ secrets.MONGODB_KUBERNETES_PASSWORD }}
          MASTER_PASSWORD: ${{ secrets.MASTER_PASSWORD }}
          HUBSPOT_API_KEY: ${{ secrets.HUBSPOT_API_KEY_PROD}}

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Build image
        run: |
          export VERSION=`cat tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/docker-compose.yml
          docker compose -f deployment/prod/docker-compose.yml build --build-arg SSH_PRIVATE_KEY="$SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY"
        env:
          SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY: ${{ secrets.SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY }}

      - name: Push image
        run: docker compose -f deployment/prod/docker-compose.yml push

      - name: Move script to container and remove last API image from gcloud
        run: |
          chmod +x .github/scripts/remove_images_from_prodapi.sh
          ./.github/scripts/remove_images_from_prodapi.sh

  collect_static:
    runs-on: ubuntu-22.04
    needs: build
    if: success()

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: 3.8

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Install docker using buildx
        uses: docker/setup-buildx-action@v3

      - name: Adding SSH access to network_clients repo
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_GITHUB_NETWORK_CLIENTS_DEPENDENCY }}

      - name: Save gcloud service accounts
        run: |
          echo `echo "${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}" | python -m base64 -d` > gcloud-box2box-cloud.json
          echo `echo "${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON_SHEETS }}" | python -m base64 -d` > gcloud-box2box-cloud-sheets.json
          echo `echo "${{ secrets.GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR }}" | python -m base64 -d` > gcloud-box2box-cloud-calendar.json
          echo `echo "${{ secrets.GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT }}" | python -m base64 -d` > gcloud-box2box-cloud-firebase.json

      - name: Create env file
        run: |
          echo "DJANGO_SETTINGS_MODULE=backoffice_api.settings.prod" >> .env
          echo "SECRET_KEY=$SECRET_KEY" >> .env
          echo "DATABASE_URL=postgres://box2box:${{ secrets.POSTGRES_KUBERNETES_PASSWORD }}@127.0.0.1:5432/backend" >> .env
          echo "SENTRY_SDK_DSN=$SENTRY_SDK_DSN" >> .env
          echo "REDIS_SERVER=redis://redis:6379/0" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT=gcloud-box2box-cloud.json" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT_FOR_SHEETS=gcloud-box2box-cloud-sheets.json" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT_FOR_CALENDAR=gcloud-box2box-cloud-calendar.json" >> .env
          echo "GOOGLE_CLOUD_FIREBASE_JSON_ACCOUNT=gcloud-box2box-cloud-firebase.json" >> .env
          echo "HUBSPOT_CLIENT_SECRET=$HUBSPOT_CLIENT_SECRET" >> .env
          echo "HUBSPOT_REFRESH_TOKEN=$HUBSPOT_REFRESH_TOKEN" >> .env
          echo "MOREAPP_API_KEY=$MOREAPP_API_KEY" >> .env
          echo "CHARGEBEE_SITE_FR=box2boxfr" >> .env
          echo "CHARGEBEE_API_KEY_FR=$CHARGEBEE_API_KEY_FR" >> .env
          echo "CHARGEBEE_SITE_ES=box2box" >> .env
          echo "CHARGEBEE_API_KEY_ES=$CHARGEBEE_API_KEY_ES" >> .env
          echo "CHARGEBEE_SITE_PT=box2boxpt" >> .env
          echo "CHARGEBEE_API_KEY_PT=$CHARGEBEE_API_KEY_PT" >> .env
          echo "CHARGEBEE_SITE_MX=box2boxmx" >> .env
          echo "CHARGEBEE_API_KEY_MX=$CHARGEBEE_API_KEY_MX" >> .env
          echo "MOLONI_CLIENT_SECRET=$MOLONI_CLIENT_SECRET" >> .env
          echo "MOLONI_PASSWORD=$MOLONI_PASSWORD" >> .env
          echo "MONGODB_PASSWORD=$MONGODB_PASSWORD" >> .env
          echo "MASTER_PASSWORD=$MASTER_PASSWORD" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_ES=gw_198LqsSulrptW2Rp" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_PT=gw_BTUTgVSvT0XSW1Bg2" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_FR=gw_BTUTgVSvT1cyO1Biq" >> .env
          echo "ADYEN_GATEWAY_ACCOUNT_ID_MX=gw_BTcLXLTfdAAEV6X4" >> .env
          echo "HUBSPOT_API_KEY=$HUBSPOT_API_KEY" >> .env
          echo "FIREBASE_USER_ID=L6kpfKCaVegVa0LR1fwhxj5Zj3Q2" >> .env
        env:
          SECRET_KEY: ${{ secrets.SECRET_KEY_PROD }}
          SENTRY_SDK_DSN: ${{ secrets.SENTRY_SDK_SDN }}
          HUBSPOT_CLIENT_SECRET: ${{ secrets.HUBSPOT_CLIENT_SECRET_PROD }}
          HUBSPOT_REFRESH_TOKEN: ${{ secrets.HUBSPOT_REFRESH_TOKEN_PROD }}
          MOREAPP_API_KEY: ${{ secrets.MOREAPP_API_KEY }}
          CHARGEBEE_API_KEY_FR: ${{ secrets.CHARGEBEE_API_KEY_FR_PROD }}
          CHARGEBEE_API_KEY_ES: ${{ secrets.CHARGEBEE_API_KEY_ES_PROD }}
          CHARGEBEE_API_KEY_PT: ${{ secrets.CHARGEBEE_API_KEY_PT_PROD }}
          CHARGEBEE_API_KEY_MX: ${{ secrets.CHARGEBEE_API_KEY_MX_PROD }}
          MOLONI_CLIENT_SECRET: ${{ secrets.MOLONI_CLIENT_SECRET }}
          MOLONI_PASSWORD: ${{ secrets.MOLONI_PASSWORD }}
          MONGODB_PASSWORD: ${{ secrets.MONGODB_KUBERNETES_PASSWORD }}
          MASTER_PASSWORD: ${{ secrets.MASTER_PASSWORD }}
          HUBSPOT_API_KEY: ${{ secrets.HUBSPOT_API_KEY_PROD}}

      - name: Collect static files
        run: |
          python3 -m venv venv
          source venv/bin/activate
          pip install -U pip pipenv
          pipenv sync --dev
          export PREFIX=""
          export VERBOSITY_EMAIL=""
          export INTRANET_DEPLOYMENT=False
          python manage.py collectstatic --no-input --settings=backoffice_api.settings.prod

      - name: Upload static files
        run: gsutil -m rsync -R staticfiles/ gs://api_resources_box2box/static/

  migration:
    runs-on: ubuntu-22.04
    needs: build
    if: success()

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Download git tag version
        uses: actions/download-artifact@v4
        with:
          name: git_version

      - name: Configure kubectl
        uses: 'google-github-actions/get-gke-credentials@v1'
        with:
          cluster_name: 'api-prod'
          location: 'europe-west1'
          project_id: 'box2box-cloud'

      - name: Add version to migration job
        run: |
          export VERSION=`cat tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-migrations-job.yaml

      - name: Run migration job
        run: kubectl apply -f deployment/prod/api-migrations-job.yaml

      - name: Wait for job completion
        run: |
          kubectl wait --for=condition=complete --timeout=1800s job/django-migrations
          kubectl delete -f deployment/prod/api-migrations-job.yaml

  deploy:
    runs-on: ubuntu-22.04
    needs: migration
    if: success()

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Download git tag version
        uses: actions/download-artifact@v4
        with:
          name: git_version

      - name: Configure kubectl
        uses: 'google-github-actions/get-gke-credentials@v1'
        with:
          cluster_name: 'api-prod'
          location: 'europe-west1'
          project_id: 'box2box-cloud'

      - name: Add version image to deployment
        run: |
          export VERSION=`cat tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-async-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-callback-chargebee-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-callback-hubspot-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-callback-hubspot-public-forms-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-callback-microservices-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_activation_email_to_user.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_quote_creation.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_subscription_cancelling.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/subscriptions_with_free_month.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_email_to_user_on_annex_signed.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_email_to_user_on_box_signed.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_email_to_user_on_moving_signed.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_annex_to_upload.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_box_to_upload.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_moving_to_upload.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/pending_inventory_email.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/add_pending_invoices_into_moloni.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/load_portugal_invoices_from_chargebee.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/mark_as_published_moloni_invoices.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/evict_stale_public_form_submissions.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/payment_reminder.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-callback-ridersapp-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/api-callback-payment-reminder-deployment.yaml
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/crons/remove_old_tracking_services.yaml

      - name: Deploy API
        run: kubectl apply -f deployment/prod/api-deployment.yaml

      - name: Deploy API Async
        run: kubectl apply -f deployment/prod/api-async-deployment.yaml

      - name: Deploy API api-callback-chargebee-deployment
        run: kubectl apply -f deployment/prod/api-callback-chargebee-deployment.yaml

      - name: Deploy API api-callback-hubspot-deployment
        run: kubectl apply -f deployment/prod/api-callback-hubspot-deployment.yaml

      - name: Deploy API api-callback-hubspot-public-forms-deployment
        run: kubectl apply -f deployment/prod/api-callback-hubspot-public-forms-deployment.yaml

      - name: Deploy API api-callback-microservices-deployment
        run: kubectl apply -f deployment/prod/api-callback-microservices-deployment.yaml

      - name: Deploy Cron pending_activation_email_to_user
        run: kubectl apply -f deployment/prod/crons/pending_activation_email_to_user.yaml

      - name: Deploy Cron pending_quote_creation
        run: kubectl apply -f deployment/prod/crons/pending_quote_creation.yaml

      - name: Deploy Cron pending_subscription_cancelling
        run: kubectl apply -f deployment/prod/crons/pending_subscription_cancelling.yaml

      - name: Deploy Cron subscriptions_with_free_month
        run: kubectl apply -f deployment/prod/crons/subscriptions_with_free_month.yaml

      - name: Deploy Cron pending_email_to_user_on_annex_signed
        run: kubectl apply -f deployment/prod/crons/pending_email_to_user_on_annex_signed.yaml

      - name: Deploy Cron pending_email_to_user_on_box_signed
        run: kubectl apply -f deployment/prod/crons/pending_email_to_user_on_box_signed.yaml

      - name: Deploy Cron pending_email_to_user_on_moving_signed
        run: kubectl apply -f deployment/prod/crons/pending_email_to_user_on_moving_signed.yaml

      - name: Deploy Cron pending_inventory_email
        run: kubectl apply -f deployment/prod/crons/pending_inventory_email.yaml

      - name: Deploy Cron pending_annex_to_upload
        run: kubectl apply -f deployment/prod/crons/pending_annex_to_upload.yaml

      - name: Deploy Cron pending_box_to_upload
        run: kubectl apply -f deployment/prod/crons/pending_box_to_upload.yaml

      - name: Deploy Cron pending_moving_to_upload
        run: kubectl apply -f deployment/prod/crons/pending_moving_to_upload.yaml

      - name: Deploy Cron load_portugal_invoices_from_chargebee
        run: kubectl apply -f deployment/prod/crons/load_portugal_invoices_from_chargebee.yaml

      - name: Deploy Cron add_pending_invoices_into_moloni
        run: kubectl apply -f deployment/prod/crons/add_pending_invoices_into_moloni.yaml

      - name: Deploy Cron mark_as_published_moloni_invoices
        run: kubectl apply -f deployment/prod/crons/mark_as_published_moloni_invoices.yaml

      - name: Deploy Cron evict_stale_public_form_submissions
        run: kubectl apply -f deployment/prod/crons/evict_stale_public_form_submissions.yaml

      - name: Deploy redis if needed
        run: |
          if [ -ne $(kubectl get pods --no-headers=true | awk '/redis/{print $1}') ]; then kubectl apply -f deployment/prod/redis-deployment.yaml; kubectl apply -f deployment/prod/redis-service.yaml; fi

      - name: Deploy API api-callback-ridersapp-deployment
        run: kubectl apply -f deployment/prod/api-callback-ridersapp-deployment.yaml

      - name: Deploy API api-callback-payment-reminder-deployment
        run: kubectl apply -f deployment/prod/api-callback-payment-reminder-deployment.yaml

      - name: Deploy Cron payment_reminder
        run: kubectl apply -f deployment/prod/crons/payment_reminder.yaml

      - name: Deploy Cron remove_old_tracking_services
        run: kubectl apply -f deployment/prod/crons/remove_old_tracking_services.yaml

  deploy_intranet:
    runs-on: ubuntu-22.04
    needs: migration
    if: success()

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Download git tag version
        uses: actions/download-artifact@v4
        with:
          name: git_version

      - name: Configure kubectl
        uses: 'google-github-actions/get-gke-credentials@v1'
        with:
          cluster_name: 'api-prod'
          location: 'europe-west1'
          project_id: 'box2box-cloud'

      - name: Add version image to deployment
        run: |
          export VERSION=`cat tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/prod/intranet-deployment.yaml

      - name: Deploy API
        run: kubectl apply -f deployment/prod/intranet-deployment.yaml
