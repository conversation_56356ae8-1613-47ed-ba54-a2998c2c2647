name: Production Moreapp Inventory Service CI/CD

on:
  push:
    branches:
      - master
    paths-ignore:
      - 'README.md'
      - 'CHANGELOG.md'


jobs:
  store_git_tag:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set git tag version globally
        shell: bash
        run: |
          expr $(git describe --tags $(git rev-list --tags --max-count=1)) > tag_version.txt

      - name: Upload version in artifact
        uses: actions/upload-artifact@v1
        with:
          name: git_version
          path: tag_version.txt

  tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Installing packages for testing
        run: |
          python3 -m venv venv
          source venv/bin/activate
          pip install -U virtualenv pip pipenv pytest
          pipenv sync --dev

      - name: Running tests
        run: |
          source venv/bin/activate
          export FASTAPI_ENV=test
          pytest --junitxml=test-reports/junit.xml

  build_and_deploy:
    runs-on: ubuntu-latest
    needs: [store_git_tag, tests]

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Download git tag version
        uses: actions/download-artifact@v1
        with:
          name: git_version

      - name: Google cloud auth
        uses: google-github-actions/auth@v0
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0

      - name: Install docker-compose
        run: |
          pip install docker-compose

      - name: Save gcloud service accounts
        run: |
          echo `echo "${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}" | python -m base64 -d` > gcloud-box2box-cloud.json
        working-directory: ./app/

      - name: Create env file
        run: |
          export VERSION=`cat git_version/tag_version.txt`
          echo "VERSION=$VERSION" >> .env
          echo "SENTRY_SDK_DSN=$SENTRY_SDK_DSN" >> .env
          echo "GOOGLE_CLOUD_JSON_ACCOUNT=gcloud-box2box-cloud.json" >> .env
          echo "MOREAPP_PASSWORD=$MOREAPP_PASSWORD" >> .env
          echo "MONGODB_PASSWORD=$MONGODB_PASSWORD" >> .env
          echo "MONGODB_URL='***********'" >> .env
        env:
          SECRET_KEY: ${{ secrets.SECRET_KEY_PROD }}
          SENTRY_SDK_DSN: ${{ secrets.SENTRY_SDK_SDN }}
          MOREAPP_PASSWORD: ${{ secrets.MOREAPP_PASSWORD }}
          MONGODB_PASSWORD: ${{ secrets.MONGODB_PASSWORD }}
        working-directory: ./app/

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Build image
        run: |
          export VERSION=`cat git_version/tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/docker-compose.yml
          docker-compose -f deployment/docker-compose.yml build

      - name: Push image
        run: docker-compose -f deployment/docker-compose.yml push

      - name: Move script to container and remove last API image from gcloud
        run: |
          chmod +x .github/scripts/remove_images_from_prodapi.sh
          ./.github/scripts/remove_images_from_prodapi.sh

      - name: Deploy ws service in cloud run
        run: |
          export VERSION=`cat git_version/tag_version.txt`
          gcloud run deploy ms-moreapp-inventory --image=eu.gcr.io/box2box-cloud/moreapp-inventory-service-prod:${VERSION} --update-env-vars FASTAPI_ENV=production --min-instances=0 --max-instances=4 --cpu=2000m --memory=2048Mi --vpc-connector=mongodb-production-vpc --vpc-egress=all-traffic --platform=managed --ingress=all --allow-unauthenticated --region=europe-west1 --port=8000 --timeout=600 --concurrency=4 --project=box2box-cloud --service-account <EMAIL>
