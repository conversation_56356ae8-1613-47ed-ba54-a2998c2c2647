name: Staging Slack Payment Reminder CI/CD

on:
  push:
    branches:
      - dev
      - master
    paths:
      - 'api_gateway_backends/authorization.py'
      - 'api_gateway_backends/common.py'
      - 'api_gateway_backends/slack_payment_reminder.py'
      - 'api_gateway_backends/requirements/slack_payment_reminder.txt'
      - '.github/workflows/slack_payment_reminder.yml'
      - 'README.md'

jobs:
  dev:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Create env file
        run: |
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "SLACK_SIGNING_SECRET: $SLACK_SIGNING_SECRET" >> .env.yaml
          echo "SLACK_PAYMENT_REMINDER_TOPIC: 'gcf-webhooks-to-backend'" >> .env.yaml
        env:
          SLACK_SIGNING_SECRET: ${{ secrets.SLACK_SIGNING_SECRET }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p slack_payment_reminder_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/common.py api_gateway_backends/authorization.py slack_payment_reminder_deployment/
          cp api_gateway_backends/slack_payment_reminder.py api_gateway_backends/requirements/slack_payment_reminder.txt .env.yaml slack_payment_reminder_deployment/
          mv slack_payment_reminder_deployment/slack_payment_reminder.txt slack_payment_reminder_deployment/requirements.txt
          mv slack_payment_reminder_deployment/slack_payment_reminder.py slack_payment_reminder_deployment/main.py

      - name: Deploy cloud function in google cloud (dev)
        run: |
          cd slack_payment_reminder_deployment/
          gcloud functions deploy slack-payment-reminder --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 256MB --project box2box-cloud --timeout 60 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 4
  prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.10
        uses: actions/setup-python@v1
        with:
          python-version: "3.10"

      - name: Google cloud auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v1

      - name: Create env file
        run: |
          echo "PROJECT_ID: 'box2box-cloud'" >> .env.yaml
          echo "SLACK_SIGNING_SECRET: $SLACK_SIGNING_SECRET" >> .env.yaml
          echo "SLACK_PAYMENT_REMINDER_TOPIC: 'gcf-webhooks-to-backend-payment-reminder-prod'" >> .env.yaml
        env:
          SLACK_SIGNING_SECRET: ${{ secrets.SLACK_SIGNING_SECRET }}

      - name: Prepare folder to deployment
        run: |
          mkdir -p slack_payment_reminder_deployment
          cp -r api_gateway_backends/__init__.py api_gateway_backends/common.py api_gateway_backends/authorization.py slack_payment_reminder_deployment/
          cp api_gateway_backends/slack_payment_reminder.py api_gateway_backends/requirements/slack_payment_reminder.txt .env.yaml slack_payment_reminder_deployment/
          mv slack_payment_reminder_deployment/slack_payment_reminder.txt slack_payment_reminder_deployment/requirements.txt
          mv slack_payment_reminder_deployment/slack_payment_reminder.py slack_payment_reminder_deployment/main.py

      - name: Deploy cloud function in google cloud (prod)
        run: |
          cd slack_payment_reminder_deployment/
          gcloud functions deploy slack-payment-reminder-prod --entry-point entrypoint --runtime python310 --trigger-http --no-allow-unauthenticated --region europe-west1 --memory 512MB --project box2box-cloud --timeout 120 --env-vars-file .env.yaml --service-account <EMAIL> --max-instances 25
