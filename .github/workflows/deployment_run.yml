name: Email wrapper over mailjet deployment -- Cloud Run

on:
  push:
    branches:
      - branchdoesnotexistandwontbetriggered
    paths:
      - 'i18n/**.py'
      - 'main_run.py'
      - '.github/workflows/deployment_run.yml'
      - 'deployment/**'
      - 'Pipfile'

jobs:
  store_git_tag:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set git tag version globally
        shell: bash
        run: |
          expr $(git describe --tags $(git rev-list --tags --max-count=1)) > tag_version.txt

      - name: Upload version in artifact
        uses: actions/upload-artifact@v1
        with:
          name: git_version
          path: tag_version.txt

  build_and_deploy:
    runs-on: ubuntu-latest
    needs: store_git_tag

    steps:
      - name: Checkout repo
        uses: actions/checkout@v1

      - name: Set up Python 3.8
        uses: actions/setup-python@v1
        with:
          python-version: 3.8

      - name: Download git tag version
        uses: actions/download-artifact@v1
        with:
          name: git_version

      - name: Authenticate into Google Cloud Platform
        uses: google-github-actions/setup-gcloud@v0
        with:
          version: 'latest'
          service_account_key: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT_JSON }}
          export_default_credentials: true

      - name: Install docker-compose
        run: |
          pip install docker-compose

      - name: Create env file
        run: |
          echo "MAILJET_API_KEY=$MAILJET_API_KEY" >> .env
          echo "MAILJET_API_SECRET=$MAILJET_API_SECRET" >> .env
          echo "DOCUMENTATION_BUCKET_NAME=docs-box2box-prod" >> .env
        env:
          MAILJET_API_KEY: ${{ secrets.MAILJET_API_KEY }}
          MAILJET_API_SECRET: ${{ secrets.MAILJET_API_SECRET }}

      - name: Configure docker for GCP
        run: gcloud auth configure-docker

      - name: Build image
        run: |
          export VERSION=`cat git_version/tag_version.txt`
          sed -i -e s/\${VERSION}/$VERSION\/g deployment/docker-compose.yml
          docker-compose -f deployment/docker-compose.yml build

      - name: Push image
        run: docker-compose -f deployment/docker-compose.yml push

      - name: Move script to container and remove last API image from gcloud
        run: |
          chmod +x .github/scripts/remove_images_from_prodapi.sh
          ./.github/scripts/remove_images_from_prodapi.sh

      - name: Deploy mail service in cloud run
        run: |
          export VERSION=`cat git_version/tag_version.txt`
          gcloud run deploy ms-emails --image=eu.gcr.io/box2box-cloud/emails-service-prod:${VERSION} --min-instances=0 --max-instances=4 --cpu=1000m --memory=1024Mi --platform=managed --no-allow-unauthenticated --region=europe-west1 --port=8000 --timeout=600 --concurrency=4 --project=box2box-cloud --service-account <EMAIL>
