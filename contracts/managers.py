from django.db import models
from django.db.models import Q

from core.managers import NonOverridableQueryset


class ContractManager(models.Manager):
    def get_queryset(self):
        return NonOverridableQueryset(self.model)


class ContractAddressManager(models.Manager):
    def manually_created(self):
        return self.filter(is_manually_added=True)


class ServiceQueryset(models.QuerySet):
    def is_complete(self):
        return self.filter(
            Q(contract_id__isnull=False),
            Q(type__isnull=False),
            Q(first_name__isnull=False) | Q(last_name__isnull=False),
        )


class ServiceManager(models.Manager):
    def get_queryset(self):
        return ServiceQueryset(self.model)
