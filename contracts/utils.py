from django.utils.translation import gettext_lazy as _

MADRID = "ES-M"
LISBOA = "PT-L"
PORTO = "PT-P"
SEVILLA = "ES-S"
BARCELONA = "ES-B"
MALAGA = "ES-ML"
VALENCIA = "ES-V"
PARIS = "FR-P"
MILANO = "IT-M"
QUERETARO = "MX-Q"
GRANADA = "ES-GR"
SALAMANCA = "ES-SA"
BILBAO = "ES-BI"
SAN_SEBASTIAN = "ES-SS"
PAMPLONA = "ES-NA"

# TODO: drop this
COUNTRY_BASES = {
    MADRID: "ES",
    BARCELONA: "ES",
    MALAGA: "ES",
    VALENCIA: "ES",
    SEVILLA: "ES",
    LISBOA: "PT",
    PORTO: "PT",
    PARIS: "FR",
    MILANO: "ES",  # Temporarily, points to Spain
    QUERETARO: "MX",
    GRANADA: "ES",
    SALAMANCA: "ES",
    BILBAO: "ES",
    SAN_SEBASTIAN: "ES",
    PAMPLONA: "ES",
}

# NEEDED FOR PUBLIC WEBSITE AND WON'T BE INCLUDED IN THE CITY MODEL
NAME_TO_CITY_BASES = {
    "madrid": MADRID,
    "barcelona": BARCELONA,
    "malaga": MALAGA,
    "valencia": VALENCIA,
    "sevilla": SEVILLA,
    "lisboa": LISBOA,
    "porto": "PT-O",  # Wrong in hubspot, but we cannot change now
    "parís": PARIS,
    "milano": MILANO,
    "queretaro": QUERETARO,
    "granada": GRANADA,
    "salamanca": SALAMANCA,
    "bilbao": BILBAO,
    "san_sebastian": SAN_SEBASTIAN,
    "pamplona": PAMPLONA,
}

SHEETS_IDS = {
    "1001": "1XLANXuIamloAApNAUDbdr5JiQH5UGkplsl46h9Ujk_4",
    "1002": "1XLANXuIamloAApNAUDbdr5JiQH5UGkplsl46h9Ujk_4",
    "1004": "1XLANXuIamloAApNAUDbdr5JiQH5UGkplsl46h9Ujk_4",
    "1021": "1MGVdBa6taCaDIU7VOcxmG1He9Or1neqnMjpROIK1DqY",
    "1031": "17YaOjqn5i5qBIxF_apwLytzCToSHlpiz2bNVWqU2o_c",
    "1032": "17YaOjqn5i5qBIxF_apwLytzCToSHlpiz2bNVWqU2o_c",
    "1041": "16KsaJ1h7ULG45x7GtEP821oFrqc5Lk7IrDUqNhlqSPA",
    "1051": "1E_yi53xbQ0lymeunGqeg2hPBW-JvUY1SQyiVF0p88ao",
    "1052": "1E_yi53xbQ0lymeunGqeg2hPBW-JvUY1SQyiVF0p88ao",
    "2001": "1xaIrVXKI6j8UHNW89ZtkngL7ijJGM7u3YgQy74Y9aHk",
    "2002": "1xaIrVXKI6j8UHNW89ZtkngL7ijJGM7u3YgQy74Y9aHk",
    "2010": "1Y6vCx5O3Em5A6HozyL2IWwPDFZqrMJuV-iYl8D_DitA",
    "2011": "1Y6vCx5O3Em5A6HozyL2IWwPDFZqrMJuV-iYl8D_DitA",
    "3001": "*****************************-7G4nC7NzULN1_c",
    "4000": "1XLANXuIamloAApNAUDbdr5JiQH5UGkplsl46h9Ujk_4",
    "5000": "1XLANXuIamloAApNAUDbdr5JiQH5UGkplsl46h9Ujk_4",
}

CURRENCIES = {
    "MX": "MXN",
}


def build_contract_id(country_base, value):
    """Helper method to make the customer_id with country base"""

    return f"{COUNTRY_BASES.get(country_base) or 'ES'}-{value}"


# Model Common Bases
BASE_CHOICES = (
    (MADRID, _("Madrid")),
    (PORTO, _("Porto")),
    (SEVILLA, _("Sevilla")),
    (BARCELONA, _("Barcelona")),
    (MALAGA, _("Malaga")),
    (VALENCIA, _("Valencia")),
    (MILANO, _("Milano")),
    (PARIS, _("Paris")),
    (LISBOA, _("Lisboa")),
    (QUERETARO, _("Queretaro")),
    (GRANADA, _("Granada")),
    (SALAMANCA, _("Salamanca")),
    (BILBAO, _("Bilbao")),
    (SAN_SEBASTIAN, _("San Sebastian")),
    (PAMPLONA, _("Pamplona")),
)


def get_country_currency(country, fallback_currency="EUR"):
    """Helper method to retrieve the currency for non EUR countries"""
    try:
        return CURRENCIES[country]
    except KeyError:
        return fallback_currency


MONTHLY = "monthly"
QUARTERLY = "quarterly"
BIANNUAL = "biannual"
ANNUAL = "annual"

DEFAULT_BILLING_CYCLE_CHOICES = (
    (MONTHLY, _("Monthly")),
    (QUARTERLY, _("Quarterly")),
    (BIANNUAL, _("Biannual")),
    (ANNUAL, _("Annual")),
)

DEFAULT = "default"
PORTUGAL = "portugal"

EXCEL_TEMPLATE_CHOICES = (
    (DEFAULT, _("Default")),
    (PORTUGAL, _("Portugal")),
)


def translate_hubspot_billing_cycle_into_internal_billing_cycle(hubspot_billing_cycle):
    """
    Translates input values to their corresponding billing cycle keys.

    Args:
        input_value (str): The input string to translate.

    Returns:
        str: The corresponding billing cycle key if found, else None.
    """

    translation_map = {
        "1mes": MONTHLY,
        "3meses": QUARTERLY,
        "Semestral": BIANNUAL,
        "Anual": ANNUAL,
    }

    return translation_map.get(hubspot_billing_cycle)
