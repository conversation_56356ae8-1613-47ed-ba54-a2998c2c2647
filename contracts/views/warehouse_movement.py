from datetime import datetime

import pytz
from django.db import transaction
from django.utils.timezone import make_aware
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from contracts.filters import WarehouseMovementFilter
from contracts.serializers.warehouse_movement import (
    WarehouseMovementRelocationSerializer, WarehouseMovementSerializer)
from core.filters import CaseInsensitiveOrderingFilter, DjangoFilterBackend
from core.paginations import WarehouseMovementPagination
from core.utils import get_openapi_error_response
from intranet.models import WarehouseMovement
from riders.permissions import IsRiderAllowedUser


class WarehouseMovementViewSet(viewsets.ModelViewSet):
    """Endpoints to handle the  warehouse discrepancies"""

    serializer_class = WarehouseMovementSerializer
    permission_classes = (permissions.IsAuthenticated, IsRiderAllowedUser)
    queryset = WarehouseMovement.objects.all()
    filter_backends = (DjangoFilterBackend, CaseInsensitiveOrderingFilter)
    ordering_fields = ("sent_at", "created_at")
    ordering = [
        "-sent_at",
    ]
    pagination_class = WarehouseMovementPagination
    filterset_class = WarehouseMovementFilter
    http_method_names = ["get", "post"]

    def get_queryset(self):
        queryset = super().get_queryset().select_related("contract", "warehouse", "warehouse__city")
        return queryset.distinct()

    @swagger_auto_schema(
        operation_summary="Update location of a warehouse movement",
        operation_description="""Update a location of a warehouse movement marking the actual one as deprecated""",
        request_body=WarehouseMovementRelocationSerializer,
        responses={
            status.HTTP_200_OK: WarehouseMovementRelocationSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, "Update movement"),
        },
    )
    @action(methods=["post"], url_path="relocation", detail=True)
    def relocation(self, request, pk=None):
        qp_serializer = WarehouseMovementRelocationSerializer(data=request.data)
        if not qp_serializer.is_valid():
            return Response(qp_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        warehouse_movement = self.get_object()
        validated_data = qp_serializer.validated_data
        raw_data = validated_data["raw_data"]
        placement_data = raw_data["data"]["placements"][0]

        with transaction.atomic():
            new_warehouse_movement = WarehouseMovement.objects.create(
                location=placement_data["location"],
                pallet=placement_data["pallet"],
                floor=placement_data["floor"],
                internal_id=placement_data["id"],
                meta=placement_data.get("meta", {}),
                sent_at=make_aware(datetime.fromtimestamp(raw_data["timestamp"] / 1000), timezone=pytz.utc),
                rider_id=raw_data["rider_id"],
                warehouse_id=raw_data["warehouse_id"],
                service_id=None,  # Relocations has not linked service
                contract=warehouse_movement.contract,
                remote_dir=warehouse_movement.remote_dir,
            )

            # Update the original movement to mark it as deprecated
            WarehouseMovement.objects.filter(id=warehouse_movement.id).versioned_update(
                expected_version=validated_data["version"],
                parent_warehouse_movement_id=new_warehouse_movement.id,
                root_warehouse_movement_id=new_warehouse_movement.id,
            )

            # Update all related movements to point to the new root
            WarehouseMovement.objects.filter(root_warehouse_movement_id=warehouse_movement.id).update(
                root_warehouse_movement_id=new_warehouse_movement.id,
            )

        return Response(WarehouseMovementSerializer(new_warehouse_movement).data, status=status.HTTP_200_OK)
