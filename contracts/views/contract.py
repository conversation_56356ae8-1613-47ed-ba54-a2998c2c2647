from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, viewsets
from rest_framework.exceptions import MethodNotAllowed

from contracts.models import Contract
from contracts.serializers.contract import (ConcreteContractSerializer,
                                            ContractSerializer)


class ContractViewSet(viewsets.ModelViewSet):
    """Contract endpoints to get private data"""

    serializer_class = ContractSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Contract.objects.all()
    http_method_names = ["get", "post"]

    def get_queryset(self):
        queryset = super().get_queryset()
        return (
            queryset.filter(
                Q(user=self.request.user),
                (Q(imported_legacy=False) | (Q(imported_legacy=True) & Q(force_display=True))),
            )
            .select_related("warehouse")
            .order_by(
                "-contract_document_id",
            )
        )

    def get_serializer_class(self):
        if self.action == "retrieve":
            return ConcreteContractSerializer
        return self.serializer_class

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)
