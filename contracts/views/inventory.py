import logging

from django.db.models import F
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, viewsets
from rest_framework.response import Response

from contracts.filters import InventoryFilter
from contracts.models import Item
from contracts.serializers.inventory import (InventoryConcreteSerializer,
                                             InventoryMultipleLookupSerializer,
                                             InventoryPartialUpdateSerializer,
                                             InventoryQuerySetSerializer,
                                             InventorySerializer)
from core.filters import DjangoFilterBackend
from core.inspectors import BasePaginationInspector
from core.mixins import MultipleFieldLookupMixin
from core.paginations import InventoryPagination
from core.serializers import APIGatewayQuerySetSerializer
from core.utils import cached_model_property

logger = logging.getLogger(__name__)


class InventoryViewSet(MultipleFieldLookupMixin, viewsets.ModelViewSet):
    """Inventory endpoints to get private data"""

    serializer_class = InventorySerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Item.objects.all()
    filter_backends = (DjangoFilterBackend,)
    pagination_class = InventoryPagination
    filterset_class = InventoryFilter
    lookup_serializer_class = InventoryMultipleLookupSerializer
    # filter_backends = (CaseInsensitiveOrderingFilter, SearchFilter, DjangoFilterBackend)
    # search_fields = ("description",)
    # ordering_fields = ("tag_id", "delivery_date", "pick_up_date")
    # ordering = ["tag_id"]
    # pagination_class = InventoryPagination
    # filterset_class = InventoryFilter
    http_method_names = ["get", "patch"]

    def get_queryset(self):
        queryset = super().get_queryset()

        queryset_params = {
            "contract__user": self.request.user,
        }

        if self.action != "partial_update" and self.action != "retrieve":
            contract_id = self.request.query_params.get("contract_id", None)

            # By default, return empty data if we don't have a valid contract id, because is mandatory to filter data
            if not contract_id:
                return queryset.none()

            queryset_params["contract"] = contract_id
        elif self.action == "retrieve":
            # This endpoint is intended to be used by riders app only
            queryset_params = {}

        return (
            queryset.filter(**queryset_params).distinct().order_by(F("delivery_date").desc(nulls_first=True), "-tag_id")
        )

    def get_serializer_class(self):
        if self.action == "partial_update":
            return InventoryPartialUpdateSerializer
        elif self.action == "retrieve":
            return InventoryConcreteSerializer
        return self.serializer_class

    @swagger_auto_schema(
        operation_summary="List Inventories",
        query_serializer=InventoryQuerySetSerializer,
        paginator_inspectors=[BasePaginationInspector],
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @cached_model_property(cache_time_in_seconds=60 * 15, is_property=False)  # 15 minutes of cache
    @swagger_auto_schema(
        operation_summary="Read Inventory",
        operation_description="Only set an `id` or `tag_id` as path parameter, but not both at the same time",
        query_serializer=APIGatewayQuerySetSerializer,
        manual_parameters=[
            openapi.Parameter(
                "id",
                openapi.IN_PATH,
                description="A UUID string identifying this item.",
                type="string <uuid>",
                required=True,
            ),
            openapi.Parameter(
                "tag_id",
                openapi.IN_PATH,
                description="A string barcode identifying this item",
                type=openapi.TYPE_STRING,
                required=True,
            ),
        ],
    )
    def retrieve(self, request, *args, **kwargs):
        """Semantically, retrieve MUST return one and only one row from the database.
        However, we can have more than one item duplicated in the database, so this method will throw MultipleObjectsReturned exception
        To avoid this, we need to override this method without super() call as it assumes 'many=False' when serializing the response.
        @see core.mixins.get_object implementation to handle more than one item
        """  # NOQA: B950
        instance = self.get_object()
        serializer = self.get_serializer(instance, many=isinstance(instance, list))

        return Response(serializer.data)
