import logging

from django.db import transaction
from django.db.models import OuterRef, Prefetch, Q, Subquery
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from contracts.filters import ServiceFilter
from contracts.models import Item, Service, Warehouse
from contracts.serializers.service import (ServiceConcreteSerializer,
                                           ServiceFinishedSerializer,
                                           ServiceSerializer,
                                           ServiceTodayCompatSerializer,
                                           ServiceTodaySerializer,
                                           ServiceTrackingSerializer)
from core.filters import CaseInsensitiveOrderingFilter, DjangoFilterBackend
from core.inspectors import BasePaginationInspector
from core.paginations import ServicePagination
from core.serializers import APIGatewayQuerySetSerializer, EmptySerializer
from core.utils import get_openapi_error_response, get_openapi_response
from core.versioning import VersionedViewSetMixin, versioned
from intranet.models import Event
from riders.models import Team
from riders.permissions import IsRiderAllowedUser

logger = logging.getLogger(__name__)


@versioned(None, bind="get_list_serializer")  # Behavior for version pre 2.1.0
def get_list_serializer_old(self, *, query_params):
    if query_params.get("today"):
        return ServiceTodayCompatSerializer

    return get_list_serializer_new(self, query_params=query_params)


@versioned(">=2.1.0", bind="get_list_serializer")  # Behavior for version 2.1.0
def get_list_serializer_new(self, *, query_params):
    if query_params.get("status") == "finished":
        return ServiceFinishedSerializer

    if query_params.get("today"):
        return ServiceTodaySerializer

    if query_params.get("tracking"):
        return ServiceTrackingSerializer

    raise AttributeError("List serializer not found")


class ServiceViewSet(VersionedViewSetMixin, viewsets.ModelViewSet):
    """Services endpoints to fetch data from db"""

    serializer_class = ServiceSerializer
    permission_classes = (permissions.IsAuthenticated, IsRiderAllowedUser)
    queryset = Service.objects.all()
    filter_backends = (
        DjangoFilterBackend,
        CaseInsensitiveOrderingFilter,
    )
    ordering_fields = ("event__start_time", "submitted_at")
    ordering = [
        "-event__start_time",
    ]
    pagination_class = ServicePagination
    filterset_class = ServiceFilter
    http_method_names = ["get", "post"]
    serializer_mapper = {
        "list": lambda self, query_parameters: self.get_list_serializer(query_parameters),
        "retrieve": lambda _, __: ServiceConcreteSerializer,
    }

    def get_list_serializer(self, query_params):
        return self.get_versioned_function("get_list_serializer", query_params=query_params)

    def get_serializer_class(self):
        try:
            return self.serializer_mapper[self.action](self, self.request.query_params)
        except (KeyError, AttributeError):
            return self.serializer_class

    def get_serializer_context(self):
        context = super().get_serializer_context()

        if hasattr(self, "warehouses"):
            context["warehouses"] = self.warehouses
        return context

    def get_queryset(self):
        is_today_parameter_present = self.request.query_params.get("today")  # NOQA: F841
        status_parameter = self.request.query_params.get("status")  # NOQA: F841
        is_tracking_parameter_present = self.request.query_params.get("tracking")
        queryset = super().get_queryset()

        if self.action == "list":
            queryset = queryset.prefetch_related(
                Prefetch(
                    "event_set",
                    queryset=Event.objects.select_related("calendar", "calendar__city"),
                    to_attr="event_data",
                ),
                Prefetch(
                    "team_set",
                    queryset=Team.objects.filter(discarded_at__isnull=True)  # Only active teams
                    .select_related("rider")
                    .order_by("accepted_at"),
                    to_attr="rider_team",
                ),
            ).select_related("warehouse", "warehouse__city")

            if is_today_parameter_present:
                queryset = queryset.prefetch_related(
                    Prefetch(
                        "event_set", queryset=Event.objects.select_related("calendar", "service", "calendar__city")
                    ),
                    Prefetch(
                        "item_set",
                        queryset=Item.objects.select_related("service", "contract").order_by("created_at"),
                    ),
                )

            if is_tracking_parameter_present:
                queryset = queryset.prefetch_related(
                    Prefetch(
                        "team_set",
                        queryset=Team.objects.filter(
                            ~Q(rider=self.request.user),
                            Q(discarded_at__isnull=True),
                            Q(warehouse_movement_status__isnull=False),
                        ).select_related("rider"),
                        to_attr="full_team",
                    ),
                )

                self.warehouses = {
                    warehouse.id: warehouse
                    for warehouse in Warehouse.objects.filter(
                        id=Subquery(
                            self.queryset.filter(chosen_warehouse_id=OuterRef("id")).values("chosen_warehouse_id")[:1]
                        )
                    )
                    .select_related("city")
                    .distinct()
                }

            filter_criteria = {
                "team__rider": self.request.user,
                "team__discarded_at__isnull": True,
            }

            if status_parameter == Service.FINISHED:
                filter_criteria.update(**{
                    "team__sent_at__isnull": False,
                })

                self.warehouses = {
                    warehouse.id: warehouse
                    for warehouse in Warehouse.objects.filter(
                        id=Subquery(
                            self.queryset.filter(chosen_warehouse_id=OuterRef("id")).values("chosen_warehouse_id")[:1]
                        )
                    )
                    .select_related("city")
                    .distinct()
                }

            queryset = queryset.filter(**filter_criteria).is_complete()
        elif self.action == "retrieve":
            queryset = queryset.prefetch_related(
                Prefetch(
                    "event_set",
                    queryset=Event.objects.select_related("calendar", "calendar__city"),
                    to_attr="event_data",
                ),
                Prefetch(
                    "team_set",
                    queryset=Team.objects.filter(discarded_at__isnull=True)  # Only active teams
                    .select_related("rider")
                    .order_by("accepted_at"),
                    to_attr="rider_team",
                ),
                Prefetch(
                    "item_set",
                    queryset=Item.objects.select_related("service", "contract").order_by("created_at"),
                ),
            ).select_related("warehouse", "warehouse__city")

            queryset = queryset.is_complete()
        elif self.action == "accept":
            queryset = queryset.prefetch_related(
                Prefetch(
                    "team_set",
                    queryset=Team.objects.select_related("service").filter(
                        rider=self.request.user, discarded_at__isnull=True
                    ),
                    to_attr="rider_team",
                )
            )

        return queryset

    @swagger_auto_schema(
        operation_summary="List Services",
        query_serializer=APIGatewayQuerySetSerializer,
        paginator_inspectors=[BasePaginationInspector],
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Read Services",
        query_serializer=APIGatewayQuerySetSerializer,
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Accept the service",
        operation_description=(
            "Behind the scenes, it will link the rider with the service preventing other riders from accepting it"
        ),
        query_serializer=APIGatewayQuerySetSerializer,
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Accepted"),
            status.HTTP_404_NOT_FOUND: get_openapi_error_response(non_field_error="Error"),
        },
    )
    @action(methods=["post"], url_path="accept", detail=True)
    def accept(self, request, pk=None):
        service = self.get_object()

        # try:
        #     team_id = service.rider_team[0].id
        # except IndexError:
        #     team_id = None

        with transaction.atomic():
            Service.objects.select_for_update().filter(id=service.id, status=Service.NOT_STARTED).update(
                status=Service.IN_PROGRESS
            )

            Team.objects.select_for_update().filter(service=service, rider=self.request.user).update(
                accepted_at=timezone.now()
            )

        # rider_has_been_updated = False
        # try:
        #     notification_service = MulticastFCMNotification()
        #     notification_response = notification_service.notify(service)
        #
        #     # Prepare response
        #     teams = []
        #     for idx, team in enumerate(notification_response.configuration):
        #         message_response = notification_response.response.responses[idx]
        #         if message_response.success:
        #             rider_has_been_updated = True
        #             teams.append(
        #                 Team(
        #                     id=team,
        #                     status=Team.SENT,
        #                     sent_at=timezone.now(),
        #                     accepted_at=Case(
        #                         When(
        #                             Q(accepted_at__isnull=True) & Q(id=team_id),
        #                             then=timezone.now(),
        #                         ),
        #                         default=F("accepted_at"),
        #                         output_field=DateTimeField(),
        #                     ),
        #                 )
        #             )
        #
        #     if teams:
        #         Team.objects.bulk_update(teams, fields=["status", "sent_at", "accepted_at"])
        # except NotificationException as error:
        #     logger.error(f"Error sending notification when accepting service {service.contract_id}: {error}")
        # finally:
        #     # Update the accept field anyway
        #     if not rider_has_been_updated and team_id:
        #         with transaction.atomic():
        #             team_subquery = (
        #                 Team.objects.filter(service=OuterRef("service"))
        #                 .values("service")
        #                 .annotate(accepted=Sum(Case(When(accepted_at__isnull=False, then=1), default=0)))
        #                 .filter(accepted=0)
        #             )
        #             subquery = Subquery(team_subquery.values("service")[:1])
        #             updated_count = (
        #                 Team.objects.select_for_update()
        #                 .filter(service=subquery)
        #                 .update(
        #                     accepted_at=Case(
        #                         When(
        #                             id=team_id,
        #                             then=timezone.now(),
        #                         ),
        #                         default=F("accepted_at"),
        #                         output_field=DateTimeField(),
        #                     )
        #                 )
        #             )
        #
        #         # Services cannot be accepted twice or more times
        #         if updated_count < 1:
        #             return Response(
        #                 {"non_field_errors": _("Service already accepted")}, status=status.HTTP_400_BAD_REQUEST
        #             )

        return Response({"non_field_errors": _("Accepted")}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Release the service",
        operation_description="Given a previously accepted service, it will marked as not accepted by given rider",
        query_serializer=APIGatewayQuerySetSerializer,
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Released"),
            status.HTTP_404_NOT_FOUND: get_openapi_error_response(non_field_error="Error"),
        },
    )
    @action(methods=["post"], url_path="release", detail=True)
    def release(self, request, pk=None):
        service = self.get_object()

        with transaction.atomic():
            Team.objects.select_for_update().filter(
                service=service, rider=self.request.user, accepted_at__isnull=False, discarded_at__isnull=True
            ).update(accepted_at=None)

            number_of_accepted_services = Team.objects.filter(service=service, accepted_at__isnull=False).count()
            if number_of_accepted_services == 0:
                Service.objects.select_for_update().filter(id=service.id, status=Service.IN_PROGRESS).update(
                    status=Service.NOT_STARTED
                )

        return Response({"non_field_errors": _("Released")}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Discard the service",
        operation_description="Remove the service from the riders' pending list",
        query_serializer=APIGatewayQuerySetSerializer,
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Discarded"),
            status.HTTP_404_NOT_FOUND: get_openapi_error_response(non_field_error="Error"),
        },
    )
    @action(methods=["post"], url_path="cancel", detail=True)
    def cancel(self, request, pk=None):
        service = self.get_object()
        Team.objects.filter(service=service, rider=self.request.user).update(discarded_at=timezone.now())

        return Response({"non_field_errors": _("Discarded")}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Mark as discarded the inventory location for the service",
        operation_description="Remove the service from the riders' pending list",
        query_serializer=APIGatewayQuerySetSerializer,
        request_body=EmptySerializer,
        responses={
            status.HTTP_200_OK: get_openapi_response("Discarded"),
            status.HTTP_404_NOT_FOUND: get_openapi_error_response(non_field_error="Error"),
        },
    )
    @action(methods=["post"], url_path="discard_tracking", detail=True)
    def discard_tracking(self, request, pk=None):
        service = self.get_object()
        Team.objects.filter(service=service, rider=self.request.user).update(warehouse_movement_status=Team.DISCARDED)

        return Response({"non_field_errors": _("Discarded")}, status=status.HTTP_200_OK)
