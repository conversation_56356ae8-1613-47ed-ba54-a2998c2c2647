import logging

from django.db.models import Prefetch
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, viewsets

from contracts.filters import TaskFilter
from contracts.serializers.task import TaskSerializer
from core.filters import CaseInsensitiveOrderingFilter, DjangoFilterBackend
from core.paginations import TaskPagination
from core.serializers import APIGatewayQuerySetSerializer
from intranet.models import Event, Task
from riders.models import Team
from riders.permissions import IsRiderAllowedUser

logger = logging.getLogger(__name__)


class TaskViewSet(viewsets.ModelViewSet):
    """Task endpoints to fetch data from db"""

    serializer_class = TaskSerializer
    permission_classes = (permissions.IsAuthenticated, IsRiderAllowedUser)
    queryset = Task.objects.all()
    filter_backends = (DjangoFilterBackend, CaseInsensitiveOrderingFilter)
    ordering_fields = ("event__start_time", "created_at")
    ordering = [
        "-event__start_time",
    ]
    filterset_class = TaskFilter
    pagination_class = TaskPagination
    http_method_names = ["get"]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        device_timezone = self.request.headers.get("X-Timezone")
        context.update({"device_timezone": device_timezone})

        return context

    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(team__rider=self.request.user, team__sent_at__isnull=True)
            .prefetch_related(
                Prefetch("event_set", queryset=Event.objects.all(), to_attr="event_data"),
                Prefetch("team_set", queryset=Team.objects.filter(rider=self.request.user), to_attr="team_data"),
            )
        )

    @swagger_auto_schema(
        operation_summary="List tasks",
        query_serializer=APIGatewayQuerySetSerializer,
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
