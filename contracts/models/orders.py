import uuid

from django.conf import settings
from django.db import models
from django.db.models.signals import post_save
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from contracts.receivers import (send_email_in_new_delivery_order,
                                 send_email_in_new_pickup_order)


class Order(models.Model):
    """Base model to handle service requests: pickup and delivery"""

    STREET_LEVEL = "street_level"
    STAIRS = "stairs"
    SMALL_LIFT = "small_lift"
    LARGE_LIFT = "large_lift"

    ACCESS_CHOICES = (
        (STREET_LEVEL, _("Street level")),
        (STAIRS, _("Stairs")),
        (SMALL_LIFT, _("Small lift")),
        (LARGE_LIFT, _("Large lift")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    date = models.DateTimeField(default=timezone.now, help_text=_("Order date"))

    access = models.CharField(
        max_length=20, choices=ACCESS_CHOICES, null=True, blank=False, default=STREET_LEVEL, help_text=_("Access type")
    )
    extra_comments = models.TextField(null=True, blank=True, help_text=_("Additional comments from the user"))
    floor = models.CharField(max_length=20, null=True, blank=False)

    # Metadata
    created_at = models.DateTimeField(default=timezone.now, help_text=_("Creation date"))

    # Foreign keys
    contract = models.ForeignKey("contracts.Contract", null=True, blank=True, on_delete=models.CASCADE)
    contract_address = models.ForeignKey("contracts.ContractAddress", null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    @property
    def backoffice_url(self):
        if self.contract:
            return f"{settings.INTRANET_URL}/contracts/{self.contract.pk}"
        return ""


class Pickup(Order):
    """Model to handle pickups"""

    box_packaging = models.BooleanField(default=False)
    furniture_packaging = models.BooleanField(default=False)
    furniture_disassemble = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.access} - {self.contract}"


class Delivery(Order):
    """Model to handle deliveries"""

    furniture_assembly = models.BooleanField(default=False)

    class Meta(Order.Meta):
        verbose_name_plural = "deliveries"

    def __str__(self):
        return f"Delivery request:: {self.created_at.strftime('%Y-%m-%d')}"


post_save.connect(send_email_in_new_pickup_order, sender=Pickup)
post_save.connect(send_email_in_new_delivery_order, sender=Delivery)
