import uuid

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>ield
from django.contrib.postgres.indexes import GinIndex
from django.db import models
from django.utils.translation import gettext_lazy as _

from contracts.utils import BASE_CHOICES, DEFAULT, EXCEL_TEMPLATE_CHOICES
from core.fields import AutoIncrementField
from core.managers import RelatedManager
from core.mixins import RelatedManagerModelMixin
from core.models.base import ModelForGeneratedFieldsMixin


class Warehouse(ModelForGeneratedFieldsMixin, models.Model, RelatedManagerModelMixin):
    LOCATION = "location"
    PALLET = "pallet"
    FLOOR = "floor"
    PICTURES = "pictures"

    TRACKING_INVENTORY_CHOICES = [
        (LOCATION, _("Location"), _("location_description")),
        (PALLET, _("Pallet"), _("pallet_description")),
        (FLOOR, _("Floor"), _("floor_description")),
        (PICTURES, _("Pictures"), _("pictures_description")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    warehouse_id = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        help_text=_("Warehouse id used for moreapp compatibility. It will dropped in future releases"),
        unique=True,
    )
    internal_id_for_differences = AutoIncrementField(null=True, start_value=6000, increment_by=1)
    warehouse_city = models.CharField(
        max_length=100,
        choices=BASE_CHOICES,
        null=True,
        blank=True,
        help_text=_("Warehouse id used for moreapp compatibility. It will dropped in future releases"),
    )
    name = models.CharField(max_length=150, null=True, blank=True, help_text=_("Warehouse name"))
    normalized_name = models.CharField(
        max_length=150,
        null=True,
        unique=True,
        help_text=_("Used to lookup the name"),
    )
    canonical_name = models.CharField(
        max_length=150,
        null=True,
        unique=True,
        help_text=_("Warehouse building canonical name"),
    )
    tracking_inventory = ArrayField(
        models.CharField(
            max_length=200,
            choices=[(value, label) for value, label, _ in TRACKING_INVENTORY_CHOICES],
            null=True,
            blank=True,
        ),
        blank=True,
        null=True,
    )
    excel_notification_template = models.CharField(
        max_length=200,
        choices=EXCEL_TEMPLATE_CHOICES,
        null=True,
        blank=True,
        default=DEFAULT,
        help_text=_("Excel template to notify the warehouses"),
    )
    allows_warehouse_differences = models.BooleanField(
        default=False, help_text=_("If it is needed to calculate the warehouse differences. Internal usage only")
    )
    deactivated = models.BooleanField(
        default=False,
        help_text=_(
            "If this warehouse can be used to book services. It is preferred this over deleting it due to all"
            " implications may have delete it"
        ),
    )

    created_at = models.DateTimeField(auto_now_add=True)

    # Foreign keys
    city = models.ForeignKey("intranet.City", null=True, blank=True, on_delete=models.SET_NULL)

    # Custom manager to add helper queries
    objects = RelatedManager()

    class Meta:
        indexes = [
            models.Index(fields=("warehouse_id",)),
            models.Index(fields=("created_at",)),
            GinIndex(fields=("normalized_name",)),
        ]

    def __str__(self):
        return self.name or "-"

    @property
    def warehouse_differences_id(self):
        return self.warehouse_id or self.internal_id_for_differences

    @property
    def warehouse_city_name(self):
        return self.city.name if self.city else None

    @classmethod
    def tracking_inventory_choices_to_dict(cls):
        return [
            {
                "id": choice[0],
                "translation": choice[1],
                "description": choice[2],
            }
            for choice in cls.TRACKING_INVENTORY_CHOICES
        ]


class WarehouseConfiguration(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    created_at = models.DateTimeField(auto_now_add=True)

    # Foreign keys
    rider = models.ForeignKey("users.User", null=True, blank=True, on_delete=models.CASCADE)
    warehouse = models.ForeignKey("contracts.Warehouse", null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        constraints = [models.UniqueConstraint(fields=["rider", "warehouse"], name="unique_warehouse_configuration")]
