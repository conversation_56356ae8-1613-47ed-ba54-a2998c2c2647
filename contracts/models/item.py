import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _


class BaseAbstractItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    description = models.TextField(null=True, blank=True, help_text=_("Object description provided in contract"))
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True


class OrderItem(BaseAbstractItem):
    """Model to save related items to delivery orders"""

    units = models.IntegerField(default=0)
    dimensions = models.CharField(
        max_length=100, null=True, blank=True, help_text=_("Item dimensions in the form of WxHxD")
    )


class PickupItem(BaseAbstractItem):
    """Model to save related items to pickup orders"""

    units = models.IntegerField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    depth = models.FloatField(default=0)

    # Foreign keys
    pickup = models.ForeignKey("contracts.Pickup", null=True, blank=True, on_delete=models.CASCADE)


class Item(BaseAbstractItem):
    """Model for item stored in different warehouses"""

    tag_id = models.CharField(max_length=200, null=True, help_text=_("Object barcode tag id"))
    delivery_date = models.DateField(null=True, blank=True, help_text=_("Delivery date for this item"))
    pick_up_date = models.DateField(null=True, blank=True, help_text=_("Pick up date for this item"))
    created_at = models.DateTimeField(auto_now_add=True)
    warehouse_key = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text=_("Warehouse where is stored. Will be deprecated in future deployments"),
    )
    remote_dir = models.CharField(max_length=300, null=True, blank=True)
    comment = models.TextField(
        null=True, blank=True, help_text=_("Additional comment for items, added thru customer portal")
    )

    app_id = models.CharField(
        max_length=100, null=True, blank=True, help_text=_("Temporal id used by the app before creating the item in db")
    )

    # Foreign keys
    contract = models.ForeignKey("contracts.Contract", null=True, blank=False, on_delete=models.CASCADE)
    delivery = models.ForeignKey("contracts.Delivery", null=True, blank=False, on_delete=models.SET_NULL)
    service = models.ForeignKey("contracts.Service", null=True, blank=False, on_delete=models.SET_NULL)
    warehouse = models.ForeignKey("contracts.Warehouse", null=True, blank=False, on_delete=models.SET_NULL)

    class Meta:
        indexes = [models.Index(fields=("tag_id",))]

    def __str__(self):
        return self.tag_id or "-"

    @property
    def is_selectable_for_delivery(self):
        return self.delivery is None

    @property
    def signed_user_name(self):
        return self.contract.signed_user_name if self.contract else None
