from abc import ABC, abstractmethod

from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _

from contracts.models import Service
from contracts.service_templates.base import (ContractRenderer, ExtraItem,
                                              ServiceTemplate)


class SpaceResolverStrategy(ABC):
    key = _("Space in m²")

    def is_applicable(self, service: Service, extra_content: dict):
        return False

    @abstractmethod
    def _set_value(self, service: Service, extra_content: dict):
        pass

    def format(self, service: Service, extra_content: dict):
        value = self._set_value(service, extra_content)
        if value is None:
            return {}

        return {
            "key": force_str(self.key),
            "value": value,
        }


class StudentPackNoSpaceResolverStrategy(SpaceResolverStrategy):
    def is_applicable(self, service: Service, extra_content: dict):
        settings = extra_content.get("settings")
        if not settings:
            return False

        return settings.student_pack_space_in_pdf is None

    def _set_value(self, service: Service, extra_content: dict):
        return None


class NumberSpaceResolverStrategy(SpaceResolverStrategy):
    def is_applicable(self, service: Service, extra_content: dict):
        settings = extra_content.get("settings")

        return not settings

    def _set_value(self, service: Service, extra_content: dict):
        contract_warehouse_space = float(service.lookup_field_on_raw_data("warehouse_space") or 0)
        contract_warehouse_space_greater_than_fifty = float(service.lookup_field_on_raw_data("plus_fifty_meters") or 0)
        return (
            "{:.1f}".format(
                max(contract_warehouse_space_greater_than_fifty, contract_warehouse_space)
                if contract_warehouse_space <= 0
                else contract_warehouse_space
            )
            .rstrip("0")
            .rstrip(".")
        )


class StudentPackSpaceResolverStrategy(SpaceResolverStrategy):
    key = _("Hired space")

    def is_applicable(self, service: Service, extra_content: dict):
        settings = extra_content.get("settings")
        if not settings:
            return False

        return settings.student_pack_space_in_pdf is not None

    def _set_value(self, service: Service, extra_content: dict):
        settings = extra_content.get("settings")
        if settings.student_pack_space_in_pdf <= -100:
            return force_str(service.pdf_space)

        return "{:.1f}".format(settings.student_pack_space_in_pdf).rstrip("0").rstrip(".")


class SpaceResolverMixin:
    space_resolver_strategies = [
        StudentPackNoSpaceResolverStrategy(),
        NumberSpaceResolverStrategy(),
        StudentPackSpaceResolverStrategy(),
    ]

    def get_space_resolver_strategy(self, service: Service, extra_content: dict) -> dict:
        for strategy in self.space_resolver_strategies:
            if strategy.is_applicable(service, extra_content):
                return strategy.format(service, extra_content)

        return StudentPackNoSpaceResolverStrategy().format(service, extra_content)


class AnnexTemplate(ServiceTemplate, SpaceResolverMixin):
    renderer = ContractRenderer
    additional_services = [
        ExtraItem(
            key="packaging", translated_key=_("Furniture packaging"), main_title=_("Furniture packaging service")
        ),
        ExtraItem(
            key="furniture_assembly_options",
            translated_key=_("Furniture assembly/disassembly"),
            main_title=_("Furniture assembly/disassembly service"),
        ),
        ExtraItem(
            key="furniture_assembly_units",
            parent="furniture_assembly_options",
            translated_key=_("Time spent"),
            extra_content="h",
            callback=lambda x: "{:.1f}".format(float(x) / 2).rstrip("0").rstrip("."),
        ),
        ExtraItem(
            key="furniture_assembly_extra",
            parent="furniture_assembly_options",
            translated_key=_("Time spent"),
            replace_to="furniture_assembly_units",
        ),
        ExtraItem(
            key="furniture_assembly_description",
            parent="furniture_assembly_options",
            translated_key=_("Assembled/Disassembled furniture"),
        ),
        ExtraItem(key="stairs_options", translated_key=_("Stairs"), main_title=_("Stairs service")),
        ExtraItem(key="stairs_floor", parent="stairs_options", translated_key=_("Floors")),
        ExtraItem(
            key="stairs_volume",
            parent="stairs_options",
            translated_key=_("Volume moved in m²"),
            extra_content="m²",
            callback=lambda x: "{:.1f}".format(float(x)).rstrip("0").rstrip("."),
        ),
        ExtraItem(
            key="packaging_box_normal_options",
            translated_key=_("Packaging of moving boxes"),
            main_title=_("Packaging of moving boxes service"),
        ),
        ExtraItem(key="amount_box_normal_packaging", parent="packaging_box_normal_options", translated_key=_("Units")),
        ExtraItem(
            key="packaging_box_closet_options",
            translated_key=_("Packaging of wardrobe boxes"),
            main_title=_("Packaging of wardrobe boxes service"),
        ),
        ExtraItem(key="amount_box_closet_packaging", parent="packaging_box_closet_options", translated_key=_("Units")),
        ExtraItem(key="parking", translated_key=_("Parked withing a range of 50m"), main_title=_("Parking")),
        ExtraItem(key="kilometers_options", translated_key=_("Kilometers"), main_title=_("Kilometers")),
        ExtraItem(
            key="kilometers",
            parent="kilometers_options",
            translated_key=_("Distance"),
            extra_content="km",
            callback=lambda x: "{:.1f}".format(float(x)).rstrip("0").rstrip("."),
        ),
        ExtraItem(key="box_normal_options", translated_key=_("Moving boxes"), main_title=_("Moving box sales")),
        ExtraItem(key="amount_box_normal", parent="box_normal_options", translated_key=_("Units")),
        ExtraItem(key="box_closet_options", translated_key=_("Wardrobe boxes"), main_title=_("Wardrobe box sales")),
        ExtraItem(key="amount_delivery_box", parent="box_closet_options", translated_key=_("Units")),
        ExtraItem(key="clean_space_options", translated_key=_("Clean spot"), main_title=_("Clean spot")),
        ExtraItem(
            key="clean_space",
            parent="clean_space_options",
            translated_key=_("Volume moved to clean spot"),
            extra_content="m²",
            callback=lambda x: "{:.1f}".format(float(x)).rstrip("0").rstrip("."),
        ),
        ExtraItem(key="blanket_options", translated_key=_("Blanket"), main_title=_("Blanket")),
        ExtraItem(key="blanket", parent="blanket_options", translated_key=_("Units")),
        ExtraItem(key="seal_options", translated_key=_("Seal"), main_title=_("Seal")),
        ExtraItem(key="seal", parent="seal_options", translated_key=_("Units")),
        ExtraItem(key="bubble_paper_options", translated_key=_("Bubble paper"), main_title=_("Bubble paper")),
        ExtraItem(key="bubble_paper", parent="bubble_paper_options", translated_key=_("Units")),
        ExtraItem(key="additional_stop", translated_key=_("Additional stop"), main_title=_("Additional stop")),
        ExtraItem(key="additional_stop_comments", parent="additional_stop", translated_key=_("Comments")),
    ]

    def parse_inventory(self, service):
        """Sort inventory items putting on the top items without pictures"""

        items_without_photos = []
        items_with_photos = []
        inventory = service.lookup_field_on_raw_data("added_items")

        for item in inventory:
            partial_item = {"tag_id": item.get("tag_id"), "description": item.get("description"), "photos": []}

            photos = item.get("photos", []) or []
            if photos:
                # Retrieve every photo url from remote GCS
                partial_item["photos"] = [
                    f"documentation/{service.contract_id}/inventory/{item.get('tag_id')}/{service.type}__{photo_id}.jpg"
                    for photo_id in photos
                ]
                items_with_photos.append(partial_item)
                continue

            items_without_photos.append(partial_item)

        # Merge inventory photos
        items_without_photos.extend(items_with_photos)
        return items_without_photos

    def build_template_context(self, service: Service, extra_content: dict):
        # Extract fields from raw response
        contract_first_name = service.lookup_field_on_raw_data("first_name")
        contract_last_name = service.lookup_field_on_raw_data("last_name")
        contract_national_id = service.lookup_field_on_raw_data("national_id")
        contract_date = service.lookup_field_on_raw_data("date")
        contract_address = service.lookup_field_on_raw_data("address")
        contract_customer_signature = service.lookup_field_on_raw_data("customer_signature")
        contract_rider_signature = service.lookup_field_on_raw_data("rider_signature")
        contract_comment = service.lookup_field_on_raw_data("comment")
        contract_inventory = self.parse_inventory(service)
        contract_services = self.parse_additional_services(service)

        space_resolver_strategy_format = self.get_space_resolver_strategy(service, extra_content)

        # Build the context
        return {
            "main": {
                "title": force_str(_("{type} delivery note").format(type=self.translated_services.get(service.type))),
                "subtitle": None,
                "type": service.type,
            },
            "contract": {
                "title": force_str(_("Contract data")),
                "id": {"key": force_str(_("Contract identifier")), "value": service.contract_id},
            },
            "personal": {
                "title": force_str(_("Personal data")),
                "firstname": {"key": force_str(_("First name")), "value": contract_first_name},
                "lastname": {"key": force_str(_("Last name")), "value": contract_last_name},
                "national_id": {"key": force_str(_("National ID")), "value": contract_national_id},
            },
            "declaration": {
                "title": force_str(_("Date and address")),
                "address": {"key": force_str(_("Address")), "value": contract_address},
                "date": {"key": force_str(_("Date")), "value": contract_date},
            },
            "service": {
                "title": force_str(_("Service")),
                "type": {"key": force_str(_("Service type")), "value": force_str(_(service.service_name))},
                "space": space_resolver_strategy_format,
            },
            "inventory": {
                "title": force_str(_("Inventory")),
                "header": {"code": force_str(_("Code")), "description": force_str(_("Description"))},
                "empty": force_str(self.empty_inventory_description.get(service.type)),
                "items": contract_inventory,
            },
            "additional_services": {"title": force_str(_("Additional Services")), "services": contract_services},
            "comment": {
                "title": force_str(_("Comment")),
                "comment": contract_comment,
            },
            "signature": {
                "title": force_str(_("Signature")),
                "customer": {
                    "title": force_str(_("Customer signature")),
                    "signature": contract_customer_signature,
                },
                "company": {"title": force_str(_("Company signature")), "signature": contract_rider_signature},
            },
        }
