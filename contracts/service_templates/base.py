import tempfile
from dataclasses import dataclass
from io import Bytes<PERSON>
from typing import Callable

import pdfkit
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _

from contracts.exceptions import ServiceContentException
from contracts.models import Service
from contracts.service_templates.utils import sanitize_input


class BaseServiceRenderer:
    template_path = None
    header_template_path = "contracts/header.html"

    @classmethod
    def render(cls, pdf_context: object) -> object:
        if cls.template_path is None:
            raise ServiceContentException("Missing template_path")

        with tempfile.NamedTemporaryFile(suffix=".html") as temp_file:
            temp_file.write(render_to_string(cls.header_template_path).encode("utf-8"))
            temp_file.flush()

            pdfkit_options = settings.PDFKIT_OPTIONS.copy()
            pdfkit_options.update(
                **{"header-html": temp_file.name, "footer-right": "[page]/[toPage]", "footer-font-size": 8}
            )

            in_memory_file = BytesIO()
            in_memory_file.write(
                pdfkit.from_string(
                    render_to_string(cls.template_path, pdf_context), options=pdfkit_options, verbose=True
                )
            )

        return in_memory_file


class ContractRenderer(BaseServiceRenderer):
    template_path = "contracts/document.html"


class ConditionsRenderer(BaseServiceRenderer):
    template_path = "contracts/conditions.html"


class BoxRenderer(BaseServiceRenderer):
    template_path = "contracts/box.html"


@dataclass
class ExtraItem:
    key: str
    translated_key: str
    parent: str = None
    replace_to: str = None
    main_title: str = None
    extra_content: str = None
    callback: Callable = lambda x: x


class BaseTemplate:
    renderer = None

    def build_template_context(self, service: Service, extra_content: dict):
        raise NotImplementedError("Not implemented yet!")

    def render_template(
        self,
        service: Service,
        extra_content: dict,
        language: str = None,
    ):
        # Activate the language
        activate(language)

        context = self.build_template_context(service, extra_content)
        return self.renderer.render(pdf_context=context)


class ServiceTemplate(BaseTemplate):
    additional_services = []
    translated_services = {
        Service.INITIAL_PICKUP: _("Pick up"),
        Service.EXTRA_PICKUP: _("Pick up"),
        Service.PARTIAL_DELIVERY: _("Delivery"),
        Service.FINAL_DELIVERY: _("Delivery"),
    }
    empty_inventory_description = {
        Service.INITIAL_PICKUP: _("No inventory picked up"),
        Service.EXTRA_PICKUP: _("No inventory picked up"),
        Service.PARTIAL_DELIVERY: _("No inventory delivered"),
        Service.FINAL_DELIVERY: _("No inventory delivered"),
        Service.MOVING: _("No pictures taken"),
    }

    def parse_additional_services(self, service, service_list=None):
        if service_list is None:
            service_list = []

        services = {}
        for item in service_list or self.additional_services:
            if item.parent and not service.lookup_field_on_raw_data(item.parent):
                continue

            if item.key not in services and not item.parent:
                services[item.key] = {"title": item.main_title, "values": []}

            if item.replace_to:
                # Replace the item with `replace_to` field and continue to next item
                for temporal_item in services[item.parent]["values"]:
                    item_value = service.lookup_field_on_raw_data(item.key)
                    if item_value and temporal_item.get("_internal_key") == item.replace_to:
                        temporal_item["value"] = item.callback(sanitize_input(item_value))
                        break
                continue

            services[item.parent or item.key]["values"].append({
                "_internal_key": item.key,
                "key": item.translated_key,
                "value": item.callback(sanitize_input(service.lookup_field_on_raw_data(item.key))),
                "extra_value": item.extra_content,
                "child": item.parent is not None,
            })
        return services.values()
