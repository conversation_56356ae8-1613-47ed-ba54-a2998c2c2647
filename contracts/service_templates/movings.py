from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _

from contracts.models import Service
from contracts.service_templates import AnnexTemplate


class MovingTemplate(AnnexTemplate):
    def build_template_context(self, service: Service, extra_content: dict):
        context = super().build_template_context(service, extra_content)

        contract_space = float(service.lookup_field_on_raw_data("space") or 0)
        contract_space = "{:.1f}".format(contract_space).rstrip("0").rstrip(".")

        context["main"]["title"] = _("Moving service")
        context["inventory"]["header"] = {"code": force_str(_("Pictures")), "description": ""}
        context["inventory"]["empty"] = force_str(self.empty_inventory_description.get(service.type))
        context["service"]["space"]["value"] = contract_space
        return context

    def parse_inventory(self, service):
        items = []
        inventory = service.lookup_field_on_raw_data("added_items")

        for item in inventory:
            photos = item.get("photos", []) or []

            if photos:
                items.extend([
                    f"documentation/{service.contract_id}/inventory/moving/moving__{photo_id}.jpg"
                    for photo_id in photos
                ])

        if items:
            items = [{"photos": items}]

        return items
