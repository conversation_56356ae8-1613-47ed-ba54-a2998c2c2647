from django.conf import settings
from django.utils.translation import gettext_lazy as _

from contracts.models import Service
from contracts.service_templates.base import BaseTemplate, ConditionsRenderer


class BaseConditionsTemplate(BaseTemplate):
    renderer = ConditionsRenderer


class ConditionsTemplateSpainPortugal(BaseConditionsTemplate):
    def build_template_context(self, service: Service, extra_content: dict):
        return {
            "main": {"title": _("Terms of service"), "subtitle": _("Legal conditions")},
            "conditions": {
                "title": _("General conditions"),
                "values": [
                    {
                        "title": "",
                        "body": _("conditions_1 {email}").format(
                            email=settings.SUPPORT_TEAM_EMAILS.get(service.country or "ES")[0]
                        ),
                    },
                    {"title": _("title_2"), "body": _("conditions_2")},
                    {"title": _("title_3"), "body": _("conditions_3")},
                    {"title": _("title_4"), "body": _("conditions_4")},
                    {"title": _("title_5"), "body": _("conditions_5")},
                    {"title": _("title_6"), "body": _("conditions_6")},
                    {"title": _("title_7"), "body": _("conditions_7")},
                    {"title": _("title_8"), "body": _("conditions_8")},
                    {"title": _("title_9"), "body": _("conditions_9")},
                    {"title": _("title_10"), "body": _("conditions_10")},
                    {"title": _("title_11"), "body": _("conditions_11")},
                    {"title": _("title_12"), "body": _("conditions_12")},
                    {
                        "title": _("title_13"),
                        "body": _("conditions_13 {email}").format(
                            email=settings.SUPPORT_TEAM_EMAILS.get(service.country or "ES")[0]
                        ),
                    },
                    {"title": _("title_14"), "body": _("conditions_14")},
                    {"title": _("title_15"), "body": _("conditions_15")},
                ],
            },
        }


class ConditionsTemplate(BaseConditionsTemplate):
    def build_template_context(self, service: Service, extra_content: dict):
        return {
            "main": {"title": _("Terms of service"), "subtitle": _("Legal conditions")},
            "conditions": {
                "title": _("General conditions"),
                "values": [
                    {
                        "title": "",
                        "body": _("conditions_1 {email}").format(
                            email=settings.SUPPORT_TEAM_EMAILS.get(service.country or "ES")[0]
                        ),
                    },
                    {"title": _("title_2"), "body": _("conditions_2")},
                    {"title": _("title_3"), "body": _("conditions_3")},
                    {"title": _("title_4"), "body": _("conditions_4")},
                    {"title": _("title_5"), "body": _("conditions_5")},
                    {"title": _("title_6"), "body": _("conditions_6")},
                    {"title": _("title_7"), "body": _("conditions_7")},
                    {"title": _("title_8"), "body": _("conditions_8")},
                    {"title": _("title_9"), "body": _("conditions_9")},
                    {"title": _("title_10"), "body": _("conditions_10")},
                    {"title": _("title_11"), "body": _("conditions_11")},
                    {"title": _("title_12"), "body": _("conditions_12")},
                    {
                        "title": _("title_13"),
                        "body": _("conditions_13 {email}").format(
                            email=settings.SUPPORT_TEAM_EMAILS.get(service.country or "ES")[0]
                        ),
                    },
                    {"title": _("title_14"), "body": _("conditions_14")},
                ],
            },
        }


class ConditionsTemplateMX(BaseConditionsTemplate):
    def build_template_context(self, service: Service, extra_content: dict):
        return {
            "main": {"title": _("Terms of service"), "subtitle": _("Legal conditions")},
            "conditions": {
                "title": _("General conditions"),
                "values": [
                    {
                        "title": "",
                        "body": _("conditions_1"),
                    },
                    {"title": _("title_2"), "body": _("conditions_2")},
                    {"title": _("title_3"), "body": _("conditions_3")},
                    {"title": _("title_4"), "body": _("conditions_4")},
                    {"title": _("title_5"), "body": _("conditions_5")},
                    {"title": _("title_6"), "body": _("conditions_6")},
                    {"title": _("title_7"), "body": _("conditions_7")},
                    {"title": _("title_8"), "body": _("conditions_8")},
                    {"title": _("title_9"), "body": _("conditions_9")},
                    {"title": _("title_10"), "body": _("conditions_10")},
                    {"title": _("title_11"), "body": _("conditions_11")},
                    {"title": _("title_12"), "body": _("conditions_12")},
                    {
                        "title": _("title_13"),
                        "body": _("conditions_13"),
                    },
                    {"title": _("title_14"), "body": _("conditions_14")},
                    {"title": _("title_15"), "body": _("conditions_15")},
                    {"title": _("title_16"), "body": _("conditions_16")},
                    {"title": _("title_17"), "body": _("conditions_17")},
                    {"title": _("title_18"), "body": _("conditions_18")},
                    {"title": _("title_19"), "body": _("conditions_19")},
                    {"title": _("title_20"), "body": _("conditions_20")},
                    {"title": _("title_21"), "body": _("conditions_21")},
                    {"title": _("title_22"), "body": _("conditions_22")},
                    {"title": _("title_23"), "body": _("conditions_23")},
                ],
            },
        }
