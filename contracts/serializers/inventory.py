from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from contracts.models import Contract, Item
from core.fields import <PERSON><PERSON><PERSON><PERSON>
from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.serializers import ModelSerializer


class InventoryQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check contract_id type and return empty queryset if is not valid"""

    contract_id = serializers.UUIDField(
        required=True,
        allow_null=False,
        help_text=_("If is not provided, will return empty data. In detailed views, this flag is not required."),
    )


class InventorySerializer(ModelSerializer):
    """Serializer to get the list of items linked to concrete contracts"""

    is_selectable = serializers.BooleanField(source="is_selectable_for_delivery")

    class Meta:
        model = Item
        fields = (
            "id",
            "tag_id",
            "delivery_date",
            "pick_up_date",
            "description",
            "is_selectable",
            "remote_dir",
            "comment",
            "delivery",
        )
        ref_name = "InventorySerializer"


class InventoryPartialUpdateSerializer(PartialUpdateMixin, ModelSerializer):
    """Serializer to update item data"""

    class Meta:
        model = Item
        fields = ("id", "comment")


class InventoryContractSerializer(ModelSerializer):
    # TODO: backward compatibility v1.0.22
    contract_id = serializers.CharField(source="contract_document_id", read_only=True)

    contractId = serializers.CharField(source="contract_document_id", read_only=True)
    signed_user_name = CharField(read_only=True, on_null="-")

    class Meta:
        model = Contract
        fields = ["id", "contractId", "signed_user_name", "contract_id"]


class InventoryConcreteSerializer(ModelSerializer):
    """Serializer to get the list of items linked to concrete contracts"""

    contract = InventoryContractSerializer()

    # TODO: backward compatibility v1.0.22
    scanned = serializers.BooleanField(
        default=False, read_only=True, help_text=_("Dummy value for the riders app business logic")
    )

    tagId = serializers.CharField(source="tag_id", read_only=True)
    deliveryDate = serializers.CharField(source="delivery_date", read_only=True)
    pickUpDate = serializers.CharField(source="pick_up_date", read_only=True)
    remoteDir = serializers.CharField(source="remote_dir", read_only=True)

    class Meta:
        model = Item
        # TODO: backward compatibility v1.0.22: "tag_id", "delivery_date", "pick_up_date","scanned",
        fields = (
            "id",
            "tagId",
            "deliveryDate",
            "pickUpDate",
            "description",
            "contract",
            "remoteDir",
            "tag_id",
            "delivery_date",
            "pick_up_date",
            "scanned",
        )


class InventoryMultipleLookupSerializer(ModelSerializer):
    """Serializer to allow lookups against declared fields"""

    class Meta:
        model = Item
        fields = (
            "id",
            "tag_id",
        )
