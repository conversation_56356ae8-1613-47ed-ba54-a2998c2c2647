from django.utils.translation import gettext_lazy as _
from django_countries.serializers import CountryFieldMixin
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from contracts.models import Contract, ContractAddress
from core import serializer_fields
from core.mixins import StringErrorsMixin
from core.serializers import ModelSerializer
from payments.models import Subscription


class ContractSerializer(ModelSerializer):
    """Serializer to get list of contracts"""

    space = serializer_fields.DecimalField(decimal_places=2, max_digits=10)
    base = serializers.CharField(source="city_name")

    class Meta:
        model = Contract
        fields = ("id", "base", "space", "display_name", "currency")


class DocumentSerializer(StringErrorsMixin, serializers.Serializer):
    """Standard serializer to return documentation from contract in response"""

    display_name = serializers.CharField(read_only=True, allow_null=True, allow_blank=True)
    type = serializers.ChoiceField(
        read_only=True,
        choices=[
            "initial_pickup",
            "extra_pickup",
            "partial_delivery",
            "final_delivery",
            "box",
            "moving",
        ],
        allow_null=True,
        allow_blank=True,
    )
    signed_date = serializers.DateField(read_only=True, allow_null=True)
    url = serializers.URLField(read_only=True, allow_null=True, allow_blank=True, source="document_url")
    space = serializers.DecimalField(
        read_only=True, allow_null=True, max_digits=10, decimal_places=2, help_text=_("Hired space")
    )
    id = serializers.PrimaryKeyRelatedField(read_only=True)
    contract_document_id = serializers.CharField(read_only=True)


class SubscriptionSerializer(ModelSerializer):
    """Serializer to get related subscription to concrete contracts"""

    hired_space = serializer_fields.DecimalField(decimal_places=2, max_digits=10)

    class Meta:
        model = Subscription
        fields = (
            "id",
            "start_date",
            "hired_space",
            "recurring_cost",
            "delivery_cost",
            "pick_up_cost",
            "billing_period",
            "status",
        )


class ContractAddressSerializer(CountryFieldMixin, ModelSerializer):
    """Serializer to get contract addresses linked to this contract"""

    full_address = serializers.CharField(read_only=True)

    class Meta:
        model = ContractAddress
        exclude = ("contract",)
        ref_name = "contract_address_concrete_contract"


class ConcreteContractSerializer(ModelSerializer):
    """Serializer to get data from concrete contract"""

    documents = serializers.SerializerMethodField()
    subscription = SubscriptionSerializer(source="subscription_set.first")
    addresses = ContractAddressSerializer(source="contractaddress_set.manually_created", many=True, allow_null=True)
    space = serializer_fields.DecimalField(decimal_places=2, max_digits=10)
    base = serializers.CharField(source="city_name")

    class Meta:
        model = Contract
        fields = (
            "id",
            "base",
            "space",
            "display_name",
            "documents",
            "subscription",
            "addresses",
            "contract_document_id",
            "currency",
        )

    @swagger_serializer_method(serializer_or_field=DocumentSerializer(many=True))
    def get_documents(self, obj):
        """Retrieve as flatten list every document, included contract itself"""
        return (
            DocumentSerializer(obj.annex_set, many=True).data
            + DocumentSerializer(obj.box_set, many=True).data
            + DocumentSerializer(obj.moving_set, many=True).data
        )
