from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django_countries.serializers import CountryFieldMixin
from rest_framework import serializers

from contracts.models import ContractAddress, Delivery, Item
from core.mixins import StringErrorsMixin
from core.serializers import ModelSerializer
from intranet.models import DeliveryRequest


class ContractAddressSerializer(CountryFieldMixin, ModelSerializer):
    """Serializer for order address fields"""

    id = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = ContractAddress
        fields = "__all__"
        extra_kwargs = {
            "address": {"required": False, "allow_null": True},
            "postal_code": {"required": False, "allow_null": True},
            "city": {"required": False, "allow_null": True},
            "state": {"required": False, "allow_null": True},
            "country": {"required": False, "allow_null": True},
        }
        ref_name = "DeliveryContractAddressSerializer"

    def validate(self, attrs):
        id = attrs.get("id")
        address = attrs.get("address")
        postal_code = attrs.get("postal_code")
        city = attrs.get("city")
        state = attrs.get("state")
        country = attrs.get("country")

        errors = {}

        if not id:
            if not address:
                errors["address"] = _("This field is required")
            if not postal_code:
                errors["postal_code"] = _("This field is required")
            if not city:
                errors["city"] = _("This field is required")
            if not state:
                errors["state"] = _("This field is required")
            if not country:
                errors["country"] = _("This field is required")
        else:
            try:
                self.Meta.model.objects.get(pk=id)
            except self.Meta.model.DoesNotExist:
                errors["id"] = _("Contract address with {id} does not exist").format(id=id)

        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class DeliverySerializer(ModelSerializer):
    """Serializer to retrieve pickups"""

    contract_address = ContractAddressSerializer(allow_null=True, required=False)

    class Meta:
        model = Delivery
        fields = (
            "id",
            "date",
            "access",
            "furniture_assembly",
            "contract_address",
            "floor",
        )


class ItemSerializer(StringErrorsMixin, serializers.Serializer):
    id = serializers.PrimaryKeyRelatedField(
        required=True, allow_null=False, allow_empty=False, queryset=Item.objects.select_related("contract").all()
    )

    def validate(self, attrs):
        item = attrs.get("id")
        contract_id = self.context["request"].query_params.get("contract_id")

        if str(item.contract_id) != contract_id:
            raise serializers.ValidationError({"id": _("You cannot select an item which not belongs to contract")})

        if not item.is_selectable_for_delivery:
            raise serializers.ValidationError(
                {"id": _("You cannot select an item which is already selected for delivery")}
            )

        # Return only the id to make later the filter and update against database directly
        return item.pk


class DeliveryItemSerializer(ModelSerializer):
    """Serializer to retrieve current items in response"""

    class Meta:
        model = Item
        fields = ("id", "tag_id", "description", "comment")


class DeliveryConcreteSerializer(ModelSerializer):
    """Serializer to retrieve concrete delivery"""

    items = DeliveryItemSerializer(required=False, allow_null=True, many=True, source="item_set")
    contract_address = ContractAddressSerializer(allow_null=True)

    class Meta:
        model = Delivery
        fields = "__all__"


class DeliveryCreateSerializer(ModelSerializer):
    """Serializer to create pickup orders"""

    items = ItemSerializer(required=True, allow_null=False, many=True, write_only=True)
    items_ordered = serializers.ListSerializer(
        child=serializers.CharField(required=True, allow_null=False, allow_blank=False),
        allow_null=False,
        read_only=True,
    )
    contract_address = ContractAddressSerializer(required=True, allow_null=False)

    class Meta:
        model = Delivery
        fields = (
            "id",
            "date",
            "access",
            "floor",
            "extra_comments",
            "furniture_assembly",
            "contract_address",
            "items",
            "items_ordered",
        )
        extra_kwargs = {
            "date": {"required": True, "allow_null": False},
            "contract": {"read_only": True, "allow_null": False},
            "floor": {"required": True, "allow_null": False},
        }

    def validate(self, attrs):
        contract_id = self.context["request"].query_params.get("contract_id")
        user_id = self.context["request"].user.pk

        if not contract_id:
            raise serializers.ValidationError({"non_field_errors": _("Invalid contract id given")})

        attrs.update(**{"contract_id": contract_id, "user_id": user_id})
        return attrs

    def create(self, validated_data):
        items = validated_data.pop("items")
        contract_address = validated_data.pop("contract_address")
        user_id = validated_data.pop("user_id")

        # Surround inside atomic block to make the creation as a unit
        with transaction.atomic():
            if contract_address.get("id") is None:
                # Create the contract address
                contract_address.update(**{"contract_id": validated_data.get("contract_id"), "is_manually_added": True})
                validated_data["contract_address"] = ContractAddress.objects.create(**contract_address)
            else:
                validated_data["contract_address_id"] = contract_address.get("id")

            # Create first the order
            delivery = super().create(validated_data)

            # Then, update items
            updated_items = Item.objects.filter(pk__in=items).only(
                "id", "tag_id", "pick_up_date", "delivery_date", "description", "remote_dir"
            )

            # Finally, transform it into Intranet delivery request
            DeliveryRequest.objects.create(
                type=DeliveryRequest.USER_DELIVERY,
                raw_data=list(
                    updated_items.values("id", "tag_id", "pick_up_date", "delivery_date", "description", "remote_dir")
                ),
                delivery_date=validated_data["date"],
                contract_id=validated_data["contract_id"],
                created_by_id=user_id,
                customer_delivery=delivery,
            )

            updated_items.update(delivery=delivery)

        delivery.items_ordered = items
        return delivery


class DeliveryQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check contract_id type and return empty queryset if is not valid"""

    contract_id = serializers.UUIDField(
        required=True,
        allow_null=False,
        help_text=_("If is not provided, will return empty data."),
    )
