from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django_countries.serializers import CountryFieldMixin
from rest_framework import serializers

from contracts.models import ContractAddress, Pickup, PickupItem
from core.mixins import StringErrorsMixin
from core.serializers import ModelSerializer


class PickupContractAddressSerializer(ModelSerializer):
    """Serializer to get addresses"""

    class Meta:
        model = ContractAddress
        exclude = ("contract",)


class PickupSerializer(ModelSerializer):
    """Serializer to retrieve pickups"""

    contract_address = PickupContractAddressSerializer(allow_null=True, required=False)

    class Meta:
        model = Pickup
        fields = (
            "id",
            "date",
            "access",
            "floor",
            "box_packaging",
            "furniture_packaging",
            "furniture_disassemble",
            "contract_address",
        )


class PickupItemSerializer(ModelSerializer):
    """Serializer to handle pickup items"""

    class Meta:
        model = PickupItem
        fields = "__all__"
        extra_kwargs = {"created_at": {"read_only": True}}


class PickupConcreteSerializer(ModelSerializer):
    """Serializer to retrieve concrete pickup"""

    items = PickupItemSerializer(required=False, allow_null=True, many=True, source="pickupitem_set")
    contract_address = PickupContractAddressSerializer(allow_null=True)

    class Meta:
        model = Pickup
        fields = "__all__"


class ContractAddressSerializer(CountryFieldMixin, ModelSerializer):
    """Serializer for order address fields"""

    id = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = ContractAddress
        fields = "__all__"
        extra_kwargs = {
            "address": {"required": False, "allow_null": True},
            "postal_code": {"required": False, "allow_null": True},
            "city": {"required": False, "allow_null": True},
            "state": {"required": False, "allow_null": True},
            "country": {"required": False, "allow_null": True},
        }

    def validate(self, attrs):
        id = attrs.get("id")
        address = attrs.get("address")
        postal_code = attrs.get("postal_code")
        city = attrs.get("city")
        state = attrs.get("state")
        country = attrs.get("country")

        errors = {}

        if not id:
            if not address:
                errors["address"] = _("This field is required")
            if not postal_code:
                errors["postal_code"] = _("This field is required")
            if not city:
                errors["city"] = _("This field is required")
            if not state:
                errors["state"] = _("This field is required")
            if not country:
                errors["country"] = _("This field is required")
        else:
            try:
                self.Meta.model.objects.get(pk=id)
            except self.Meta.model.DoesNotExist:
                errors["id"] = _("Contract address with {id} does not exist").format(id=id)

        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class PickupCreateSerializer(ModelSerializer):
    """Serializer to create pickup orders"""

    items = PickupItemSerializer(required=True, allow_null=False, many=True)
    contract_address = ContractAddressSerializer(required=True, allow_null=False)

    class Meta:
        model = Pickup
        fields = (
            "id",
            "date",
            "access",
            "extra_comments",
            "floor",
            "box_packaging",
            "furniture_packaging",
            "furniture_disassemble",
            "contract_address",
            "items",
        )
        extra_kwargs = {
            "date": {"required": True, "allow_null": False},
            "floor": {"required": True, "allow_null": False},
            "access": {"required": True, "allow_null": False},
            "contract": {"read_only": True, "allow_null": False},
        }

    def validate(self, attrs):
        contract_id = self.context["request"].query_params.get("contract_id")

        if not contract_id:
            raise serializers.ValidationError({"non_field_errors": _("Invalid contract id given")})

        attrs["contract_id"] = contract_id
        return attrs

    def create(self, validated_data):
        items = validated_data.pop("items")
        contract_address = validated_data.pop("contract_address")

        # Surround inside atomic block to make the creation as a unit
        with transaction.atomic():
            if contract_address.get("id") is None:
                # Create the contract address
                contract_address.update(**{"contract_id": validated_data.get("contract_id"), "is_manually_added": True})
                validated_data["contract_address"] = ContractAddress.objects.create(**contract_address)
            else:
                validated_data["contract_address_id"] = contract_address.get("id")

            # Create first the order
            pickup = super().create(validated_data)

            # Then, create every item and link it
            items = [PickupItem(**item, pickup=pickup) for item in items]

            if items:
                PickupItem.objects.bulk_create(items)

        pickup.items = items
        return pickup


class PickupQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check contract_id type and return empty queryset if is not valid"""

    contract_id = serializers.UUIDField(
        required=True,
        allow_null=False,
        help_text=_("If is not provided, will return empty data."),
    )
