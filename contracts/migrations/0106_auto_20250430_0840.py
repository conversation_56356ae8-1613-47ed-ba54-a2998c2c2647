# Generated by Django 3.2.25 on 2025-04-30 08:40

from django.db import migrations


def set_allows_warehouse_differences(apps, schema_editor):
    Warehouse = apps.get_model("contracts", "Warehouse")

    Warehouse.objects.filter(city__canonical_name="barcelona").update(allows_warehouse_differences=True)


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0105_merge_20250430_0821"),
    ]

    operations = [
        migrations.RunPython(set_allows_warehouse_differences, reverse_code=migrations.RunPython.noop),
    ]
