# Generated by Django 3.2.15 on 2023-02-08 19:54

from django.db import migrations

WAREHOUSE_ID_BASES = {
    "1001": "Nuestro",
    "1002": "<PERSON><PERSON>win",
    "1003": "Intelligent",
    "1004": "LQS",
    "1021": "Ascor",
    "1031": "Nuestro",
    "1032": "VCD",
    "1041": "Nordcargo",
    "1051": "Sevilla I",
    "1052": "Sevilla Suardiaz",
    "2001": "LQS",
    "2002": "Thyman",
    "2010": "Pantoja",
    "2011": "Portugalenses",
    "3001": "Paris",
    "4000": "ADP",
    "5000": "Querétaro",
}


def set_warehouse_name(apps, schema_editor):
    Warehouse = apps.get_model("contracts", "Warehouse")

    warehouses = Warehouse.objects.filter(warehouse_id__in=WAREHOUSE_ID_BASES.keys())

    for warehouse in warehouses:
        warehouse.name = WAREHOUSE_ID_BASES[warehouse.warehouse_id]

    Warehouse.objects.bulk_update(warehouses, fields=["name"])


class Migration(migrations.Migration):
    dependencies = [
        ("contracts", "0046_warehouse_name"),
    ]

    operations = [
        migrations.RunPython(set_warehouse_name, reverse_code=migrations.RunPython.noop),
    ]
