# Generated by Django 3.2.13 on 2022-06-29 10:30

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0030_contract_signed_user_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Delivery",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("date", models.DateTimeField(default=django.utils.timezone.now, help_text="Order date")),
                (
                    "access",
                    models.CharField(
                        choices=[
                            ("empty", "Empty"),
                            ("stairs", "Stairs"),
                            ("small_lift", "Small lift"),
                            ("large_lift", "Large lift"),
                        ],
                        default="empty",
                        help_text="Access type",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "extra_comments",
                    models.TextField(blank=True, help_text="Additional comments from the user", null=True),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now, help_text="Creation date")),
                ("furniture_assembly", models.BooleanField(default=False)),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.contract"
                    ),
                ),
                (
                    "contract_address",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contracts.contractaddress",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "deliveries",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Pickup",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("date", models.DateTimeField(default=django.utils.timezone.now, help_text="Order date")),
                (
                    "access",
                    models.CharField(
                        choices=[
                            ("empty", "Empty"),
                            ("stairs", "Stairs"),
                            ("small_lift", "Small lift"),
                            ("large_lift", "Large lift"),
                        ],
                        default="empty",
                        help_text="Access type",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "extra_comments",
                    models.TextField(blank=True, help_text="Additional comments from the user", null=True),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now, help_text="Creation date")),
                ("floor", models.CharField(max_length=20, null=True)),
                ("box_packaging", models.BooleanField(default=False)),
                ("furniture_packaging", models.BooleanField(default=False)),
                ("furniture_disassemble", models.BooleanField(default=False)),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.contract"
                    ),
                ),
                (
                    "contract_address",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contracts.contractaddress",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PickupItem",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "description",
                    models.TextField(blank=True, help_text="Object description provided in contract", null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("units", models.IntegerField(default=0)),
                ("width", models.FloatField(default=0)),
                ("height", models.FloatField(default=0)),
                ("deep", models.FloatField(default=0)),
                (
                    "pickup",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.pickup"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RemoveField(
            model_name="item",
            name="order",
        ),
        migrations.RemoveField(
            model_name="orderitem",
            name="order",
        ),
        migrations.DeleteModel(
            name="Order",
        ),
        migrations.AddField(
            model_name="item",
            name="delivery",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.delivery"),
        ),
    ]
