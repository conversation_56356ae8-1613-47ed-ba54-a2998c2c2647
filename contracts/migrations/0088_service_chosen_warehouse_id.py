# Generated by Django 3.2.25 on 2024-11-27 16:44

import django.db.models.expressions
import django.db.models.functions.comparison
from django.db import migrations, models

import core.fields


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0087_auto_20241127_1245"),
    ]

    operations = [
        migrations.AddField(
            model_name="service",
            name="chosen_warehouse_id",
            field=core.fields.GeneratedField(
                base_field=models.UUIDField(),
                expression=django.db.models.functions.comparison.Coalesce(
                    django.db.models.functions.comparison.Cast(
                        django.db.models.expressions.Func(
                            django.db.models.expressions.F("raw_data__data"),
                            django.db.models.expressions.Value("warehouse_id"),
                            function="jsonb_extract_path_text",
                        ),
                        output_field=models.UUIDField(),
                    ),
                    django.db.models.functions.comparison.Cast(
                        django.db.models.expressions.Func(
                            django.db.models.expressions.F("raw_data__raw_data__data"),
                            django.db.models.expressions.Value("warehouse_id"),
                            function="jsonb_extract_path_text",
                        ),
                        output_field=models.UUIDField(),
                    ),
                ),
                null=True,
            ),
        ),
    ]
