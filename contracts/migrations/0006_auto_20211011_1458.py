# Generated by Django 3.2.8 on 2021-10-11 12:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0005_auto_20211008_1329"),
    ]

    operations = [
        migrations.AddField(
            model_name="annex",
            name="delivery_space",
            field=models.FloatField(default=0, help_text="Released space in squared meters"),
        ),
        migrations.AddField(
            model_name="annex",
            name="pickup_space",
            field=models.FloatField(default=0, help_text="Hired space in squared meters"),
        ),
        migrations.AlterField(
            model_name="annex",
            name="signed_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="contract",
            name="signed_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="item",
            name="delivery_date",
            field=models.DateField(blank=True, help_text="Delivery date for this item", null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="item",
            name="pick_up_date",
            field=models.DateField(blank=True, help_text="Pick up date for this item", null=True),
        ),
    ]
