# Generated by Django 3.2.25 on 2024-11-20 17:39

from django.db import migrations
from django.db.models import F


def copy_warehouse_id_into_warehouse_key(apps, schema_editor):
    Contract = apps.get_model("contracts", "Contract")
    Annex = apps.get_model("contracts", "Annex")

    Contract.objects.update(warehouse_key=F("warehouse_id"))
    Annex.objects.update(warehouse_key=F("warehouse_id"))


class Migration(migrations.Migration):
    dependencies = [
        ("contracts", "0075_auto_20241120_1738"),
    ]

    operations = [
        migrations.RunPython(copy_warehouse_id_into_warehouse_key, reverse_code=migrations.RunPython.noop),
    ]
