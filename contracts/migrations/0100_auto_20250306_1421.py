# Generated by Django 3.2.25 on 2025-03-06 14:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0099_warehouse_internal_id_for_differences"),
    ]

    operations = [
        migrations.AlterField(
            model_name="annex",
            name="service",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.service"
            ),
        ),
        migrations.AlterField(
            model_name="box",
            name="service",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.service"
            ),
        ),
        migrations.AlterField(
            model_name="moving",
            name="service",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.service"
            ),
        ),
    ]
