# Generated by Django 3.2.8 on 2021-10-25 11:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0010_alter_moving_space"),
    ]

    operations = [
        migrations.AddField(
            model_name="annex",
            name="uploaded_remote_file",
            field=models.BooleanField(
                default=False, help_text="Flag to know if related documents have been uploaded to storage"
            ),
        ),
        migrations.AddField(
            model_name="box",
            name="uploaded_remote_file",
            field=models.BooleanField(
                default=False, help_text="Flag to know if related documents have been uploaded to storage"
            ),
        ),
        migrations.AddField(
            model_name="moving",
            name="uploaded_remote_file",
            field=models.BooleanField(
                default=False, help_text="Flag to know if related documents have been uploaded to storage"
            ),
        ),
    ]
