# Generated by Django 3.2.25 on 2025-02-20 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0094_service_billing_cycle_notes"),
    ]

    operations = [
        migrations.AddField(
            model_name="warehouse",
            name="allows_warehouse_differences",
            field=models.BooleanField(
                default=False, help_text="If it is needed to calculate the warehouse differences. Internal usage only"
            ),
        ),
        migrations.AddIndex(
            model_name="warehouse",
            index=models.Index(fields=["created_at"], name="contracts_w_created_ffe784_idx"),
        ),
    ]
