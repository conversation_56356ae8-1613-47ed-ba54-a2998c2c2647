# Generated by Django 3.2.25 on 2024-12-03 12:05

from django.db import migrations, models

import core.encoders


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0091_service_placements_squashed_0092_alter_service_warehouse_location"),
    ]

    operations = [
        migrations.AlterField(
            model_name="annex",
            name="raw_data",
            field=models.JSONField(blank=True, encoder=core.encoders.CustomDjangoJSONEncoder, null=True),
        ),
        migrations.AlterField(
            model_name="box",
            name="raw_data",
            field=models.JSONField(blank=True, encoder=core.encoders.CustomDjangoJSONEncoder, null=True),
        ),
        migrations.AlterField(
            model_name="moving",
            name="raw_data",
            field=models.JSONField(blank=True, encoder=core.encoders.CustomDjangoJSONEncoder, null=True),
        ),
        migrations.AlterField(
            model_name="service",
            name="raw_data",
            field=models.JSONField(blank=True, encoder=core.encoders.CustomDjangoJSONEncoder, null=True),
        ),
    ]
