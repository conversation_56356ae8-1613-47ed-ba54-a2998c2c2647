# Generated by Django 3.2.25 on 2024-11-21 08:50

from django.db import migrations
from django.db.models import OuterRef, Subquery


def set_warehouse(apps, schema_editor):
    Item = apps.get_model("contracts", "Item")
    Warehouse = apps.get_model("contracts", "Warehouse")

    subquery = (
        Warehouse.objects.filter(
            warehouse_id=OuterRef("warehouse_key"),
        )
        .values("id")
        .distinct()
    )

    Item.objects.update(warehouse_id=Subquery(subquery[:1]))


def unset_warehouse(apps, schema_editor):
    Item = apps.get_model("contracts", "Item")

    Item.objects.update(warehouse=None)


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0082_auto_20241121_0850"),
    ]

    operations = [
        migrations.RunPython(set_warehouse, reverse_code=unset_warehouse),
    ]
