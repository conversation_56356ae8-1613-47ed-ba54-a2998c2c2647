# Generated by Django 3.2.8 on 2021-10-19 15:30

import uuid

import django.db.models.deletion
import django.utils.timezone
import django_countries.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0007_auto_20211011_1527"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="annex",
            name="delivery_space",
        ),
        migrations.RemoveField(
            model_name="annex",
            name="pickup_space",
        ),
        migrations.RemoveField(
            model_name="contract",
            name="contract_id",
        ),
        migrations.RemoveField(
            model_name="contract",
            name="internal_id",
        ),
        migrations.RemoveField(
            model_name="contract",
            name="raw_data",
        ),
        migrations.RemoveField(
            model_name="contract",
            name="remote_file",
        ),
        migrations.AddField(
            model_name="annex",
            name="annex_type",
            field=models.CharField(
                choices=[
                    ("initial_pickup", "Initial pickup"),
                    ("extra_pickup", "Extra pickup"),
                    ("partial_delivery", "Partial delivery"),
                    ("final_delivery", "Final delivery"),
                    ("unknown", "Unknown"),
                ],
                default="unknown",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="annex",
            name="contract_document_id",
            field=models.CharField(blank=True, help_text="External id for this document", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="annex",
            name="space",
            field=models.FloatField(default=0, help_text="Space in squared meters"),
        ),
        migrations.AddField(
            model_name="contract",
            name="contract_document_id",
            field=models.CharField(blank=True, help_text="External id for this document", max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="annex",
            name="base",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ES-M", "Madrid"),
                    ("PT-P", "Porto"),
                    ("ES-S", "Sevilla"),
                    ("ES-B", "Barcelona"),
                    ("ES-ML", "Malaga"),
                    ("ES-V", "Valencia"),
                    ("FR-P", "Paris"),
                    ("PT-L", "Lisboa"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="contract",
            name="base",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ES-M", "Madrid"),
                    ("PT-P", "Porto"),
                    ("ES-S", "Sevilla"),
                    ("ES-B", "Barcelona"),
                    ("ES-ML", "Malaga"),
                    ("ES-V", "Valencia"),
                    ("FR-P", "Paris"),
                    ("PT-L", "Lisboa"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="Moving",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("signed_date", models.DateField(blank=True, null=True)),
                (
                    "contract_document_id",
                    models.CharField(blank=True, help_text="External id for this document", max_length=100, null=True),
                ),
                ("country", django_countries.fields.CountryField(blank=True, max_length=2, null=True)),
                ("remote_file", models.CharField(blank=True, max_length=300, null=True)),
                ("raw_data", models.JSONField(blank=True, null=True)),
                (
                    "internal_id",
                    models.CharField(
                        blank=True,
                        help_text="Internal id from moreapp to track every form submitted",
                        max_length=200,
                        null=True,
                        unique=True,
                    ),
                ),
                ("space", models.FloatField(default=0, help_text="Space moved in squared meters")),
                (
                    "contract",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.contract"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Box",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("signed_date", models.DateField(blank=True, null=True)),
                (
                    "contract_document_id",
                    models.CharField(blank=True, help_text="External id for this document", max_length=100, null=True),
                ),
                ("country", django_countries.fields.CountryField(blank=True, max_length=2, null=True)),
                ("remote_file", models.CharField(blank=True, max_length=300, null=True)),
                ("raw_data", models.JSONField(blank=True, null=True)),
                (
                    "internal_id",
                    models.CharField(
                        blank=True,
                        help_text="Internal id from moreapp to track every form submitted",
                        max_length=200,
                        null=True,
                        unique=True,
                    ),
                ),
                ("quantity", models.PositiveIntegerField(default=0, help_text="Number of boxes delivered")),
                (
                    "contract",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.contract"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
