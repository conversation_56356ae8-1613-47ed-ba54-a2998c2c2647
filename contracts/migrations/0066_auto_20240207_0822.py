# Generated by Django 3.2.23 on 2024-02-07 08:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("contracts", "0065_auto_20240124_1605"),
    ]

    operations = [
        migrations.AddField(
            model_name="annex",
            name="service",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.service"
            ),
        ),
        migrations.AddField(
            model_name="box",
            name="service",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.service"
            ),
        ),
        migrations.AddField(
            model_name="moving",
            name="service",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.service"
            ),
        ),
    ]
