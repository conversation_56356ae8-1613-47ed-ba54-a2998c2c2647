# Generated by Django 3.2.9 on 2021-11-17 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0013_item_remote_dir"),
    ]

    operations = [
        migrations.AddField(
            model_name="annex",
            name="first_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="annex",
            name="identity_document",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="annex",
            name="last_name",
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="box",
            name="first_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="box",
            name="identity_document",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="box",
            name="last_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="moving",
            name="first_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="moving",
            name="identity_document",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="moving",
            name="last_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
