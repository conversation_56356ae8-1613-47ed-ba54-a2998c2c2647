from django.conf import settings
from django.db import transaction
from django.utils.encoding import force_str
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _

from core.emails.message import EmailMessage
from core.utils import TRANSLATES


def send_email_in_new_pickup_order(sender, instance, created, **kwargs):
    """Send email to support team on pickup orders"""

    def send_email(instance):
        sender = instance.contract.user.email_domain

        # Activate localeness
        activate(sender)
        objects = instance.pickupitem_set.values("units", "description")

        message = EmailMessage(
            subject=_("{prefix}pickup order for contract {contract}").format(
                prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX,
                contract=instance.contract.contract_document_id,
            ),
            to=settings.SUPPORT_TEAM_EMAILS.get(sender),
            template="pick_up_order_email",
            sender=sender,
            context={
                "contract_id": instance.contract.display_name,
                "date": instance.date.strftime("%d/%m/%Y"),
                "comments": instance.extra_comments,
                "access": instance.get_access_display(),
                "floor": instance.floor,
                "box_packaging": force_str(TRANSLATES.get(instance.box_packaging)),
                "furniture_packaging": force_str(TRANSLATES.get(instance.furniture_packaging)),
                "furniture_disassemble": force_str(TRANSLATES.get(instance.furniture_disassemble)),
                "address": instance.contract_address.full_address,
                "url": instance.backoffice_url,
                "objects": list(objects),
            },
        )
        message.send()

    if created:
        transaction.on_commit(lambda: send_email(instance))


def send_email_in_new_delivery_order(sender, instance, created, **kwargs):
    """Send email to support team on delivery orders"""

    def send_email(instance):
        sender = instance.contract.user.email_domain

        # Activate localeness
        activate(sender)
        objects = instance.item_set.values("tag_id", "description")

        message = EmailMessage(
            subject=_("{prefix}delivery order for contract {contract}").format(
                prefix=settings.ACCOUNT_EMAIL_SUBJECT_PREFIX,
                contract=instance.contract.contract_document_id,
            ),
            to=settings.SUPPORT_TEAM_EMAILS.get(sender),
            template="delivery_order_email",
            sender=sender,
            context={
                "contract_id": instance.contract.display_name,
                "date": instance.date.strftime("%d/%m/%Y"),
                "comments": instance.extra_comments,
                "access": instance.get_access_display(),
                "floor": instance.floor,
                "furniture_assembly": force_str(TRANSLATES.get(instance.furniture_assembly)),
                "address": instance.contract_address.full_address,
                "url": instance.backoffice_url,
                "objects": list(objects),
            },
        )
        message.send()

    if created:
        transaction.on_commit(lambda: send_email(instance))


# def create_slack_notification_in_new_order(sender, instance, created, **kwargs):
#     """Send a Slack notification on new delivery/pick up orders"""
#
#     # Skip slack notification in local environment
#     if created and settings.ENVIRONMENT != "LOCAL":
#         order_type = _("Pickup") if instance.type == instance.PICK_UP else _("Delivery")
#         full_address = instance.contract_address.full_address if instance.contract_address else _("Empty direction")
#         email = instance.contract.user.email if instance.contract.user else _("Empty email")
#
#         # Create the notification in slack
#         slack_client.notify_pickup_or_deliver_order(
#             contract_id=instance.contract.display_name,
#             date=instance.date.strftime("%d/%m/%Y"),
#             address=full_address,
#             user=instance.contract.user,
#             order_type=order_type,
#             email=email,
#         )
