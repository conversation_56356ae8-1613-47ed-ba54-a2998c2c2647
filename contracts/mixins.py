from django.conf import settings
from django.contrib import admin, messages
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from core.external_services import storage_client


class AdminPDFFileMixin:
    @admin.action(description=_("View pdf"))
    def view_pdf(self, request, obj):
        """
        Generate a signed URL for viewing a PDF file stored in remote storage and display it to the user.

        This admin action checks if the PDF file exists in remote storage, generates a signed URL for
        accessing it, and displays a download link to the user. If the file is not uploaded or empty,
        an appropriate error message is shown instead.

        Parameters:
            request (HttpRequest): The HTTP request object from the admin interface.
            obj (Model): The model instance containing the PDF file reference.
                         Expected to have 'uploaded_remote_file' and 'remote_file' attributes.

        Returns:
            HttpResponse: The result of message_user() which displays a message to the user
                         in the admin interface. No explicit return value as this is used
                         for its side effect of showing messages to the user.
        """
        if not obj.uploaded_remote_file:
            message, level = (
                _("PDF file is not uploaded to remote server"),
                messages.ERROR,
            )
            return self.message_user(request, message=message, level=level)

        file_url = storage_client.get_signed_object(
            bucket_name=settings.DOCUMENTATION_BUCKET_NAME, blob_name=f"documentation/{obj.remote_file[1:]}"
        )

        if not file_url:
            message, level = (
                _("Empty PDF file"),
                messages.ERROR,
            )
            return self.message_user(request, message=message, level=level)

        link = format_html(
            '<a href="{}" target="_blank" style="text-decoration: underline;">{}</a>', file_url, _("Download")
        )
        self.message_user(request, message=format_html(_("PDF file is ready: {}"), link), level=messages.INFO)
