from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>

from contracts.views.contract import ContractViewSet
from contracts.views.delivery import DeliveryViewSet
from contracts.views.inventory import InventoryViewSet
from contracts.views.pickup import PickupViewSet
from contracts.views.service import ServiceViewSet
from contracts.views.task import TaskViewSet
from contracts.views.warehouse_movement import WarehouseMovementViewSet
from core.routers import OptionalSlashRouter

router = DefaultRouter()
router.register("contracts", ContractViewSet, basename="contracts")
router.register("pickups", PickupViewSet, basename="pickups")
router.register("deliveries", DeliveryViewSet, basename="deliveries")

# Needed for API Gateway as trailing slash is not handled properly
optional_slash_router = OptionalSlashRouter()
optional_slash_router.register("inventory", InventoryViewSet, basename="inventory")
optional_slash_router.register("services", ServiceViewSet, basename="service")
optional_slash_router.register("tasks", TaskViewSet, basename="tasks")
optional_slash_router.register("placements", WarehouseMovementViewSet, basename="placements")


# Override internal auth urlpatterns with user viewset methods
urlpatterns = [] + router.urls + optional_slash_router.urls

# Needed to add namespace in backoffice_api urls.py
app_name = "contracts"
