<mjml>
  <mj-head>
    <mj-include path="./base/styles.mjml" />
  </mj-head>
  <mj-body>
    <mj-include path="./base/header.mjml" />
    <mj-wrapper mj-class="white-box">
      <mj-section>
        <mj-column>
          <mj-text mj-class="title">{{ sys_delivery_order }} </mj-text>
          <mj-spacer mj-class="spacer"></mj-spacer>
          <mj-text> <span style="font-weight: 600">{{ sys_contract }}</span> <span>{{ contract_id }}</span></mj-text>
          <mj-text> <span style="font-weight: 600">{{ sys_date }}</span> <span>{{ date }}</span></mj-text>
          <mj-text> <span style="font-weight: 600">{{ sys_address }}</span> <span>{{ address }}</span></mj-text>
          <mj-text> <span style="font-weight: 600">{{ sys_floor }}</span> <span>{{ floor }}</span></mj-text>
          <mj-text> <span style="font-weight: 600">{{ sys_access }}</span> <span>{{ access }}</span></mj-text>
          <mj-text> <span style="font-weight: 600">{{ sys_furniture_assembly }}</span> <span>{{ furniture_assembly }}</span></mj-text>
          <mj-raw>{{#if comments}}</mj-raw>
            <mj-text mj-class="strong"> {{ sys_comments }}</mj-text>
            <mj-text align="justify"> {{ comments }}</mj-text>
          <mj-raw>{{/if}}</mj-raw>
          <mj-button href="{{ url }}" align=center>{{ sys_cta }}</mj-button>
          <mj-spacer mj-class="spacer"></mj-spacer>
          <mj-text mj-class="subtitle">
              {{ sys_items }}
          </mj-text>
          <mj-table>
            <tr style="border-bottom:1px solid #ecedee;text-align:center;">
              <th style="padding: 0 15px 0 0;">{{ sys_id }}</th>
              <th style="padding: 0 15px 0 0;">{{ sys_description }}</th>
            </tr>
            {{#each objects}}
            <tr style="text-align:center;">
              <td style="padding: 0 15px 0 0;">{{ this.tag_id }}</td>
              <td style="padding: 0 15px 0 0;">{{ this.description }}</td>
            </tr>
            {{/each}}
          </mj-table>
          <mj-spacer mj-class="spacer"></mj-spacer>
          <mj-text>
            {{ sys_thanks }},<br>
            {{ sys_team }}
          </mj-text>
          <mj-spacer mj-class="spacer"></mj-spacer>
        </mj-column>
      </mj-section>
    </mj-wrapper>
    <mj-include path="./base/footer.mjml" />
  </mj-body>
</mjml>
