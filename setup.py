#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import re

import setuptools
from setuptools import setup


def get_version(*file_paths):
    """Retrieves the version from network_clients/__init__.py"""
    filename = os.path.join(os.path.dirname(__file__), *file_paths)
    version_file = open(filename).read()
    version_match = re.search(r"^__version__ = ['\"]([^'\"]*)['\"]", version_file, re.M)
    if version_match:
        return version_match.group(1)
    raise RuntimeError("Unable to find version string.")


version = get_version("network_clients", "__init__.py")

setup(
    name="boxtobox-network-clients",
    version=version,
    description="""Several network common clients to avoid duplicate code""",
    author="Sergio",
    author_email="<EMAIL>",
    url="https://github.com/box2box-SL/clients.git",
    packages=setuptools.find_packages(exclude="tests"),
    include_package_data=True,
    install_requires=[
        "requests>=2.0",
        "google-cloud-pubsub>=2.8",
        "google-cloud-tasks>=2.5",
        "google-cloud-storage>=1.42",
        "python-dateutil>=2.8",
        "chargebee>=2.10,<2.13",
    ],
    license="MIT",
    zip_safe=False,
    classifiers=[
        "Intended Audience :: Developers",
        "License :: OSI Approved :: BSD License",
        "Natural Language :: English",
        "Programming Language :: Python :: 3.8",
    ],
)
