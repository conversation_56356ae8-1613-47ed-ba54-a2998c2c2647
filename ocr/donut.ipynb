#%%
from donut import DonutModel
from PIL import Image
import torch
import numpy as np

# Create a new virtualenv:
# virtualenv venv
#   source venv/bin/activate
# Install donut-python and these dependencies: pip install torch==1.11.0 torchvision==0.12.0 pytorch-lightning==1.6.4 transformers==4.25.1 timm==0.5.4
#%%
model = DonutModel.from_pretrained("naver-clova-ix/donut-base-finetuned-cord-v2")
#%%
model.encoder.to(torch.float)
model.eval()

image = Image.open("./analisis/sample_image_cord_test_receipt_00004.png").convert("RGB")
#%%
output = model.inference(image=image, prompt="<s_cord-v2>")
#%%
output
#%%
image = Image.open("./analisis/analitica-copia.jpg").convert("RGB")
output = model.inference(image=image, prompt="<s_cord-v2>")
#%%
output