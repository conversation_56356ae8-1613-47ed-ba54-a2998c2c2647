from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django_countries.serializers import CountryFieldMixin
from rest_framework import serializers

from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.serializers import ModelSerializer
from payments.models import BillingData
from payments.utils import (EU_VAT_COUNTRIES, get_vat_type_from_number,
                            validate_vat)


class BillingDataSerializer(CountryFieldMixin, ModelSerializer):
    """Serializer to get billing details from organization"""

    class Meta:
        model = BillingData
        fields = "__all__"


class BillingDataCreateSerializer(CountryFieldMixin, ModelSerializer):
    """Serializer to create billing attrs for given organization and link them with Stripe"""

    class Meta:
        model = BillingData
        fields = "__all__"
        extra_kwargs = {
            "include_tax": {"read_only": True},
            "tax_id_type": {"read_only": True},
            "invoice_locale": {"read_only": True},
        }

    def validate(self, attrs):
        country = attrs.get("country", None)
        vat = attrs.get("vat", None)
        organization = attrs.get("organization", None)

        # Sent vat and company_country
        if vat and country:
            vat_valid = validate_vat(vat, country)

            if not vat_valid:
                raise serializers.ValidationError({"vat": _("Invalid VAT")})

        if not organization or (organization and not organization.stripe_customer_id):
            raise serializers.ValidationError(
                {"non_field_errors": _("Could not be possible to save billing data. Please, contact with support.")}
            )

        # Set tax_id_type for Spain only, because is mandatory
        attrs["tax_id_type"] = get_vat_type_from_number(vat) if country in EU_VAT_COUNTRIES else None

        return attrs

    def create(self, validated_data):
        """Override this method to propagate changes in Stripe"""

        with transaction.atomic():
            instance = super(BillingDataCreateSerializer, self).create(validated_data)

            # Update in stripe
            instance.update_stripe()

            # Set tax id in stripe only for EU countries
            if instance.country in EU_VAT_COUNTRIES:
                instance.update_tax_id()

        return instance


class BillingDataPartialUpdateSerializer(PartialUpdateMixin, BillingDataCreateSerializer):
    """Serializer to update billing data from given organization and reflect data in Stripe"""

    class Meta(BillingDataCreateSerializer.Meta):
        extra_kwargs = {
            "organization": {"read_only": True},
            "tax_id_type": {"read_only": True},
            "id": {"read_only": True},
            "include_tax": {"read_only": True},
            "invoice_locale": {"read_only": True},
            "country": {"required": False},
        }

    def validate(self, attrs):
        country = attrs.get("country", None)
        vat = attrs.get("vat", None)

        # Sent vat and company_country
        if vat and country:
            vat_valid = validate_vat(vat, country)

            if not vat_valid:
                raise serializers.ValidationError({"vat": _("Invalid VAT")})

        # Set tax_id_type for Spain only, because is mandatory
        attrs["tax_id_type"] = get_vat_type_from_number(vat) if country in EU_VAT_COUNTRIES else None

        return attrs

    def update(self, instance, validated_data):
        """Guess tax_id_type from vat. In this version, tax_id_type will be restricted to Spain"""
        country = validated_data.get("country", None)
        vat = validated_data.get("vat", None)

        # Save the current vat
        current_vat = instance.vat

        # Surround inside transaction atomic block to rollback db operation
        with transaction.atomic():
            # Update the model
            updated_instance = super(BillingDataPartialUpdateSerializer, self).update(instance, validated_data)

            # Propagate changes in Stripe only if has changed and we are in Spain
            if country in EU_VAT_COUNTRIES and vat and current_vat != instance.vat:
                # Update first tax_id in stripe
                instance.update_tax_id(delete_only=False)

            if country is not None and country not in EU_VAT_COUNTRIES:
                # Delete the tax_id if needed
                instance.update_tax_id(delete_only=True)

            # Check if more fields has been changed to update the rest of fields in Stripe
            updated_fields = validated_data.copy()
            updated_fields.pop("vat", None)

            # Update the rest of fields only if has changed
            if updated_fields:
                instance.update_stripe()

        return updated_instance


class BillingDataQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check organization_id type and return empty queryset if is not valid"""

    organization = serializers.UUIDField(
        required=False,
        allow_null=False,
        help_text=_(
            "If is not provided, will return billing datas from organizations which user is member (in organization) "
            "or empty data if no matching criteria. In detailed views, this flag is used to filter the whole queryset "
            "from database and is used as base to get the concrete object."
        ),
    )
