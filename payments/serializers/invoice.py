from django.utils.translation import gettext_lazy as _
from django_countries.serializer_fields import <PERSON><PERSON><PERSON>
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from core.fields import DecimalInCentsField, TimestampField
from core.mixins import StringErrorsMixin
from core.serializers import ModelSerializer
from payments.models import Invoice, need_tax


class InvoiceSerializer(ModelSerializer):
    class Meta:
        model = Invoice
        exclude = ("subscription",)


class BillingDataSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to allowing pass billing data to check if tax is applicable or not"""

    vat = serializers.CharField(required=True, allow_null=True, allow_blank=False)
    state = serializers.CharField(required=True, allow_null=True, allow_blank=False)
    country = CountryField(required=True, allow_null=True, allow_blank=False)


class InvoiceUpcomingSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to parse needed data for upcoming invoice and show the resume to customer"""

    customer_id = serializers.CharField(
        required=True, allow_null=False, allow_blank=False, help_text=_("Stripe customer_id")
    )

    billing_data = BillingDataSerializer(
        required=True, allow_null=False, help_text=_("Billing data to check if tax is applicable or not")
    )

    subscription_id = serializers.CharField(
        required=False, allow_null=False, allow_blank=False, help_text=_("Stripe subscription_id.")
    )

    price_id = serializers.CharField(required=True, allow_null=False, allow_blank=False, help_text=_("Stripe plan"))
    coupon_id = serializers.CharField(
        required=False, allow_null=True, allow_blank=False, help_text=_("Stripe coupon_id")
    )

    with_taxes = serializers.BooleanField(read_only=True)
    change_mode = serializers.ChoiceField(
        choices=["upgrade", "downgrade"],
        required=False,
        allow_null=False,
        allow_blank=False,
        help_text=_(
            "If upcoming invoice is for an upgrade subscription, downgrade or none of them. This field is "
            "mandatory if you pass the subscription_id."
        ),
    )

    def validate(self, attrs):
        subscription_id = attrs.get("subscription_id", None)
        billing_data = attrs.get("billing_data")
        change_mode = attrs.get("change_mode", None)

        if (change_mode and not subscription_id) or (not change_mode and subscription_id):
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "Upgrading or downgrading a subscription needs a valid subscription_id and change mode"
                    )
                }
            )

        attrs["with_taxes"] = need_tax(billing_data["vat"], billing_data["country"], billing_data["state"])
        attrs["change_mode"] = change_mode
        return attrs


class InvoicePeriodSerializer(StringErrorsMixin, serializers.Serializer):
    start = TimestampField()
    end = TimestampField()


class InvoiceResumeSerializer(StringErrorsMixin, serializers.Serializer):
    """Upcoming invoice resume serializer"""

    total = DecimalInCentsField(decimal_places=2, default=0, max_digits=10)
    subtotal = DecimalInCentsField(decimal_places=2, default=0, max_digits=10)
    tax = DecimalInCentsField(decimal_places=2, default=0, max_digits=10)
    invoice_period = serializers.SerializerMethodField()

    @swagger_serializer_method(serializer_or_field=InvoicePeriodSerializer)
    def get_invoice_period(self, obj):
        period = obj["lines"]["data"][0]["period"]
        return InvoicePeriodSerializer(period).data

    def to_representation(self, instance):
        representation = super(InvoiceResumeSerializer, self).to_representation(instance)

        if not representation["tax"]:
            representation["tax"] = 0
        return representation


class InvoiceQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check organization_id type and return empty queryset if is not valid"""

    organization = serializers.UUIDField(
        required=False,
        allow_null=False,
        help_text=_(
            "If is not provided, will return ivoicess from organizations which user is member (in organization) "
            "or empty data if no matching criteria. In detailed views, this flag is used to filter the whole queryset "
            "from database and is used as base to get the concrete object."
        ),
    )
