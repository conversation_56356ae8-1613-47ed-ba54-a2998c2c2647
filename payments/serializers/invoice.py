from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from contracts.models import Contract
from core.mixins import StringErrorsMixin
from core.serializers import ModelSerializer
from payments.models import Invoice


class InvoiceContractSerializer(ModelSerializer):
    """Serializer to retrieve contract on invoice"""

    class Meta:
        model = Contract
        fields = ("id", "display_name")


class InvoiceQuerySetSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check contract_id type and return empty queryset if is not valid"""

    contract_id = serializers.UUIDField(
        required=False,
        allow_null=False,
        help_text=_(
            "If is not provided, will return invoices related to the user who made the request. In detailed views, this"
            " flag is used to filter the whole queryset from database and is used as base to get the concrete object."
        ),
    )


class InvoiceSerializer(ModelSerializer):
    """Serializer to get the list of invoices linked to subscription"""

    is_ready = serializers.BooleanField(default=False, source="chargebee_site")
    contract = InvoiceContractSerializer(allow_null=True)

    class Meta:
        model = Invoice
        fields = ("id", "date", "reference", "currency", "total", "tax_base", "tax", "is_ready", "status", "contract")
