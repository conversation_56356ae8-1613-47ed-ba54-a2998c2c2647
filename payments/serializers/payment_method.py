from datetime import datetime

import pytz
from django.db import transaction
from django.utils.timezone import make_aware
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.mixins import PartialUpdateMixin, StringErrorsMixin
from core.serializers import ModelSerializer
from core.utils import get_chargebee_instance_with_payment_gateway
from payments.models import Card


class PaymentIntentSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to get the payment intent id created on chargebee"""

    id = serializers.CharField(allow_null=False, allow_blank=False)
    status = serializers.CharField(allow_null=False, allow_blank=False)
    amount = serializers.IntegerField(
        allow_null=False,
    )
    gateway_account_id = serializers.CharField(allow_null=False, allow_blank=False)
    expires_at = serializers.IntegerField(
        allow_null=False,
    )
    payment_method_type = serializers.Char<PERSON>ield(allow_null=False, allow_blank=False)
    created_at = serializers.IntegerField(
        allow_null=False,
    )
    modified_at = serializers.IntegerField(
        allow_null=False,
    )
    updated_at = serializers.IntegerField(
        allow_null=False,
    )
    resource_version = serializers.IntegerField(
        allow_null=False,
    )
    object = serializers.CharField(allow_null=False, allow_blank=False)
    customer_id = serializers.CharField(allow_null=False, allow_blank=False)
    currency_code = serializers.CharField(allow_null=False, allow_blank=False)
    gateway = serializers.CharField(allow_null=False, allow_blank=False)


class PaymentMethodCreateBodySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to create a payment method using the payment intent id"""

    id = serializers.CharField(required=True, allow_null=False, allow_blank=False)


class PaymentMethodMixinSerializer:
    def create_payment_method_in_chargebee(self, validated_data):
        user = validated_data.get("user")
        payment_intent_id = validated_data.pop("payment_intent_id")

        chargebee_client, payment_gateway_id = get_chargebee_instance_with_payment_gateway(user.email_domain)
        payment_source = chargebee_client.create_payment_method(
            customer_id=user.chargebee_customer_id,
            replace_primary_payment_source=False,
            payment_intent=payment_intent_id,
        )

        # Set this payment method as primary
        payment_source_id = payment_source.payment_source.id
        chargebee_client.assign_payment_role(
            customer_id=user.chargebee_customer_id, payment_source_id=payment_source_id
        )

        # Finally, create the payment method into database
        payment_method = payment_source.payment_source.card
        validated_data.update(**{
            "remote_id": payment_source_id,
            "funding": payment_method.funding_type,
            "brand": payment_method.brand,
            "exp_month": payment_method.expiry_month,
            "exp_year": payment_method.expiry_year,
            "last4": payment_method.last4,
            "created": make_aware(
                datetime.fromtimestamp(payment_source.payment_source.created_at), timezone=pytz.utc
            ).date(),
        })

        return validated_data


class PaymentMethodCreateSerializer(PaymentMethodMixinSerializer, ModelSerializer):
    """Serializer to handle payment_method creation"""

    payment_intent_id = serializers.CharField(required=True, write_only=True)
    brand_name = serializers.CharField(read_only=True)

    class Meta:
        model = Card
        fields = "__all__"
        extra_kwargs = {
            "user": {"read_only": True},
            "funding": {"read_only": True},
            "exp_month": {"read_only": True},
            "exp_year": {"read_only": True},
            "last4": {"read_only": True},
            "created": {"read_only": True},
            "brand": {"read_only": True},
        }

    def validate(self, attrs):
        user = self.context["request"].user

        if not user.created_in_chargebee:
            raise serializers.ValidationError(
                {"non_field_errors": _("User is not created in payment processor platform. Try again later")}
            )

        if user.has_payment_method:
            raise serializers.ValidationError(
                {"non_field_errors": _("User already has a payment method. You should update it instead")}
            )

        attrs["user"] = user
        return attrs

    def create(self, validated_data):
        """
        In order to create a new payment_method:
        - Create the payment method in chargebee
        - Set payment method as primary
        - Create the entry into database
        ** side_effect: We are listening the PaymentSource added hook from chargebee, so we need to handle it accordingly
        """

        card_data = self.create_payment_method_in_chargebee(validated_data)
        with transaction.atomic():
            return super().create(card_data)


class PaymentMethodPartialUpdateSerializer(PaymentMethodMixinSerializer, PartialUpdateMixin, ModelSerializer):
    """Serializer to handle payment_method creation"""

    payment_intent_id = serializers.CharField(required=True, write_only=True)
    brand_name = serializers.CharField(read_only=True)

    class Meta:
        model = Card
        fields = "__all__"
        extra_kwargs = {
            "user": {"read_only": True},
            "funding": {"read_only": True},
            "exp_month": {"read_only": True},
            "exp_year": {"read_only": True},
            "last4": {"read_only": True},
            "created": {"read_only": True},
            "brand": {"read_only": True},
        }

    def validate(self, attrs):
        user = self.context["request"].user

        if not user.created_in_chargebee:
            raise serializers.ValidationError(
                {"non_field_errors": _("User is not created in payment processor platform. Try again later")}
            )

        attrs["user"] = user
        return attrs

    def update(self, instance, validated_data):
        """
        In order to update an existing payment_method:
        - Create the payment method in chargebee
        - Set payment method as primary
        - Create the entry into database
        ** side_effect: We are listening the PaymentSource added hook from chargebee, so we need to handle it accordingly
        """

        card_data = self.create_payment_method_in_chargebee(validated_data)

        # Drop unused parameter
        card_data.pop("user")
        with transaction.atomic():
            return super().update(instance, card_data)


class PaymentMethodSerializer(ModelSerializer):
    """Serializer to get payment method metadata"""

    class Meta:
        model = Card
        fields = "__all__"
