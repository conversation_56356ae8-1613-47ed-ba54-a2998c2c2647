import pytz
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from core.mixins import StringErrorsMixin
from payments.models import Subscription


class SubscriptionSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to get subscription metadata"""

    class Meta:
        model = Subscription
        fields = "__all__"


class SubscriptionCreateResponseSerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to encode the subscription creation response"""

    id = serializers.UUIDField(read_only=True)
    organization = serializers.UUIDField(read_only=True)
    remote_id = serializers.CharField(read_only=True)
    status = serializers.CharField(read_only=True)
    current_period_start = serializers.DateTimeField(read_only=True)
    current_period_end = serializers.DateTimeField(read_only=True)
    created = serializers.DateField(read_only=True)
    price_id = serializers.CharField(read_only=True)
    client_secret = serializers.CharField(required=False, read_only=True)
    payment_method_id = serializers.CharField(required=False, read_only=True)


class SubscriptionCreateSerializer(StringErrorsMixin, serializers.ModelSerializer):
    """Serializer to check needed params to create a subscription"""

    price_id = serializers.CharField(
        required=True, allow_null=False, allow_blank=False, help_text=_("Stripe product_id")
    )
    coupon_id = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=False,
        help_text=_("Stripe coupon id if this subscription has a discount"),
    )
    trial_period_days = serializers.IntegerField(
        min_value=0,
        max_value=30,
        required=False,
        allow_null=False,
        help_text=_("Trial period days for this subscription"),
    )
    cancel_at_period_end = serializers.BooleanField(
        default=False, allow_null=False, help_text=_("If subscription must be canceled at period end")
    )

    class Meta:
        model = Subscription
        fields = ("organization", "price_id", "coupon_id", "trial_period_days", "cancel_at_period_end")

        extra_kwargs = {"organization": {"required": True}}

    def validate(self, attrs):
        trial_period_days = attrs.get("trial_period_days", None)
        trial_end = attrs.get("trial_end", None)
        organization = attrs.get("organization")

        if not hasattr(organization, "billingdata"):
            raise serializers.ValidationError(
                {"billing_data": _("To create subscriptions, is mandatory to have billing data set")}
            )

        if not hasattr(organization, "paymentmethod"):
            raise serializers.ValidationError(
                {"payment_method": _("A valid payment method is needed to create a subscription")}
            )

        if organization.subscription_set.filter(
            status__in=[
                Subscription.SUBSCRIPTION_ACTIVE,
                Subscription.SUBSCRIPTION_PENDING,
                Subscription.SUBSCRIPTION_TRIAL,
            ]
        ):
            raise serializers.ValidationError(
                {"subscription": _("Organization has already a non canceled subscription.")}
            )

        if trial_period_days and trial_end:
            raise serializers.ValidationError(
                {"non_field_errors": _("You cannot set trial_period_days and trial_end both. Please, set only one.")}
            )

        if trial_end:
            attrs["trial_end"] = attrs["trial_end"].replace(tzinfo=pytz.utc).strformat("%s")

        return attrs


class SubscriptionChangePriceQuerySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check params for plan change """

    price_id = serializers.CharField(
        required=True, allow_null=False, allow_blank=False, help_text=_("Stripe product_id")
    )

    change_mode = serializers.ChoiceField(
        required=True,
        choices=["downgrade", "upgrade"],
        allow_null=False,
        allow_blank=False,
        help_text=_(
            "Future plan for this subscription:\n"
            "- **Downgrade**: In that case, downgrade the subscribed plan **without** prorration nor resetting the "
            "billing cycle. Current plan will be active until the end and only then, the new one will be billed.\n"
            "- **Upgrade**: In that case, upgrade the subscribed plan **with** prorration and resetting the billing "
            "cycle to now. Current plan will be changed and billed immediately.\n"
            "- **Note**: To change to free plan, please, cancel the subscription at period end instead."
        ),
    )

    billing_cycle_anchor = serializers.CharField(read_only=True)
    proration_behavior = serializers.CharField(read_only=True)

    def validate(self, attrs):
        change_mode = attrs.get("change_mode")
        price_id = attrs.get("price_id")
        if change_mode == "upgrade":
            attrs.update(
                **{
                    "proration_behavior": "create_prorations",
                    "billing_cycle_anchor": "now",
                }
            )
        else:
            attrs.update(
                **{
                    "proration_behavior": "none",
                    "billing_cycle_anchor": None,
                }
            )

        price_checker = settings.STRIPE_PRODUCT_MATRIX.get(self.instance.price_id, [])
        validated_change = "downgrade" if price_id in price_checker else "upgrade"
        if validated_change != change_mode:
            raise serializers.ValidationError(
                {
                    "change_mode": _(
                        "You are trying to {mode} the subscription, but the selected plan is a {right_mode}"
                    ).format(mode=change_mode, right_mode=validated_change)
                }
            )

        return attrs


class SubscriptionCancelQuerySerializer(StringErrorsMixin, serializers.Serializer):
    """Serializer to check if subscription must be canceled now or at period end"""

    now = serializers.BooleanField(
        required=True,
        allow_null=False,
        help_text=_(
            "If now is true, the subscription will be cancel immediately. In other case, will be marked to cancel at"
            " period end."
        ),
    )
