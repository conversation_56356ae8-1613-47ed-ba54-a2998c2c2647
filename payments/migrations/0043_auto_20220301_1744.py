# Generated by Django 3.2.12 on 2022-03-01 16:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0028_auto_20220224_1411"),
        ("payments", "0042_alter_quote_chargebee_remote_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="invoice",
            name="invoice_reference",
            field=models.CharField(help_text="Remote invoice number", max_length=200, null=True),
        ),
        migrations.AlterUniqueTogether(
            name="invoice",
            unique_together={("invoice_reference", "contract")},
        ),
        migrations.RemoveField(
            model_name="invoice",
            name="recurring_interval",
        ),
    ]
