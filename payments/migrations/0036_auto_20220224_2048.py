# Generated by Django 3.2.12 on 2022-02-24 19:48

from django.db import migrations
from django.db.models import F


def keep_current_susbcription_status(apps, schema_editor):
    Subscription = apps.get_model("payments", "Subscription")

    # Only add if we don't have any configuration yet
    Subscription.objects.update(temporal_status=F("status"))


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0035_auto_20220224_2047"),
    ]

    operations = [
        migrations.RunPython(keep_current_susbcription_status, reverse_code=migrations.RunPython.noop),
    ]
