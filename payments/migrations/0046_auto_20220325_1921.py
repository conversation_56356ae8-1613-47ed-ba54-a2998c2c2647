# Generated by Django 3.2.12 on 2022-03-25 18:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0045_auto_20220318_2250"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="subscription",
            name="exclude_next_free_month",
        ),
        migrations.AddField(
            model_name="subscription",
            name="in_month_free",
            field=models.BooleanField(
                default=False,
                help_text=(
                    "Indicates that subscription is in free month. If this is set true would means the next month is"
                    " free and recurring interval is 5"
                ),
            ),
        ),
    ]
