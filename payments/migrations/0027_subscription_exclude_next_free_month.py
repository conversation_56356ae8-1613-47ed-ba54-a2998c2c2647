# Generated by Django 3.2.12 on 2022-02-11 09:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0026_subscription_free_month_allowed"),
    ]

    operations = [
        migrations.AddField(
            model_name="subscription",
            name="exclude_next_free_month",
            field=models.BooleanField(
                default=False,
                help_text=(
                    "If next free month should be skipped. This is useful for imported subscriptions to avoid applying"
                    " the free month twice"
                ),
            ),
        ),
    ]
