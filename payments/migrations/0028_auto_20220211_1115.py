# Generated by Django 3.2.12 on 2022-02-11 10:15

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("payments", "0027_subscription_exclude_next_free_month"),
    ]

    operations = [
        migrations.CreateModel(
            name="Card",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("remote_id", models.CharField(max_length=200, null=True)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("created", models.DateField(null=True)),
                ("last4", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "funding",
                    models.Char<PERSON>ield(
                        choices=[
                            ("credit", "Credit"),
                            ("prepaid", "Prepaid"),
                            ("debit", "Debit"),
                            ("not_known", "Unknown"),
                        ],
                        default="not_known",
                        max_length=200,
                    ),
                ),
                (
                    "brand",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        choices=[
                            ("american_express", "American Express"),
                            ("diners_club", "Diners Club"),
                            ("discover", "Discover"),
                            ("jcb", "Jcb"),
                            ("mastercard", "Mastercard"),
                            ("unionpay", "Unionpay"),
                            ("visa", "Visa"),
                            ("other", "Other"),
                        ],
                        default="not_known",
                        max_length=200,
                    ),
                ),
                ("exp_month", models.PositiveIntegerField(blank=True, null=True)),
                ("exp_year", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "user",
                    models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SepaMandate",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("remote_id", models.CharField(max_length=200, null=True)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("created", models.DateField(null=True)),
                ("last4", models.CharField(blank=True, max_length=200, null=True)),
                ("bank_name", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "user",
                    models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.DeleteModel(
            name="PaymentMethod",
        ),
    ]
