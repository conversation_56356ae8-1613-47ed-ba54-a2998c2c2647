# Generated by Django 3.2.7 on 2021-09-29 19:06

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("contracts", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Subscription",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("start_date", models.DateTimeField(default=django.utils.timezone.now)),
                ("hired_space", models.IntegerField(default=0)),
                ("recurring_cost", models.IntegerField(default=0)),
                ("delivery_cost", models.IntegerField(default=0)),
                ("pick_up_cost", models.IntegerField(default=0)),
                (
                    "subscription_id",
                    models.CharField(
                        blank=True, help_text="Subscription id in external service", max_length=200, null=True
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("empty", "No created"),
                            ("active", "Active"),
                            ("pending", "Pending"),
                            ("failed", "Failed"),
                            ("trial", "Trial Period"),
                            ("canceled", "Canceled"),
                        ],
                        default="empty",
                        max_length=200,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.contract"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Invoice",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("date", models.DateField(help_text="Date where invoice has been emitted")),
                ("invoice_url", models.URLField(blank=True, null=True)),
                ("invoice_number", models.CharField(help_text="Remote invoice number", max_length=200, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("currency", models.CharField(max_length=3, null=True)),
                ("total", models.IntegerField(default=0)),
                ("tax_base", models.IntegerField(default=0)),
                ("tax", models.IntegerField(default=0)),
                (
                    "subscription",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to="payments.subscription"
                    ),
                ),
            ],
            options={
                "ordering": ("-date", "-invoice_number"),
            },
        ),
    ]
