# Generated by Django 3.2.12 on 2022-02-25 20:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0028_auto_20220224_1411"),
        ("payments", "0038_rename_temporal_status_subscription_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="subscription",
            name="chargebee_quote_id",
            field=models.CharField(
                blank=True, help_text="Quote id in external service", max_length=200, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="quote",
            name="chargebee_remote_id",
            field=models.Char<PERSON>ield(
                blank=True, help_text="Remote id in external service", max_length=200, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="quote",
            name="contract",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.contract"
            ),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="subscription",
            name="chargebee_remote_id",
            field=models.CharField(
                blank=True, help_text="Remote id in external service", max_length=200, null=True, unique=True
            ),
        ),
    ]
