# Generated by Django 3.2.8 on 2021-10-26 16:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0009_deposit"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoice",
            name="recurring_interval",
            field=models.PositiveIntegerField(default=0, help_text="Monthly (1), Biannual (6), Annual (12)"),
        ),
        migrations.AlterField(
            model_name="deposit",
            name="status",
            field=models.CharField(
                choices=[("empty", "No created"), ("paid", "Paid"), ("failed", "Failed")],
                default=(("empty", "No created"), ("paid", "Paid"), ("failed", "Failed")),
                max_length=200,
            ),
        ),
    ]
