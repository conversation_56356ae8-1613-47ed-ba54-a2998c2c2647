# Generated by Django 3.2.9 on 2021-11-16 10:45

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contracts", "0013_item_remote_dir"),
        ("payments", "0013_invoice_remote_id"),
    ]

    operations = [
        migrations.RenameField(
            model_name="subscription",
            old_name="stripe_subscription_id",
            new_name="stripe_remote_id",
        ),
        migrations.RemoveField(
            model_name="subscription",
            name="service_type",
        ),
        migrations.AddField(
            model_name="invoice",
            name="contract",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="contracts.contract"),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="status",
            field=models.CharField(
                choices=[
                    ("empty", "No created"),
                    ("active", "Active"),
                    ("pending", "Pending"),
                    ("failed", "Failed"),
                    ("created", "Created"),
                    ("canceled", "Canceled"),
                ],
                default="empty",
                max_length=200,
            ),
        ),
        migrations.CreateModel(
            name="OneTimePayment",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "stripe_remote_id",
                    models.CharField(
                        blank=True,
                        help_text="Subscription id in external service",
                        max_length=200,
                        null=True,
                        unique=True,
                    ),
                ),
                ("stripe_quote_id", models.CharField(blank=True, max_length=200, null=True, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("empty", "No created"),
                            ("active", "Active"),
                            ("pending", "Pending"),
                            ("failed", "Failed"),
                            ("created", "Created"),
                            ("canceled", "Canceled"),
                        ],
                        default="empty",
                        max_length=200,
                    ),
                ),
                (
                    "internal_id",
                    models.CharField(
                        blank=True,
                        help_text="Internal id from moreapp to avoid duplicate subscription entries",
                        max_length=200,
                        null=True,
                        unique=True,
                    ),
                ),
                ("customer_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "coupon",
                    models.CharField(
                        blank=True,
                        help_text="Discountable coupon to be used on first creation",
                        max_length=100,
                        null=True,
                    ),
                ),
                ("line_items", models.JSONField(blank=True, null=True)),
                ("tax", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("cost", models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                (
                    "service_type",
                    models.CharField(
                        choices=[("moving", "Moving"), ("box_delivery", "Box delivery"), ("unknown", "Unknown")],
                        default="unknown",
                        max_length=100,
                    ),
                ),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="contracts.contract"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="invoice",
            name="one_time",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to="payments.onetimepayment"
            ),
        ),
    ]
