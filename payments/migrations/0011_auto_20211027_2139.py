# Generated by Django 3.2.8 on 2021-10-27 19:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0010_auto_20211026_1840"),
    ]

    operations = [
        migrations.AddField(
            model_name="subscription",
            name="coupon",
            field=models.CharField(
                blank=True, help_text="Discountable coupon to be used on first creation", max_length=100, null=True
            ),
        ),
        migrations.AddField(
            model_name="subscription",
            name="recurring_interval",
            field=models.PositiveIntegerField(default=0, help_text="Monthly (1), Biannual (6), Annual (12)"),
        ),
    ]
