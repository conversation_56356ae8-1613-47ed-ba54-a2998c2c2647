# Generated by Django 3.2.8 on 2021-10-25 11:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0006_auto_20211022_1712"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="invoice",
            name="invoice_reference",
            field=models.Char<PERSON>ield(help_text="Remote invoice number", max_length=200, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="invoice",
            name="tax",
            field=models.DecimalField(decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="invoice",
            name="tax_base",
            field=models.DecimalField(decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="invoice",
            name="total",
            field=models.DecimalField(decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("empty", "No created"),
                    ("active", "Active"),
                    ("pending", "Pending"),
                    ("failed", "Failed"),
                    ("canceled", "Canceled"),
                ],
                default="empty",
                max_length=200,
            ),
        ),
    ]
