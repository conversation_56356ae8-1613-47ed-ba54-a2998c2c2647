# Generated by Django 3.2.12 on 2022-03-18 21:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0044_alter_subscription_recurring_interval"),
    ]

    operations = [
        migrations.AlterField(
            model_name="subscription",
            name="current_space",
            field=models.DecimalField(decimal_places=1, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="delivery_cost",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="hired_space",
            field=models.DecimalField(decimal_places=1, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="pick_up_cost",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="recurring_cost",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, null=True),
        ),
    ]
