from django.conf import settings
from stripe import Invoice


class InvoiceMixin:
    def create_upcoming_invoice(
        self, customer_id, subscription_id=None, coupon_id=None, *, price_id, with_taxes, subscription_change
    ):
        """Create an upcoming invoice for active subscriptions"""

        invoice_metadata = {
            "subscription_cancel_at_period_end": False,
            "subscription_default_tax_rates": [settings.STRIPE_TAX_RATE_ID] if with_taxes else None,
        }

        if subscription_id:
            subscription = self.get_subscription(subscription_id)

            invoice_metadata.update(
                **{
                    "subscription": subscription.stripe_id,
                    "subscription_items": [
                        {
                            "id": subscription["items"]["data"][0].id,
                            "deleted": True,
                        },
                        {
                            "price": price_id,
                            "deleted": False,
                        },
                    ],
                }
            )

            if subscription_change == "upgrade":
                invoice_metadata.update(
                    **{
                        "subscription_proration_behavior": "create_prorations",
                        "subscription_billing_cycle_anchor": "now",
                    }
                )
            else:
                invoice_metadata.update(
                    **{
                        "subscription_proration_behavior": "none",
                        "subscription_billing_cycle_anchor": None,
                    }
                )

        else:
            invoice_metadata.update(
                **{
                    "subscription_billing_cycle_anchor": "now",
                    "subscription_proration_behavior": "none",
                    "subscription_items": [{"price": price_id}],
                }
            )

        if coupon_id:
            invoice_metadata.update(**{"coupon": coupon_id})

        return Invoice.upcoming(customer=customer_id, **invoice_metadata)
