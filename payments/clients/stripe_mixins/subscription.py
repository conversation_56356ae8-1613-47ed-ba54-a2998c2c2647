import uuid

from django.conf import settings
from stripe import Subscription


class SubscriptionMixin:
    def create_subscription(
        self,
        customer_id,
        price_id,
        *,
        cancel_at_period_end=False,
        include_tax=False,
        coupon=None,
        metadata=None,
        trial_period_days=None,
    ):
        """Create a subscription in Stripe monthly renewable"""

        subscription_keys = {
            "customer": customer_id,
            "items": [
                {
                    "price": price_id,
                }
            ],
            "cancel_at_period_end": cancel_at_period_end,
            "proration_behavior": "none",
            "off_session": True,
            "expand": ["latest_invoice.payment_intent", "latest_invoice"],
        }

        if include_tax:
            subscription_keys["default_tax_rates"] = [settings.STRIPE_TAX_RATE_ID]

        if coupon:
            subscription_keys["coupon"] = coupon

        if metadata:
            subscription_keys["metadata"] = metadata

        if trial_period_days:
            subscription_keys["trial_period_days"] = trial_period_days

        return Subscription.create(idempotency_key=uuid.uuid4().hex, **subscription_keys)

    def get_subscription(self, subscription_id):
        """Retrieve subscription data from Stripe"""
        return Subscription.retrieve(subscription_id)

    def update_subscription(
        self, subscription_id, price_id=None, billing_cycle_anchor=None, proration_behavior=None, **updatable_keys
    ):
        """Update certain data in stripe subscription"""

        if price_id:
            subscription = self.get_subscription(subscription_id)
            item_id = subscription["items"]["data"][0].id
            updatable_keys.update(**{"items": [{"id": item_id, "price": price_id}]})

        if billing_cycle_anchor:
            updatable_keys.update(**{"billing_cycle_anchor": billing_cycle_anchor})

        if proration_behavior:
            updatable_keys.update(**{"proration_behavior": proration_behavior})

        return Subscription.modify(subscription_id, idempotency_key=uuid.uuid4().hex, **updatable_keys)

    def cancel_subscription(self, subscription_id, now=False):
        """Cancel the subscription now or at period end"""
        if now:
            return Subscription.delete(subscription_id)
        return Subscription.modify(subscription_id, cancel_at_period_end=True)
