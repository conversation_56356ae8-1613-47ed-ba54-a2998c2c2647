from django.contrib import admin, messages
from django.db.models import F
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from moneyed import Money

from core.admin_utils.utils import DjangoAdminActions
from payments.forms import DropRequiredFieldsInInvoiceForm, SubscriptionForm
from payments.models import (BillingData, Card, Coupon, CreditNote, Invoice,
                             Quote, SepaMandate, Subscription)


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ["reference", "date", "total_amount", "status", "contract"]
    raw_id_fields = ("contract", "user")
    search_fields = ("contract__contract_document_id",)
    form = DropRequiredFieldsInInvoiceForm

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("contract")

    @admin.display(description=_("Total"))
    def total_amount(self, invoice):
        return Money(invoice.total, (invoice.currency or "EUR").upper()) if invoice.total is not None else "-"


class InvoiceInline(admin.TabularInline):
    model = Invoice

    def invoice(self, instance):
        url = reverse(f"admin:{instance._meta.app_label}_invoice_change", args=(instance.pk,))
        return format_html('<b><a href="{}" target="_blank">{}</a></b>', url, instance.reference)

    invoice.verbose_name = _("Invoice")

    fields = ("invoice", "date", "total_amount")
    readonly_fields = fields
    extra = 0

    @admin.display(description=_("Total"))
    def total_amount(self, invoice):
        return Money(invoice.total, (invoice.currency or "EUR").upper()) if invoice.total is not None else "-"

    def has_add_permission(self, request, obj):
        return False


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ["contract", "chargebee_id", "status", "start_date", "imported_legacy"]
    raw_id_fields = ("contract",)
    search_fields = ("chargebee_remote_id",)
    form = SubscriptionForm

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("contract")

    @admin.display(description=_("Chargebee id"))
    def chargebee_id(self, obj):
        return obj.chargebee_remote_id


@admin.register(Quote)
class QuoteAdmin(DjangoAdminActions, admin.ModelAdmin):
    list_display = ["contract", "chargebee_id", "status", "service_type", "created_at"]
    raw_id_fields = ("contract",)
    search_fields = ("contract__contract_document_id",)
    change_actions = ["create_in_chargebee"]
    ordering = (F("chargebee_remote_id").desc(nulls_last=True),)
    list_filter = ("status", "service_type")

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related(
            "contract",
        )

    @admin.display(description=_("Chargebee id"))
    def chargebee_id(self, obj):
        return obj.chargebee_remote_id

    @admin.action(description=_("Create in chargebee"))
    def create_in_chargebee(self, request, obj):
        """Create quote in chargebee"""

        obj.create_in_chargebee()
        message, level = (_("Quote {quote} created successfully").format(quote=obj.pk), messages.SUCCESS)
        if obj.status == Quote.FAILED:
            message, level = (_("Quote {quote} not created").format(quote=obj.pk), messages.ERROR)

        self.message_user(request, message=message, level=level)


@admin.register(BillingData)
class BillingDataAdmin(admin.ModelAdmin):
    list_display = ["user", "address", "city", "postal_code", "state"]
    raw_id_fields = ("user",)


@admin.register(Card)
class CardAdmin(admin.ModelAdmin):
    list_display = ["user", "last4", "funding", "brand", "expiration_date"]
    raw_id_fields = ("user",)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("user")

    def expiration_date(self, obj):
        return f"{obj.exp_month}/{obj.exp_year}"


@admin.register(SepaMandate)
class SepaMandateAdmin(admin.ModelAdmin):
    list_display = ["user", "last4", "bank_name"]
    raw_id_fields = ("user",)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("user")


@admin.register(CreditNote)
class CreditNoteAdmin(admin.ModelAdmin):
    list_display = ["reference", "date", "remote_id", "total_amount", "status", "contract"]
    raw_id_fields = ("contract", "user")
    search_fields = ("contract__contract_document_id",)
    form = DropRequiredFieldsInInvoiceForm

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("contract")

    @admin.display(description=_("Total"))
    def total_amount(self, invoice):
        return Money(invoice.total, (invoice.currency or "EUR").upper()) if invoice.total is not None else "-"


@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    list_display = ["name", "chargebee_id", "site", "active"]
