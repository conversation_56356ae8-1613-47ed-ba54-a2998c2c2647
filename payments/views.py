import logging

import chargebee
from django.conf import settings
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response
from rest_framework.views import APIView

from core.filters import CaseInsensitiveOrderingFilter
from core.inspectors import BasePaginationInspector
from core.paginations import CreditNotePagination, InvoicePagination
from core.utils import (get_chargebee_instance_with_payment_gateway,
                        get_openapi_error_response, get_openapi_response)
from payments.models import BillingData, Card, CreditNote, Invoice
from payments.serializers.billing_data import (
    BillingDataCreateSerializer, BillingDataPartialUpdateSerializer,
    BillingDataSerializer)
from payments.serializers.credit_note import (CreditNoteQuerySetSerializer,
                                              CreditNoteSerializer)
from payments.serializers.invoice import (InvoiceQuerySetSerializer,
                                          InvoiceSerializer)
from payments.serializers.payment_method import (
    PaymentIntentSerializer, PaymentMethodCreateSerializer,
    PaymentMethodPartialUpdateSerializer, PaymentMethodSerializer)

logger = logging.getLogger(__name__)


@method_decorator(
    name="retrieve",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
class CreditNoteViewSet(viewsets.ModelViewSet):
    """Credit note endpoints to get private date"""

    serializer_class = CreditNoteSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = CreditNote.objects.all()
    filter_backends = (CaseInsensitiveOrderingFilter,)
    ordering_fields = (
        "reference",
        "date",
    )
    ordering = ["-date", "-reference"]
    pagination_class = CreditNotePagination
    http_method_names = ["get"]

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset_params = {
            "user": self.request.user,
        }

        if self.action not in ["view_credit_note_pdf", "download_credit_note_pdf"]:
            contract_id = self.request.query_params.get("contract_id", None)

            # By default, return all document related to authenticated user
            if contract_id:
                queryset_params["contract"] = contract_id

        return queryset.select_related("contract").filter(**queryset_params).order_by("-date")

    @swagger_auto_schema(
        operation_summary="List Credit Notes",
        query_serializer=CreditNoteQuerySetSerializer,
        paginator_inspectors=[BasePaginationInspector],
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve credit note pdf as inline",
        responses={
            status.HTTP_200_OK: get_openapi_response("url"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                None, _("Error retrieving invoice. Try again later.")
            ),
        },
    )
    @action(methods=["get"], url_path="view-credit-note", detail=True)
    def view_credit_note_pdf(self, request, pk=None):
        credit_note = self.get_object()

        try:
            return Response({"url": credit_note.document_url}, status=status.HTTP_200_OK)
        except chargebee.api_error.APIError as err:
            logger.warning(f"Error retrieving credit note due to: {err}")

        return Response(
            {"non_field_errors": _("Error retrieving credit note. Try again later.")},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @swagger_auto_schema(
        operation_summary="Retrieve credit note pdf as attachment",
        responses={
            status.HTTP_200_OK: get_openapi_response("url"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                None, _("Error retrieving credit note. Try again later.")
            ),
        },
    )
    @action(methods=["get"], url_path="download-credit-note", detail=True)
    def download_credit_note_pdf(self, request, pk=None):
        credit_note = self.get_object()

        try:
            return Response({"url": credit_note.downloadable_document_url}, status=status.HTTP_200_OK)
        except chargebee.api_error.APIError as err:
            logger.warning(f"Error retrieving credit note for downloading due to: {err}")

        return Response(
            {"non_field_errors": _("Error retrieving credit note. Try again later.")},
            status=status.HTTP_400_BAD_REQUEST,
        )


@method_decorator(
    name="retrieve",
    decorator=swagger_auto_schema(
        auto_schema=None,
    ),
)
class InvoiceViewSet(viewsets.ModelViewSet):
    """Invoice endpoints to get private data"""

    serializer_class = InvoiceSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = Invoice.objects.all()
    filter_backends = (CaseInsensitiveOrderingFilter,)
    ordering_fields = (
        "reference",
        "date",
    )
    ordering = ["-date", "-reference"]
    pagination_class = InvoicePagination
    http_method_names = ["get"]

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset_params = {
            "user": self.request.user,
        }

        if self.action not in ["view_invoice_pdf", "download_invoice_pdf"]:
            contract_id = self.request.query_params.get("contract_id", None)

            # By default, return all document related to authenticated user
            if contract_id:
                queryset_params["contract"] = contract_id

        return queryset.select_related("contract").filter(**queryset_params).order_by("-date")

    @swagger_auto_schema(
        operation_summary="List Invoices",
        query_serializer=InvoiceQuerySetSerializer,
        paginator_inspectors=[BasePaginationInspector],
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve invoice pdf as inline",
        responses={
            status.HTTP_200_OK: get_openapi_response("url"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                None, _("Error retrieving invoice. Try again later.")
            ),
        },
    )
    @action(methods=["get"], url_path="view-invoice", detail=True)
    def view_invoice_pdf(self, request, pk=None):
        invoice = self.get_object()

        try:
            return Response({"url": invoice.document_url}, status=status.HTTP_200_OK)
        except chargebee.api_error.APIError as err:
            logger.warning(f"Error retrieving invoice due to: {err}")

        return Response(
            {"non_field_errors": _("Error retrieving invoice. Try again later.")}, status=status.HTTP_400_BAD_REQUEST
        )

    @swagger_auto_schema(
        operation_summary="Retrieve invoice pdf as attachment",
        responses={
            status.HTTP_200_OK: get_openapi_response("url"),
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(
                None, _("Error retrieving invoice. Try again later.")
            ),
        },
    )
    @action(methods=["get"], url_path="download-invoice", detail=True)
    def download_invoice_pdf(self, request, pk=None):
        invoice = self.get_object()

        try:
            return Response({"url": invoice.downloadable_document_url}, status=status.HTTP_200_OK)
        except chargebee.api_error.APIError as err:
            logger.warning(f"Error retrieving invoice for downloading due to: {err}")

        return Response(
            {"non_field_errors": _("Error retrieving invoice. Try again later.")}, status=status.HTTP_400_BAD_REQUEST
        )


class BillingDataViewSet(viewsets.ModelViewSet):
    serializer_class = BillingDataSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = BillingData.objects.all()
    http_method_names = ["get", "post", "patch"]

    def get_queryset(self):
        queryset = super().get_queryset()

        # We must restrict billing data to authorized organizations
        return queryset.select_related("user").filter(user=self.request.user)

    def get_serializer_class(self):
        if self.action == "create":
            return BillingDataCreateSerializer
        elif self.action == "partial_update":
            return BillingDataPartialUpdateSerializer
        return self.serializer_class

    @swagger_auto_schema(auto_schema=None)
    def list(self, request, *args, **kwargs):
        raise MethodNotAllowed(request.method)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Create a chargebee billing data",
        request_body=BillingDataCreateSerializer,
        operation_description="Update billing date in Chargebee and then, update the related model in db.",
        responses={
            status.HTTP_200_OK: BillingDataCreateSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(BillingDataCreateSerializer, "Error"),
        },
    )
    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except chargebee.api_error.APIError as error:
            logger.error(f"Error updating billing data due to a chargebee error {error}")

        return Response(
            {"non_field_errors": _("Error updating billing data . Please try again, try again later")},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @swagger_auto_schema(
        operation_summary="Update a chargebee billing data",
        request_body=BillingDataPartialUpdateSerializer,
        operation_description="Update billing date in Chargebee and then, update the related model in db.",
        responses={
            status.HTTP_200_OK: BillingDataPartialUpdateSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(BillingDataPartialUpdateSerializer, "Error"),
        },
    )
    def partial_update(self, request, *args, **kwargs):
        try:
            return super().partial_update(request, *args, **kwargs)
        except chargebee.api_error.APIError as error:
            logger.error(f"Error updating billing data due to a chargebee error {error}")

        return Response(
            {"non_field_errors": _("Error updating billing data . Please try again, try again later")},
            status=status.HTTP_400_BAD_REQUEST,
        )


class PaymentIntentAPIView(APIView):
    """Endpoint to handle the payment intent creation as prev step to add a card into Chargebee"""

    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        operation_summary="Create a payment intent",
        operation_description="Create a payment intent to add later a payment method",
        responses={
            status.HTTP_200_OK: PaymentIntentSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(None, _("Error creating the payment intent")),
        },
    )
    def post(self, request, format=None):
        user = self.request.user

        if not user.created_in_chargebee:
            return Response(
                {"non_field_errors": _("User is not created in payment processor platform. Try again later")}
            )

        try:
            chargebee_client, payment_gateway_id = get_chargebee_instance_with_payment_gateway(user.email_domain)
            amount = settings.DEFAULT_AMOUNT.get(user.email_domain)
            payment_intent = chargebee_client.create_payment_intent(
                customer_id=user.chargebee_customer_id, amount=amount, payment_gateway=payment_gateway_id
            ).payment_intent
        except chargebee.api_error.APIError as error:
            logger.warning(f"Error creating the payment intent - {error}")
            return Response(
                {"non_field_errors": _("Error updating the payment method. Try again later")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(payment_intent.values, status=status.HTTP_200_OK)


class PaymentMethodViewSet(viewsets.ModelViewSet):
    """Endpoints to handle payment method creation in chargebee. Only cards at the moment"""

    serializer_class = PaymentMethodSerializer
    queryset = Card.objects.all()
    permission_classes = (permissions.IsAuthenticated,)
    http_method_names = ["post", "patch"]

    def get_queryset(self):
        queryset = super().get_queryset()

        if self.action in ["create", "partial_update"]:
            queryset = queryset.select_related("user")

        # We have to restrict subscriptions to authorized users
        return queryset.filter(user=self.request.user)

    def get_serializer_class(self):
        if self.action == "create":
            return PaymentMethodCreateSerializer
        elif self.action == "partial_update":
            return PaymentMethodPartialUpdateSerializer
        return self.serializer_class

    @swagger_auto_schema(
        operation_summary="Create a chargebee payment method",
        request_body=PaymentMethodCreateSerializer,
        operation_description=(
            "Create first a new payment method in Chargebee and then, update the related model in db."
            "This payment method will be set as primary in Chargebee, keeping both the new and older payment methods"
        ),
        responses={
            status.HTTP_200_OK: PaymentMethodCreateSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(PaymentMethodCreateSerializer, "Error"),
        },
    )
    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except chargebee.api_error.APIError as error:
            logger.error(f"Error creating payment method due to a chargebee error {error}")

        return Response(
            {"non_field_errors": _("Error creating payment method. Please try again, try again later")},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @swagger_auto_schema(
        operation_summary="Update a chargebee payment method",
        request_body=PaymentMethodPartialUpdateSerializer,
        operation_description=(
            "Create first a new payment method in Chargebee and then, update the related model in db."
            "This payment method will be set as primary in Chargebee, keeping both the new and older payment methods"
        ),
        responses={
            status.HTTP_200_OK: PaymentMethodCreateSerializer,
            status.HTTP_400_BAD_REQUEST: get_openapi_error_response(PaymentMethodCreateSerializer, "Error"),
        },
    )
    def partial_update(self, request, *args, **kwargs):
        try:
            return super().partial_update(request, *args, **kwargs)
        except chargebee.api_error.APIError as error:
            logger.error(f"Error updating payment method due to a chargebee {error}")

        return Response(
            {"non_field_errors": _("Error updating payment method. Please try again later")},
            status=status.HTTP_400_BAD_REQUEST,
        )
