import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _


class Coupon(models.Model):
    ES = "ES"
    FR = "FR"
    PT = "PT"
    MX = "MX"

    CHARGEBEE_SITES_CHOICES = (
        (ES, _("ES")),
        (FR, _("FR")),
        (PT, _("PT")),
        (MX, _("MX")),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    name = models.CharField(max_length=200, null=True, blank=True, help_text=_("Internal name for this coupon"))
    chargebee_id = models.CharField(max_length=200, null=True, blank=True, help_text=_("Chargebee id for this coupon"))
    site = models.CharField(
        max_length=200,
        choices=CHARGEBEE_SITES_CHOICES,
        default=ES,
        help_text=_("Chargebee site where the coupon has been created"),
    )
    active = models.BooleanField(default=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["chargebee_id", "site"], name="chargebee_coupon_unique_constraint")
        ]
