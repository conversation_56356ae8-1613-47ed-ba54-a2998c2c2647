import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _


class Invoice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    remote_id = models.CharField(max_length=200, null=True, blank=True)
    subscription = models.ForeignKey("payments.Subscription", on_delete=models.CASCADE, null=True)

    # name = models.CharField(
    #     max_length=200, blank=True, null=True, help_text=_("Customer's full name or company/business name")
    # )
    # address = models.Char<PERSON>ield(max_length=200, blank=True, null=True, help_text=_("Customer's address"))
    # postal_code = models.CharField(max_length=10, blank=True, null=True)
    # city = models.CharField(max_length=200, blank=True, null=True)
    # state = models.CharField(max_length=200, blank=True, null=True)
    # vat = models.CharField(max_length=200, blank=True, null=True)
    # country = CountryField(null=True, blank=False)
    # tax_base = models.DecimalField(max_digits=10, decimal_places=2, null=False, blank=False, default=0)
    # tax = models.DecimalField(max_digits=10, decimal_places=2, null=False, blank=False, default=0)
    currency = models.CharField(max_length=3, null=True)
    total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=False)
    date = models.DateField(null=False, blank=False)
    invoice_url = models.URLField(blank=True, null=True)
    invoice_number = models.CharField(max_length=200, blank=False, null=True, help_text=_("Stripe invoice number"))
    display = models.BooleanField(
        default=True, help_text=_("If this invoice must be shown in Organization invoice list")
    )

    class Meta:
        ordering = ("-date", "-invoice_number")
