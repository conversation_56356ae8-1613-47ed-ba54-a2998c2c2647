import uuid

from django.contrib.auth import get_user_model
from django.db import models
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class PaymentMethod(models.Model):
    """Payment method metadata"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    remote_id = models.CharField(max_length=200, null=True)

    name = models.CharField(max_length=200, null=True, blank=True)

    # Chargebee metadata
    created = models.DateField(null=True, blank=False)
    last4 = models.CharField(max_length=200, null=True, blank=True)

    # Foreign keys
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=False, blank=False)

    class Meta:
        abstract = True

    def __str__(self):
        return self.remote_id or str(self.pk)


class Card(PaymentMethod):
    CREDIT = "credit"
    PREPAID = "prepaid"
    DEBIT = "debit"
    UNKNOWN = "not_known"

    FUNDING_CHOICES = (
        (CREDIT, _("Credit")),
        (PREPAID, _("Prepaid")),
        (DEBIT, _("Debit")),
        (UNKNOWN, _("Unknown")),
    )

    AMERICAN_EXPRESS = "american_express"
    DINERS_CLUB = "diners_club"
    DISCOVER = "discover"
    JCB = "jcb"
    MASTERCARD = "mastercard"
    MAESTRO = "maestro"
    UNIONPAY = "unionpay"
    VISA = "visa"
    OTHER = "other"

    BRAND_CHOICES = (
        (AMERICAN_EXPRESS, _("American Express")),
        (DINERS_CLUB, _("Diners Club")),
        (DISCOVER, _("Discover")),
        (JCB, _("Jcb")),
        (MASTERCARD, _("Mastercard")),
        (UNIONPAY, _("Unionpay")),
        (VISA, _("Visa")),
        (OTHER, _("Other")),
    )

    funding = models.CharField(max_length=200, choices=FUNDING_CHOICES, default=UNKNOWN)
    brand = models.CharField(max_length=200, choices=BRAND_CHOICES, default=UNKNOWN)
    exp_month = models.PositiveIntegerField(null=True, blank=True)
    exp_year = models.PositiveIntegerField(null=True, blank=True)

    @property
    def brand_name(self):
        return self.get_brand_display()


class SepaMandate(PaymentMethod):
    bank_name = models.CharField(max_length=200, null=True, blank=True)
