from datetime import datetime

from django.utils.timezone import make_aware, utc
from stdnum.es import cif
from stdnum.eu import vat as check_vat

EU_VAT_COUNTRIES = [
    "AT",  # Austria
    "BE",  # Belgium
    "BG",  # Bulgaria
    "HR",  # Croatia
    "CY",  # Republic of Cyprus
    "CZ",  # Czech Republic
    "DK",  # Denmark, except the Faroe Islands and Greenland
    "EE",  # Estonia
    "FI",  # Finland, but excluding the Åland Islands
    "FR",
    # France, including Monaco but excluding Guadeloupe, Martinique, Réunion, St. Pierre and Miquelon, and French Guiana
    "DE",  # Germany, except Büsingen and the Isle of Heligoland
    "EL",  # Greece
    "HU",  # Hungary
    "IE",  # Ireland
    "IT",  # Italy, except the communes of Livigno and Campione d’Italia and the Italian waters of Lake Lugano
    "LV",  # Latvia
    "LT",  # Lithuania
    "LU",  # Luxembourg
    "MT",  # Malta
    "NL",  # Netherlands
    "PL",  # Poland
    "PT",  # Portugal, including the Azores and Madeira
    "RO",  # Romania
    "SK",  # Slovak Republic
    "SI",  # Slovenia
    "ES",  # Spain, including the Balearic Islands but excluding Ceuta, Melilla and the Canary Islands
    "SE",  # Sweden
]


def validate_vat(vat, country):
    """Check if given vat is valid for specific country"""
    if country in EU_VAT_COUNTRIES:
        return check_vat.is_valid(country + vat)

    return True


def need_tax(vat, country, state):
    """Check if for given state, country and vat is needed to set taxes or not"""
    if country == "ES":
        return state not in ["Ceuta", "Melilla", "Las Palmas", "Santa Cruz de Tenerife"]

    return not check_vat.check_vies(str(country) + vat).valid if country in EU_VAT_COUNTRIES else False


def get_vat_type_from_number(number):
    """Helper method to get, given a VAT number, if it is a cif to set the stripe ID correctly"""
    if cif.is_valid(number):
        return "es_cif"

    return "eu_vat"


def create_invoice(invoice_object):
    """Create local invoice from Stripe metadata"""

    from payments.models import Invoice, Subscription

    invoice_metadata = {
        "remote_id": invoice_object["id"],
        "invoice_number": invoice_object["number"],
        "invoice_url": invoice_object["invoice_pdf"],
        "date": make_aware(datetime.fromtimestamp(invoice_object["created"]), timezone=utc),
        "total": round(invoice_object["total"] / 100, 2),
        "currency": invoice_object["currency"],
    }

    try:
        subscription = Subscription.objects.get(remote_id=invoice_object["subscription"])
        Invoice.objects.create(subscription=subscription, **invoice_metadata)
    except Subscription.DoesNotExist:
        pass
