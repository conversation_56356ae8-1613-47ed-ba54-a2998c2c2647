from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>

from payments.views import (BillingDataViewSet, InvoiceViewSet,
                            PaymentMethodViewSet, SubscriptionViewSet)

router = DefaultRouter()
router.register("billings", BillingDataViewSet, basename="billing")
router.register("subscriptions", SubscriptionViewSet, basename="subscription")
router.register("paymentmethods", PaymentMethodViewSet, basename="paymentmethod")
router.register("invoices", InvoiceViewSet, basename="invoice")

# Override internal auth urlpatterns with organization viewset methods
urlpatterns = [] + router.urls

# Needed to add namespace in adtuo_api urls.py
app_name = "payments"
