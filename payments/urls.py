from django.urls import path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from payments.views import (BillingDataViewSet, CreditNoteViewSet,
                            InvoiceViewSet, PaymentIntentAPIView,
                            PaymentMethodViewSet)

router = DefaultRouter()
router.register("invoices", InvoiceViewSet, basename="invoices")
router.register("credit-notes", CreditNoteViewSet, basename="credit_notes")
router.register("payment-methods", PaymentMethodViewSet, basename="payment_methods")
router.register("billing-data", BillingDataViewSet, basename="billing_data")

# Override internal auth urlpatterns with user viewset methods
urlpatterns = [
    path("payment-intent/", PaymentIntentAPIView.as_view(), name="payment_intent"),
] + router.urls

# Needed to add namespace in backoffice_api urls.py
app_name = "payments"
