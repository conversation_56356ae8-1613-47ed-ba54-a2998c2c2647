import logging

import stripe
from django.core.management import BaseCommand

from organizations.models import Membership
from payments.clients.stripe import stripe_client

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Check stripe customers for organizations and create them missing"""

    def handle(self, *args, **options):

        for membership in (
            Membership.objects.filter(role__name="owner", organization__stripe_customer_id__isnull=True)
            .only("organization", "user")
            .distinct()
        ):
            try:
                organization = membership.organization
                user = membership.user

                # Add a stripe customer for this user
                customer = stripe_client.create_customer(user=user, organization=organization)
                organization.stripe_customer_id = customer.get("id")
                organization.save(update_fields=["stripe_customer_id"])
            except stripe.error.StripeError as error:
                logger.warning(f"Error creating customer in stripe in background command: {error.json_body}")
