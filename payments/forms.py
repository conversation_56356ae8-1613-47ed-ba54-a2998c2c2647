from django import forms


class DropRequiredFieldsInInvoiceForm(forms.ModelForm):
    """Form to skip requireness fields over invoice model"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.fields:
            self.fields[field].required = False


class SubscriptionForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["internal_status"].required = False
