import logging
import os

import firebase_admin
import pandas as pd
from firebase_admin import firestore

GOOGLE_CLOUD_JSON_ACCOUNT = os.getenv("GOOGLE_CLOUD_JSON_ACCOUNT")
GOOGLE_CLOUD_PROJECT_NAME = os.getenv("GOOGLE_CLOUD_PROJECT_NAME")
CODES_PATH = "/home/<USER>/Descargas/BRICODEPOT - CODES - Códigos 1 mes gratis Brico Depot - 5 dígitos (2).csv"
COLLECTION = "promotion_bricodepot_prod"

logger = logging.getLogger(__name__)


def init_firestore():
    cred = firebase_admin.credentials.Certificate(GOOGLE_CLOUD_JSON_ACCOUNT)
    firebase_admin.initialize_app(
        cred,
        {
            "projectId": GOOGLE_CLOUD_PROJECT_NAME,
        },
    )

    return firestore.client()


def prepare_data_in_batches(after=None):
    df = pd.read_csv(CODES_PATH, sep=",")
    df = df.iloc[after or 0 :]

    partial_list = []
    for index, code in df.iterrows():
        code = code["Code"]
        partial_list.append(code)
        if index % 500 == 0:
            yield partial_list
            partial_list = []
    yield partial_list


if __name__ == "__main__":
    # Init firestore
    db = init_firestore()

    for partial in prepare_data_in_batches(after=25000):
        logger.warning(f"{len(partial)}, {partial[-1]}")
        batch = db.batch()

        for code in partial:
            db_ref = db.collection(COLLECTION).document(code)
            batch.set(db_ref, {"code": code, "used_at": None})

        # Commit the batch
        batch.commit()
