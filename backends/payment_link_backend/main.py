"""Backend to create the payment link in firestore"""
import logging
import os
import traceback

import firebase_admin
from entrypoints.create import CreatePaymentLink
from entrypoints.update import UpdatePaymentLink
from firebase_admin import credentials, firestore
from tenacity import retry, stop_after_attempt, wait_random

# Environmental variables to set the config
PROJECT_ID = os.getenv("PROJECT_ID")
FIRESTORE_PAYMENT_LINK_COLLECTION = os.getenv("FIRESTORE_PAYMENT_LINK_COLLECTION")

logger = logging.getLogger(__name__)


# @see https://www.timvink.nl/google-cloud-functions/
# @see https://stackoverflow.com/questions/52129628/python-google-cloud-function-connection-reset-by-peer
@retry(stop=stop_after_attempt(3), wait=wait_random(min=1, max=4), reraise=True)
def get_cloud_service():
    # Use the application default credentials
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(
        cred,
        {
            "projectId": PROJECT_ID,
        },
    )
    return firestore.client()


try:
    # Initialize firestore
    db = get_cloud_service()
except Exception as err:
    logger.error(
        f"Error initializing firestore client :: {err} - {traceback.print_exc(chain=False)}"
    )
    db = None

CRUD_ENTRYPOINTS = {"POST": CreatePaymentLink, "PATCH": UpdatePaymentLink}


def entrypoint(request):
    if db is None:
        return "KO", 400

    data = request.get_json(silent=True)
    crud_entrypoint = CRUD_ENTRYPOINTS.get(request.method)

    if not crud_entrypoint:
        logger.warning(f"Unknown crud endpoint {crud_entrypoint}")
        return "KO", 400

    try:
        firestore_document = data.get("id")
        db_ref = db.collection(FIRESTORE_PAYMENT_LINK_COLLECTION).document(
            firestore_document
        )

        # Instantiate the crud entrypoint class and validate it before performing the action
        crud_entrypoint = crud_entrypoint()
        crud_entrypoint.run(payload=data, db_ref=db_ref)
        return "OK", 200
    except Exception as e:
        logger.error(
            f"Error parsing payment link information: {data}. Reason: {str(e)}"
        )

    return "KO", 400
