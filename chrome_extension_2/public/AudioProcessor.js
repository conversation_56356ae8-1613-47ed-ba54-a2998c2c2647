// Nodes of the Web Audio API process the audio stream in frames of the length
// of 128 samples. Cf https://www.w3.org/TR/webaudio/#rendering-loop
// (and they call it a quantum, plural quanta)
const quantumSize = 128;

// Number of quanta per packet we will send to the speech to text.
// Typical recommendation is to send a packet every 100ms
const quantaPerPacket = 24; // (1/32 kHz) * 128 * 24 = 96 ms

const std = (array) => {
  if (array.length === 0) {
    return 0;
  }
  const mean = array.reduce((acc, current) => {
    return acc + current / array.length;
  }, 0);
  const variance = array.reduce((acc, current) => {
    return acc + ((current - mean) * (current - mean)) / array.length;
  }, 0);
  return Math.sqrt(variance);
};

export class RawPCM16Processor extends AudioWorkletProcessor {
  constructor() {
    super();

    this.accumulatedQuantaCount = 0;
    this.paquet = new Int16Array(quantumSize * quantaPerPacket);
    this.preferredChannel = null;
  }

  process(inputs, _, __) {
    const offset = quantumSize * this.accumulatedQuantaCount;
    let stats = [];
    const channels = inputs[0];
    // At the beginning, decide which channel will be used for the recording.
    // This decision is not made until one of the channel, at least, is not 0 only.
    if (channels.length > 0) {
      if (this.preferredChannel == null) {
        const stds = channels.map((channel) => std(channel));
        const maxStd = stds.reduce((acc, current) => {
          if (current > acc) return current;
          return acc;
        }, -1);
        if (maxStd >= 1e-5) {
          this.preferredChannel = stds.indexOf(maxStd);
        }
      }

      const actualChannel =
        this.preferredChannel !== null &&
        this.preferredChannel < channels.length
          ? this.preferredChannel
          : 0;

      channels[actualChannel].forEach(
        (sample, idx) =>
          (this.paquet[offset + idx] = Math.floor(sample * 0x7fff)),
      );
      this.accumulatedQuantaCount = this.accumulatedQuantaCount + 1;
      if (this.accumulatedQuantaCount === quantaPerPacket) {
        this.port.postMessage(this.paquet);
        this.accumulatedQuantaCount = 0;
      }
    }
    return true;
  }
}

registerProcessor("pcm-16-worker", RawPCM16Processor);
