console.log('Hello from the content-script', chrome.runtime.onMessage);
import axios from "axios";
const encodeAudio = (buffers, settings) => {
    const sampleCount = buffers.reduce((memo, buffer) => {
        return memo + buffer.length;
    }, 0);

    const bytesPerSample = settings.sampleSize / 8;
    const bitsPerByte = 8;
    const dataLength = sampleCount * bytesPerSample;
    const sampleRate = 16000; //settings.sampleRate;

    const arrayBuffer = new ArrayBuffer(44 + dataLength);
    const dataView = new DataView(arrayBuffer);

    dataView.setUint8(0, "R".charCodeAt(0)); // <10>
    dataView.setUint8(1, "I".charCodeAt(0));
    dataView.setUint8(2, "F".charCodeAt(0));
    dataView.setUint8(3, "F".charCodeAt(0));
    dataView.setUint32(4, 36 + dataLength, true);
    dataView.setUint8(8, "W".charCodeAt(0));
    dataView.setUint8(9, "A".charCodeAt(0));
    dataView.setUint8(10, "V".charCodeAt(0));
    dataView.setUint8(11, "E".charCodeAt(0));
    dataView.setUint8(12, "f".charCodeAt(0));
    dataView.setUint8(13, "m".charCodeAt(0));
    dataView.setUint8(14, "t".charCodeAt(0));
    dataView.setUint8(15, " ".charCodeAt(0));
    dataView.setUint32(16, 16, true);
    dataView.setUint16(20, 1, true);
    dataView.setUint16(22, 1, true);
    dataView.setUint32(24, sampleRate, true);
    dataView.setUint32(28, sampleRate * 2, true);
    dataView.setUint16(32, bytesPerSample, true);
    dataView.setUint16(34, bitsPerByte * bytesPerSample, true);
    dataView.setUint8(36, "d".charCodeAt(0));
    dataView.setUint8(37, "a".charCodeAt(0));
    dataView.setUint8(38, "t".charCodeAt(0));
    dataView.setUint8(39, "a".charCodeAt(0));
    dataView.setUint32(40, dataLength, true);

    let index = 44;

    for (const buffer of buffers) {
        for (const value of buffer) {
            dataView.setInt16(index, value * 0x7fff, true);
            index += 2;
        }
    }

    return new Blob([dataView], { type: "audio/wav" });
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("CONTENT SCRIPT -  ", message, sender, sendResponse, message.msg)

    if (message.msg === "MICRO") {
        navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false
        }).then(async (stream) => {
            console.log("MICRO STREAM!!!!", stream);
            // const outputContext = new AudioContext();
            // const outputStream = outputContext.createMediaStreamSource(stream);
            // outputStream.connect(outputContext.destination);

            const downSampledContext = new AudioContext({
                sampleRate: 16000,
            });
            console.log("HEHEHEHE")
            await downSampledContext.audioWorklet.addModule(
                `chrome-extension://${chrome.runtime.id}/AudioProcessorWav.js`
            );
            const pcmWorker = new AudioWorkletNode(
                downSampledContext,
                "pcm-worker",
                { outputChannelCount: [2] }
            );
            const downSampledStream =
                downSampledContext.createMediaStreamSource(stream);
            downSampledStream.connect(pcmWorker);
            const [track] = stream.getAudioTracks();

            var value = 0;
            var blocks = 5;
            var chunks = [];
                

            pcmWorker.port.onmessage = async (message) => {
                value += 1;
                if (value >= 500) {
                    value = 0;
                    blocks -= 1;
                    const formData = new FormData();
                    const config = {
                        header: {
                            "Content-Type": "multipart/form-data",
                        },
                    };
                    formData.append(
                        "audio-blob",
                        encodeAudio(chunks, track.getSettings())
                    );
                    formData.append(
                        "user",
                        "microphone"
                    );
                    chunks = [];
                    console.log("FORM DATA ", formData);
                    await axios.post("http://localhost:5000/chunk", formData, config);
                } else {
                    chunks.push(message.data.buffer);
                }

                if (blocks <= 0) {
                    pcmWorker.port.onmessage = null;
                    pcmWorker.port.close();
                }
            };

            console.log("HOAL!!!!")

            pcmWorker.port.start();
        })

    }

    // navigator.mediaDevices.getUserMedia({
    //     audio: true,
    //     video: false
    // }).then(async (stream) => {
    //     console.log("CONTENT STRING ", stream);

    //     const outputContext = new AudioContext();
    //     const outputStream = outputContext.createMediaStreamSource(stream);
    //     outputStream.connect(outputContext.destination);

    //     console.log("MICRO!! ", stream);

    //     const downSampledContext = new AudioContext({
    //         sampleRate: 16000,
    //     });
    //     await downSampledContext.audioWorklet.addModule(
    //         `chrome-extension://${chrome.runtime.id}/AudioProcessor.js`
    //     );
    //     const pcmWorker = new AudioWorkletNode(
    //         downSampledContext,
    //         "pcm-16-worker",
    //         { outputChannelCount: [2] }
    //     );
    //     const downSampledStream =
    //         downSampledContext.createMediaStreamSource(stream);
    //     downSampledStream.connect(pcmWorker);
    //     pcmWorker.port.onmessage = async (message) => {
    //         const data = new Uint8Array(message.data.buffer);
    //         console.log("DATOS MICRO ", data);
    //     };
    //     pcmWorker.port.start();
    // });

    return true;
});