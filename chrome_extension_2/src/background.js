chrome.runtime.onMessage.addListener(async function (request, sender, sendResponse) {
  console.log('Hello from the background', request, sender, sendResponse);

  console.log("CHROME - MSG - 221123 ", chrome.tabCapture, "EY", chrome.runtime.getURL("listener.js"));
  // const formData = new FormData();
  // formData.append("audio-blob", new Uint8Array([1,2,3,4]));
  // const response = await fetch("http://localhost:5000/chunk", {
  //   method: "POST",
  //   // headers: {
  //   //   "Content-Type": "multipart/form-data",
  //   // },
  //   body: new Blob([new Uint8Array([1,2,3,4])]),
  // });
  // console.log("RESPONSE ", response)
  // https://chat.openai.com/share/1ea78bd5-a539-4db8-8f5a-d731cc79e43b

  // chrome.tabCapture.capture({
  //   audio: true, video: false,
  // }, (stream) => {
  //   const outputContext = new AudioContext();
  //   const outputStream = outputContext.createMediaStreamSource(stream);
  //   outputStream.connect(outputContext.destination);


  //   console.log("STREAM NUEVO ", stream);

  //   const downSampledContext = new AudioContext({
  //     sampleRate: 16000,
  //   });
  //   downSampledContext.audioWorklet.addModule(
  //     `chrome-extension://${chrome.runtime.id}/AudioProcessor.js`
  //   );
  //   const pcmWorker = new AudioWorkletNode(
  //     downSampledContext,
  //     "pcm-16-worker",
  //     { outputChannelCount: [2] }
  //   );
  //   const downSampledStream =
  //     downSampledContext.createMediaStreamSource(stream);
  //   downSampledStream.connect(pcmWorker);
  //   pcmWorker.port.onmessage = async (message) => {
  //     const data = new Uint8Array(message.data.buffer);
  //     console.log("DATA")
  //   };
  //   pcmWorker.port.start();
  // })

  // browser.tabs.executeScript({
  //   file: 'content-script.js',
  // });
})
