swagger: "2.0"
info:
  title: api-gateway-prod
  description: "Public API Gateway to handle webhook notifications from third-party systems"
  version: "1.0.0"
schemes:
  - "https"
produces:
  - "application/json"
x-google-management:
  metrics:
    - name: "moreapp_quota"
      displayName: "Number of post requests to moreapp endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "slack_quota"
      displayName: "Number of post requests to slack endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "hubspot_quota"
      displayName: "Number of post requests to hubspot endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "chargebee_quota"
      displayName: "Number of post requests to chargebee endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "chargebee_pi_quota"
      displayName: "Number of post requests to chargebee payment intent endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "chargebee_pay_quota"
      displayName: "Number of post requests to chargebee pay endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "chargebee_release_transaction_quota"
      displayName: "Number of post requests to chargebee release transaction endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "chargebee_payment_link_quota"
      displayName: "Number of get requests to chargebe payment link generation endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "hubspot_funnel_quota"
      displayName: "Number of post requests to hubspot funnel endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "chargebee_customer_quota"
      displayName: "Number of post requests to chargebee customer endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "moreapp_inventory_quota"
      displayName: "Number of post requests to moreapp endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "service_quota"
      displayName: "Number of post requests to riders endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "service_id_quota"
      displayName: "Number of post requests to riders endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "service_accept_quota"
      displayName: "Number of post requests to accept endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "service_cancel_quota"
      displayName: "Number of post requests to cancel endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "service_release_quota"
      displayName: "Number of post requests to release endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "service_discard_tracking_quota"
      displayName: "Number of post requests to release endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "riders_warehouses_quota"
      displayName: "Number of post requests to warehouses endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "riders_patch_quota"
      displayName: "Number of post requests to riders device endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "device_quota"
      displayName: "Number of post requests to device endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "inventory_quota"
      displayName: "Number of post requests to inventory endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "auth_login_quota"
      displayName: "Number of post requests to login endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "password_quota"
      displayName: "Number of post requests to login endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "task_quota"
      displayName: "Number of post requests to riders endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "tracking_quota"
      displayName: "Number of post requests to riders endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "placement_quota"
      displayName: "Number of post requests to placements endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "placement_relocation_quota"
      displayName: "Number of post requests to placements endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "slack_payment_reminder_quota"
      displayName: "Number of post requests to slack payment reminder endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "password_reset_quota"
      displayName: "Number of post requests to password reset endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "password_reset_confirm_quota"
      displayName: "Number of post requests to password reset confirm endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "devices_quota"
      displayName: "Number of post requests to devices endpoint"
      valueType: INT64
      metricKind: DELTA
    - name: "task_id_quota"
      displayName: "Number of get requests to tasks endpoint"
      valueType: INT64
      metricKind: DELTA
  quota:
    limits:
      - name: "moreapp"
        metric: "moreapp_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "slack"
        metric: "slack_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 25
      - name: "hubspot"
        metric: "hubspot_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "chargebee-pi"
        metric: "chargebee_pi_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "chargebee-pay"
        metric: "chargebee_pay_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "chargebee-release-transaction"
        metric: "chargebee_release_transaction_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "chargebee"
        metric: "chargebee_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "chargebee-payment-link"
        metric: "chargebee_payment_link_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "hubspot-funnel"
        metric: "hubspot_funnel_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "chargebee-customer"
        metric: "chargebee_customer_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "moreapp-inventory"
        metric: "moreapp_inventory_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 200
      - name: "service-quota"
        metric: "service_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "service-id-quota"
        metric: "service_id_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "service-accept-quota"
        metric: "service_accept_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "service-cancel-quota"
        metric: "service_cancel_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "service-release-quota"
        metric: "service_release_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "service-discard-tracking-quota"
        metric: "service_discard_tracking_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "riders-warehouses-quota"
        metric: "riders_warehouses_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "riders-patch-quota"
        metric: "riders_patch_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "inventory-quota"
        metric: "inventory_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "device-quota"
        metric: "device_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "login-quota"
        metric: "auth_login_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "password-quota"
        metric: "password_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "task-quota"
        metric: "task_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "tracking-quota"
        metric: "tracking_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "placement-quota"
        metric: "placement_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "placement-relocation-quota"
        metric: "placement_relocation_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "slack-payment-reminder"
        metric: "slack_payment_reminder_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "password-reset-quota"
        metric: "password_reset_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "password-reset-confirm-quota"
        metric: "password_reset_confirm_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "devices-quota"
        metric: "devices_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
      - name: "task-id-quota"
        metric: "task_id_quota"
        unit: "1/min/{project}"
        values:
          STANDARD: 80
host: gateway-prod-25opf9o4cwrn3.apigateway.box2box-cloud.cloud.goog
x-google-endpoints:
  - name: gateway-prod-25opf9o4cwrn3.apigateway.box2box-cloud.cloud.goog
    allowCors: True

paths:
  /moreapp:
    post:
      summary: "Get notifications from moreapp and send data to backend"
      operationId: "moreapp"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/moreapp-prod
      x-google-quota:
        metricCosts:
          "moreapp_quota": 1
      security:
        - api_key: []
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /slack-command:
    post:
      summary: "Send slack button responses from DLQ tasks again to main pubsub topic"
      operationId: "slack"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/slack-command-prod
      x-google-quota:
        metricCosts:
          "slack_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /hubspot:
    post:
      summary: "Get notifications from hubspot and send data to backend"
      operationId: "hubspot"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/hubspot-prod
      x-google-quota:
        metricCosts:
          "hubspot_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /chargebee:
    post:
      summary: "Get notifications from chargebee and send data to backend"
      operationId: "stripe"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/chargebee-prod
      x-google-quota:
        metricCosts:
          "chargebee_quota": 1
      security:
        - api_key: [ ]
      parameters:
        - name: hook_key
          in: query
          description: Integrity key
          required: true
          type: string
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /chargebee-payment-intent:
    post:
      summary: "Authorize payment intent in chargebee"
      operationId: "chargebee-payment-intent"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/chargebee-payment-intent-prod
      x-google-quota:
        metricCosts:
          "chargebee_pi_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /chargebee-pay:
    post:
      summary: "Perform the payment in chargebee"
      operationId: "chargebee-pay"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/chargebee-pay-prod
      x-google-quota:
        metricCosts:
          "chargebee_pay_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /chargebee-release-transaction:
    patch:
      summary: "Release the firebase transaction on payment failures"
      operationId: "chargebee-release-transaction"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/chargebee-release-transaction-prod
      x-google-quota:
        metricCosts:
          "chargebee_release_transaction_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /hubspot-form:
    post:
      summary: "Create contacts and deals in hubspot using public-web forms"
      operationId: "hubspot-form"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/hubspot_public_forms_prod
      security:
        - google_id_token: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /chargebee-payment-link:
    get:
      summary: "Get payment link metadata form remote"
      operationId: "chargebee-payment-link"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/chargebee-payment-link-metadata-prod
      x-google-quota:
        metricCosts:
          "chargebee_payment_link_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /hubspot-funnel:
    post:
      summary: "Update deal information"
      operationId: "hubspot-funnel"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/hubspot_funnel_prod
      x-google-quota:
        metricCosts:
          "hubspot_funnel_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /chargebee-customer:
    post:
      summary: "Create or update a customer in Chargebee"
      operationId: "chargebee-customer"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/chargebee-customer-prod
      x-google-quota:
        metricCosts:
          "chargebee_customer_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "KO"
          schema:
            type: string
        403:
          description: "Unauthorized"
  /moreapp-inventory:
    post:
      summary: "Get notifications from moreapp and send data to backend to parse the inventory data"
      operationId: "moreapp-inventory"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/moreapp-inventory-prod
      x-google-quota:
        metricCosts:
          "moreapp_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /services:
    post:
      summary: "Get notifications from riders app and send data to backend"
      operationId: "services_post"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/services-prod
      x-google-quota:
        metricCosts:
          "service_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
    get:
      summary: "Get notifications from riders app and send data to backend"
      operationId: "services_get"
      x-google-backend:
        address: https://api.box2box.es/services
      x-google-quota:
        metricCosts:
          "service_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /services/{id}:
    get:
      summary: "Get service details"
      operationId: "services_id_get"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "service_id_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /services/{id}/accept:
    post:
      summary: "Accept the service"
      operationId: "services_id_accept"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "service_accept_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /services/{id}/cancel:
    post:
      summary: "Cancel the service"
      operationId: "services_id_cancel"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "service_cancel_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /services/{id}/release:
    post:
      summary: "Release the service"
      operationId: "services_id_release"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "service_release_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /services/{id}/discard_tracking:
    post:
      summary: "Release the service"
      operationId: "services_id_discard_tracking"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "service_discard_tracking_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /riders/{id}/warehouses:
    get:
      summary: "Get rider allowed warehouses"
      operationId: "riders_id_warehouses_get"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "riders_warehouses_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /riders/{id}:
    patch:
      summary: "Get rider device metadata"
      operationId: "riders_id_patch"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "riders_patch_quota": 1
      parameters:
        - name: id
          in: path
          description: Rider identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /inventory/{id}:
    get:
      summary: "Get item from inventory"
      operationId: "inventory_id_get"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "inventory_quota": 1
      parameters:
        - name: id
          in: path
          description: Service identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /auth/login:
    post:
      summary: "Perform login against main api"
      operationId: "auth_login"
      x-google-backend:
        address: https://api.box2box.es/auth/login
        disable_auth: true
      x-google-quota:
        metricCosts:
          "auth_login_quota": 1
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /auth/password/change:
    post:
      summary: "Perform password change against main api"
      operationId: "auth_password_change"
      x-google-backend:
        address: https://api.box2box.es/auth/password/change
      x-google-quota:
        metricCosts:
          "password_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /auth/refresh:
    post:
      summary: "Perform token refresh against main api"
      operationId: "auth_refresh"
      x-google-backend:
        address: https://api.box2box.es/auth/refresh
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /tasks:
    post:
      summary: "Get notifications from riders app and send data to backend"
      operationId: "tasks_post"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/tasks-prod
      x-google-quota:
        metricCosts:
          "task_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
    get:
      summary: "Get notifications from riders app and send data to backend"
      operationId: "tasks_get"
      x-google-backend:
        address: https://api.box2box.es/tasks
      x-google-quota:
        metricCosts:
          "task_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /tracking:
    post:
      summary: "Get notifications from riders app and send data to backend"
      operationId: "tracking_post"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/tracking-prod
      x-google-quota:
        metricCosts:
          "tracking_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /placements:
    get:
      summary: "Get inventory movements for a given contract"
      operationId: "placements_id_get"
      x-google-backend:
        address: https://api.box2box.es/placements
      x-google-quota:
        metricCosts:
          "placement_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /placements/{id}/relocation:
    post:
      summary: "Relocate a given inventory"
      operationId: "placements_id_relocate"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "placement_relocation_quota": 1
      parameters:
        - name: id
          in: path
          description: Warehouse movement identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
        429:
          description: "Too many requests"
  /slack-payment-reminder:
    post:
      summary: "Get notifications from hubspot thru slack and send data to backend to send payment reminder emails"
      operationId: "slack-payment-reminder"
      x-google-backend:
        address: https://europe-west1-box2box-cloud.cloudfunctions.net/slack-payment-reminder-prod
      x-google-quota:
        metricCosts:
          "slack_payment_reminder_quota": 1
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /auth/riders/password/reset:
    post:
      summary: "Reset password"
      operationId: "auth_password_reset"
      x-google-backend:
        address: https://api.box2box.es/auth/riders/password/reset
        disable_auth: true
      x-google-quota:
        metricCosts:
          "password_reset_quota": 1
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /auth/riders/password/reset/confirm:
    post:
      summary: "Reset password confirm"
      operationId: "auth_password_reset_confirm"
      x-google-backend:
        address: https://api.box2box.es/auth/riders/password/reset/confirm
        disable_auth: true
      x-google-quota:
        metricCosts:
          "password_reset_confirm_quota": 1
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
  /devices:
    post:
      summary: "Store a new token"
      operationId: "devices_post"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "devices_quota": 1
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
        429:
          description: "Too many requests"
  /tasks/{id}:
    get:
      summary: "Get tasks details"
      operationId: "tasks_id_get"
      x-google-backend:
        address: https://api.box2box.es
        path_translation: APPEND_PATH_TO_ADDRESS
      x-google-quota:
        metricCosts:
          "task_id_quota": 1
      parameters:
        - name: id
          in: path
          description: Task identifier
          required: true
          type: string
      security:
        - api_key: [ ]
      responses:
        200:
          description: "OK"
          schema:
            type: string
        400:
          description: "Wrong request"
        403:
          description: "Unauthorized"
securityDefinitions:
  # This section configures basic authentication with an API key.
  api_key:
    type: "apiKey"
    name: "key"
    in: "query"

  google_id_token:
    authorizationUrl: ""
    flow: "implicit"
    type: "oauth2"
    x-google-issuer: "https://accounts.google.com"
    x-google-jwks_uri: "https://www.googleapis.com/oauth2/v3/certs"
    x-google-audiences: "https://api-gateway-production-d5ppcvke.ew.gateway.dev/hubspot-form"
