workers = 2  # (2 * multiprocessing.cpu_count()) + 1
worker_class = "gevent_wsgi"
# worker_class = "gevent"
accesslog = "-"
worker_connections = 1024
timeout = 300
bind = "0.0.0.0:8000"
keepalive = 5
worker_tmp_dir = "/dev/shm"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(l)s "%(T)s"'
max_requests = 1024


# def profiler_enable(worker, req):
#     worker.profile = cProfile.Profile()
#     worker.profile.enable()
#     worker.log.info(f"PROFILING {worker.pid}: {req.uri}")
#
#
# def profiler_summary(worker, req):
#     s = StringIO()
#     worker.profile.disable()
#     ps = pstats.Stats(worker.profile, stream=s).sort_stats('time', 'cumulative')
#     ps.print_stats(100)
#
#     worker.log.error(f"\n[{worker.pid}] [INFO] [{req.method}] URI {req.uri}")
#     worker.log.error(f"[{worker.pid}] [INFO] {s.getvalue()}")
#
#
# def pre_request(worker, req):
#     worker.start_time = time.time()
#     profiler_enable(worker, req)
#
#
# def post_request(worker, req, *args):
#     total_time = time.time() - worker.start_time
#     worker.log.info(f"\n[{worker.pid}] [INFO] [{req.method}] Load Time: {total_time}s\n")
#     profiler_summary(worker, req)


def post_fork(server, worker):
    # BugFix: https://github.com/googleapis/google-cloud-python/issues/9192
    # grpcio with gevent doesnt work unless init grpc with gevent explicitly
    import grpc._cython.cygrpc
    from gevent import monkey
    from psycogreen.gevent import patch_psycopg

    monkey.patch_all()
    patch_psycopg()
    grpc._cython.cygrpc.init_grpc_gevent()

    worker.log.info("Green green green!!")
