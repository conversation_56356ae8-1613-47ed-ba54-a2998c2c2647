FROM python:3.8-slim-buster

ARG DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get dist-upgrade -y  && apt-get install -y --no-install-recommends \
    curl \
    wget \
    git \
    ssh \
    net-tools \
    nano \
#    wkhtmltopdf \
#    gcc \
#    gcc-4.8 \
#    libc-dev \
    vim \
    xfonts-75dpi \
    xfonts-base \
    fontconfig \
    libjpeg62-turbo \
    libx11-6 \
    libxcb1 \
    libxext6 \
    libxrender1 \
    fonts-liberation \
    fonts-liberation2 \
    fonts-urw-base35

RUN wget -q -O /tmp/wkhtmltox_0.12.6-1.buster_amd64.deb https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.buster_amd64.deb \
    && dpkg -i /tmp/wkhtmltox_0.12.6-1.buster_amd64.deb \
    && rm /tmp/wkhtmltox_0.12.6-1.buster_amd64.deb
RUN pip3 install -U pip setuptools wheel pipenv
CMD ["python3"]


###### To install uwsgi, uncomment L12-15
