version: '3.8'

services:
  warehouse:
    image: eu.gcr.io/box2box-cloud/ms-warehouse:${VERSION}  # como se va a llamar en eu.gcr.io
    build:
      context: ../
      dockerfile: ./deployment/Dockerfile
    command: >
      sh -c "export FASTAPI_ENV='local' && gunicorn --chdir ./app/ -w 2 -k uvicorn.workers.UvicornWorker --preload --bind 0.0.0.0:8000 --access-logfile=- main:app"
    ports:
      - "8003:8000"
