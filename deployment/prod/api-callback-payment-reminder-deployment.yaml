---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-reminder-callback
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: payment-reminder-callback
  template:
    metadata:
      labels:
        app: payment-reminder-callback
    spec:
      containers:
      - name: payment-reminder-callback
        image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
        lifecycle:
          preStop:
            exec:
              command: [ "sleep", "90" ]
        env:
          - name: PYTHONUNBUFFERED
            value: "1"
          - name: PREFIX
            value: ""
          - name: VERBOSITY_EMAIL
            value: ""
          - name: INTRANET_DEPLOYMENT
            value: ""
        command: ["./deployment/prod/start_callbacks.sh", "payment_reminder_subscriber"]
        imagePullPolicy: Always
        livenessProbe:
          exec:
            command: ["cat", "/tmp/alive"]
          timeoutSeconds: 20
          failureThreshold: 15
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          exec:
            command: [ "cat", "/tmp/alive" ]
          timeoutSeconds: 20
          initialDelaySeconds: 10
          successThreshold: 1
          periodSeconds: 10
      - image: gcr.io/cloudsql-docker/gce-proxy:1.34.0
        name: cloudsql-proxy
        lifecycle:
          preStop:
            exec:
              command: [ "sleep", "90" ]
        command: ["/cloud_sql_proxy", "--dir=/cloudsql", "-term_timeout=90s",
                  "-instances=box2box-cloud:europe-west1:api-prod=tcp:5432",
                  "-credential_file=/secrets/cloudsql/credentials.json"]
        volumeMounts:
          - name: cloudsql-oauth-credentials
            mountPath: /secrets/cloudsql
            readOnly: true
          - name: ssl-certs
            mountPath: /etc/ssl/certs
          - name: cloudsql
            mountPath: /cloudsql
      # [START volumes]
      volumes:
        - name: cloudsql-oauth-credentials
          secret:
            secretName: cloudsql-oauth-credentials
        - name: ssl-certs
          hostPath:
            path: /etc/ssl/certs
        - name: cloudsql
          emptyDir:
      # [END volumes]
      terminationGracePeriodSeconds: 120
