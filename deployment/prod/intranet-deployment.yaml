---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intranet
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: intranet
  template:
    metadata:
      labels:
        app: intranet
    spec:
      containers:
      - name: intranet
        image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
        lifecycle:
          preStop:
            exec:
              command: ["sleep", "90"]
        env:
          - name: PREFIX
            value: ""
          - name: VERBOSITY_EMAIL
            value: ""
          - name: PYTHONUNBUFFERED
            value: "1"
          - name: INTRANET_DEPLOYMENT
            value: "True"
          - name: PORT
            value: "8002"
          - name: LIMIT
            value: "0"
          - name: WORKER_CLASS
            value: "gevent"
        command: ["./deployment/prod/start.sh"]
        imagePullPolicy: Always
        ports:
          - containerPort: 8002
            protocol: TCP
        livenessProbe:
          httpGet:
            path: /ihealthz/
            port: 8002
            httpHeaders:
              - name: Custom-Header
                value: Awesome
          timeoutSeconds: 10
          failureThreshold: 15
          initialDelaySeconds: 40
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ihealth/
            port: 8002
          initialDelaySeconds: 40
          periodSeconds: 5
          timeoutSeconds: 10
          successThreshold: 1
      - image: gcr.io/cloudsql-docker/gce-proxy:1.28.0
        name: cloudsql-proxy
        lifecycle:
          preStop:
            exec:
              command: [ "sleep", "90" ]
        command: ["/cloud_sql_proxy", "--dir=/cloudsql", "-term_timeout=90s",
                  "-instances=box2box-cloud:europe-west1:api-prod=tcp:5432",
                  "-credential_file=/secrets/cloudsql/credentials.json"]
        volumeMounts:
          - name: cloudsql-oauth-credentials
            mountPath: /secrets/cloudsql
            readOnly: true
          - name: ssl-certs
            mountPath: /etc/ssl/certs
          - name: cloudsql
            mountPath: /cloudsql
      # [START volumes]
      volumes:
        - name: cloudsql-oauth-credentials
          secret:
            secretName: cloudsql-oauth-credentials
        - name: ssl-certs
          hostPath:
            path: /etc/ssl/certs
        - name: cloudsql
          emptyDir:
      # [END volumes]
      terminationGracePeriodSeconds: 120
