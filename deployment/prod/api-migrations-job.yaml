---
apiVersion: batch/v1
kind: Job
metadata:
  name: django-migrations
spec:
  template:
    spec:
      containers:
      - name: api-django
        image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
        command: ["sh", "-c", "export $$(grep -v '^#' .env | xargs); export PREFIX=''; export VERBOSITY_EMAIL=''; export INTRANET_DEPLOYMENT=''; export DATABASE_URL=postgres://$${POSTGRES_USER}:$${POSTGRES_PASSWORD}@10.41.176.3:5432/backend; python manage.py migrate"]
      restartPolicy: Never
  backoffLimit: 3
