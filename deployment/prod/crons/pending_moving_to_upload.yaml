---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: pending-moving-to-upload
spec:
  schedule: "55 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: pending-moving-to-upload
            image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
            command: ["./deployment/prod/start_command.sh", "pending_moving_to_upload"]
          restartPolicy: OnFailure
