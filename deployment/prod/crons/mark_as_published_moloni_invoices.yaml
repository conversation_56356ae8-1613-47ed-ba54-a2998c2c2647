---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: mark-as-published-moloni-invoices
spec:
  schedule: "59 19 4 * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: mark-as-published-moloni-invoices
            image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
            command: ["./deployment/prod/start_command.sh", "mark_as_published_moloni_invoices"]
          restartPolicy: OnFailure
