---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: pending-email-to-user-on-box-signed
spec:
  schedule: "5 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: pending-email-to-user-on-box-signed
            image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
            command: ["./deployment/prod/start_command.sh", "pending_email_to_user_on_box_signed"]
          restartPolicy: OnFailure
