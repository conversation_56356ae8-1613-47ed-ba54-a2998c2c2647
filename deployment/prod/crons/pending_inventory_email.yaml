---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: pending-inventory-email
spec:
  schedule: "20 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: pending-inventory-email
            image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
            command: ["./deployment/prod/start_command.sh", "pending_inventory_email"]
          restartPolicy: OnFailure
