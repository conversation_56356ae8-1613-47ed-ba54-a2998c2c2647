---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: load-portugal-invoices-chargebee
spec:
  schedule: "0 5 1 * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: load-portugal-invoices-chargebee
            image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
            command: ["./deployment/prod/start_command.sh", "load_portugal_invoices_from_chargebee"]
          restartPolicy: OnFailure
