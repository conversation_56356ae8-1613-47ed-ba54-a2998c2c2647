---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: evict-stale-public-form-submissions
spec:
  schedule: "0 7 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: evict-stale-public-form-submissions
            image: eu.gcr.io/box2box-cloud/api-prod:${VERSION}
            command: ["./deployment/prod/start_command.sh", "evict_stale_public_form_submissions"]
          restartPolicy: OnFailure
