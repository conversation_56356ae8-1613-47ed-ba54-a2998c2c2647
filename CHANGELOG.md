## [v1.0.0] - 14-04-2021
### Sa<PERSON><PERSON> has born!
- First version for the new product

## [v1.1.0] - 15-04-2021
### allowing async processing for production environment
- settings: allowing cloud_tasks in production
- settings: adding testapp.adtuo.com in cors allowed origins

## [v1.1.1] - 15-04-2021
### improvements and minor fixes
- settings cleaning unused settings and adding skip_domain for historic insights
- users: skip @adtuo.com users from extracting historic insights from hiperion
- several: passing date_end to hiperion metrics endpoint to fetch reports data and removing trailing path in request
- core: passing organization_id in create_message method for social message
- campaigns: renaming tree variables and updating redoc
- users: Changes in the user avatar, now it is a Text without limit.

## [v1.1.2] - 16-04-2021
### minor fixes
- organizations: retrieving alias and email in organization GET
- core: fixing hiperion workers client signatures and getting stop time from campaign metadata call
- campaigns: restricting window of dates when fetching data from facebook to created_at until stop_time
- pipfile: updating dependencies
- settings: adding django default_auto_field for 3.2

## [v1.1.3] - 19-04-2021
### adding commands to keep updated campaigns
- campaigns: adding commands to keep up-to-date campaign structure
- deployment_files: installing dependency on python-base image

## [v1.2.0] - 21-04-2021
### adding community support
- several: fixing environments for FacebookClient
- users: removing welcome email
- core: fixing facebook client environment instance
- community: adding endpoints and related business logic
- core: updating schema generator for redoc and refactoring comments endpoints

## [v1.2.1] - 21-04-2021
### changing temporarily tasks url
- settings: changing temporarily tasks url

## [v1.2.2] - 21-04-2021
### fixing bugs
- campaigns: fixing receiver business logic to fetch individual parts of campaign
- campaigns: fixing calls to metadatas on imported campaign
- core: fixing call in activities endpoint
- core: fixing url for read_comments function in social client
- settings: fixing target_audience and social url
- settings: adding target audience to generate google token for social
- core: adding organization_id for production client

## [v1.2.3] - 21-04-2021
### fixing bugs
- core: fixing http_method on async tasks and missing parameter in helper function

## [v1.2.4] - 21-04-2021
### adding right token for async tasks
- core: adding right token for cloud tasks

## [v1.2.5] - 22-04-2021
### fetching adset and ad metadata when they are created to avoid race conditions
- campaigns: fetching adset/ad metadata when they are imported

## [v1.2.6] - 22-04-2021
### fixing business logic to fetch facebook data on user signup
- several: fixing business logic when user is registred to extract data from their user token

## [v1.2.7] - 22-04-2021
### fixing bugs
- several: fixing webhook to fetch every data from user

## [v1.2.8] - 22-04-2021
### restoring user_id
- users: restoring user_id on fetch every data from facebook call

## [v1.3.0] - 26-04-2021
### refactoring public endpoints
- settings: updating webapp url in staging environment
- users: adding avatar image to admin
- core: fixing redoc
- community: fixing redoc for public endpoints
- core: adding total_comments property to public campaign model
- core: adding hiperion endpoint to fetch total comments in concrete view
- community: disabling security scheme for redoc in public endpoints
- core: updating response in view
- community: retrieving user metadata on comments serialized response
- core: fixing public flag pass to serializer
- core: fixing post comments to allow publish a comment without organization_id only if is public
- community: passing user id on concrete response
- core: dropping unneeded parameter for general metrics in community endpoint
- several: updating querysets
- core: fixing validation in private comments serializer
- campaigns: retrieving the number of comments instead of if has comments in overview table
- settings: updating cors
- settings: adding preapi.adtuo.com to allowed_hosts
- core: passing is_public on public campaign GET endpoint
- core: make organization_id as no required for public comments
- settings: adding community urls to CORS allowed origins
- several: adding public prefix path for community urls
- community: fixing 500 error on pub-comments
- user: now an entry is added in EmailAdress after creating the user when invited.

## [v1.4.0] - 26-04-2021
### adding customerio mail provider
- settings: adding new pubsub_email_topic
- users: updating change_email business logic and set right template on email validation during onboarding process
- settings: fixing pubsub_email_topic for staging
- pipfile: fixing lock for adtuo-django-email
- users: a function is included in manager that filters users by email to deliver the required format.
- users: fixing migrations
- settings: fixing debounce_key for only prod environment
- users: Changes to use the debounce validator email and commented on the shipments that did not have the new templates.
- users: Changes in email validation now use debounce.

## [v1.4.1] - 26-04-2021
### fixing bugs
- core: adding token to sync social client
- core: fixing weird 500 error when 204 NO CONTENT is returned from public comments
- github: adding debounce key for production environment

## [v1.4.2] - 26-04-2021
### fixing microservice url
- settings: updating social microservice url

## [v1.4.3] - 27-04-2021
### fixing validation email
- users: fixing debounce validation email
- core: fixing social endpoint and url

## [v1.4.4] - 27-04-2021
### fixing email validation business logic
- users: fixing email validator business logic

## [v1.5.0] - 27-04-2021
### fixing errors
- users: passing token from social login
- settings: adding debounce api key in staging environment
- users: validate scope for token permissions before linking account
- core: adding get_permissions method to facebookclient
- users: adding email validator in test environment
- templates: fixing objective display and business name in report-title
- business: adding business to ctx
- Update pdf report translations

## [v1.6.0] - 28-04-2021
### fixing bugs in userinvitations and adding no sensitiveness filtering for aliases
- organization: adding parent name to userinvitation email
- organizations: set invited_by always and change parent name in userinvitation email
- users: adding alias to onboarding_setup property
- core: adding extra scope (response) to sentry message
- users: fixing send email for userinvitations
- core: adding http errors middleware and send them to sentry
- organization: changes are applied to the serializer that sends the invitations for the test environment.
- organization: override model admin form to avoid integrityerrors for unique aliases
- users: override model admin form to avoid integrityerrors for unique aliases
- users: updating lookups in public viewset
- users: adding functional index on alias field
- organizations: adding functional index on alias field
- organizations: serializer fix and redoc has been revised to adjust it to the new operation.
- organizations: refactor flow invited user.
- organizations: a function is included that filters users by email to deliver the required format and rename field in user_invitation.

## [v1.6.1] - 28-04-2021
### fixing bug with social client headers
- core: remove unneeded gcloud headers on social call

## [v1.6.2] - 30-04-2021
### fixing deadlock
- deployment_files: adding deployment files for another instance of api marked as task handler
- urls: adding tasks url endpoints option
- deployment_files: updating gunicorns
- core: closing old connections after threadpool has been completed on syncing campaign
- core: refactoring sync imported campaign business logic
- core: adding threadpoolexecutor helper class wrapping gevent group
- several: retrieve in list campaigns imported flag
- several: updating email ctx keys

## [v1.6.3] - 04-05-2021
### improving campaign import business logic
- campaigns: generate draft on published campaigns only if it has body changes
- core: fixing business logic for videos
- campaigns: adding object_type to facebook ad model with related migration
- core: updating parser business logic to fetch video from outside of object_story_spec context
- core: adding videomixin to facebookclient
- deployment_files: fixing relative paths on docker files

## [v1.6.4] - 04-05-2021
### updating settings
- several: updating settings and action

## [v1.6.5] - 11-05-2021
### minor fixings
- users: commenting code to fetch historic data from user token
- settings: updating CORS in prod environment

## [v1.6.6] - 12-05-2021
### fixing CORS
- settings: adding new CORS origin

## [v1.7.0] - 25-05-2021
### release 2
- settings: updating recommendations url for production environment
- several: dropping unused setting and restoring hiperion task url/queue for metrics
- webhooks: updating metric status and sending notification when importing data has finished
- core: updating notification_context business logic to allow empty users
- campaigns: updating importing status value to IN_PROCESS
- core: fixing serializer name from comments to notifications
- core: passing has_unread_comments field for notification pagination
- several: minor fixings in business logic
- core: The name of the campaign and the user's name are added to travel to websocket.
- core: the build_notification_context function is parameterized to receive the who and user_id separately.
- core: fixing create comment view business logic
- core: simplifying serializers
- core: passing kwargs to social methods in order to set per request custom parameters
- core: adding notification context helper method in notification client
- campaigns: adding custom manager to facebook models
- campaigns: adding custom manager to encapsulate notification context generation
- core: the formatting is changed when creating a comment to organize the information when creating a notification.
- deployment: tunning gunicorn config adding "harakiri" config to workers
- core: adding full sync business logic when importing campaign parts
- core: The business_id is added to the comments context for notifications.
- core: Fields required to create notifications are added to comments.
- campaigns: adding redoc serializers
- core: fixing read_recommendations business logic when no dates are provided
- core: fixing serializers for comments
- campaigns: surrounding in try/except blocks ml calls to remote service
- campaign: is_published condition added for recommendations.
- campaign: Removed the migration that added an unwanted field for FB campaigns.
- core/business/campaign: The traffic_light_status field is added and is added when obtaining the campaigns from business and when obtaining the campaign.
- core: sanitize fields for custom field and adding callable fields support
- core: minor changes in the serializer.
- core: changes in the limit to obtain the notifications, now it is an interger.
- core: changes in the serializer for notifications.
- core: removing 400 when no content in delete comment
- core: fixing read_comments endpoint for chart comments
- business: fixing date format for campaign filtering
- core/campaign: The endpoint to get the traffic light status has been added to the recommendation client, the new traffic_light_status field has been added to the recommendation flow and to the fetching of all facebook campaigns.
- business: Change in the time format for facebook filtering.
- core: new field added to the serialization of the notification response.
- core: the get of notifications is included in the get of notifications limit.
- core: the call to obtain the traffic light is added to the client.
- core: adding default help text on custom field for redoc
- core: updating notificationcontext serializer
- core: adding custom field to serializer model attributes from isolated keys
- users: reducing page picture cache from 6 to 1 month
- business/core: a filter is added for the FB client.
- users: revoke tokens when modifying in_beta_program/is_staff from admin ui
- users: changing is_superuser for is_staff in token payload
- users: overriding token serializer to add custom fields in the payload
- users: adding in_beta_program attribute and related migration
- settings: overriding tokenpair serializer to add custom fields in emitted tokens
- settings: updating notifications prod url to internal domain
- core: simplifying validation business logic
- settings: adding trailing bar to base url and changing prod url for internal domain
- core: cleaning duplicated code
- several: split aggregated calls in smaller tasks and set another queue for panel metrics
- settings: set the right environments for new hiperion services
- core: move the call to the client class to init.
- core: Add the endpoint to get the recommendations of the adtuo-ai microservices.
- deployment_files: adding network name to tasks deployment to get access to the right services
- organizations: migrations merge.
- campaigns: fixing field validation in adset
- campaigns: adding more types of locations in serializer validation and fixing minor bugs
- several: fixing validations and changing names
- several: fixing redoc and adding validation for landing_page_views optimization goal
- campaigns: renaming overriden attribute in facebookcampaign to avoid collisions
- campaigns: allowing is_daily_budget in POST campaigns
- core: refactoring notifications view
- core/organizations: A field has been added in membership with the date on which the entry is created, also the flow of notifications has been adapted to take the date and filter the notifications.
- core: final adjustments to the task requirements, changes in the flow of notifications and client, POST and DELETE endpoints are removed.
- campaigns: fixing redoc endpoint names
- campaigns: adding bulkdeletemodel mixin to adset/ad viewsets
- core: adding mixins for bulk deletions
- campaigns: adding serializers to allow bulk deletions
- core: Fixed the campaign bug when getting comments, also removed the hardcoded code from the social client.
- campaigns: allowing nulls in several serializers
- core: passing kpi/date in pubsub client for comments
- several: improving performance on bulk_writes for ads
- several: refactoring code and fixing redoc
- campaigns: cleaning code
- core: updating bulkupdatelist serializer to set the right instance per child
- campaigns: WIP - allowing multi validation on patch
- campaigns: adding bulk partial update serializer for ads
- core: adding mixin for bulk updates
- campaigns: filter_queryset by only retrieved ads
- several: adding bulk router and set campaigns endpoints with bulk router
- core: Final adjustments to the flow of comments.
- campaigns: updating stop_time on campaign pause/activate and when refreshing kpis in background
- campaigns: fixing query for management commands
- campaigns: updating management commands to use stop_time attribute
- campaigns: adding stop_time on campaign model with related migration
- core: adding bid_strategy to adset model from parser
- campaigns: disabling every required field in admin form
- core: changes for the social client and enpoints, still need to fix the connection with the microservice.
- campaigns: changing signal ordering on post_save campaign
- business: retrieving metrics importing status on campaigns serialized response and updating old campaign data in new migration
- webhooks: fixing campaign update business logic
- core: updating redoc
- webhooks: adding new endpoint for hiperion metrics call and related business logic
- campaigns: adding new attribute to denote syncing metric status with related migration
- core: websocket communication settings.
- campaigns: passing organization_id to ai_service on post_save campaign created
- core: updating ai client to add organization_id when persisting campaign metadata for recommendations
- core: passing api_id to hiperion call to fetch report metrics
- business: adding pk on hiperion metrics call
- core: passing extra api_campaign_id to get_overview_chart call
- campaigns: passing api_campaign_id to hiperion client to format chart overview and retrieving comments
- core: bug detected when formatting the code.
- core: The client is adjusted for notifications with the new settings, serializers and calls have also been changed.
- core: client creation and endpoint flow for connection to adtuo-notification.

## [v1.7.1] - 25-05-2021
### fixing headers in notification client
- core: fixing notification client

## [v1.7.2] - 25-05-2021
### fixing social microservice url
- settings: fixing social microservice url in production

## [v1.7.3] - 25-05-2021
### fixing workers url 
- settings: updating prod task url for hiperion workers

## [v1.7.4] - 25-05-2021
### fixing bugs
- several: adding panel queues/urls and updating hiperion client to use them
- core: parsing headers to send them through pubsub message in order to reach social microservice

## [v1.7.5] - 27-05-2021
### fixing public comments
- webhooks: send notification using pubsub client instead of sync client
- core: adding notification mixin to build context regards to client type
- core: passing public key for comment serializer in context
- core: fixing scope error on public comments

## [v1.7.6] - 28-05-2021
### updating update metrics business logic 
- user: minor change.
- users: a command to delete the OutstandingToken of <NAME_EMAIL> is added.
- campaigns: excluding in metrics fetch process campaigns that they are importing metrics
- campaigns: split aggregated calls to hiperion when updating insights

## [v1.8.0] - 02-06-2021
### adding minor fixes
- organizations: passing only payload in serialized response for ai-auth endpoint
- github: updating pipelines for encoding key
- organizations: fixing redoc for ai-auth endpoint
- organizations: adding more data to payload for text-generation endpoint authorization
- settings: adding text_generation service audience
- settings: adding encoding key for staging and production envs
- core: adding encoding_key to helper function to build a signed payload
- users: updating onboarding_setup condition
- several: passing for social accounts if has expired tokens or not in serialized response
- core: adding business logic to facebookclient in order to add service name and check if token has been revoked
- several: returning 424 error on proxy call
- several: calling facebook client with named attributes
- core: fixing socialaccount token in proxy view
- organizations: getting encoded payload for text-generation calls
- core: adding new method to encode payload with hmac
- users: a small error is solved and the query is formatted correctly.
- core: fixing graph condition for token=user
- organizations: adding token generator endpoint for gpt3 endpoint
- core: adding gcloud token generator function

## [v1.9.0] - 02-06-2021
### adding sync key to manage campaign importation notification
- several: adding verbosity to error messages derived from facebook
- users: cleaning unused fields from serializer
- organizations: improving social_account serialized response in organizations endpoint business logic
- github: adding excluded-paths in pipeline
- github: fixing collect_static pipeline
- campaigns: passing custom key for metrics on campaign importation
- core: adding custom header to create unique temporal checks in firestore for client notification when finishing campaign importation

## [v1.9.1] - 02-06-2021
### updating metrics finish webhook
- webhooks: fixing campaign_id data when querying in facebookcampaigns on imported campaign notification

## [v1.9.2] - 08-06-2021
### removing cached responses from facebook pages/google accounts
- users: disabling cache on facebook_pages/google accounts

## [v1.10.0] - 09-06-2021
### updating campaign parts feature
- business: adding ordering in model meta to avoid warning in pagination
- campaigns: updating overview table query to exclude dynamic ads
- core: updating proxy endpoint business logic to allow video encoded data
- core: adding custom base64videofield for serializing requests
- core: adding files option to facebook proxy in order to send files as form-data encoded
- core: passing budget when importing campaigns as daily or lifetime
- core: improving parser business logic for dynamic ads
- webhooks: adding missing param on async call to get data from campaign
- campaigns: adding more data in admin view for inlines
- campaigns: set internal metadata only if token is not revoked
- campaigns: adding limit restriction to sync campaign endpoint and capture facebookrequesterror to set internal flags accordingly
- campaigns: updating internal flag on facebook request error
- core: set sync_key as empty string as default is none is provided to avoid cloud tasks creation error
- campaigns: adjusting delays on updating metrics when syncing campaign
- core: updating importing campaigns business logic
- campaigns: adding mixin to fetch insights from facebook when syncing campaign data
- business: getting only non expired tokens to use in facebook calls
- settings: adding delay to sync campaign data from remote source
- core: updating importer business logic
- campaigns: adding related_name to FK in order to allow reverse lookups
- core: adding initial dynamic_ads support in parser and related business logic
- core: adding more fields to retrieve objects from facebook
- campaigns: solving migration conflicts
- users: minor change in admin view
- campaigns:recursive relationship is added to FacebookAd.
- campaigns: removed is_dynamic_creative field from list_display.
- campaigns: minor change.
- campaigns: a field is added to the ad sets to know if it contains any dynamic creative.
- several: fixing serializers to pass stop_time on campaign creation
- campaigns: adding stop_time as allowed field when creating campaigns
- core: adding stop_time when importing campaign
- several: calling facebook client with named attributes

## [v1.10.1] - 09-06-2021
### minor fixes
- campaigns: adding currency on syncing campaign metadata
- core: skipping state in campaign parser to avoid errors on ad creation and fixing adset filtering query to update event location
- business: passing adaccount currency to facebook importer
- campaigns: saving stop_time on refreshing campaign

## [v1.10.2] - 11-06-2021
### fixing update_metrics command
- campaigns: parsing stop_time as date to avoid 400 when updating insights

## [v1.10.3] - 11-06-2021
### parsing non-null stop_time to date 
- campaigns: parsing to date non default dates to set stop_time

## [v1.10.4] - 11-06-2021
### fixing typo in command
- campaigns: fixing typo in command

## [v1.10.5] - 11-06-2021
### fixing bug in command
- campaigns: fixing date cast in command

## [v1.11.0] - 15-06-2021
### adding new command and improving business logic of existing commands
- campaigns: updating commands business logic and adding a new one to sync remote status

## [v1.12.0] - 16-06-2021
### adding fixes for campaign creation and minor changes
- core: passing new extra field in serialized response for notifications
- webhooks: passing pomegranate errors flag to notification
- core: dropping extra data in notification context
- campaigns: set sandbox adaccount as default for every environment except production
- campaigns: relaxing lead_form_id constraints in ad serializer and set page_likes default CTA if none is provided
- webhooks: updating pomegranate webhook to create notification on finishing creation or edition
- core: adding extra_fields parameter to notifications service
- business: passing has_errors value in campaign list view
- campaigns: improving ad serializers business logic
- campaigns: updating default name if none is provided for campaigns
- core: updating bulk_create mixin to call save() method and dispatch the side effect business logic
- campaigns: adding new valid choice for facebook positions
- core: improving parser business logic to take into account not allowed flexible specs in targeting validation

## [v1.12.1] - 16-06-2021
### updating notifications pubsub client call
- core: adding extra_data to create_notification in pubsub client

## [v1.12.2] - 17-06-2021
### passing extra data in campaign when building pomegranate message
- campaigns: passing pixel_id in campaign body when building pomegranate message

## [v1.12.3] - 17-06-2021
### fixing errors in campaign creation
- campaigns: fixing validations in serializers
- campaigns: discard filtering business tokens by user to avoid errors
- webhooks: use a linked token to business instead of trying to use the user token to activate campaign on success
- core: passing locale to targeting_suggestions endpoint
- campaigns: use a token linked to business instead of trying to use the user token

## [v1.13.0] - 22-06-2021
### business insights and minor fixes
- campaigns: set paused as default value if none is provided
- campaigns: fixing typo in pause adset
- campaigns: fixing local status on campaign mutation
- campaigns: return directions in ad serialized response as is without parsing fbgeo://
- campaigns: fixing validate in ad partial update serializer
- campaigns: changing emptyness for phone/country_code/direction in ad partial update serializer+
- campaigns: adding call_to_action_value format in ad serialized response for GET ads endpoint
- campaigns: formatting call_to_action_value in directions or phone in ad serialized response
- core: adding helper method to parse phone number
- campaigns: fixing validated_phone business logic in adserializers
- campaigns: replacing validated_phone business logic
- campaigns: setting CTA option in model
- several: save more data in adset locations
- business: sending null response with 200 code to avoid CORS errors
- business: reduce cached time for thumbnail business pictures
- business: fixing first page for business insights
- business: adding insights response serializer for redoc
- business: adding business_insights call in model to wrap the real call against hiperion
- business: adding endpoint to get business insights
- core: adding insights call in hiperion client
- business: adding serializers for insights

## [v1.14.0] - 29-06-2021
### adding stripe subscriptions
- payments: setting the right ordering in meta model options
- business: adding in business insights serializer nullable objective attribute
- payments: setting stripe tax_id only for european countries
- payments: adding command to create customer in stripe for missing organizations
- payments: saving invoice currency in db with related and migration and updating admin view
- payments: fixing cancel subscription business logic when setting the right status
- payments: avoid creating invoice on sync subscription creation to have voided invoices in our backend
- webhooks: create the invoice always on hook invoice paid response
- payments: adding invoice as inline in admin view to track per subscription invoices
- core: wrap stripe call to update customer in stripe into try/catch to avoid interrupting the process
- core: updating stripe async task handler and stripe customers query to db
- payments: updating stripe error response to send to client
- users: adding business logic to update customer email in stripe on email change
- core: adding async task to process in background stripe email updating
- webhooks: adding hook to update stripe customer email on email change
- webhooks: changing hook for invoice paid as doc suggests
- several: wrapping calls to stripe into try/catch block
- payments: catching the most general exception on stripe calls
- webhooks: setting subscription failed only in subscription_cycle states
- payments: catching stripe card error to send the right response to client
- payments: refactoring subscriptions statuses and improving business logic
- webhooks: listening invoice payment failed hook
- payments: adding subscriptioncreateresponse serializer to send data to client
- payments: removing 3DS state and updating business logic
- payments: adding first version for 3ds managing
- payments: dropping 3DS status for active_subscription condition
- payments: managing stripe_subscription_status on subscription creation
- campaigns: updating more fields on syncing remote campaign
- core: reset draft if any when refreshing campaign data structure
- payments: adding coupon_id when generating an upcoming invoice
- payments: passing coupon_id to stripe endpoint
- payments: fixing subscription updating when tax_rate has change
- payments: setting default invoice local to english with related migration
- payments: updating business logic for billing data and updating redoc
- payments: adding VAT for european countries in invoice
- pipfile: updating dependencies
- payments: improving business logic to set the right tax_id
- payments: updating business logic to set spanish tax_id in stripe invoices using the vat
- payments: allowing null coupon_id in serializer
- campaigns: updating status from remote source on different use cases
- campaigns: adding cache to traffic light status
- campaigns: adding campaign statuses in overview table response
- several: getting status instead of effective status on campaign mutations and set the internal status accordingly
- core: updating hiperion client to pass data aggregation mode as parameter
- campaigns: updating GET endpoint to allow data aggregation and updating redoc
- business: adding data aggregation option to reports and updating redoc
- campaigns: passing conversion_domain to pomegranate_message
- core: adding conversion_domain in parsed ad
- campaigns: updating ad serializers to pass custom_domains in post/patch calls
- core: fetching custom_domain from remote ads when importing them
- core: adding custom validator to allow urls without scheme
- campaigns: adding new attribute to ad and replace urlvalidator for a couple of attributes
- campaigns: set default ordering for adsets/ads by date_added in asc order
- core: updating parser for exclusions
- campaigns: changing M2M widget by another one to avoid slowness load in admin view+

## [v1.14.1] - 29-06-2021
### fixing mutate_campaign business logic for adsets/ads
- campaigns: adding facebook_business_account property in adset/ad model to mutate status in remote

## [v1.14.2] - 29-06-2021
### improving facebook importer for targetings
- core: updating targeting importer business logic

## [v1.15.0] - 30-06-2021
### changing invitation business logic
- users: passing invitation error as non_field_errors instead of details
- organizations: updating business logic to allow more than one invitation per user for the same organization
- users: updating user_invitation business logic to avoid delete them
- users: mark invitation as accepted instead of deleting them when user is registered
- organizations: set invitation as accepted instead of deleting them and filter queryset to avoid retrive it into response to client

## [v2.0.0] - 14-07-2021
### adding google ads
- users: improving business logic in password reset form
- users: updating uid generation to reset password
- campaigns: adding default stop_time in command
- campaigns: adding commands to update google campaigns and updating facebook commands
- payments: filtering invoice queryset to show only displayable invoices and updating model with related migration
- payments: wrapping billing data creation business logic into transaction atomic to do an implicit rollback in case of stripe error and managing better the customer_id validation
- several: updating hiperion calls and related business logic
- campaigns: fixing GET all serialized response for facebook campaigns
- core: passing social_network in social client to add extra_data in notification
- campaigns: passing date_added attribute in GET all serialized response for campaigns
- core: improving modelrepresentationfield business logic to allow multiple querysets to serialize response
- users: updating businesssocialaccount linked to business when updating social account
- webhooks: adding social_network when importing campaign on webhook
- campaigns: making generic campaign_type on campaign imported notification
- campaigns: adding genericrelation on base model instead of facebook model
- business: adding pixel_name with related migration
- business: adding pixel_id in facebook business account with related migration
- campaigns: adding is_draft in base model
- core: updating queryset in filtering endpoint
- core: removing unneeded filter_query in filtering endpoint
- core: adding google campaigns filtering for quicksearch
- business: passing type of campaign on reports serialized response
- campaigns: adding missing serializers for concrete GET campaign
- campaigns: enabling PATCH for GA campaigns
- core: fixing parser mapping fields for content ads
- business: fixing report create serializer and passing date_added in campaign LIST serialized response
- campaigns: return partial responses on updating campaign structure  mixin
- campaigns: refactoring viewset endpoint with a mixin
- campaigns: adding bring-campaign-parts for google campaigns and fixing importer business logic
- campaigns: refactoring highly couple methods and adding new attributes to google model with related migration
- campaigns: fixing business logic for activate/pause campaign/adset/ad endpoints
- campaigns: fixing adsets in google campaign serialized response
- users: check if google account exists when connecting the account and raise error
- campaigns: catching campaignmanagementmixin with the right exception
- campaigns: refactoring update insights mixin business logic
- campaigns: refactoring remote social actives statuses in centralized dict
- core: updating campaign internal status when importing process has finished
- business: deprecating temporarily unused endpoint and updating serializers to avoid errors
- business: updating serializers to include business accounts in business
- business: passing request in context for link_business_account serializer
- business: linking social_account when adding new business account to business
- campaigns: refactoring management mixin and adding activate/pause endpoints to every campaign part
- campaigns: adding basic serializers for adsets and ads
- core: adding helper method to mutate google ads objects
- campaigns: adding pause and activate calls in google models
- core: adding adset and ad mixins to google client
- several: updating GoogleAdsClient parameter in every coincidence
- business: improving link_business_account validation business logic
- campaigns: refactoring management mixin to make it generic to every campaign
- core: adding debug_token business logic in google client and fixing condition in FacebookClient debug_token method
- users: cast to string the account id from google accounts to filter already imported campaigns in db
- campaigns: transforming budget to cents in google concrete campaign serialized response
- several: adding social_network type on campaign serialized response in business and concrete GET
- users: updating serializer to pass new attributes from customer in google accounts serialized response
- users: adding time_zone in google accounts response
- users: removing duplicated key
- users: returning more information on response for google ads accounts
- business: updating set_metrics signal to get default insights for reports from campaign
- core: renaming every adgroup found to adset and hiperion api base url
- campaigns: updating google campaign serializers
- campaigns: refactoring manager to hierarchies regardless of campaign type
- campaigns: refactoring google adgroup model and overview calls in model to mixin with related migration
- webhooks: updating metrics_finish business logic to make it generic
- campaigns: updating campaign status on finishing import metrics
- core: getting dynamically model campaign through parameters when creating a notification
- users: returning 400 in response when fetching social accounts
- core: fixing google ads call in mixin
- core: updating google ads api call business logic to get only the first response
- core: fixing params parameter when passing data in hiperion async_tasks and client
- campaigns: passing base_url parameter to fetch campaign details with trailing slash
- core: adding new google ads api call in mixin client
- core: refactoring hiperion client calls for metrics and aggregated metrics
- core: refactoring params for hiperion common async tasks to make it loose coupled to specific campaigns
- campaigns: refactoring get_campaign_details signal to code a general business logic for every campaign
- users: updating socialtoken manager to allow filtering against token or token_secret
- pipfile: updating dependencies
- core: updating google client to handle oauth errors better
- campaigns: updating view for google endpoints
- several: updating serializers for google campaigns in business and full response for campaign
- core: adding strategy pattern to get the right ad content and improving the related business logic
- core: updating campaign importing process business logic
- campaigns: updating google campaign create serializer business logic
- campaigns: adding pre_delete signal to delete non shared budgets on campaign deletion
- campaigns: replace mixin for concrete ad types models for abstract base model
- campaigns: fixing google model with related migration
- campaigns: updating django admin view
- core: updating google ad parser and finishing first version of google importer campigns
- campaigns: updating google models with related migration
- business: wrapping into sanitize helper method the where clause of GAQL
- core: updating google ads client business logic and adding first version of parser
- core: refactoring metaclass baseparser to do it generic
- core: adding first version of google campaign importer WIP
- campaigns: updating create method on google campaign serializer
- campaigns: refactoring fetch_and_create_every_campaign_part to allow generic business logic regarding social network
- campaigns: cleaning google model and improving business logic with related migration
- core: updating targeting importer business logic
- users: set callback_url for google to postmessage to avoid redirect_uri error for client integration
- campaigns: updating models with related migration
- campaigns: updating admin view for google campaings
- core: refactoring google client to summarize common business logic
- business: updating serializer for importing google campaigns
- core: adding helper methods and updating business logic for google campaigns
- campaigns: adding viewset for google campaign
- campaigns: adding google campaign serializers for read and create
- business: updating google campaign importer call and adding serializers
- campaigns: updating google campaign model with related migration
- core: parsing proto.Repeated to do it serializable
- business: adding google campaigns in business campaigns serialized response
- core: refactoring campaignimporter and adding business logic for google campaigns
- core: adding google client with mixins and helper functions
- campaigns: updating google campaign model with related migration
- business: adding google remote campaigns endpoint to import them and adding related serializer
- business: adding campaign only import business logic in model
- settings: updating google ads version to 7
- campaigns: export google campaigns on init
- core: adding custom validator for schemaless urls
- campaigns: updating models for google campaigns
- core: adding more robust error managing for ads client
- pipfile: updating dependencies

## [v2.0.1] - 14-07-2021
### fixing minor bugs
- payments: fixing updating billing data serializer
- core: fixing async method to import campaigns

## [v2.0.2] - 14-07-2021
### updating webhooks business logic
- webhooks: updating async serializer and business logic to fetch partial data from campaigns on importation

## [v2.0.3] - 14-07-2021
### adding missing social_network param in async call
- core: passing the social_network type into async call when importing campaign

## [v2.0.4] - 15-07-2021
### fixing typo in service call
- campaigns: fixing typo in parameter when importing campaigns in hiperion call
- campaigns: renaming ad content field with related migration

## [v2.0.5] - 22-07-2021
### adding stripe production tax rate
- github: updating stripe tax rate id in production

## [v2.1.0] - 22-07-2021
### adding unknown ads model for google ads
- core: updating ad_parsers business logic to parse non mapped ads in models
- campaigns: adding unknown ads in serialized concrete response
- campaigns: adding generic model with related migration for unknown ads
- several: updating google ads version to v8
- pipfile: updating dependencies

## [v2.1.1] - 22-07-2021
### updating google campaign importer business logic
- core: fixing side_effect when importing google campaigns with ads but no related adsets

## [v2.2.0] - 26-07-2021
### adding business deletion and slack integration
- users: fixing default value for customer_io call when sending email to user is performed
- core: updating message access in comment when sending email
- business: changing on_delete behavior on businessocialaccount when deleting an user
- organizations: set default exclude as iterable when is none for sending email
- webhooks: fixing campaign fetch from queryset to notify email and slack
- organizations: disabling slack notifications in local environment
- users: exclude slack connection from socialaccounts response
- several: adding slack callback url per environment and updating the view method
- organizations: updating slack format message in proxy method to send notification
- several: updating slack notification calls
- core: updating slack formatters
- campaigns: simplifying hierarchy manager method
- organizations: adding delete endpoint for thirdpartyintegrations with related business logic
- core: updating redoc for slack integration
- users: fixing extra_data for slack adapter
- organizations: adding delete endpoint for third party integrations with related business logic
- organizations: updating integrations model with related migration
- organizations: fixing typo getting format_type from slack_metadata
- several: send slack notification for campaign importation/creation and new comment
- organizations: updating slack notification model method business logic to format first the message
- core: adding new formatters for slack client
- campaigns: retrieving business name from hierarchy manager method
- core: inject slack client and checking http content type to avoid errors
- core: adding slack client and formatter class to simplify slack message format
- organizations: adding model method to send slack notifications
- users: updating slack social connect serializer to enforce organization and creating the relation between app and organization
- organizations: adding thirdparty model with related migration
- campaigns: updating facebook error message shown to user
- users: adding slack connect serializer (WIP)
- users: overriding slack adapter to save the incoming hook url and extra data
- users: adding endpoint for slack connection
- settings: adding slack provider integration
- business: parsing uuid as str to avoid json errors and updating manager function to retrieve campaigns
- business: adding pre_delete signal to send async messages in order to mark as unavailable related comments and notifications
- business: adding businessmanager to manage related campaigns and children from businesses
- core: adding method to notification and social clients to mark as unavailable comments/notifications in mongodb
- core: improving error management for google ads client
- business: setting default logo_src in validate instead of attribute field
- business: allowing null and blank in logo_src in report serializer
- organizations: updating send_email business logic to make use of User manager and delete OrganizationManager
- users: refactoring customerio_recipients and updating related code in several files
- users: adding configuration attributes and related migration
- pre-commit: updating config
- core: updating business logic to send email only for private comments without include it the current user
- core: sending mail on private comment
- webhooks: sending mail on metrics/pomegranate finished process
- organizations: updating default manager and adding helper method to send emails
- organizations: adding manager to send emails to all users who belongs to it
- templates: updating base.html reports template to add dynamic logo source
- business: passing logo source in reports serializer to pass custom logo url to render pdf
- organizations: adding reports_logo attribute with related migration
- business: adding delete endpoint for businesses
- users: updating onboarding_setup condition
- campaigns: adding new CTA to facebook ad model
- campaigns: adding call_to_action_value in ad partial serializer

## [v2.2.1] - 28-07-2021
### restoring pubsub topic name for social comments
- core: fixing pubsub topic name for social

## [v2.2.2] - 28-07-2021
### fixing comments extra_data parameter for pubsub messages
- core: passing extra_data in social messages as json encoded for pubsub message

## [v2.3.0] - 28-07-2021
### updating facebook dependency to 11
- pipfile: updating facebook dependency to 11

## [v2.3.1] - 05-08-2021
### fixing business_account method in facebook models
- campaigns: adding business_account property in facebook adsets and ads for compatibility
