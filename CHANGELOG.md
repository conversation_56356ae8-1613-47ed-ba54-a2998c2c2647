
## [v1.0.0] - 08-09-2021
### adding first version of network clients
- adding pubsub client and http client
- generate_changelog: adding script to create and package version in github
- pipfile: updating dependencies
- adding tests for clients

## [v1.0.1] - 08-09-2021
### fixing setup.py
- setup.py: fixing package name
- wrapping code inside python package to install it later

## [v1.0.2] - 08-09-2021
### fixing setup.py
- setup.py: fixing packages resolution and adding dependencies

## [v1.1.0] - 09-09-2021
### adding slack client
- tests: adding slack client tests
- network_clients: adding slack client to notify leads

## [v1.2.0] - 13-09-2021
### adding task and odoo client
- tests: adding tests for cloud tasks client
- network_clients: adding odoo client with tests
- network_clients: adding cloud tasks client business logic
- pipfile: updating dependency for cloud tasks

## [v1.2.1] - 13-09-2021
### updating odoo client business logic and fixing tests
- network_clients: replacing odoo client to wrap business logic into context manger to ensure right connection handling

## [v1.2.2] - 28-09-2021
### adding customer mixin to odoo client
- tests: adding tests for customermixin in odoo client
- odoo: adding args and kwargs in lead_mixin functions
- odoo: adding customer_mixin to fetch customer data from odoo

## [v1.2.3] - 04-10-2021
### adding new endpoint for slack notification
- slack: addiing order notification with tests
- pipfile: updating dependencies

## [v1.2.4] - 08-10-2021
### adding susbcriber and moreapp clients
- network_clients: adding tests for subscriber client
- tests: adding tests for storage client
- pipfile: updating dependencies
- network_clients: adding storage_client
- network_clients: adding subscriber client
- network_clients: adding moreapp http client

## [v1.2.5] - 08-10-2021
### fixing dependencies
- setup.py: updating  dependencies

## [v1.2.6] - 13-10-2021
### adding extra kwargs in storage client
- network_clients: passing extra kwargs to upload_from_string in storage_client

## [v1.2.7] - 21-10-2021
### adding stripe client
- setup: updating dependencies
- tests: adding tests for moreapp client
- stripe: adding tests for stripe client
- pipfile: updating dependencies

## [v1.2.8] - 22-10-2021
### updating stripe client
- network_clients: updating release version
- tests: fixing stripe tests in regression
- network_clients: updating subscription item arguments

## [v1.2.9] - 22-10-2021
### adding new methods for stripe client
- network_clients: updating version
- tests: adding more tests for task client
- network_clients: adding quote business logic to stripe client with testing
- network_clients: adding subscription_schedule method to stripe client

## [v1.2.10] - 26-10-2021
### adding new method to stripe client
- network_clients: adding apply_coupon method to subscription mixin with tests

## [v1.2.11] - 27-10-2021
### adding coupon mixin to stripe client
- network_clients: updating version
- network_clients: adding coupon mixin to stripeclient with tests

## [v1.2.12] - 10-11-2021
### adding taxesmixin to stripeclient
- network_clients: adding taxmixin to stripe client with unit testing

## [v1.2.13] - 25-11-2021
### adding chargebee client
- network_clients: updating version
- chargebee: adding unit testing and updating some functions
- chargebee: adding chargebee client with customer, payment source and subscription mixins
- pipfile: updating  dependencies

## [v1.2.14] - 29-11-2021
### fixing dependencies in setup.py
- network_clients: updating version
- setup: updating dependencies

## [v1.2.15] - 30-11-2021
### adding one-time payments mixin to chargebee client
- network_clients: updating version
- chargebee: adding one-time payments mixin with unit test

## [v1.2.16] - 01-12-2021
### adding missing parameter to billing info in chargebee customer
- network_clients: updating version
- chargebee: adding missing flag to billing data with updated tests

## [v1.2.17] - 01-12-2021
### adding new parameter to item dataclass for chargebee client
- network_clients: updating version
- chargebee: adding unit_price to item dataclass

## [v1.2.18] - 01-12-2021
### adding coupon mixin to chargebee client
- network_clients: updating version
- chargebee: adding coupon mixin to the client with related tests

## [v1.2.19] - 01-12-2021
### fixing coupon creation in chargebee
- network_clients: updating version
- chargebee: fixing coupon creation business logic

## [v1.2.20] - 01-12-2021
### adding new methods for coupons and subscriptions in chargebee client
- network_clients: updating version
- chargebee: adding new functions to client with unit testing

## [v1.2.21] - 02-12-2021
### updating paymentsource mixin for chargebee client
- network_clients: updating version
- chargebee: updating payment_source business logic to allow creating payment source using card_token or payment intent with related tests

## [v1.2.22] - 02-12-2021
### fixing create payment method business logic in chargebee client
- network_clients: updating version
- chargebee: fixing business logic

## [v1.2.23] - 21-12-2021
### surrounding subscriber callback inside atomic block
- pipfile: updating lock
- network_clients: removing duplicated code
- pubsub_client: wrapping callback code in atomic block on subscriber
- core: adding termination signal handler context manager utility

## [v1.2.24] - 30-12-2021
### adding missing fields in chargebee dataclass
- network_clients: updating version
- network_clients: adding more attributes to item dataclass

## [v1.2.25] - 30-12-2021
### Fixing quote creation for susbcriptions
- network_clients: fixing quote creation call for subscriptions with related test

## [v1.2.26] - 14-02-2022
### fixing cancel_subscription method
- network_clients: fixing cancel_subscription method

## [v1.2.27] - 23-02-2022
### dropping validation on chargebeeitem dataclass
- network_clients: disabling post_init validation on chargebeeitem dataclass

## [v1.2.28] - 23-02-2022
### fixing chargebee client
- network_clients: updating condition in chargebee_sdk property to get the right instance

## [v1.2.29] - 23-02-2022
### fixing chargebee client business logic
- network_clients: fixing chargebee client tests in regression
- network_clients: fixing chargebee customer to make calls overriding the environment
- network_clients: upgrading version in __init__

## [v1.2.30] - 02-03-2022
### updating chargebee client
- network_clients: uploading version
- network_clients: adding comment mixing to chargebee client with unit testing

## [v1.2.31] - 11-03-2022
### updating chargebee client
- network_clients: adding new method to update chargebee subscriptions

## [v1.2.32] - 11-03-2022
### adding mixin to chargebee client
- network_clients: adding chargebee mixin to list item prices

## [v1.2.33] - 15-03-2022
### adding google sheets client
- network_clients: updating version
- network_clients: adding google sheets client with unit test

## [v1.2.34] - 15-03-2022
### fixing google sheets client business logic
- network_clients: updating version
- network_clients: fixing call in google sheets client

## [v1.2.35] - 18-03-2022
### fixing coupon creation
- chargebee: updating coupon application

## [v1.2.36] - 21-03-2022
### fixing clashing with chargebee wrapper
- network_clients: updating version
- network_clients: changing chargebee client and fixing tests

## [v1.2.37] - 21-03-2022
### locking chargebee dependency version due to a bug
- network_clients: upgrading version
- pipfile: locking chargebee version to 2.12.1

## [v1.2.38] - 21-03-2022
### updating setup.py for chargebee
- network_clients: setting fixed chargebee version in setup.py

## [v1.2.39] - 23-03-2022
### adding moloni client
- tests: adding unit testing to moloni client
- network_clients: fixing moloni request call passing headers and method type
- network_clients: WIP adding moloni client with related mixins

## [v1.2.40] - 23-03-2022
### adding invoice list in chargebee client
- tests: updating business logic for chargebee tests
- network_clients: adding new method to list invoices from chargebee

## [v1.2.41] - 23-03-2022
### fixing invoice list business logic in chargebee client
- network_clients: updating version
- netowrk_clients: fixing yield call in list generator

## [v1.2.42] - 23-03-2022
### updating moloni client
- network_clients: adding fetch next free number in moloni customer mixin

## [v1.2.43] - 24-03-2022
### updating moloni client
- network_clients: updating version
- network_clients: wrapping wrong JSON response into try/except and propagate the exception to caller in moloni client

## [v1.2.44] - 25-03-2022
### updating chargebee client
- network_clients: adding new method to chargebee subscription mixin

## [v1.2.45] - 05-04-2022
### updating moloni client
- Upgrading version
- network_clients: updating moloni client

## [v1.2.46] - 03-05-2022
### updating moloni client
- init: upgrading version
- moloni_client: adding get_invoices and update_invoice methods in client with related tests

## [v1.2.47] - 03-05-2022
### adding credit notes method in the chargebee client
- network_clients: upgrading version
- network_clients: adding tests for credit_note mixin
- network_clients: adding creditnote mixin to client

## [v1.2.48] - 03-05-2022
### adding method to moloni client
- network_clients: upgrading version
- tests: adding tests for moloni client
- network_clients: adding credit_notes mixin for moloni client

## [v1.2.49] - 04-05-2022
### adding new methods to moloni client
- network_clients: upgrading version
- network_clients: adding more methods to credit_notes mixin

## [v1.2.50] - 30-05-2022
### adding list_subscriptions method in chargebee client
- network_clients: updating version
- chargebee: adding method to subscription mixin with related tests

## [v1.2.51] - 30-05-2022
### adding chargebee method in subscription mixin
- chargebee: adding method to filter subscriptions by ids

## [v1.2.52] - 02-06-2022
### refactoring storage client
- network_clients: fixing version

## [v1.2.53] - 02-06-2022
### refactoring storage client
- several: recovering refactor clients

## [v1.2.54] - 06-06-2022
### upgrading business logic to handle cloud tasks when serializing payload
- network_clients: handling cloud task payload as json compatible

## [v1.2.55] - 24-06-2022
### adding new method to chargebee clietn
- network_clients: updating version
- network_clients: adding pending_invoices method to chargebee client with related tests

## [v1.2.56] - 27-06-2022
### updating chargebee client
- network_clients: updating chargebee update_customer client signature and updating tests

## [v1.2.57] - 12-07-2022
### updating chargebee client
- network_clients: adding method to download  credit notes in chargebee client

## [v1.2.58] - 28-07-2022
### fixing chargebee client
- chargebee: fixing payment source mixin on payment intent and payment method creation

## [v1.2.59] - 28-07-2022
### adding method to chargebee client
- network_clients: adding assign_payment_role method chargebee client

## [v1.2.60] - 03-08-2022
### updating apply_coupon function in chargebee client
- network_clients: passing kwargs on apply_coupon function for chargebee client

## [v1.2.61] - 09-08-2022
### updating chargebee client
- chargebee: adding transaction mixin and new method in subscription with related tests

## [v1.2.62] - 09-08-2022
### adding new method to chargebee client
- chargebee_client: adding one time charges to customer without using quotes

## [v1.2.63] - 16-08-2022
### fixing chargebee client
- chargebee: fixing invoicemixin method call

## [v1.2.64] - 22-08-2022
### adding new method into chargebee client
- network_clients: adding get_invoice method to chargebee client

## [v1.2.65] - 19-12-2023
### add delete_file method in storage client wrapper
- network_clients: update version
- storage_client: add tests
- storage_client: add delete_file method
- pipfile: update dependencies

## [v1.2.66] - 29-04-2024
### add new method to chargebee client to handle end terms on subscriptions
- generate_changelog: add method to update automatically the package version
- chargebee: add charge_term_end function to client

## [v1.2.67] - 07-05-2024
### fix dependencies
- pipfile: update dependencies

## [v1.2.68] - 08-05-2024
### add create comment in quote method in chargebee client
- chargebee: add create comment in quote method
