
## [v1.0.0] - 25-01-2022
### First deployment
- First deployment for the backend (lot of things have been made during these months)

## [v1.0.1] - 25-01-2022
### fixing deployment
- github: fixing master steps
- deployment: adding managed certs and fixing spec
- setings: adding CIDR_NETS for production environment
- pipfile: updating dependencies
- github: dropping temporarily master jobs
- urls: adding health/ for live/readyness probes
- github: fixing push image step in job
- github: fixing master pipeline
- github: adding dropped config in master

## [v1.0.2] - 25-01-2022
### fixing master pipeline
- github: dropping unneeded varenv
- github: fixing step in pipeline
- api-migrations-job: fixing image name

## [v1.0.3] - 25-01-2022
### fixing deployment
- deployment: adding start.sh script to api-deployment
- deployment: fixing image name in api deployment
- github: fixing master pipeline

## [v1.1.0] - 25-01-2022
### adding callback topics as varenvs
- webhooks: replacing hardcoded topic in callbacks for varenv topic
- settings: adding topics per environment for callback jobs

## [v1.2.0] - 26-01-2022
### adding dummy callback to test in k8s
- webhooks: adding dummy subscriber
- deployment: adding frontend to ingress to handle https redirection

## [v1.3.0] - 26-01-2022
### adding signal handler for SIGTERM
- webhooks: fixing settings import for callbacks
- settings: updating prod environment for signal handling
- core: adding signal handler for SIGTERM
- core: adding custom middleware to disable health probes in production
- gitignore: updating

## [v1.3.1] - 26-01-2022
### fixing logs output
- deployment: adding unbuffered varenv in start script
- webhooks: trial

## [v1.3.2] - 26-01-2022
### fixing logs
- deployment: dropping unused varenv
- settings: disabling log filtering
- settings: updating documentation_bucket_name for prod environment
- api-deployment: finetuning rolling update strategy

## [v1.4.0] - 26-01-2022
### adding missing configuration
- github: adding new deployments to master pipeline
- deployment: adding cron jobs
- settings: adding preffix varenv and managing routes
- deployment: adding tasks deployment and updating ingress
- webhooks: wrapping main loop with is_alive context manager for liveness probe in k8s
- core: fixing alive business logic
- deployment: adding callback processes deployments

## [v1.4.1] - 26-01-2022
### fixing deployment files
- several: fixing deployment
- github: adding missing deployments

## [v1.4.2] - 27-01-2022
### parametrizing PORT for gunicorn deployment
- deployment: passing port as varenv in async deployment
- gunicorn: passing port as varenv
- deployment: fixing probes health
- deployment: fixing typo

## [v1.4.3] - 27-01-2022
### updating k8s deployments
- deployment: updating liveness probe url
- backoffice_api: adding healtz url for liveness probe
- core: fixing middleware to shutdown pod
- api-async-deployment: passing PORT as deployment env
- backoffice_api: fixing urls
- deployment: fixing typo in redinessprobes for api-callbacks
- master: fixing pipeline

## [v1.4.4] - 27-01-2022
### k8s fine tuning configuration
- several: fixing deployment

## [v1.4.5] - 28-01-2022
### tunning k8s deployment
- deployment: updating spec for api
- core: changing log message for SIGTERM handle
- settings: updating middleware order
- deployment: fixing callback deployment adding missing prefix

## [v1.4.6] - 28-01-2022
### finishing k8s deployment
- settings: adding prod/staging IPs to allowed hosts
- deployments: updating preStop and graceful shutdown

## [v1.4.7] - 28-01-2022
### fixing callback deployment command
- deployment: fixing callbacks deployment
- deployment: fixing files

## [v1.4.8] - 28-01-2022
### fixing email activation return type and updating settings
- users: fixing activation account url and mail sender function
- settings: adding public_webapp_urls

## [v1.4.9] - 28-01-2022
### fixing domain email variable in user to send emails from right domain
- several: fixing email domain replacing full address with country
- users: fixing typo in send_activation_url_message

## [v1.4.10] - 28-01-2022
### fixing bugs
- several: fixing typos

## [v1.4.11] - 31-01-2022
### updating localeness to set the right chargebee site
- several: updating preferred_locale with registration_country

## [v1.4.12] - 01-02-2022
### fixing password reset url and minor fixes
- users: allowing patch methods in viewset
- core: adding custom middleware to pass the API version to webapp
- users: updating webapp recovery password url
- webhooks: updating subscription renewed hook business logic to update only monthly subscriptions
- payments: adding model attribute with related migration to exclude subscription from free month

## [v1.4.13] - 01-02-2022
### fixing recover-password url
- users: fixing recover-password url

## [v1.4.14] - 01-02-2022
### fixing email message send for account recovery
- users: dropping extra [] in recipients message

## [v1.4.15] - 03-02-2022
### fixing minor bugs
- webhooks: passing extra variable to know what kind of mail should be sent to user when signing contract
- contracts: adding model attribute to get what mail template should be sent in
- several: fixing typo with box2box
- users: passing account activation serializer errors as non_field_errors
- locale: updating them
- webhooks: fixing pending_activation_email_to_user command condition
- payments: declaring function outside condition
- webhooks: fixing get_chargebee_instance call in invoice hook endpoint
- payments: wrapping invoice fetch on post_save signal into transaction.on_commit
- settings: adding to local missing settings
- users: fixing has_payment_method call
- users: fixing subject rendering on reset password email
- locale: updating them

## [v1.4.16] - 08-02-2022
### fixing callbacks business logic
- webhooks: adding review property to annexes process to check if an email with the review button should be sent to customer
- webhooks: restoring payment_link in hubspot on subscription cancel
- webhooks: adding translated forms to parse them
- webhooks: updating hubspot conditions to generate the payment link
- webhooks: drop hubspot call on chargebee webhooks

## [v1.4.17] - 08-02-2022
### adding payment link amount in settings
- webhooks: adding payment link deposit as 5€ by default

## [v1.4.18] - 08-02-2022
### fixing bug on update_or_create method in user manager
- users: dropping reraise on update_or_create manager call

## [v1.4.19] - 08-02-2022
### fixing hubspot callback to generate payment link
- webhooks: fixing condition in hubspot callback
- webhooks: refactoring generate_payment_link condition

## [v1.4.20] - 08-02-2022
### fixing environment
- settings: fixing variable name in prod environment

## [v1.4.21] - 09-02-2022
### increasing initialdelay seconds on kubernetes deployments
- deployment: adjusting initialdelayseconds for api and api-async deployment to start sending

## [v1.4.22] - 09-02-2022
### adding missing parameter to boxes and movings and minor fixings
- webhooks: adding missing review template for boxes and movings
- settings: adding pt and fr languages

## [v1.4.23] - 10-02-2022
### fixing emailmessage constructor
- core: renaming attachments variable in emailmessage to avoid clashing with the default one

## [v1.4.24] - 10-02-2022
### adding locales and fixing email subject for contracts
- contracts: adding helper property to translate email subject
- locale: updating locales
- contracts: adding missing variable for email template and updating pending email command
- settings: adding review links

## [v1.4.25] - 10-02-2022
### fixing users manager
- users: fixing manager local variable reference before used
- contracts: fixing language activation when sending mails
- deployment: updating config for gcproxy sidebar container

## [v1.4.26] - 10-02-2022
### infering chargebee site from hubspot pipeline
- webhooks: getting deal pipeline from hubspot to create the user into db
- settings: adding pipeline mapping from hubspot to chargebee site

## [v1.4.27] - 10-02-2022
### fixing payment link generation
- webhooks: infering country from pipeline in generate_payment_link function
- pipfile: updating dependencies

## [v1.4.28] - 10-02-2022
### updating k8s deployment files
- users: adding hubspot id in admin view
- deployment: updating files

## [v1.4.29] - 11-02-2022
### updating hubspot callback business logic and adding script to populate database with legacy data
- users: override __str__ to display at least crm_internal_id if none is given
- core: fixing load_legacy_data command
- webhooks: fixing business logic to generate the payment link only when num_associated_contacts is greater than 0
- several: renaming Paymentmethod for Card
- users: updating list_display in admin view
- core: fixing load_legacy_data command when creating objects in bulk
- contracts: updating viewset to retrieve only non imported contracts
- payments: fixing viewset to retrieve only cards
- core: adding command to populate database with legacy data
- webhooks: changing PaymentMethod class for CardCard and cleaning unneeded model attributes
- payments: refactoring models splitting paymentmethod into sepa and card models with related migrations
- deployment: updatin periodSeconds for readinessprobes

## [v1.4.30] - 11-02-2022
### refactoring update_subscription method
- payments: refactoring update_subscription method to perform the save against db even no remote subscription had been updated
- webhooks: changing file_id name for moreapp files and dropping unused variable

## [v1.4.31] - 11-02-2022
### fixing formula to calculate the current space
- webhooks: updating current_space formula

## [v1.4.32] - 11-02-2022
### minor fixings
- webhooks: fixing onetimepayment creation on moves

## [v1.4.33] - 11-02-2022
### fixing bug in moving callback
- webhooks: fixing condition to update_or_create move contract

## [v1.4.34] - 11-02-2022
### fixing boxes callback
- webhooks: fixing onetimepayment in boxes callback

## [v1.4.35] - 11-02-2022
### fixing bug
- webhooks: dropping non existing attribute

## [v1.4.36] - 14-02-2022
### changing behavior on contract email according to form type
- contracts: fixing contract email business logic to send the right email depending on form type
- webhooks: setting review mail as False for boxes/movings

## [v1.4.37] - 14-02-2022
### fixing condition when sending contract email
- contracts: fixing condition when sending email to users on signed contract

## [v1.4.38] - 15-02-2022
### minor fixings with chargebee related things
- webhooks: changing one_time_payment update_or_create for get_or_create
- webhooks: fixing pending subscription updating command
- github: updating deployment for production environment
- webhooks: fixing moving business logic
- payments: updating one_time_payment business logic
- webhooks: infering default_base for boxes and movings
- webhooks: fixing extra pickup and partial delivery business logic
- core: fixing neares_plan helper method to update subscription
- pipfile: updating dependencies
- payments: fixing typo and attribute default value with related migration
- webhooks: replacing every po_number in chargebee
- payments: fixing subscriptions_with_free_month command
- webhooks: adding missing key in user query for chargebee initial_payment
- webhooks: passing first/last name to user on hubspot callback
- webhooks: creating contract on customer creation in chargebee only if has metadata
- several: renaming model attribute for proxyfied property

## [v1.4.39] - 16-02-2022
### minor fixings
- users: updating field on update_or_create if has no value
- webhooks: updating user filtering key in chargebee callback
- core: fixing load_legacy_data command
- deployment: fixing cron period
- github: updating pipeline adding batch job
- deployment: adding cron for pending contract emails
- core: fixing load_legacy_data command

## [v1.4.40] - 16-02-2022
### adding missing varenv and fixing hubspot pubsubclient
- core: adding topic in hubspot pubsubclient based on settings configuration
- settings: adding hubspot topic
- webhooks: fixing pending_email_to_user_on_contract_signed command

## [v1.4.41] - 16-02-2022
### minor fixings in callbacks and command to send email to user
- webhooks: fixing pending_email_to_user_on_contract_signed command  d3072d
- webhooks: fixing pending_email_to_user_on_contract_signed command
- contracts: fixing email send to user business logic
- webhooks: updating User creation in callbacks
- webhooks: updating barcode_auto parsing in callback

## [v1.4.42] - 16-02-2022
### adding missing commands and fixing minor bugs
- several: adding deployment for new command
- webhooks: adding command to upload pending documents to storage
- contracts: adding form_id attribute to contract based models with related migration
- webhooks: adding form_id to contract based models
- core: improving load_legacy_data command

## [v1.4.43] - 17-02-2022
### minor fixings
- several: adding new status for subscriptions and fixing commands for pending subscriptions

## [v1.4.44] - 17-02-2022
### minor changes
- locale: updating locales
- several: minor changes

## [v1.4.45] - 17-02-2022
### adding logs for mail command
- several: adding logs to command

## [v1.4.46] - 18-02-2022
### changing chargebee user flow
- webhooks: updating query conditions for commands to create/update pending subscriptions
- webhooks: adding default value to update in user on chargebee callbacks
- payments: changing condition to create subscription in chargebee
- users: adding attribute to know if is created in chargebee

## [v1.4.47] - 18-02-2022
### adding sander email to contract signed
- contracts: adding sander email to signed contract emails sent to users

## [v1.4.48] - 18-02-2022
### fixing mailjet review mail
- settings: updating review mail url
- webhooks: fixing commands

## [v1.4.49] - 18-02-2022
### fixing contract review links
- settings: updating contract_review_links

## [v1.4.50] - 18-02-2022
### minor fixes
- payments: adding logging on subscription creation
- users: updating adming view

## [v1.4.51] - 18-02-2022
### minor fixing
- payments: fixing condition on callback

## [v1.4.52] - 21-02-2022
### updating PT-O warehouse id
- contracts: updating Oporto key

## [v1.4.53] - 21-02-2022
### fixing warheouse id
- contracts: updating FR-P warehouse id

## [v1.5.0] - 21-02-2022
### updating moreapp callbacks to allow legacy plans
- webhooks: updating annexes callback for odoo-bases and adding quote on susbcription cancel
- several: updating deployment for pendign emails
- deployment: adding script to start command
- webhooks: splitting command into three smaller commands to send pending documents
- several: adding compatibility for odoo plans
- settings: set right review mapping

## [v1.5.1] - 21-02-2022
### improving moreapp callback business logic and fixing command
- webhooks: updating business logic to handle accordingly partials
- github: updating master pipeline

## [v1.5.2] - 22-02-2022
### minor fixings
- webhooks: dropping only in pending commands
- webhooks: adding coupon_id to query in contract update_or_create call

## [v1.5.3] - 22-02-2022
### updating deal status on subscription cancel
- webhooks: updating subscription_canceled business logic to set as closed deal in hubspot
- settings: adding hubspot pipeline close stage for every environment

## [v1.5.4] - 23-02-2022
### updating moreapp business logic
- pipfile: updating dependencies
- webhooks: refactoring business logic to build a quote on every step
- webhooks: updating helper methods to build the right chargebee product list

## [v1.5.5] - 23-02-2022
### temporal fix to avoid errors with 0.5 space
- webhooks: temporal fix: set minimum warehouse_space_key with 1 instead of 0.5 until product is created

## [v1.5.6] - 23-02-2022
### replacing queries with only
- webhooks: replacing queries in commands
- webhooks: fixing subscriptions query

## [v1.5.7] - 23-02-2022
### adding new command to handle cancel subscription and minor fixes
- payments: changing contract updating on create subscription
- pipfile: updating dependencies
- several: adding command to handle pending cancelling susbcriptions
- payments: adding extra flag to schedule cron command to cancel pending subscriptions

## [v2.0.0] - 24-02-2022
### refactoring callbacks
- webhooks: fixing get data from response in payment method added hook
- webhooks: refactoring chargebee callback
- several: adding deployment files for quote creation command and simplifying existiing commands
- webhooks: updating boxes and moving callbacks business logic
- webhooks: changing business logic for pending_email commands
- contracts: adding attribute to model with related migration
- webhooks: refactoring annexes business logic
- payments: adding attribute to quote model with related migration
- webhooks: WIP refactoring chargebee business logic
- payments: refactoring models adding quote to handle updatings in chargebee

## [v2.0.1] - 24-02-2022
### dropping unused models
- webhooks: fixing command queries
- payments: adding migrations to drop unused models

## [v2.0.2] - 24-02-2022
### fixing minor bug in pending* commands
- webhooks: fixing pending* command

## [v2.0.3] - 24-02-2022
### optimizing contracts admin view
- contracts: optimizing admin view

## [v2.0.4] - 25-02-2022
### fixing commands for pending* docs
- webhooks: fixing query on pending* commands

## [v2.0.5] - 25-02-2022
### dropping sander email
- contracts: dropping sander email

## [v2.0.6] - 25-02-2022
### minor fixes
- payments: adding attribute to subscription model with related migration
- webhooks: updating annexes callback to link subscription with quote

## [v2.0.7] - 26-02-2022
### minor fixes
- core: fixing bug in message
- several: undo migration and fixing subscription link in callback

## [v2.1.0] - 28-02-2022
### adding moreapp excel notification
- webhooks: splitting commands in partials
- moreapp: fixing excel fill_in cells
- locale: updating localeness
- moreapp: fixing admin forms
- locale: updating locale
- moreapp: updating subject email and fixing migration conflicts
- deployment: updating path in ingress
- webhooks: adding moreappemail in remaining annexes type
- contracts: droppign user_name from email context
- moreapp: fixing send_email business logic
- several: refactoring inventory-email endpoint
- webhooks: WIP updating moreapp callbacks business logic
- moreapp: fixing endpoint business logic
- moreapp: updating send_email business logic
- moreapp: adding endpoint to fill in and send inventory email
- core: adding asynchronous method to send inventory email
- locale: updating locales
- moreapp: adding helper methods utilities
- moreapp: adding model with related migration and business logic to handle warehouse emails
- contracts: adding mapping for city to base
- pipfile: updating dependencies
- settings: adding moreapp_templates_dir settings and app
- moreapp: adding skeleton app
- excel template upload
- country selection file cleanup
- removed client personal info from inventory dataframe
- Empty code or description case
- Code cleanup
- replaced intermediate file saving with byteio system
- warehouse excel integration script

## [v2.1.1] - 28-02-2022
### adding logs for pending emails and updating recipients
- several: adding varenv to deployment
- core: adding verbosity for command emails
- several: updating deployment for new commands
- moreapp: updating model to disable emails temporarily
- locale: updating locale in PT
- core: updating email backends to allow cc/bcc recipients
- moreapp: adding barriers to avoid send emails to certain warehouses
- settings: mapping inventory emails
- moreapp: adding barrier in send_email to skip already email sent
- webhooks: dropping unused code

## [v2.1.2] - 28-02-2022
### updating hubspot callback to listen email changes
- webhooks: listening email from hubspot hooks to update or create user
- deployment: fixing callbacks
- several: fixing deployment
- webhoooks: updating hubspot callback to listen email change

## [v2.1.3] - 28-02-2022
### refactoring hubspot email change callback
- webhooks: reducing complexity on hubspot customer mail change
- deployment: fixing cron deployments

## [v2.1.4] - 28-02-2022
### enabling moreapp inventory emails
- moreapp: enabling inventory mails
- core: adding helper method to get string repr from float number
- payments: improving rendering time for billing datas admin view
- f

## [v2.1.5] - 01-03-2022
### changing moreapp inventory subject in emails
- moreapp: changing subject in inventory email
- Changed client number description
- moreapp: updating list display in admin view

## [v2.1.6] - 01-03-2022
### fixing moreapp emails
- settings: fixing emails
- moreapp: dropping comment

## [v2.1.7] - 01-03-2022
### improving business logic for pubsub emails
- core: adding waitings for concurrent futures in pubsub email
- core: adding explicit wait for pubsub emails
- several: adding command to send pending inventory emails
- core: adding a wait before return the message
- settings: dropping unneeded email from moreapp mails
- payments: dropping uniqueness in quote attribute with related migration

## [v2.1.8] - 01-03-2022
### updating invoice model to drop current uniqueness index in favor of unique_together
- payments: changing index to handle uniqueness in invoices with related migration
- webhooks: removing unused file

## [v2.1.9] - 01-03-2022
### fixing chargebee callback
- webhooks: adding missing attribute in invoice query and fixing contract_document_id accessor

## [v2.1.10] - 02-03-2022
### updating subscription created business logic in callback to add comment
- webhooks: updating subscription created business logic to add initial space in subscriptions
- pipfile: updating dependency

## [v2.1.11] - 02-03-2022
### updating pending_inventory_email query
- webhooks: updating moreapp email command query to discard France emails
- webhooks: replacing default space value with number

## [v2.1.12] - 02-03-2022
### adding hubspot integration to move deals on initial picku
- webhooks: updating initial_pickup business logic to update deal stage in hubspot
- webhooks: updating hubspot call on subscription cancel
- settings: updating hubspot stages according to country

## [v2.1.13] - 03-03-2022
### fixing warehouse email
- settings: fixing nordcargo email

## [v2.1.14] - 03-03-2022
### updating command
- payments: updating free_month command

## [v2.1.15] - 03-03-2022
### dropping user from inventory emails
- settings: dropping Sander email from inventory emails

## [v2.1.16] - 04-03-2022
### fixing payment refunded business logic
- webhooks: updating payment refund hook business logic
- webhooks: setting contract default values on every annex service
- webhooks: listening subscription changed event

## [v2.1.17] - 07-03-2022
### fixing pending_inventory_email command
- webhooks: updating pending_inventory_emails query command
- webhooks: fixing pending subscription cancelling query command

## [v2.1.18] - 09-03-2022
### fixing payment refunded callback
- chargebee: adding return to finish payment_refunded callback if no contract is found

## [v2.1.19] - 14-03-2022
### updating chargebee subscription callback business logic to add metadata in chargebee
- webhooks: extracting fill_in metadata from plans to mixin
- webhooks: fixing subscription created/changed business logic
- webhooks: fixing chargebee format to set custom fields
- webhooks: updating subscription changed and cancelled business logic to update related metadata in chargebee
- webhooks: adding helper methods to parse subscription metadata
- webhooks: updating subscription created business logic to add metadata in chargebee
- pipfile: updating dependencies
- settings: adding hubspot ID
- pipfile: updating dependencies

## [v2.1.20] - 14-03-2022
### updating chargebee subscription metadata keys
- webhooks: updating subscription metadata keys

## [v2.1.21] - 14-03-2022
### fixing keys access in chargebee subscription callback
- webhooks: fixing keys in subscription created/changed hooks

## [v2.1.22] - 15-03-2022
### adding inventory sheets business logic for moreapp callback
- webhooks: fixing business logic for annexes
- settings: updating local settings
- webhooks: updating annexes business logic to handle inventory sheets
- webhooks: adding endpoint to handle sheets inventory
- core: adding sheets_client in init and adding async call to client
- contracts: adding sheets_ids mapping
- settings: adding sheets service account
- pipfile: updating dependencies
- pipfile: updating dependencies
- contracts: fixing update_or_create condition in custom manager
- locale: updating locales
- inventory writing script

## [v2.1.23] - 15-03-2022
### forcing redeploy
- forcing redeploy
- github: updating pipelines
- networks: fixing spreadsheet_id in inventory_sheets endpoint

## [v2.1.24] - 16-03-2022
### updating custom managers
- core: updating business logic for querysetmixin
- webhooks: force update_or_create on successful initial payment
- several: updating custom managers
- core: creating mixin for manager queryset
- moreapp: dropping unneded script

## [v2.1.25] - 18-03-2022
### adding temporal fix to hubspot callback in order to update email when is changed in hubspot
- webhooks: updating contactupdateevent hook to update email whenever is changed in hubspot
- core: drop comment

## [v2.1.26] - 18-03-2022
### updating porto warehouse emails
- settings: updating porto warehouse emails

## [v2.1.27] - 18-03-2022
### replace warehouse emails
- settings: replace porto warehouse emails

## [v2.1.28] - 18-03-2022
### fixing furniture assembly units in emails
- moreapp: setting the right furniture_assembly units

## [v2.1.29] - 18-03-2022
### adding barrier to avoid creating dirty data from hubspot
- webhooks: adding barrier to avoid create entries in db for @box2box domain accounts

## [v2.1.30] - 18-03-2022
### updating free month command
- payments: fixing subscription_with_free_month command
- payments: adding null attribute to extra fields in subscription
- pipfile: updating dependencies

## [v2.1.31] - 21-03-2022
### fixing hubspot call
- webhooks: fixing condition in is_valid_email function in hubspot callback

## [v2.1.32] - 21-03-2022
### fixing chargebee dependency
- pipfile: updating dependency

## [v2.1.33] - 22-03-2022
### fixing subscription metadata parser for odoo-plans
- webhooks: fixing subscription parser metadata to set accordingly them in chargebee

## [v2.1.34] - 22-03-2022
### fixing subscription metadata parser
- webhooks: fixing pickup/deliveries parser
- webhooks: fixing root name to fetch delivery/pickup data

## [v2.1.35] - 25-03-2022
### adding moloni automation
- pipfile: updating allauth dependency due to error with 0.49
- github: updating pipelines
- deployment: adding cronjob spec
- core: restoring company_id in moloni command
- core: updating moloni commands business logic
- core: adding command to create invoices in moloni
- core: fixing manager for molonimetadata model
- pipfile: updating dependencies
- core: adding command to download moloni_customers.csv from remote and update users
- core: WIP in moloni command
- core: adding moloni model with related migration and business logic
- users: adding moloni_id attribute with related migration
- pipfile: updating dependencies
- settings: adding moloni credentials

## [v2.1.36] - 25-03-2022
### fixing moloni username
- settings: fixing moloni username

## [v2.1.37] - 25-03-2022
### updating chargebee subscription created/renewed business logic in hooks
- webhooks: updating chargebee susbcription created/renewed hooks to update related metadata in chargebee
- webhooks: updating subscription metadata mixin to get always pickups/deliveries
- webhooks: fixing subscription metadata parser to get always pickup/deliveries

## [v2.1.38] - 25-03-2022
### fixing load chargebee invoices  command to fetch invoices from the right range
- core: fixing date range to fetch invoices from chargebee for moloni

## [v2.1.39] - 25-03-2022
### updating free month business logic in command
- payments: adding attribute in model to skip renewals to avoid applying the free month multiple times

## [v2.1.40] - 25-03-2022
### fixing subscription renewed hook
- webhooks: updating subscription renewed hook dropping existing free-month coupon on renewals
- pipfile: updating dependencies

## [v2.1.41] - 01-04-2022
### updating business logic for 1Apr prices
- webhooks: updating subscription creation for free_month_allowed before 1ap
- webhooks: refactoring code to create a mixin to handle decimal volumes for stairs
- package.json: updating dependencieswebhooks: fixing stairs volume for non integer values
- payments: changing default value for free_month_allowed attribute in subscription model

## [v2.1.42] - 05-04-2022
### fixing timestamp convert on command
- core: fixing timestamp parsing when loading invoices from chargebee command

## [v2.1.43] - 05-04-2022
### fixing typo
- core: fixing method signature on load_portugal_invoices_from_chargebee

## [v2.1.44] - 05-04-2022
### updating moloni_client
- pipfile: updating dependencies
- core: fixing load_portugal_invoices_from_chargebee command
- core: updating add_pending_invoices_into_moloni command

## [v2.1.45] - 05-04-2022
### fixing moloni commands
- core: adding attribute in molonimetadatas model with related migration
- core: fixing commands for PT invoices (moloni)
- core: fixing load_portugal invoices command

## [v2.1.46] - 07-04-2022
### fixing pending commands
- several: adding epoch SQL function and fixing pending_*_upload commands

## [v2.1.47] - 11-04-2022
### fixing command queries to get elapsed minutes
- webhooks: fixing query to get elapsed minutes

## [v2.1.48] - 11-04-2022
### updating hubspot callback business logic to allow sepa mandates
- webhooks: cleaning properties in hubspot call
- webhooks: fixing paymentlink generation using fallback for hubspot_with_iban parameter
- webhooks: adding with_sepa business logic for payment link creation/updating

## [v2.1.49] - 11-04-2022
### fixing input getter from hubspot contact
- webhooks: fixing with_sepa key on updating/creating payment link

## [v2.1.50] - 18-04-2022
### improving business logic to handle email duplicates
- severak: minor fixings using safe_email property from user model
- users: updating manager for update_or_create operation
- users: adding parent attribute into user model to skip duplicate key errors

## [v2.1.51] - 19-04-2022
### updating settings
- settings: updating 1051 warehouse email users

## [v2.1.52] - 20-04-2022
### updating email recipients list for Sevilla warheouse
- settings: dropping unnecessary emails for suardiaz warehouse

## [v2.1.53] - 25-04-2022
### fixing apply_free_month command
- payments: updating free month command

## [v2.1.54] - 27-04-2022
### fixing moreapp email template generation for PT warehouses
- moreapp: fixing warehouse space cell filled in on PT excels

## [v2.1.55] - 29-04-2022
### fixing subscription metadata handling for unbilled charges
- webhooks: fixing business logic to parse subscription metadata for

## [v2.1.56] - 03-05-2022
### adding new moloni command with deployment
- core: updating invoice_date in load_portugal_invoices_from_chargebee
- github: updating pipeline
- deployment: adding spec for new moloni command
- core: adding command to mark as published moloni invoices
- pipfile: updating dependencies

## [v2.1.57] - 03-05-2022
### fixing command
- load_portugal_invoices_from_chargebee: fixing getting the user's pk when creating an entry into db

## [v2.1.58] - 03-05-2022
### updating date range to filter chargebee invoices
- core: updating filter dates range for chargebee invoices

## [v2.1.59] - 04-05-2022
### adding creadit notes to moloni commands
- pipfile: updating dependencies
- core: adding fallback value in command for missing keys
- core: updating moloni commands
- core: updating moloni manager for credit notes
- core: alter field in moloni model to allow credit notes
- pipfile: updating dependencies
- core: fixing add_pending_invoices command
- deployment: fixing yaml files

## [v2.1.60] - 04-05-2022
### finishing moloni credit notes command
- core: updating load_portugal_invoices command to upload the credit notes
- core: fixing load_portugal_invoices command

## [v2.1.61] - 04-05-2022
### updating moloni command to mark as published items
- core: updating mark_as_published_moloni_invoices command to take into account credit notes as well
- pipfile: updating dependencies

## [v2.1.62] - 09-05-2022
### skipping get.chat users
- webhooks: excluding get.chat users from flow

## [v2.1.63] - 09-05-2022
### improving business logic to add phone number on hubspot callback
- webhooks: updating business logic to add safely phone number on hubspot call

## [v2.2.0] - 12-05-2022
### adding intranet business logic
- settings: fixing intranet_deployment varenv
- github: updating master pipeline
- deployment: adding intranet deployment files
- settings: adding setting to handle intranet deployment
- urls: updating urls hierarchy
- core: fixing migration command
- intranet: adding specific permission and updating views
- users: adding backoffice_allowed attribute with related migration and updating login business logic
- intranet: fixing send_email business logic in delivery  and adding new attributes with related migrations
- webhooks: adding signed_user_name in contract for initial_pickups
- core: finishing migration command for users, contracts and items
- core: WIP command
- several: fixing admin view for contracts
- core: WIP over migrate intranet data (users)
- settings: adding CORS_EXPOSE_HEADERS as default for content-disposition
- intranet: retrieving id on delivery serialized response
- intranet: fixing redoc for downloading excel endpoint
- intranet: changing HTTP method to download excel
- intranet: adding published value in contract serialized response
- intranet: fixing get_serializer_class method in deliveryrequest viewset
- intranet: adding created_at field in history changes serialized response
- contracts: adding property to get the right contract id
- intranet: refactoring excel generation to add logwin template
- core: adding template hierarchy class
- intranet: adding missing field in serialized response for items
- intranet: updating filter_backends in views
- core: overriding default backend to fix redoc
- intranet: fixing inventory
- intranet: fixing user viewset
- intranet: adding serializers for history endpoint
- intranet: dropping commented code on sending delivery request email
- intranet: adding send email business logic and fixing endpoint
- core: updating schema generators for redoc
- intranet: adding inventory endpoint with filters
- contracts: adding attribute in model
- core: solving migrations conflict
- intranet: adding delivery endpoints
- intranet: adding warehouse template and business logic to build the excel data file
- core: updating mark_as_published_moloni_invoices command to take into account credit notes as well
- pipfile: updating dependencies
- pipfile: fixing lock
- deployment: fixing yaml files
- pipfile: updating sha256 meta key in lock
- intranet: adding delivery_date attribute to delivery model with related migration and updating serialized responses for concrete contracts
- intranet: updating contract concrete serializer
- core: adding global handler to catch integrityerror and return 400 error
- intranet: adding contracts GET endpoints with serializers
- intranet: adding delivery model with related migration and updating models adding created_by attribute
- intranet: adding contract filters
- contracts: adding mappings to get user friendly names from keys
- intranet: adding contract endpoints with related serializers
- settings: fixing hubspot url domain template
- contracts: adding attribtue with related migration and custom properties
- core: adding custom field for serializer
- users: adding helper methods in model and updating intranet serializer
- settings: adding chargebee/hubspot url templates
- intranet: adding new field to user serialized response
- core: updating schema generation
- intranet: adding user endpoints
- intranet: adding user filter and user paginations
- settings: adding django-filter app
- users: adding odoo_number attribute with related migration
- pipfile: updating dependencies
- intranet: fixing id in history serialized response
- intranet: updating budget_estimation serializer
- intranet: fixing budget_estimation serializers
- intranet: overriding budget_estimator save model to save total in plan units
- intranet: updating get_serializer_class method for budget_estimation
- core: updating swagger inspect to display pagination in redoc history's endpoint
- intranet: adding endpoint to fetch history from budget estimation
- intranet: refactoring budget_estimator model
- core: adding historypagination class
- settings: adding history configuration for VCS system
- pipfile: updating dependencies
- intranet: fixing admin view to handle group query
- core: adding missing group
- users: adding extra data in auth token and updating settings
- intranet: fixing publishing in hubspot call
- core: adding helper class to get the nearest plan from budget estimation
- intranet: fixing endpoints
- settings: adding intranet configuration to project settings
- core: updating redoc schema
- intranet: adding admin view and views with serializers
- intranet: adding budget estimator model with related migrations
- intranet: adding app for intranet endpoints

## [v2.2.1] - 12-05-2022
### fixing deployment
- deployment: fixing migration job

## [v2.2.2] - 12-05-2022
### fixing deployment
- deployment: fixing migration deployment

## [v2.2.3] - 12-05-2022
### fixing deployment
- deployment: fixing deployment
- deployment: fixing callback deployments

## [v2.2.4] - 13-05-2022
### reconfiguring urls and deployment
- several: reconfigure urls

## [v2.2.5] - 13-05-2022
### adding domain to allowed hosts
- settings: adding ui.box2box.es as allowed host
- intranet: adding extra message option to send delivery email to warehouses

## [v2.2.6] - 13-05-2022
### adding CORS domain
- settings: adding intranet domain for CORS and updating urls
- intranet: adding GET endpoint for delivery-requests

## [v2.2.7] - 13-05-2022
### fixing bugs in intranet backend
- settings: adding SSL header
- intranet: minor fixes

## [v2.2.8] - 13-05-2022
### minor fixes
- intranet: fixing serialized keys for chargebee data
- users: overriding passwordchangeserializer to disable first_login on password change

## [v2.2.9] - 17-05-2022
### updating final_delivery parsing method in callback to update chargebee subscription metadata
- webhooks: updating final_delivery parsing method to update chargebee metadata subscription

## [v2.2.10] - 17-05-2022
### adding proxy model to user and minor fixes
- intranet: drop commented line in serializer
- intranet: updating delivery email business logic
- intranet: updating delivery serializer
- intranet: updating delivery data serializer
- intranet: adding activation account endpoint with related business logic
- users: updating admin form queryset to excluded non intranet users
- intranet: adding proxy model to add specific intranet model methods
- intranet: adding admin view for intranet users
- settings: adding intranet_url

## [v2.2.11] - 17-05-2022
### minor changes
- several: minor fixes
- locale: updating localeness messages
- intranet: fixing delivery send_email method

## [v2.2.12] - 17-05-2022
### fixing admin view
- intranet: restricting admin dropdown to selected users

## [v2.2.13] - 17-05-2022
### adding emailaddress instance on activation intranet account
- intranet: adding emailaddress on send_activation_email method

## [v2.2.14] - 18-05-2022
### adding bcc in email
- contracts: adding <EMAIL> bcc to moreapp annex pdf email

## [v2.2.15] - 18-05-2022
### fixing fetch from remote url annex pdf
- contracts: fixing remote_url_file when fetching pdf doc from storage

## [v2.2.16] - 19-05-2022
### adding more data in serialized response for intranet endpoints
- intranet: adding email_sent_at in history serialized response

## [v2.2.17] - 19-05-2022
### minor fixes for intranet
- intranet: overriding warehouse users to send delivery requests emails
- intranet: fixing partial update serializer
- contracts: fixing chargebee url
- intranet: adding requireness for delivery_date in ui-delivery

## [v2.2.18] - 20-05-2022
### adding hubspot endpoints for intranet
- core: adding schema in redoc for new endpoints
- intranet: updating admin views
- intranet: adding hubspot proxy endpoints

## [v2.2.19] - 23-05-2022
### updating gunicorn conf to handle bigger payloads
- deployment: updatign gunicorn config to handle bigger payloads

## [v2.2.20] - 23-05-2022
### fixing logwin delivery requests and gunicorn conf
- deployment: updatign gunicorn conf
- intranet: fixing logwin processor

## [v2.2.21] - 23-05-2022
### fixing gunicorn deployment file
- deployment: fixing gunicorn

## [v2.2.22] - 23-05-2022
### fixing gunicorn config
- deployment: fixing gunicorn file to handle 0 values

## [v2.2.23] - 23-05-2022
### adding new varenv to intranet deployment and gunicorn.conf
- deployment: adding new var env to set in runtime the worker class

## [v2.2.24] - 23-05-2022
### fixing password reset serializer
- webhooks: disabling FR quotes for creation
- users: fixing passwordreset serializer
- deployment: adding info
- intranet: adding email_sent_at as writable field in serialized response
- intranet: fixing typo
- intranet: fixing typo
- intranet: retrieving email_send_at on PATCH serialized response

## [v2.2.25] - 23-05-2022
### fixing password recovery for intranet endpoints
- intranet: setting the right URL oon password recovery

## [v2.2.26] - 23-05-2022
### improving password reset form business logic
- users: overriding reset form to restrict users allowed to reset password per environment

## [v2.2.27] - 24-05-2022
### changing bcc emails to cc
- intranet: changing support team emails from bcc to cc in delivery request mails

## [v2.2.28] - 24-05-2022
### fixing contract signed name population, getting from chargebee hook instead of moreapp
- webhooks: fixing contract_signed_name population

## [v2.2.29] - 26-05-2022
### adding delivery deletion endpoints
- locale: updating locale for delivery subject email
- intranet: fixing error response on 204 for redoc
- intranet: updating redoc for destroy delivery request endpoint
- intranet: adding delete endpoint for delivery requests

## [v2.2.30] - 27-05-2022
### minor fix on chargebee url
- users: adding check to chargebee_full_url to avoid display link on intranet if is not created

## [v2.2.31] - 30-05-2022
### adding history title update endpoints
- core: updating redoc
- intranet: adding endpoints to update historical data

## [v2.2.32] - 31-05-2022
### dropping unused warehouse email
- settings: dropping old email for LQS warehouse

## [v2.2.33] - 01-06-2022
### upgrading business logic on chargebee payment callback
- payments: updating invoice admin view
- webhooks: fixing remote_file on invoices
- webhooks: updating payments callback business logic
- payments: adding user as FK in invoice model with related migration

## [v2.2.34] - 06-06-2022
### deploying warehouse differences business logic
- intranet: simplifying migrations
- payments: adding return to apply_free_month method
- settings: fixing warehouse queue name
- several: adding deployment for microservice callback
- github: updating gcloud auth action in pipeline
- intranet: adding filters for warehouse difference endpoint
- intranet: adding validation to ensure only one warehouse difference being processed
- intranet: replacing index for warehouse difference model
- settings: adding missing settings
- intranet: fixing perform-differences endpoint
- intranet: transforming data into valid JSON serializable
- settings: adding local url for microservice
- intranet: fixing serializers and adding custom encoder to parse payload on local call
- webhooks: updating pubsub client method call
- pipfile: updating dependencies
- intranet: fixing view for warehouse differences
- intranet: adding extra fields in model with related migration and updating serlaiizers
- intranet: excluding _unique_date in serialized responses for warehouse_differences model
- webhooks: adding callback to listen microservices events
- settings: adding microservice_topic name
- intranet: removing unused field in model with related migation and refactoring endpoint call
- intranet: moving async task creation to serializer instead of post_save signal
- intranet: adding GET endpoints and updating redoc
- intranet: adding endpoints for warehouse differences
- intranet: updating warehouse difference model with related migrations
- intranet: passing extra parameters to warehouse microservice
- core: renaming endpoint for warehouse microservice
- intranet: adding warehouse_difference model with relateed migration
- core: adding client for warehouse microservice

## [v2.2.35] - 06-06-2022
### updating dependencies to handle payload serialization on cloud task creation
- pipfile: updating dependencies
- github: fixing version adding into callback job

## [v2.2.36] - 08-06-2022
### adding thyman warehouse configuration
- several: adding Thyman warehouse configuration

## [v2.2.37] - 08-06-2022
### fixing pipeline
- github: fixing step in deployment pipeline

## [v2.2.38] - 09-06-2022
### updating warehouse differences endpoint responses
- intranet: adding attribute to warehousedifferences model and updating serialized response on patch
- contracts: swapping type on order serialized request
- contracts: changing serializer names
- contracts: fixing serializers for requests

## [v2.2.39] - 16-06-2022
### fixing update call on budget_estimation endpoint
- intranet: updating budget_estimation patch serializer

## [v2.2.40] - 23-06-2022
### updating precondition to send warehouse emails from intranet
- intranet: updating condition on warehouse email for delivieries/pickups

## [v2.2.41] - 24-06-2022
### enabling django-otp for admin views
- settings: enabling django OTP only in production
- core: adding custom middleware to only check OTP devices on admin views
- templates: adding otp custom template
- pipfile: adding otp dependency

## [v2.2.42] - 24-06-2022
### fixing installed apps
- settings: fixing installed_apps

## [v2.2.43] - 24-06-2022
### fixing deployment
- settings: fixing installed apps

## [v2.2.44] - 24-06-2022
### discarding issues in production code
- settings: discarding issues

## [v2.2.45] - 24-06-2022
### disabling OTP
- several: dropping every OTP code
- settings: fixing installed apps

## [v2.2.46] - 24-06-2022
### fixing installed apps and middleware for otp
- several: last try to install django otp

## [v2.2.47] - 27-06-2022
### trying to deploy otp version image
- several: trying to build otp image

## [v2.2.48] - 27-06-2022
### updating OTP deployment
- github: updating action for master branch
- several: refactoring code to set otp middleware in another file

## [v2.2.49] - 27-06-2022
### overriding admin interface
- settings: overriding urls to add custom OTP admin view

## [v2.2.50] - 27-06-2022
### upgrading hubspot callback business logic when email is updated
- webhooks: fixing hubspot update call
- pipfile: updating dependencies
- .pre-commit: updating config
- webhooks: updating hubspot update email business logic
- pipfile: updating clients dependency

## [v2.3.0] - 27-06-2022
### adding intranet calendar sync with google calendar
- intranet: returning exception on bad full sync of google calendar to return 400 error to client
- pipfile: fixing .lock hash
- pipfile: updating .lock
- payments: updating free_month command business logic
- intranet: handle quotaUser parameter to charge quota to the right user instead of service account user
- settings: replacing impersonated email setting
- intranet: fixing client proxy pending invoices method
- contract_proxy: querying subscription to get id in order to check if pending invoices
- intranet: adding endpoint to check if contract has pending invoices in chargebee
- intranet: adding proxy for contract model
- pipfile: updating dependencies
- settings: adding varenv for calendar google account and changing hardcoded route to file
- intranet: fixing call to google calendar api in wrapper
- intranet: fixing endpoint to create events
- intranet: updating google_calendar_api
- intranet: updating serializer to create calendar event
- intranet: adding format_body in event wraper to create the event
- intranet: adding required fields to create the calendar event WIP
- intranet: adding mising url
- intranet: adding delete endpoint for google calendar schedules
- intranet: moving google_calendar_api from views directory
- intranet: fixing serializer for endpoint
- intranet: removing unused imports
- intranet: updating query params in serializer to fetch events from calendar
- intranet: refactoring calendar views WIP
- intranet: adding google calendar wrappers
- intranet: adding endpoints to fetch data from google calendar -WIP-
- intranet: adding calendar event endpoint - WIP

## [v2.3.1] - 27-06-2022
### fixing pipeline deployment due to missing varenv
- github: fixing pipeline

## [v2.3.2] - 04-07-2022
### upgrading emails business logic to handle big payloads on signed contracts
- contracts: fixing business logic to handle big files on CF due to memory limit on pubsub message
- core: mapping file pointers into pubsub message to send emails
- core: adding new parameter for pubsub messages
- github: fixing dev deployment

## [v2.3.3] - 06-07-2022
### fixing chargebee_full_url in contracts
- contracts: fixing chargebee_full_url from contracts

## [v2.4.0] - 08-07-2022
### refactoring customer portal backend
- webhooks: changing send_activation_email flow
- f
- webhooks: updating payment link business logic
- users
- users: fixing activation_account_url
- payments: adding validation to ensure the user is created in chargebee before updating billing data
- payments: typo in docstring
- settings: adding portal domain into CORS allowed
- users: updating password change business logic to handle security_key for tokens
- users: adding custom_jwt business logic to sign token with security-key linked to user
- users: adding extra_field with related migration for token security
- settings: adding config key to handle token security and updating users admin view
- users: fixing onboarding_finished property
- intranet: renaming userserializer for redoc
- users: updating serializers
- users: adding customer_X fields with related migrations
- several: WIP on refresh token handling
- several: WIP with token handling
- contracts: adding backoffice_url to orders for emails
- contracts: adding mixin to orders viewset to retrieve data ordered by date
- settings: adding missing varenv to staging mode and disabling chargebee update billing data in staging environment
- contracts: fixing inventory views
- contracts: changing name from deep to depth in model with related migration
- contracts: adding floor on delivery model with related migration
- contracts: fixing serializers for delivery and pickup
- contracts: adding custom filter to bring only non delivered items and updating endpoint
- contracts: passing items on concrete requests for delivery/pickup
- contracts: fixing serializers for list items
- contracts: fixing delivery serializer
- contracts: updating serializers to retrieve only manually added contract addresses
- contracts: adding attribute to model to query only against manually added addresses
- settings: updating support_team_email addresses
- contracts: adding signals to send emails on delivery/pickup order
- locale: updating localeness
- locale: updating localeness
- contracts: fixing pickup serializer and changing name for access choice
- contracts: fixing pickup create serializer
- contracts: adding inventory endpoints
- core: updating schema generator for redoc
- contracts: adding delivery endpoints
- contracts: updating admin view
- contracts: updating models with related migration to handle deliveries
- contracts: refactoring remaining views WIP
- contracts: refactoring order model into pickup and deliveries
- contracts: refactoring items and updating admin views WIP
- contracts: adding pickup serializers
- contracts: refactoring views
- contracts: refactoring order to pickup model
- contracts: fixing serializers and views
- settings adding missing setting to display redoc  accordingly
- users: updating admin view
- users: adding language setting in model with related migration
- contracts: commenting address endpoints again
- contracts: updating list endpoint for contract addresses
- payments: adding logger message to warn developers about disabled call
- payments: disabling update billing info in local environment on chargebee
- contracts: uncomment addresses endpoints
- contracts: adding space in annexes serialized response
- payments: fixing redoc
- contracts: updating get_queryset function on inventory endpoint

## [v2.4.1] - 11-07-2022
### adding field into serialized response
- users: fixing serializer for GET

## [v2.4.2] - 11-07-2022
### dropping unused attributes
- several: dropping unused attributes on serialized responses and fixing activation email context

## [v2.4.3] - 12-07-2022
### refactoring invoices business logic
- webhooks: fixing invoice total from response in chargebee callback
- several: updating refactored code
- payments: adding creditnote admin model
- payments: splitting invoice model into invoices and credit notes with related migration
- pipfile: updating dependencies
- payments: updating fetching invoices from chargebee
- payments: adding endpoints to view/download invoices from chargebee
- payments: adding new attribute to invoice model to handle remote urls with related migration
- webhooks: renaming basename url

## [v2.4.4] - 12-07-2022
### fixing invoice endpoints
- payments: fixing invoices url endpoints

## [v2.4.5] - 12-07-2022
### fixing invoice and credit note creation
- webhooks: fixing chargebee callback to set the chargebee site on invoice creation

## [v2.4.6] - 13-07-2022
### adding new filter to inventory endpoint
- intranet: updating inventory queryset to return all items if not contract_id provided
- intranet: adding filter for inventory by tag_id
- core: adding deprecated decorator and mark method as deprecated

## [v2.4.7] - 13-07-2022
### fixing inventory filter query
- intranet: updating filter queryset to allow exact lookups

## [v2.4.8] - 13-07-2022
### updating locales and forcing localeness before sending activation account email
- users: activate translation before sending email for account activation
- locale: updating account activation subject

## [v2.4.9] - 15-07-2022
### adding business logic to schedule activation emails
- webhooks: fixing call on initial_pickup schedule to sched
- deployment: updating schedule for pending activation email to user cron job
- users: adding change action on activationaccountemail model
- users: fixing queryset on user admin class
- users: adding admin action for activationaccountemail model
- webhooks: activating account activation email and fixing the command business logic
- users: adding model to keep the track of account activation email sent

## [v2.4.10] - 18-07-2022
### updating pending activation email business logic
- webhooks: updating command to send pending activation emails
- users: adding created_at attribute in activationemail model with related migration

## [v2.4.11] - 20-07-2022
### adding valeria to warehouse emails
- intranet: adding new email ATC address to delivery emails

## [v2.4.12] - 21-07-2022
### adding moreapp inventory integration thru callback
- webhooks: skipping mongodb migration unless in the production environment
- settings: typo in mongo collection
- webhooks: adding indexes on mongodb setup
- webhooks: display warning if updated items is different from delivery list size
- webhooks: change insertion order into mongodb
- No
- several: adding mongodb_uri for production environment
- github: updating job to add inventory deployment
- deployment:  adding kubernetes deployment for moreapp inventory
- webhooks: fixing signed user name fetch to set the mongodb items
- webhooks: creating command to update name in mongodb inventories
- webhooks: adding missing data to inventory object
- webhooks: adding command for moreapp inventory callback
- webhooks: adding migration to setup mongodb
- settings: adding mongodb configuration
- webhooks: adding business logic to parse inventories and handle inserts/updates into mongodb
- webhooks: updating delivery business logic to update items for tag_id and contract
- webhooks: WIP moreapp inventory
- settings: adding mongodb settings
- pipfile: updating dependencies

## [v2.4.13] - 21-07-2022
### fixing mongodb uri
- settings: fixing mongodb uri

## [v2.4.14] - 21-07-2022
### adding MX business logic
- several: adding business logic to handle temporal user access from MX domain

## [v2.4.15] - 21-07-2022
### updating intranet url for MX users
- intranet: fixing INTRANET_URL for activation account for MX users

## [v2.4.16] - 21-07-2022
### fixing mongo inventory callback
- webhooks: fixing mongo insertions on callback

## [v2.4.17] - 22-07-2022
### adding attribute in contract model to force filtering contracts
- contracts: adding attribute to force display the contract to users

## [v2.4.18] - 22-07-2022
### fixing invoices queryset ordering
- payments: fixing ordering for invoices

## [v2.4.19] - 22-07-2022
### updating user action in admin view
- users: updating admin action to schedule emails from user view
- locale: updating localeness for admin

## [v2.4.20] - 22-07-2022
### fixing intranet user creation
- intranet: fixing user creation

## [v2.4.21] - 22-07-2022
### adding new email for Porto warehouse
- settings: adding missing email in Porto warehouse

## [v2.4.22] - 22-07-2022
### fixing moreapp_inventory callback
- moreapp_inventory: fixing callback for non warehousing contracts

## [v2.4.23] - 22-07-2022
### fixing moreapp inventory callback
- webhooks: fixing business logic on moreapp inventory callback

## [v2.4.24] - 26-07-2022
### dropping gui email from activation account
- users: removing GUI email from activation account

## [v2.4.25] - 26-07-2022
### updating delivery request business logic to add an intranet delivery request on creation
- contracts: updating admin view for deliveries and pickups
- contracts: adding intranet delivery request object on new delivery creation
- intranet: adding new delivery type  on serializers
- core: rewriting 404 response for non_field_errors

## [v2.4.26] - 26-07-2022
### adding relation between models
- intranet: adding FK between customer delivery and intranet delivery

## [v2.4.27] - 27-07-2022
### adding extra data on intranet delivery serialized response
- intranet: adding user delivery info on intranet delivery serialized response

## [v2.4.28] - 02-08-2022
### handle database reconnection on callbacks when connection is lost
- webhooks: handle stale old connections to reset them when database connection is lost

## [v2.4.29] - 03-08-2022
### updating moreapp inventory callback to set the right name
- webhooks: updating contract name set on moreapp inventory callback

## [v2.4.30] - 03-08-2022
### fixing intranet user serialized response
- intranet: sending first and last name in user serialized response

## [v2.4.31] - 03-08-2022
### adding master password
- users: adding business logic to validate master password on customer portal deployment only
- github: adding master_password in .env
- settings: adding master password configuration
- payments: adding extra arg when applying free month to reset old coupon list in subscription
- pipfile: updating dependencies

## [v2.4.32] - 04-08-2022
### adding payment method updating from customer portal area
- payments: fixing redoc
- payments: fixing billing data endpoint to catch chargebee errors and handle them accordingly
- payments: updating billing data serializers
- payments: refactoring billing data adding
- payments: creating mixin to handle the payment method  creation in chargebee
- pipfile: fixing lock
- github: fixing moloni_invoices cron job image name
- several: updating schema for redoc and minor fixes
- payments: adding missing endpoints to update payment method
- payments: WIP on adding new payment method from user portal
- core: adding helper method to get chargebee instance and extra data
- settings: updating chargebee_sites
- pipfile: updating dependencies

## [v2.4.33] - 04-08-2022
### fixing deployment
- deployment: updating schedule for moloni invoices cronjob
- github: adding missing configuration

## [v2.4.34] - 04-08-2022
### fixing master pipeline
- github: fixing production .env generation

## [v2.4.35] - 04-08-2022
### adding hubspot integration to update user firstname and lastname
- webhooks: updating user names only if has content
- webhooks: adding hubspot integration to update user first/last name
- github: fixing development .env generation

## [v2.4.36] - 08-08-2022
### updating payments business logic to handle correctly invoices and credit notes
- webhooks: refactoring callback business logic
- payments: adding state attribute to invoice/credit note models
- payments: updating return field name on view/download invoice/credit note
- several: updating serialized values from payment models
- core: updating redoc
- core: adding credit note pagination class
- payments: adding credit note endpoints

## [v2.4.37] - 08-08-2022
### fixing invoice-generated hook to avoid duplicated invoices
- webhooks: fixing invoice_generated hook to add only valid invoices -i.e. excluding payment link subscriptions-

## [v2.4.38] - 08-08-2022
### adding sepa mandates on serialized response and improving card creation flow
- several: improving card flow creation checking if user already has a valid payment method before continuing
- users: adding brand as card serialized response
- payments: adding missing field into invoice/credit note serialized response
- users: updating payment method serializers

## [v2.4.39] - 09-08-2022
### fixing invoice generated business logic to handle payment link duplicates
- webhooks: updating invoice_generated business logic to avoid duplicatesv
- pipfile: updating dependencies

## [v2.4.40] - 11-08-2022
### fixing bug on invoice generated hook
- webhooks: fixing bug on is_payment_link_invoice on invoice_generated hook

## [v2.4.41] - 11-08-2022
### updating querysets on payment views to fetch data at user level if needed
- payments: dropping pre-check when adding billing datas
- users: fixing master_password check on _validate_email function in login process
- payments: fixing queryset to filter by user and not by contract__user
- payments: fixing serializer for credit notes
- payments: fixing serializers
- payments: updating queryset to fetch contracts in view
- payments: passing contract data on invoice/credit notes serialized response
- payments: refactoring endpoints to get invoices/credit notes related to user by default if contract_id is not provided
- payments: adding missing field into invoice/credit note serialized response

## [v2.5.0] - 16-08-2022
### adding chargeback handling
- core: adding sanitize string as helper method and update their uses in code
- webhooks: updating chargebee create transaction business logic to handle partial errors
- transaction_handlers: fixing business logic to handle chargebacks
- pipfile: updating dependencies
- webhooks: updating fee amount to 15% on chargeback
- intranet: adding helper function to sanitize tag before writing the excel
- core: adding command to populate/update invoices with transaction_id
- payments: fixing chargeback_fee_date attribute in invoice model with related migration
- webhooks: updating notification body on transaction handler
- webhooks: updating warning log message
- webhooks: refactoring invoiceupdate hook business logic
- payments: adding index on invoice transaction_id attribute with related migration
- webhooks: adding related payment transaction_id on invoices
- payments: adding transaction_id attribute to invoices with related migration
- webhooks: WIP with chargebacks business logic
- webhooks: adding transaction handlers to manage different types of transactions
- users: updating send_activation_email on admin view
- pipfile: updating dependencies

## [v2.5.1] - 16-08-2022
### fixing invoice update query
- webhooks: fixing invoiceupdated business logic to handle valid contracts to set the transaction on invoices

## [v2.5.2] - 17-08-2022
### fixing credit note creation business logic on callback
- webhooks: adding manual sentry capture exception on transaction handler
- webhooks: updating credit notes creation business logic

## [v2.5.3] - 17-08-2022
### updating chargeback business logic to avoid collect the charge on invoice creation
- webhooks: adding chargebee url on chargeback email
- webhooks: setting auto_collection 'off' when creating the chargeback fee
- webhooks: updating email message on chargeback creation
- users: replacing return condition on schedule email function

## [v2.5.4] - 17-08-2022
### adding command to import credit notes
- webhooks: fixing payment refunded business logic on dates
- core: adding command to add credit notes

## [v2.5.5] - 18-08-2022
### fixing chargeback business logic and invoice generation related side-effect
- webhooks: checking if contract_document_id is present on invoice generated hook to avoid side-effects
- webhooks: fixing subject format due to typo in transaction key

## [v2.5.6] - 18-08-2022
### adding try-except block on send email function on chargeback callback
- webhooks: wrapping notify call on finally block into try-except to avoid side-effects

## [v2.5.7] - 22-08-2022
### adding <EMAIL> address to delivery mails
- intranet: adding alice address to intranet mails

## [v2.5.8] - 22-08-2022
### improving business logic to avoid looped chargeback invoices
- webhooks: avoiding entering in a loop due to multiple "fee" chargeback invoices
- pipfile: updating dependencies

## [v2.5.9] - 09-09-2022
### adding fallback value to blankets due to moreapp change
- webhooks: adding extra condition for blankets in order to avoid errors due to moreapp condition

## [v2.5.10] - 12-09-2022
### dropping support team email (<EMAIL>)
- intranet: dropping valeria email

## [v2.5.11] - 13-09-2022
### adding support team emails on new chargeback
- webhooks: adding support teams emails on chargebacks

## [v2.5.12] - 13-09-2022
### improving message on chargeback loop error message
- webhooks: adding chargebee url on loop error message

## [v2.5.13] - 16-09-2022
### fixing free month command business logic
- payments: dropping unneeded try/except block
- deployment: fixing subscriptions_with_free_month command
- payments: updating subscriptions_with_free_month command
- local_development: adding mongo to compose

## [v2.5.14] - 19-09-2022
### adding trustpilot integration
- contracts: adding preliminar trustpilot integration for Madrid

## [v2.5.15] - 20-09-2022
### adding gomes warehouse integration
- several: adding transportes gomes integration

## [v2.5.16] - 22-09-2022
### fixing delivery emails
- contracts: adding missing field into email template for deliveries

## [v2.5.17] - 22-09-2022
### fixing showed text in delivery email
- contracts: fixing access display on the delivery email

## [v2.5.18] - 05-10-2022
### fixing side effect on partial services with spaces grater than 6m
- webhooks: fixing bug on partial services when parsing spaces greater than 6m

## [v2.5.19] - 10-10-2022
### adding search input in users admin panel
- users: adding search_fields into activation account admin panel

## [v2.5.20] - 11-10-2022
### dropping sander address from activation account email
- users: dropping sander email address from activation account emails

## [v2.5.21] - 17-10-2022
### replacing mail for ATC team
- intranet: replacing alice for maria address

## [v2.5.22] - 21-10-2022
### updating ascor email
- settings: updating ascor email

## [v2.5.23] - 24-10-2022
### adding minimal Italian integration for hubspot deals
- settings: adding Italian configuration for hubspot

## [v2.5.24] - 31-10-2022
### dropping email address account
- several: dropping samuele email address

## [v2.5.25] - 02-11-2022
### updating load chargebee invoices into db script
- core: updating load portugal invoices from chargebee

## [v2.5.26] - 02-11-2022
### fixing bug on get_range_of_days functon
- load_portugal_invoices_from_chargebee: fixing instruction order

## [v2.5.27] - 10-11-2022
### disabling trustpilot for Madrid contracts
- contracts: disabling trustpilot reviews for Madrid

## [v2.5.28] - 14-11-2022
### adding new hubspot form creation business logic
- settings: adding missing setting in staging environment
- settings: updating hubspot forms per site
- github: updating master ci/cd deployment
- deployment. adding callback deployment for hubspot public forms
- hubspot_public_forms: fixing form creation
- settings: adding production settings
- settings: use current emails to send emails up to migraton is completed
- webhooks: updating condition to avoid deal creation on the backend
- webhooks: fixing hubspot form submission endpoint business logic
- webhooks: check if email is valid before sending it
- settings: adding hubspot pricing mapping for website keys
- locale: adding welcome_email subject in all languages
- several: WIP on hubspot callback
- webhooks: WIP on form submission
- core: updating cloud queue on async_api
- webhooks: WIP on hubspot public form callback
- webhooks: WIP on form submission endpoint
- webhooks: adding firebase and hubspot clients for sync operations
- settings: adding new keys
- webhooks: WIP on hubspot forms handling
- core: adding async method to handle deal creation in hubspot
- pipfile: updating dependencies
- settings: adding hubspot forms var envs

## [v2.5.29] - 14-11-2022
### fixing hubspot callback
- webhooks: fixing hubspot callback

## [v2.5.30] - 14-11-2022
### fixing hubspot public_form callback business logic
- webhooks: updating hardcoded collection hubspot public forms

## [v2.5.31] - 14-11-2022
### improving hubspot public forms business logic
- webhooks: check if firestore_id does not exist to discard message

## [v2.5.32] - 14-11-2022
### fixing hubspot public forms business logic
- webhooks: fixing hubspot form endpoint on sending email
- locale: updating email subjects

## [v2.5.33] - 14-11-2022
### fixing send email on new hubspot form submission callback
- webhooks: fixing email_body on deal creation on new hubspot forms

## [v2.5.34] - 14-11-2022
### fixing hubspot new form submission
- webhooks: fixing send_email call on new hubspot form submission

## [v2.5.35] - 15-11-2022
### updating emails on new hubspot forms callback
- webhooks: adding more context to emails

## [v2.5.36] - 18-11-2022
### adding cronjob to evict stale form submissions from firestore
- templates: updating logo size in admin view
- github: updating CI/CD pipeline
- deployment: adding cronjob script
- webhooks: adding command to evict stale form submissions from firestore
- settings: updating Oporto warehouse email address
- templates: updating logo in admin view

## [v2.5.37] - 21-11-2022
### adding booking wizard form business logic
- webhooks: adding price to booking response on booking form submission
- webhooks: updating business logic to handle deal updatings on booking form submission
- webhooks: refactoring hubspot_public_forms to handle booking wizard submissions

## [v2.5.38] - 21-11-2022
### updating email addresses
- several: updating team email addresses

## [v2.5.39] - 23-11-2022
### adding italia integration
- webhooks: replacing payment link url for new links
- settings: adding box2boxstorage.com as CORS domains
- settings: adding italian warehouse email addresses
- several: updating .xlsx resources
- several: updating internal email domains
- several: adding Italy configuration
- contracts: adding italy to base model
- webhooks: updating migration for mongodb
- webhooks: adding migration for mongodb Italy  collection
- several: adding Italia configuration

## [v2.5.40] - 30-11-2022
### adding card chargeback management
- webhooks: replacing attribute accessor for dict accessor on card chargebacks
- webhooks: improving business logic to handle card chargebacks
- webhooks: WIP on card chargebacks
- pre-commit: update dependencies
- core: updating load_chargebee_products script

## [v2.5.41] - 01-12-2022
### fixing chargebee invoices script for moloni
- core: skipping voided invoices from chargebee to upload them into moloni

## [v2.5.42] - 13-12-2022
### fixing side effect when user does not exists on initial_pickup
- webhooks: fixing side effect on initial_pickup annex

## [v2.5.43] - 13-12-2022
### Fixing dependencies in .lock
- pipfile.lock: updating dependencies

## [v2.5.44] - 19-12-2022
### adding missing chargebee keys for Italian integration
- settings: adding missing IT chargebee keys

## [v2.5.45] - 19-12-2022
### fixing CI/CD for master deployment
- github: updating configure kubectl job in pipeline

## [v2.5.46] - 19-12-2022
### adding missing italian configuration
- several: fixing italian warehouse integration
- github: adding extra performance on last job in dev pipeline

## [v2.5.47] - 19-12-2022
### adding missing italian translations
- locale: adding missing IT translations

## [v2.5.48] - 19-12-2022
### fixing Porto base for hubspot on deal creation
- contracts: fixing base option on deal creation in hubspot

## [v2.5.49] - 27-12-2022
### adding partnership - b2b integration
- webhooks: fixing partnership email business logic on deal creation endpoint
- webhooks: fixing create_hubspot_contact call on hubspot client
- webhooks: restoring email senders on public forms callback
- webhooks: cleaning unused code on public forms callback
- webhooks: adding missing context to partnership collab emails
- hubspot_public_forms: WIP refactoring
- locale: updating localness
- webhooks: simplifying chain of responsibility pattern on public forms callback
- webhooks: updating business logic to send partnership_email on deal creation
- locale: adding partnerships subject translations
- github: updating auth version job
- webhooks: refactoring to code to add hubspot operations inside the client
- webhooks: add company to contact association on deal creation
- webhooks: adding is_partner_deal parameter on hubspot deal creation
- hubspot_public_forms: adding partnership codes to hubspot integration
- webhooks: updating chaing condition on public forms callback
- webhooks: updating chain of responsibility business logic
- webhooks: adding chain of responsibility pattern to decide what "chain of steps" must be triggered
- locale: updating locales
- settings: adding partnership email addresses
- webhooks: adding business logic in hubspot client to link company to contact

## [v2.5.50] - 27-12-2022
### adding company association to deal if present
- webhooks: adding company_id association to deal if it is present

## [v2.5.51] - 27-12-2022
### fixing locales for internal emails
- locale: fixing internal email locales for form submissions

## [v2.5.52] - 28-12-2022
### adding owner_id to deal on creation for b2b
- webhooks: adding parnership owner id to deal when is a b2b deal
- settings: adding partnership owner id
- several: dropping hubspot team id because is a read only property
- webhooks: adding partnership b2b team id on deal creation
- settings: adding partnership B2B hubspot id

## [v2.5.53] - 02-01-2023
### fixing chargebee to moloni parser script
- core: fixing load_portugal_invoices_from_chargebee script to handle more than one discount

## [v2.5.54] - 02-01-2023
### fixing chargebee to moloni parser script
- core: adding new exception in catch to skip "missing discounts" on chargebee to moloni parser script

## [v2.5.55] - 02-01-2023
### fixing chargebee to moloni script
- core: fixing set_discount function in chargebee to moloni parser

## [v2.5.56] - 02-01-2023
### updating CORS domains
- settings: adding box2boxstorage.com domain to CORS

## [v2.5.57] - 03-01-2023
### updating payment link generation URL
- settings: adding leading www. to payment link domains
- webhooks: updating payment link URL generation
- webhooks: improving condition to avoid creating wrong contracts on payment refunded
- settings: adding public website domain as valid origin for CORS
- settings: adding localhost:3000 as valid CORS origin

## [v2.5.58] - 16-01-2023
### adding subscription type in model and listening chargebee hook as well
- webhooks: rollback chargebee renewed event business logic
- contracts: adding billing period in subscription response from contract endpoint
- webhooks: adding subscription changed hook to update the billing period
- webhooks: updating chargebee hook to set the subscription billing period on created/renewed hook
- payments: adding billing_period on subscription created/renewed events

## [v2.5.59] - 18-01-2023
### updating suardiaz email
- settings: adding new suardiaz email

## [v2.5.60] - 19-01-2023
### adding paymentsuccessfulevent hook to send email on payment successful
- webhooks: f
- webhooks:  adding fallback when no hubspot deal details are provided
- webhooks: wrapping payment success email call into try catch to avoid send it if missing hubspot deal metadata
- webhooks: adding missing context on email for payment_success_email template
- locale: updating email subject for payment_success_email
- webhooks: adding missing context data for payment successful email template
- locale: adding subject translations for deposit payment email
- webhooks: adding payment successful event to microservices callback in order to send the email to the user
- webhooks: adding sanity check to hubspot client on retrieve deal details

## [v2.5.61] - 19-01-2023
### fixing locales
- locale: restoring locale keys

## [v2.5.62] - 30-01-2023
### fixing payment link update on hubspot update email hook
- webhooks: fixing payment_link parser on hubspot callback

## [v2.5.63] - 02-02-2023
### updating chargebee invoices script
- core: updating script to fetch chargebee invoices for Portugal

## [v2.5.64] - 07-02-2023
### fixing deduplication on collaboration leads
- webhooks: updating form API submission to avoid deduplication on partnerhips

## [v2.5.65] - 23-02-2023
### updating partnership email recipients
- settings: adding pablo to partnership emails recipients

## [v2.5.66] - 06-03-2023
### adding maria email to chargeback emails
- webhooks: add Maria to chargeback emails

## [v2.5.67] - 13-03-2023
### adding livensa key integration on hubspot public form creation
- webhooks: add livensa key to hubspot public form creation

## [v2.5.68] - 28-03-2023
### add traces_sampler to handle sentry transactions
- settings: add traces_sampler to exclude kubernetes probes from transactions

## [v2.5.69] - 18-04-2023
### add ciudadela integration
- webhooks: update hubspot public forms view to handle ciudadela collaboration
- webhooks: update hubspot client for ciudadela collaboration

## [v2.6.0] - 08-05-2023
### add México deployment
- intranet: copy raw_data in raw_data_with_preserved_order before full migration is done on updates
- intranet: update budget estimation serializers
- intranet: add is_legacy attribute to budget estimation model  and  update related historyModel
- settings: remove unused USER_DETALS_SERIALIZER
- intranet: update redoc on activation account endpoint
- users: refactor login detailed serializers
- users: add custom loginview to retrieve needed data according to user type
- users: restore groups in serialized response
- users: drop unused serialized field in GET
- payments: typo in payment intent name
- locale: fix locales for ES
- locale: fix locales for MX
- webhooks: restrict owner_id for MX deals on hubspot creation
- payments: update payment intent amount when adding card from customer portal
- several: drop old MX POC code
- settings: add missing variables for MX
- github: add chargebee varenvs for MX
- settings: add MX hubspot form
- settings: update deposit amount for MX
- webhooks: update payment link creation with the right amount for deposit
- settings: create a dict of deposit amounts, each per site
- webhooks: add MX to sheets serializer to skip process it
- locale: add MX locale keys
- moreapp: add MX warehouse code to set the right excel template
- contracts: add MX keys in model and utils
- settings: add missing variables for MX set up
- webhooks: add empty migration to create MX collection
- settings: add MX hubspot pipelines
- intranet: fix contracts and inventory default query
- intranet: fix contract creation on budgetestimation serializer
- intranet: update contracts and inventory query in views
- contracts: fix admin view
- contract: replace support_user with site attribute to set the intranet site
- intranet: add TODO (WIP) to budget estimation create endpoint
- intranet: update budget estimation admin view
- intranet: update budgetestimation queryset in view
- intranet: add site attribute to budget estimation model
- core: check environment to avoid make hubspot async calls  on local deployment
- intranet: update budget estimation serializers for history and partial updates
- intranet: add automatically the support user on contract when budget estimation model is created in serializer
- core: add Q() to restrict by group mixin
- several: update admin views
- intranet: update views to restrict retrieved data by groups
- intranet: add raw_data with JSON order preserved field in budgetestimation model
- core: add admin wdget to handle relationships on raw_id_fields
- core. add custom JSONField to preserve the order
- contracts: add support user attribute to contract model
- intranet: add groups in user serialized response
- users: retrieve user groups on login response
- intranet: add disallow mexican user to permission classes in warehouse_differences viewset
- intranet: add permission to disallow the access for mexican users
- users: override get method in manager to prefetch groups
- intranet: update get_queryset method to restrict the data obtained from the user according to the group to which they belong
- core: add mixin to restrict queryset data
- intranet: add migration to populate django groups

## [v2.6.1] - 08-05-2023
### remove unused field in budget estimation model
- templates: add needed .css into admin templates for django-countries flags
- intranet: fix performance on admin view displaying sprite.css instead of make lots of HTTP calls
- contracts: fix performance on admin view displaying sprite.css instead of make lots of HTTP calls
- pipfile: update toolbar dependency and disable history
- github: fix collect_static job
- intranet: restore raw_data copy on update
- intranet: remove raw_data field from budget estimation

## [v2.6.2] - 10-05-2023
### fix business logic for chargebacks when coming from cards in transaction_created event
- webhooks: fix chargebacks for card payments when coming from transaction_created hook

## [v2.6.3] - 13-05-2023
### add MX email address to send email on hubspot deal creation
- settings: add MX support email to send email on deal creation

## [v2.6.4] - 25-05-2023
### fix queryset on budget estimation endpoint
- intranet: fix query in budget estimation endpoints

## [v2.6.5] - 29-05-2023
### update MXN deposit amount and added GMB link for MX
- settings: add GMB link for MX
- settings: update signal amount for MX
- github: update gateway account id for MX

## [v2.6.6] - 12-06-2023
### fix kilometer_units key in moreapp parser to allow float numbers
- webhooks: fix kilometer_units in annexes parser to allow floats

## [v2.6.7] - 12-06-2023
### update nordcargo email address
- settings: update nordcargo email

## [v2.6.8] - 13-06-2023
### update emails topic name to cloud run microservice
- settings: change google cloud email topic name to point out to the cloud run microservice

## [v2.6.9] - 15-06-2023
### refactor email address format to use sendgrid integration
- several: refactor email format to send emails
- several: update attachments parameter on mail for sendgrid

## [v2.6.10] - 20-06-2023
### fix coupon creation for MX quotes
- contracts: set the currency_code when creating a coupon to discount the deposit
- contracts: add helper method to set MXN currency on discount coupon

## [v2.6.11] - 30-06-2023
### adding firebase token generation
- github: add missing venvs
- users: add firebasetoken class to create remote tokens
- users: add remote-token endpoint to create a firebase token
- settings: adding service acount to sign firebase tokens and firebase user id

## [v2.6.12] - 30-06-2023
### add name to firebase token app to avoid clashing
- users: set name in firebase app for generating tokens

## [v2.6.13] - 14-07-2023
### fix typo on movings and boxes mapper keys in moreapp callback
- webhooks: fix typo in boxes and movings for PT and FR

## [v2.6.14] - 03-08-2023
### add <EMAIL> in intranet mails
- several: add luis email in intranet mails

## [v2.6.15] - 04-09-2023
### fix load portuguese invoice command
- core: add trimestral periodicity for moloni invoices

## [v2.6.16] - 14-09-2023
### drop valencia email from settings
- settings: drop valencia warehouse email

## [v2.6.17] - 25-09-2023
### exclude urls from sentry trace sampler
- settings: exclude tasks health check endpoints from sentry traces

## [v2.6.18] - 25-09-2023
### fix typo in trace sampler
- settings: fix typo in health check url

## [v2.6.19] - 26-09-2023
### add cbre promotion integration
- webhooks: add cbre promo code for hubspot

## [v2.6.20] - 05-10-2023
### disable contract address creation when parsing moreapp form because of troubles with the app
- webhooks: disable contract address creation on moreapp form parsing

## [v2.6.21] - 13-11-2023
### drop valenciacontainer emails for 1032 form
- settings: drop valenciacontainer emails for form 1032

## [v2.6.22] - 20-11-2023
### update chargeback mails
- webhooks: update chargeback emails

## [v3.0.0] - 30-11-2023
### add riders app integration
- webhooks: replace key access in response to avoid attribute error
- webhooks: add missing docstring
- intranet: restore create-event endpoint
- intranet: uncomment sendUpdates parameter in event serializer
- locale: update
- users: drop unused endpoint
- several: fix redoc
- contracts: remove unused endpoint
- contracts: add search_fields in item admin view
- intranet: restore update deal stage when deleting an event
- contracts: retrieve only completed services on listing endpoints
- contracts: add custom manager for service model
- core: set country on create_event_and_services command
- intranet: add service link in calendar event admin view
- locale: update translations
- contracts: update calendar creation migration to drop unused calendars
- core: clean code on create_event_and_services command
- moreapp: update log message when a warehouse has no users linked
- riders: update admin view
- contracts: fix items in finished services serialized response
- contracts: clean unused warehouses in migration
- contracts: add empty message on movings' pdf when no photos
- core: add force_send attribute on async methods
- webhooks: move outside transaction block the quote creation in service callbacks
- webhooks: force to send inventory email if force_send flag is set
- webhooks: add force_send in send_inventory_email
- webhooks: update service parsers
- payments: fix transaction.atomic place when creating quote
- contracts: update service finished serializer for movings
- contracts: fix get_items in service finished serializer response
- webhooks: fix boxes parser
- webhooks: update contract-pdf endpoint to allow force_send parameter
- webhooks: add force_send parameter in build_service_documents to handle updated services
- webhooks: fix several bugs on update behavior when parsing forms
- webhooks: add update behavior in movings parser
- webhooks: add update behavior in material delivery parser
- webhooks: add missing update parameter in base methods
- webhooks: add update parameter in parse method for ridersapp forms
- webhooks: add update behavior to final_delivery parser
- webhooks: send warehouse email when updating items on initial_pickup parser
- webhooks: add update behavior to extra_pickup parser
- webhooks: add update behavior to initial_pickup parser
- webhooks: add flag to ridersapp entry point to check if service has been updated
- webhooks: add missing items behavior on final delivery parser
- contracts: add delivery_date in items for service serialized response
- webhooks: add missing seal and bubble paper items in additional services parser
- webhooks: add missing items behavior on partial delivery parser
- contracts: add service type in context to render contract pdf
- templates: remove border in document.html for movings
- locale: update loccales to render contract pdf
- locale: fix mexican contract content
- contracts: update mexican conditions content for pdf
- contracts: add service country in serialized response
- locale: update locales
- contracts: add header on inventory section in pdf for movings
- contracts: fix furniture_handling rendering issue
- contracts: fix space in moving template
- contracts: add default base city for every country
- webhooks: add sanitize_country method to handle without errors default cities for movings and boxes
- webhooks: drop contract address and fix base for movings/boxes parsers
- contracts: fix rider content in services serializer response
- contracts: fix serialized items in finished service response
- contracts: fix service template rendering code
- webhooks: change name for updated services to avoid override the original one
- templates: add section to render empty inventory on service pdf
- contracts: add callback for floats extra items to render the service pdf
- contracts: return false when key is not present on raw data to parse additional services in pdf generation
- contracts: add callaack to ExtraItem to apply an extra operation to format data
- webhooks: fix keys for parsing service form
- templates: fix service document style
- contracts: wrap django country into string to render the service pdf template
- pre-commit: update
- webhooks: fix item creation when parsing annexes
- contracts: fix blob_name when rendering services pdf
- contracts: add warehouse_location in service serialized response
- contracts: add release endpoint in service viewset
- contracts: add submitted_at in ordering_fields in service endpoints
- webhooks: add ypdating behavior for riders app forms
- contracts: update return type of finished service serializer
- contracts: remove item finished serializer and replace it by itemserializer for finished services
- contracts: update items serialized response for finished services
- contracts: add customer signature in service finished serialized response
- contracts: fix address in service finished serializer
- contracts: drop requireness for field in service admin view
- contracts: drop unused serialized field in service today serializer
- contracts: fix parse inventory for rendering the pdf later for movings
- contracts: update service_template inventory parser to build the pdf
- contracts: add missing serializer fields in service finished serializer
- local_development: set redis version in compose
- contracts: drop requireness in updated_at attribute in service model
- contracts: add new attributes in service model to know when it has been submitted
- contracts: add new fields in finished service serialized data
- contracts: fix error in serializer for upcoming services due to missing attribute
- intranet: fix event timezone to create it in google calendar
- intranet: add missing fields in calendar create serialized response
- contracts: add filter to exclude services given the id
- contracts: set the order by in prefetch when is today parameter is set
- contracts: create serializer for finished servie and factory to get the right serializer on view
- webhooks: fix additional stop options key on additional services
- webhooks: replace barcode by tag_id on pickup callbacks
- contracts: replace barcode by tag_id on rendering .pdf files for contracts
- contracts: create dummy value for null raw_data in order to avoid crashing the app
- contracts: update service admin view
- users: update account activation response~
- users: fix activation account url for riders
- users: remove riderconfiguration creation on rider form
- users: update rider activate account
- deployment: update python-base image tag in test/prod dockerfiles
- deployment: fix dockerfile for base python image
- deployment: add required fonts to base python image
- deployment: add missing dependencies to build python-base image with pdfkit patched
- templates: fix document.html to render moving inventory photos
- contracts: fix inventory format for template in movings
- intranet: add country base to set default language in services
- contracts: add admin form to remove required fields in service view
- contracts: add country attribute in service model with related migration
- contracts: fix moving template renderer
- locale: add translation keys for services in MX
- locale: add translation keys for services in IT
- locale: add translation keys for services in FR
- templates: update width in box template
- contracts: fix box template generation
- templates: reduce width for signatures in annex pdf
- locale: add i18n keys for services - PT
- settings: remove unused setting
- contracts: refactor conditions_file method in service model
- contracts: refactor general conditions and add a Factory
- locale: add general conditions for MX
- locale: add general conditions for IT
- locale: add general conditions for FR
- locale: add general conditions for PT
- templates: remove unused code in conditions.html
- locale: update ES conditions
- contracts: fix condition templates removing unused text
- contracts: add property in service model to get the warehouse id
- core: fix riders link getting only defaulted ones
- core: fix link riders business logic and clean some attendees emails
- core: fix creating rider configuration entries for existing riders
- contracts: fix 0040 migration to restrict mexican users to calendars
- core: add riders to services in create_event_and_services command
- core: update create_riders command to split warehouses and default warehouse for rider
- intranet: update event create serializer to skip warehouse_id if is not a valid service type
- contracts: fix queryset in service viewset
- core: fix update existing users to set is_rider flag
- core: add updated riders in a list to bulk update in create_riders command
- core: set is_rider on existing users in create_riders command
- core: update create_riders command add the warehouse configuration
- settings: add ngrok new domain to allowed hosts in local settings
- settings: update local settings
- core: update create_riders command business logic
- users: update riders account activation url
- core: add create_riders command
- core: update HTMLParser in create_event_and_services command
- core: add missing space in create_event_and_services command and fix space parsing
- core: parse timeslot times in create_event_and_services command
- core: update create_event_and_services business logic
- contracts: update 0047 migration to add MX warehouse name
- core: WIP on create_event_and_services command
- intranet: add warehouse on event create/read/update operations
- contracts: update migrations for warehouses and calendars
- core: add command to fetch google calendar events WIP
- contracts: fix warehouse serialized response on /services to be compliant with the expected format by the app
- contracts: fix 500 errror when no query params present in /services viewset
- contracts: update serializer to send back the relocation address
- contracts: update service admin view
- webhooks: fix ridersapp callback to drop the notification and unused field in service
- contracts: fix service viewset to fetch linked warehouse
- contracts: remove warehouse_id and add FK to warehouse in service
- riders: fix warehouses endpoint and add missing serializer
- intranet: add timeZone to create-calendar endpoint
- intranet: add property in calendar model to know if is created on remote calendar
- intranet: set default timezone on event update
- intranet: update riders endpoint given a calendar
- intranet: update riders endpoint given a calendar
- contracts: update service endpoint to retrieve all data in /GET if today
- intranet: update link-riders endpoint in warehouse viewset
- intranet: add serializers on link-* endpoints for redoc
- intranet: add serializer to handle warehouse-rider link
- intranet: update rider serializer to link warehouses
- intranet: fix link-warehouses endpoint in rider viewset
- intranet: fix rider serializer for creating/updation ops
- users: fix keys creation on GCP when rider is created
- several: add default flag for warehouses in serializers
- intranet: fix queryset in rider viewset
- intranet: update rider serializer to get warehouses from related model
- contracts: fix riders related admin views
- riders: add admin form
- intranet: add dummy serializer to handle riders through warehouseconfiguration model
- intranet: fix filter to "flat" the M2M into separated tables
- contracts: remove M2M in warehouse model for riders
- several: fix admin views because of dropping M2M relation
- intranet: update query to filter riders who belong to a certain calendar
- intranet: delete unused view and serializers to link warehouse and calendar
- intranet: remove M2M relation between warehouse and  calendar
- intranet: add endpoints to link calendar and riders to warehouse
- intranet: add missing serializers and remove non used for warehouse endpoints
- intranet: remove unused serializers for calendar
- intranet: add warehouse filters
- intranet: add filter by city for calendar viewset
- intranet: fix date_leave in update rider serializer
- intranet: fix revoke-api-key route path
- riders: update service_account_name property in model to be random
- intranet: add rider configuration serializer
- intranet: add rider configuration endpoints
- intranet: update rider configuration serializer
- core: update schema generator
- settings: override CACHES with redis server
- settings: add  CORS_ALLOW_HEADERS and redis configuration settings for locking
- riders: add method in model to delete remote keys as well on rider deletion
- riders: add destroy endpoint
- riders: add status property on model
- users: update riders admin view
- intranet: optimize query on rider viewset
- intranet: update riders serializers adding missing business logic
- intranet: add prefetch and activation_email endpoint in rider viewset
- intranet: update riders serializers to handle missing business logic
- users: update rider admin view
- users: add attribute to model to keep leave date
- intranet: add endpoint to link warehouses to rider in riders viewset
- intranet: update serializers for rider and warehouse endpoints
- intranet: refactor calendar event endpoints
- intranet: fix start/end time fields in event serializer
- intranet: update queryset on list endpoint for events
- intranet: fix serializer on event creation on dates
- contracts: fix filter_backends on services endpoints
- core: add mixin to show filter fields on swagger and create default filter
- contracts: add ordering on list services endpoint and updating redoc
- webhooks: fix updated items on riders deliveries
- contracts: fix filter by today in services endpoint
- intranet: add variable in serialized response to check if event is published on google calendar
- contracts: add moving address in service model
- intranet: drop temporarily unused endpoint
- intranet: add business logic to handle updates on inventory items
- contracts: add final delivery body parser
- intranet: optimize inventory queryset selecting service as related
- intranet: add extra attribute on inventory serializer to check if item is linked to another service
- intranet: update event creation for deliveries
- contracts: override save method in service model to populate inventory_list on deliveries
- intranet: add filter by document_id on inventory endpoints
- contracts: add required fields into services list serialized response
- intranet: fix inventory ref_name serializer
- intranet: typo in google calendar delete eventv
- calendar: add is_all_day attribute in model
- service: add extra_pickup attributes and refactor body message
- contracts: fix service filters
- contracts: update service endpoints
- contracts: update service serializer to add the rider who accepted the service
- contracts: update service filters for /GET endpoint
- contracts: add today's filter to service
- contracts: update accept endpoint business logic and cancel endpoint too
- riders: add discarded_at attribute in model
- contracts: update accept business logic to avoid multiple acceptances
- contracts: update service list serializer with rider details
- locale: update localeness
- users: add rider configuration when creating a rider in admin panel
- webhooks: fix message key access for riders app service callback
- contracts: fix blob_name in service_templates for signatures
- contracts: fix service parser on model
- core: fix notification business logic in case of missing registration_id to skip it
- users: add configuration for rider in login response
- riders: replace base64 with base32 encoding to generate the Service Account name
- Pipfile: update dependencies
- intranet: add configuration on calendar creation serializer
- intranet: fix serializers to create calendar and update events
- intranet: add timezone attribute in calendar model
- intranet: add wrapper for calendarList API from google calendar
- contracts: update body property in service model
- intranet: update several event serializers
- intranet: fix cities endpoint in warehouse
- intranet: update event and calendar serializers
- intranet: add border_color attribute in event model
- contracts: pass "raw_data" in concrete service endpoint
- intranet: retrieve inventory from hubspot and pass it in the response
- intranet: fix serializer parameters on event creation
- intranet: add title attribute in calendar event model
- intranet: add exclude riders on /ui-calendar/riders/ endpoint to filter already selected
- intranet: add /riders/ endpoint in calendar view to fetch riders linked to calendars
- contracts: fix migration to set warehouses at calendar level
- contracts: add query parameter to retrieve contract with user when filtering the request
- contracts: add scanned dummy variable to service response on items key
- contracts: pass start_time/end_time slot in the /services GET api call
- contracts: retrieve remote_dir on inventory GET call
- contracts: fix id on update service to querying the db
- several: refactor bases to get single shared point to access them
- intranet: add endpoint in user to link calendar to it
- intranet: add pagination to calendar GET endpoint
- intanet: add filter to calendar to get calendars without owners
- intranet: optimize calendar queryset in view
- intranet: update calendar serializer to retrieve configuration linked to it
- intranet: change FK type in calendar model for owner
- intranet: fix redoc
- intranet: update user filters
- intranet: add owner in calendar model
- intranet: prefetch calendar config on calendar endpoints
- intranet: prefetch groups on user endpoint
- intranet: create admin form for calendar admin view
- contracts: update calendar creation migration
- intranet: fix transaction import
- intranet: allow empty and null in share-calendars endpoint
- intranet: update filter name for excluding users on request
- contracts: fix graph dependency in migrations
- several: fix migration conflicts merging them
- pipfile.lock: update it
- several: update service account description to avoid misunderstandings
- riders: update GET serializer to pass the service account key to app
- riders: fix service account name in model to fit in <= 30 chars
- several: pass description when creating the service account
- core: add description as required parameter when creating service accounts
- riders: add admin change actions for Service Accounts
- riders: add status to model with related migration
- users: add service account on rider creation (post_save)
- riders: add service account fields with related migration
- core: drop unused api target on API gateway key
- core: add service accounts wrapper to handle creation/deletion
- intranet: update share calendars endpoint to remove users from shared calendar as well
- intranet: add isBackofficeAllowedUser permission
- intranet: add configuration on concrete calendar serializer
- intranet: add filter on users to exclude them giving a list of ids
- intranet: drop temporarily M2M relationship in calendar serializers
- intranet: add color attribute to calendar model
- contracts: update calendar migration to add the color
- contracts: add dummy variable in serialized response for inventory
- intranet: add cities endpoint to fetch available cities for warehouses
- contracts: add missing fields in Item response for /service endpoint
- riders: add "id" on partial update response
- users: add signature in riders admin view
- users: inherit riders form in admin view from djangouserform to fix performance hits
- riders: add partial update serializer and view
- users: refactor user detailed serializers
- several: update serializers in intranet/user views
- users: inherit dj_rest_auth loginview to get the right user detailed response
- users: drop unused auth serializers
- users: add signature remote dir field with related migration
- core: add cloud storage target on rider API Key
- contracts: update redoc for read inventory endpoint
- contracts: update inventory viewset to handle lookups by tag_id as well as id
- core: create view mixin to handle multiple lookup fields on the same view or viewset
- contracts: add lookup serializer to allow search by pk or tag_id on inventories
- intranet: fix select_related query in services endpoints
- contracts: fix remote_file url for TOS attachment
- intranet: fix calendar serializers
- templates: fix document and box.html templates
- webhooks: fix ridersapp callback business logic
- webhooks: fix url to build contract from ridersapp form
- contracts: update list queryset on service viewset
- contracts: fix is_accepted filter on service
- webhooks: add callback to ridersapp subscriber
- webhooks: remove unused code
- webhooks: add moving callback business logic
- local_deployment: update postgres service command in docker-compose
- contracts: optimize service querysets on viewset
- contracts: drop accepted_by field in service model
- riders add accepted_at field to team model
- webhooks: add notification to riders on finished service
- webhooks: add box callback business logic
- webhooks: rename package for annexes
- webhooks: add finaly_delivery callback business logic
- webhooks: adding partial_delivery callback business logic
- webhooks: add helper method to utils
- webhooks: add extra_pickup callback business logic
- webhooks: adding initial_pickup callback business logic
- webhooks: add helper methods to utils
- webhooks: create endpoint to handle ridersapp document business logic
- core: create async method to handle ridersapp .pdf files
- contracts: add conditions_file method to render tos.pdf in memory on service model
- contracts: send tos.pdf as attachment to customer
- webhooks: WIP on ridersapp_ext callbacks
- contracts: add billing_cycle field in service model
- contracts: update conditions template
- core: mark async endpoints as deprecated to delete them later
- contracts: simplify comment block
- contracts: refactor service templates
- contracts: add service template for movings
- locales: updating ES localeness
- templates: fix box comment section
- contracts: pass language as argument in to_file method in service model
- contracts: refactor service_template code
- locale: update ES localeness
- templates: add conditional rendering for boxes and document
- locales: update conditions i18n messages for ES
- contracts: add to_file method to render the document
- templates: removing unused html
- templates: add comment block
- contracts: refactor service_templates
- templates: adding box.html template
- contracts: WIP on service templates
- contracts: add custom exceptions for services
- contracts: add method in service model to get fields from raw data
- several: refactor code for TRANSLATES
- templates: removing text from document.html
- settings: adding pdkit_options setting
- templates: updating content on templates
- deployment: updating dockerfile for python base image
- contracts: WIP on building templates to render .pdf
- templates: fixing contract template and removing unused code
- templates: finishing conditions
- templates: updating template to make the compliant with older webkit
- templates: WIP
- templates: WIP on contracts
- templates: WIP on contracts
- webhooks: WIP on ridersapp callbacks
- several: renaming hubspot async client instance
- f
- pipfile: updating dependencies
- contracts: notify riders when service is accepted
- webhooks: updating hubspot client to remove hardcoded properties
- core: update schema generator for redoc
- intranet: add hubspot endpoints to fetch deal/contact metadata
- intranet: WIP on hubspot viewset
- core: updating schema generator
- intranet: adding riders endpoints
- intranet: adding filter to users endpoint to fetch only teammates
- intranet: adding visibility endpoint for calendar view
- intranet: adding filter for calendar GET request
- contracts: retrieving space on services list
- Update README.md
- intranet: adding share-calendar endpoint
- intranet: adding unique_together to calendarconfig
- intranet: adding google calendar single wrapper to handle calendar ops
- core: updating schema generator
- intranet: adding calendar endpoints
- core: updating swagger schema generator
- intranet: adding delete endpoint for warehouses
- intranet: adding partialupdatemixin to handle updated fields only
- intranet: updating exclude_fields query in update serializer to avoid delete the rider
- intranet: WIP on warehouse endpoints -PATCH-
- several: refactoring admin forms
- core: adding mixin to handle M2M writable fields
- intranet: fixing filter_by_city queryset on service filters
- intranet: adding delete endpoint on service viewset
- several: refactoring rider notification business logic
- core: adding notification client abstraction
- intranet: adding GET and POST endpoints in service viewset
- intranet: adding service filters
- contracts: adding new fields in service model
- riders: fixing update_fields on send_push_notification in model
- core: updating swagger schema generator
- intranet: adding team endpoint
- webhooks: adding multicast message method on FCM client
- riders: adding sent_at field and send_push_notification method
- core: updating schema generator
- intranet: adding google calendar event creation endpoint
- intranet: updating delete business logic to drop related service
- settings: adding hubspot booking canceled
- intranet: adding delete endpoint for events
- intranet: adding partial update endpoint for events
- intranet: adding create endpoint on events
- contracts: add nullable to raw_data in service model
- users: replacing api keys wrapper on post_save signal for factory
- intranet: adding GET endpoints for events
- intranet: adding indexes for start/end time on event model
- intranet: fixing filter on event endpoint for multiple choices
- Update README.md
- core: adding filter class to handle multiple choices on query paramaters
- intranet: WIP on event endpoints
- intranet: adding admin model for calendarconfig model
- intranet: adding calendar config model with related migration
- settings: disabling intranet_deployment setting in local
- Update README.md
- Update README.md
- .env.example: updating example file
- webhooks: adding lazy init on firebase client
- contracts: adding service creation for development purposes
- contracts: adding services endpoints as optionaslashrouter
- contracts: adding inventory endpoints in optionalslashrouter
- core: adding router to handle optional trailing slash
- users: adding missing ?$ on re_paths
- users: replace path for re_path on gateway common endpoints
- users: updating redoc for auth endpoints
- several: updating redoc for riders and inventory endpoints
- contracts: updating redoc for services endpoints
- core: adding API GW query serializer for redoc
- settings: adding api gateway authentication class
- contracts: adding router for services endpoints without trailling slash
- users: adding API Gateway authentication backend
- webhooks: WIP on riders callback
- settings: adding riders_topic for every environment
- users: updating post_save signal on rider creation to add google api keys
- riders: adding api_key generation in admin site
- core: adding api_keys wrapper to create Google keys programatically
- settings: adding varenv in different environments
- core: adding api_key wrapper class to handle Google API Keys
- pipfile: adding dependency
- core: updating schema_generator for riders endpoints
- riders: adding endpoints to handle devices
- users: fixing swagger error on user_serializer
- riders: change FK in configuration model to Onetoone
- users: adding signal to create Device/Configuration entries on rider creation
- users: dropping unneeded field in user login details serializer if is_rider
- riders: renaming user FK in rider model
- riders: updating widget url ind admin view for rider FK
- contracts: WIP on POST service endpoint
- contracts: fixing retrieve endpoint for inventory and adding cache
- riders: adding fields for warehouse serializer response
- contracts: adding warehouse name in model with related migrations
- contracts: adding rider widget for M2M warehouse model in admin view
- riders: fixing fetch warehouses endpoint
- core: updating swagger definition
- contracts: adding GET detailed endpoint for inventory
- contracts: updating queryset for accept endpoint
- contracts: fixing accepted_by FK with realted migration
- contracts: updating redoc for services endpoints
- contracts: adding POST endpoint to accept a service
- contracts: adding service GET endpoints
- intranet: adding city property on event model
- contracts: updating service list endpoint
- contracts: removing city from service model
- users: fixing rider manager to inherit from djangousermanager
- several: adding choice filter and updating swagger generation
- riders: adding unique_together in  model between rider and service
- riders: adding raw_id widget and custom  form to restrict choices
- contracts: WIP on service endpoints -list added-
- contracts: WIP on service endpoints
- contracts: updating accepted_by relationship with related migration
- core: updating swagger schema generator
- riders: adding permission to access to endpoints
- contracts: adding city field in service model with related migration
- riders: adding endpoint to fetch warehouses given a rider
- users: adding manager to handle rider queryset
- users: filter non-riders users in admin view
- contracts: adding foreign key in warehouse model to rider model
- riders: adding configuration model with related migration
- contracts: removing FK from service with related migration
- intranet: adding calendar FK to events with related migration
- contracts: fixing FK for calendar and updating migrations
- intranet: adding calendar model with related migration
- riders: fixing FK relationship to calendar with related migrations
- users: fixing migration graph dependency
- contracts: adding inlines in admin view
- contracts: refactoring models with related migrations
- riders:  refactoring models
- contracts: fixing fields in City model
- contracts: fixing reverse_code on migrtion
- contracts: addning city name in model as choice
- riders: renaming models and adding missing fields
- contracts: updating admin view for warehouses
- contracts: adding reverse_code on warehouse migration
- contracts: adding warehouse_city into warehouse model with related migrations
- users: adding registration country in user add form
- users: fixing account activation business logic for riders
- users: changing account activation URL for riders
- users: adding activation account business logic
- users: adding proxy model from to User to reflect the Rider concept
- riders: renaming model from rider to device
- contracts: fixing foreignkey on service model
- users: fixing migration dependency
- riders:  updating models and admin views
- contracts: updating migrations and admin view
- contracts: register service and city models into admin panelv
- several: creating missing models for riders app with related migrations
- settings: adding riders app to installed_apps
- several: WIP on models without committing migrations
- webhooks: refactoring code to support firebase push messaging
- riders: adding initial app for riders endpoints

## [v3.0.1] - 30-11-2023
### add missing dependency (pandas)
- pipfile: fix coreapi missing dependency
- settings: add allauth accountmiddleware in middleware section
- pipfile: update dependencies

## [v3.0.2] - 30-11-2023
### fixing locales
- locale: update

## [v3.0.3] - 01-12-2023
### update locales and fix remote content on events
- intranet: fix remote calendar event title
- contracts: add in movings/boxes format body conditional rendering
- contracts: fix service mixin format body
- locale: update

## [v3.1.0] - 07-12-2023
### add intranet service endpoints to fetch a list of them
- core: add generic decorator to cache custom methods
- service: update summary method
- intranet: update services endpoints

## [v3.2.0] - 12-12-2023
### add intranet map endpoints
- intranet: drop billing cycle on list events for map endpoint
- intranet: update format_body for calendar wrapper to make attendees as optional
- intranet: update eventupdateserializer to handle teams accordingly
- intranet: fix event update when team is empty
- contracts: add lat/long attributes in service model
- intranet: update event viewset to allow filter by date to populate map

## [v3.2.1] - 13-12-2023
### fix locales
- locale: fix fuzzy entry in .po
- github: update master pipeline
- github: update dev pipeline

## [v3.3.0] - 18-12-2023
### adding routes view
- intranet: decrease delay when updating remote calendar events on routes
- core: route calendar events to specific queue
- intranet: drop unused fields from db in service endpoint for routing
- async_api: add quota_user parameter for update calendar events endpoint
- intranet: add create-route endpoint on Team viewset
- webhooks: add endpoint to update calendar events in background when setting a route
- core: add async api method to update multiple calendar events
- intranet: move format dates method to event model and refactor serializers
- intranet: add optional field in calendar format_body
- several: add SUPPORT_TEAM_ATC_EMAILS setting and refactor code
- intrant: add rider filters
- intranet: add create-route endpoint on team viewset
- intranet: update view to handle routes and services views
- intranet: add serializer for routes view
- intranet: update service filters
- core: add django filter to handle nulls
- webhooks: check if rider has linked the service on submit in ridersapp
- intranet: add calendar change behavior to update event serializer

## [v3.3.1] - 18-12-2023
### fix attendees set on event when it is updated
- intranet: check if attendess is not none explicitly when formatting event body

## [v3.4.0] - 08-02-2024
### add tasks concept and fixing bugs
- moreapp: fix national_id in moreapp parser
- templates: replace cdn for twemoji in base.html and fix size using css instead of config callback
- webhooks: remove should_update_deal_stage flag on extra pickup parser
- webhooks: fix quote creation on delivery callback
- moreapp: remove unused attributes from model
- webhooks: add guard to skip riders callback if no service found
- webhooks: fix raw_data on annex creation
- webhooks: fix raw_data on annex creation for ridersapp callbacks
- intranet: remove unused property in task model
- pre-commit: update
- contracts: fix get_queryset on service viewset
- core: add more context to generic exception on handler
- intranet: remove validation condition on event update serializer
- locale: fix MX terms of service
- contracts: add ordering for inlines in admin view
- webhooks: fix annex creation and items updating
- webhooks: fix quote creation on update in moving callback
- webhooks: update remote_dir on service update on deliveries
- locale: fix terms of service in ES
- intranet: validate riders on route creation serializer
- payments: add created_at in quote list_display admin view
- webhooks: add service on annex creation and update build_products signature for ridersapp parsers
- contracts: add admin action in annex admin view to create quotes
- contracts: add service FK to annex model
- intranet: return customvalidationerror when validating riders on event creation/updating
- core: add custom APIException to return not-only-string encoded errors
- core: replace hardcoded variable by setting in exception handler
- intranet: better handling of 410 status on google calendar call
- intranet: validate riders before creating/updating events
- intranet: fix riders endpoint in calendar viewset to handle inactive riders
- intranet: fix event destroy endpoint checking if is a task or event service
- intranet: add delete_related method in Task model to remove FKs if needed
- intranet: add completed attribute in tasks serialized response to know how many riders have completed their task
- contracts: set priority task on serializer using device timezone converted into utc
- contracts: inject timezone from request headers into task serializer
- intranet: change fields for teaminline in task admin view
- webhooks: disable image deletion when no service is found
- core: create helper method to convert aware datetime into utc
- contracts: set app_id on item when it is saved through admin view
- webhooks: refactor ridersapp callbacks
- webhooks: update condition when inventory has excluded items on pickups
- webhooks: refactor delivery parser
- webhooks: fix additional_stop key from additional services
- templates: fix rendering of comment with breaklines and emojis
- core: add templatetags with replace filter
- moreapp: replace hardcoded services for Service variables in parsers
- webhooks: refactor code to handle pickups
- moreapp: add source attribute with related migration
- moreapp: create specific parsers for moreapp and ridersapp payload
- webhooks: fix typo when get service_type on ridersapp callback
- payments: set filters and ordering in the quote admin view
- core: add is_valid_uuid method in utils
- contracts: fix service finished serialized response
- webhooks: fix update business logic for partial deliveries
- contracts: add app_item attribute in Item model
- webhooks: fix service_type key access on parse method
- contracts: add event as inline in service admin view
- contracts: filter by sent_at instead of accepted_at on tasks queryset in taskviewset
- webhooks: set sent_at attribute in Team on TaskHandler
- intranet: return raw_data as is on team serializer for tasks intranet endpoint
- intranet: add images into team serialized response in tasks
- intranet: fix eventtaskserializer fields
- webhooks: update team data only if raw_data is null in ridersapp callback
- webhooks: update ridersapp callback to handle tasks
- settings: update allowed_hosts in local environment
- contracts: override retrieve method in inventory viewset to handle duplicated items when searching by tag_id
- core: handle multiple objects returned in MultipleFieldLookupMixin
- core: fix pagination stats for tasks endpoint
- locale: fix PT contract clause message
- contracts: add rider and time_id in task serializer response
- contracts: prefetch team in tasks viewset
- contracts: fix today filter for tasks
- webhooks: WIP on ridersapp to handle tasks
- intranet: add event title into Task admin view
- intranet: add TASK attribute in task model for handling the app response
- contracts: add priority on tasks serialized response
- settings: set base time_zone to UTC
- core: add admin middleware to set the timezone to Madrid
- core: drop date awareness on tasks pagination to get sum of different kind of tasks
- core: calculate upcoming mtric for every tasks endpoint call with pagination
- intranet: remove raw_data attribute from task model
- riders: add raw_data to riders model
- users: add timezone property to user model
- core: add custom pagination for task list endpoint to display stats
- contracts: restrict task list endpoint to rider tasks
- contracts: fix time queries in task filters
- intranet: fix default value for date in WarehouseDifferences model
- core: update schema generator for redoc
- contracts: add task endpoints
- contracts: add task filters
- intranet: add prefetch for warehouses in riders list endpoint
- several: add team as inline in contract and rider admin views
- intranet: add serializer for task endpoints
- intranet: add tasks endpoints
- riders: add admin filter to filter teams by service or task
- intranet: drop unused attributes from Task model
- intranet: add default ordering in intranetserviceviewset
- intranet: filter by active riders on riders endpoint in calendar viewset
- core: fix defaultwithullorderingfilter
- intranet: fix list endpoint to retrieve teams as a list
- contracts: remove color property from service model
- intranet: fix events endpoints to fetch event color
- intranet: drop border_color and add color attribute in event model
- contracts: add tasks endpoints WIP
- intranet: fix color in task model for events
- intranet: fix event admin view
- intranet: fix update event to allow adding tasks
- intranet: add Task model in admin view
- intranet: update create event serializer to handle tasks
- several: add Task model
- core: add storage_client instance available in the project
- webhooks: update message to delete stale images
- webhooks: add storage callback to delete stale images
- pipfile: update dependencies
- settings: add STORAGE_TOPIC
- core: add storage as event type
- webhooks: add storage subscriber to handle stale images
- webhooks: emit message on service discarded to clear storage from pending images
- contracts: fix translation keys in service model
- locale: add missing keys

## [v3.4.1] - 08-02-2024
### refactor riderproxy model
- core: fix create_event_and_services command
- several: fix black styling
- several: refactor riderproxy model

## [v3.4.2] - 12-02-2024
### uncomment auth/remote-token url

## [v3.4.3] - 12-02-2024
### update pre-commit and code style
- several: run python code inspection
- pre-commit: update it
- intranet: set colorId on event creation/updating
- core: add colors utility to handle event colors

## [v3.4.4] - 16-02-2024
### fix queryset on services and add MX emails for chargebacks
- webhooks: add MX emails to chargeback notifications
- webhooks: update team sent_at attribute on service submission
- contracts: update filters in queryset for services viewset
- users: move user.is_active before check_token on creation to warn user about that
- riders: add configuration inline in rider admin view
- intranet: set username on rider creation in serializer

## [v3.4.5] - 16-02-2024
### update hubspot deal creation to allow students promo flag
- webhooks: add optional students_promo when hubspot deal is created

## [v3.4.6] - 16-02-2024
### add deployment for ridersapp callback
- deployment: add k8s deployment for ridersapp callback

## [v3.4.7] - 16-02-2024
### fix k8s ridersapp deployment
- github: fix deployment on production pipeline

## [v3.4.8] - 20-02-2024
### add missing emails to ATC/data when services are submitted and set quote name accordingly when services are updated
- payments: fix quote creation for services updatings
- core: remove unused code
- webhooks: add data@ email to send contracts as well as ATC team and customer
- payments: add is_updating_another_service atttribute in quote model
- webhooks: set is_updating_another_service in quote when a service is submitted
- webhooks: update commands and views to send pending contracts to set additional_recipients
- contracts: add service as raw_id_field in box and moving admin views
- contracts: refactor send_pf_document_to_user to add dynamic bcc recipients

## [v3.4.9] - 20-02-2024
### update TOS .pdf file name
- contracts: rename TOS .pdf file

## [v3.4.10] - 21-02-2024
### fix create remote event endpoint and remove transactions on event update
- intranet: validate if event is created on remote calendar before updating it
- intranet: validate if event has service/task and fix google calendar calls on event creation
- intranet: add distributed lock when creating event in remote calendar
- intranet: remove unnecessary transaction when updating event
- settings: change redis lock background timeout to 0
- intranet: add missing keys on event remote creation

## [v3.4.11] - 22-02-2024
### fix locale activation when creating quotes for the ridersapp integration
- payments: activate language before calling create_quote methods

## [v3.4.12] - 23-02-2024
### fix event color on google calendar
- intranet: set colorId on event create/update only if is different from calendar color
- intranet: set colorId on create_event only if is different from calendar color
- core: add helper method to check if two colors are equals

## [v3.4.13] - 04-03-2024
### fix month checker when loading chargebee invoices for moloni and submission date on ridersapp callback
- core: check month based in Madrid timezone to load chargbee invoices
- contracts: fix porto warehouse id in migration
- webhooks: set submitted_at on service submission only if it is not updated

## [v3.4.14] - 06-03-2024
### fix business logic on initial_pickup callback and finished services serialized response
- contracts: fix warehouse_space and plus_fifty_meters on finished services serialized response
- webhooks: fix subscription space sanitizing it
- core: add method to sanitize space for subscriptions
- pre-commit: disable vulture
- webhooks: fix subscription creation on pickups
- pyproject.toml: add vulture config

## [v3.4.15] - 14-03-2024
### add payment reminders and fix cancelling subscription on final delivery callback
- intranet: fix migrations
- webhooks: set deal_id in payment reminder model on payment reminder callback
- core: update redoc
- intranet: add payment reminder endpoint
- intranet: add deal_id attribute into payment reminder model
- payments: add subscription form in admin view to handle requireness on fields
- webhooks: cancel subscription on final delivery callback
- webhooks: add should_cancel_subscription flag to final delivery callback
- payments: add internal flag to subscription to avoid having long running transactions
- intranet: fix validate_email import in payment reminder model
- webhooks: catch generic exception when sending email
- webhooks: sanitize event_id on payment reminder callback
- intranet: remove blank-null in event_id attribute on PaymentReminder model
- webhooks: add info log when payment reminder command has finished
- payment_reminder: add default language in case of pipeline is not set
- webhooks: parse message before displaying as info log
- webhooks: simplify logger info message
- webhooks: fix .po generation in views
- intranet: update context in send_email
- contracts: fix .po generation in service
- locale: update
- webhooks: improve exception handling on payment reminder callback
- webhooks: fix bulk_update call on payment reminder callback
- intranet: add ordering in payment reminder admin view
- webhooks: fix model population on payment reminder callback
- intranet: add payment reminder admin view
- github: add deployments for payment reminder
- deployment: add cron job and callback deployments on k8s
- intranet: add payment reminder model
- webhooks: add callback to handle payment reminders
- settings: add SLACK_PAYMENT_REMINDER_TOPIC

## [v3.4.16] - 15-03-2024
### fix payment reminder date insertion on callback when it is not null
- webhooks: fix date parsing on payment reiminder callback

## [v3.4.17] - 19-03-2024
### fixing intranet bugs and adding new enhancements
- intranet: change time_slot attribute to textfield
- contracts: add in remote event description the kind of service
- locale: update
- intranet: increase max_length for time_slot attribute in payment reminder model
- intranet: add chargebee endpoint to fetch subscription metadata
- intranet: update serialized response for events
- intranet: update all services business logic on route creation
- webhooks: fix date on payment reminder callback
- intranet: add by_calendar filter in service endpoint
- contracts: set '-' on warehouse location remote event if is none
- contracts: fix save overriding in service model
- intranet: fix service filter by cities
- intranet: fix inventory on event update
- deployment: fix deployment for payment reminder command
- locale: update
- locale: update mx conditions
- intranet: add time_slots in services serialized response
- intranet: add event in list services serialized response

## [v3.4.18] - 19-03-2024
### change varying length fields on payment reminder model
- intranet: change address & first_name attributes in payment reminder model for textfields

## [v3.4.19] - 19-03-2024
### fix payment reminder command
- webhooks: set time as UTC on payment reminder command

## [v3.4.20] - 19-03-2024
### change date attribute in payment reminder model to date to drop times
- intranet: set date attribute in payment reminder as data instead of datetime

## [v3.4.21] - 08-04-2024
### add resa residences integration
- webhooks: add resa promo variable when creating deals

## [v3.4.22] - 10-04-2024
### fix movings callback for spaces > 50m
- webhooks: fix movings callback to handle spaces
- locale: update 10th condition in MX

## [v3.4.23] - 11-04-2024
### fix update event in intranet
- intranet: fix update event when putting data in google calendar

## [v3.4.24] - 16-04-2024
### fix fee invoice creation on chargebacks for MX
- webhooks: set charge_id and default fee per site, including MXN and not only EUR

## [v3.4.25] - 17-04-2024
### fix email body on chargeback emails
- webhooks: fix currency on email body on chargeback emails

## [v3.4.26] - 18-04-2024
### enable trustpilot again instead of google reviews
- contracts: enable trustpilot again for services review

## [v3.4.27] - 23-04-2024
### add new warehouses
- contracts: add cities

## [v3.4.28] - 07-05-2024
### add b2b owner id for logistics form submissions
- webhooks: update hubspot client to be compliant with v9
- webhooks: add business lead check to set up the right owner id on hubspot deal creation
- settings: add hubspot owner id for b2b deals
- pipfile: add comment in dependency
- pipfile: update dependencies

## [v3.4.29] - 09-05-2024
### add student pack
- contracts: pass extra_content to render_template method to add additional context
- contracts: handle student pack when rendering annex template
- intranet: add attribute in list_display in global settings admin view
- webhooks: add mixin to add comments in quotes
- intranet: add attribute to display space in pdf in global settings model
- webhooks: add comment mixin to pickup and delivery parsers
- core: update schema generator for swagger
- contracts: change serialized space variable in concrete contract serializer
- webhooks: fix comment on quote creation in pickup/delivery callbacks
- payments: add comments attribute in quote model and add comment in chargebee quote if any
- pipfile: update dependencies
- webhooks: drop force_replace on update_or_create method in pickup callback
- webhooks: add mixing to get the correct space to set up contracts and annexes
- core: add parameter to sanitize_space method to "echo" the warehouse service
- webhooks: fix extra charges creation
- webhooks: fix products parameter call on forms callback
- contracts: update summary on service model to handle student pack spaces
- core: add BLANK option to custom char field
- intranet: fix format_date method in global settings model
- webhooks: pass products dict to set an additional charge for promo packs
- webhooks: fix extra charge creation when billing cycle is promo_pack
- Pipfile.lock: fix 
- intranet: add patch endpoint on global settings
- webhooks: set additional charge on forms processing taking into account if billing_cycle is promo
- webhooks: add mixin to handle additional charges on forms processing
- intranet: add custom migration to set up the initial global settings
- intranet: add GET endpoint for global settings viewset
- intranet: add new attributes to globalsettings model
- webhooks: add subscription creation mixin to handle next billing dates for subscriptions with student-plan set
- webhooks: set billing_cycle accordingly for student plans in initial_pickup callback
- Pipfile: update dependencies
- intranet: add GlobalSettings model
- core: add singleton model
- contracts: add annex, box, moving inlines on service admin view
- contracts/riders: add filters and search by attributes in admin models
- webhooks: change hardcoded monthly key by variable
- contracts: add especial billing cycle in service model
- core: refactor billing cycle choices into common pattern
- intranet: update event title for promo packs
- webhooks/ridersapp: check service billing_cycle to get the right warehouse space key
- webhooks: get right base for product lookup to create the quote
- core: add quarterly option in billing cycle choices in product model
- contracts: add new choice (promo) in billing cycle attribute in service model

## [v3.4.30] - 09-05-2024
### fix build pdf delivery notes
- contracts:  add missing extra_content parameter in build_template_context

## [v3.4.31] - 09-05-2024
### set email on rider creation as lowercase by default
- intranet: normalize email input to lowercase

## [v3.4.32] - 17-05-2024
### fix initial pickup quotes on riders app callback
- webhooks: fix initial_pickup quotes when billing cycle is ESPECIAL

## [v3.4.33] - 17-05-2024
### fix event titles when space is promo_pack
- intranet: fix event title when space is -100

## [v3.4.34] - 21-05-2024
### fix hubspot contact creation
- webhooks: fix hubspot contact creation for phone numbers after updating dependency

## [v3.4.35] - 23-05-2024
### added reset password business logic for riders and restrict queries per deployment to avoid sending the recovery email to non authorized users
- settings: add django_otp in installed apps for staging
- settings: fix OTP configuration
- settings: add missing configuration for otp
- settings: add otp_email in base installed apps
- intranet: create an EmailDevice when rider is created
- core: add migration to create EmailDevices per each rider to handle OTP tokens
- riders: add auth urls to handle password reset
- users: override get_users in password reset form to restrict the lookup to customers
- intranet: override get_users in password reset form to restrict the lookup to intranet users
- locale: update
- settings: set OTP_EMAIL_TOKEN_VALIDITY  to 300 seconds
- core: add OTP device to handle reset password from app
- riders: add form to reset password using OTP token - WIP -
- riders: add first_name and last_name on patch serialized response
- intranet: add calendar_id on event serialized response
- contracts: add cities
- intranet: add event in task serialized response
- intranet: fix query for inventories on deliveries

## [v3.4.36] - 03-06-2024
### fix pdf generation files on residence hall services
- settings: add <EMAIL> as default address for livensa-resa services for inventory email
- settings: add missing bases to contract review links

## [v3.4.37] - 03-06-2024
### update business logic related for promo packs
- several: replace promo_pack checkings by model property
- contracts: change promo pack check when building the pdf template
- webhooks: fix promo_pack check for quotes and different metadatas
- settings: add new emails for porto warehouse
- contracts: add property to check if given service has promotional pack

## [v3.4.38] - 03-06-2024
### fix admin action to create quotes 
- contracts: fix admin action to create quotes

## [v3.4.39] - 03-06-2024
### add comment on quote creation through admin view
- contracts: add missing attributes on quote creation in admin view

## [v3.4.40] - 03-06-2024
### fix billing cycle plans on initial pickup services when they are promo packs
- contracts: fix billing cycle plan for promo packs

## [v3.4.41] - 01-07-2024
### add default email for inventories on hakotrans services
- settings: add <EMAIL> on hakotrans services

## [v3.4.42] - 03-07-2024
### fix quotes for 50+ planes in ridersapp services
- webhooks: fix condition to set 50+ plans accordingly for ridersapp services

## [v3.4.43] - 03-07-2024
### fix admin action to create quotes coming from ridersapp services
- contracts: fix create quote admin action

## [v3.4.44] - 03-07-2024
### fix error messages on quote creation in admin view
- contracts: fix message on quote creation in admin view

## [v3.4.45] - 03-07-2024
### fix .pdf generation when space is greater than 50
- contracts: fix space in .pdf when space is 50+

## [v3.4.46] - 08-07-2024
### update ATC email addresses 
- settings: update ATC emails in ES and MX

## [v3.4.47] - 13-08-2024
### update warehouse emails
- settings: replace Sevilla warehouse email

## [v3.4.48] - 19-08-2024
### remove rosa email from ATC Support team emails
- settings: drop rosa email from ATC support emails setting

## [v3.4.49] - 22-08-2024
### add Kleymar email to settings
- settings: add Kleymar email support ATC emails

## [v3.4.50] - 26-08-2024
### remove kleymar email from settings
- settings: remove ATC email

## [v3.4.51] - 24-09-2024
### update code to support v2 of app
- intranet: add inline for tasks admin view
- contracts: fix ordering in service and task viewsets
- webhooks: set app_id on items in delivery callback
- contracts: fix space in movings serialized response
- contracts: add ordering fields for backwards compat with v1.0.22
- contracts: add ordering_fields for v1.0.22 compatibility
- core: fix custom filter when handling datetimes or related fields
- contracts: add backwards compatibility on inventory serializer with current app
- contract: update default item for finished movings
- templates: fix annex template when additional service is null
- contracts: set fallback for comment in service template
- intranet: add event type in admin view
- intranet: add accepted_at in inline in admin view
- settings: override form rendered to allow setting custom templates for widgets
- riders: fix widget to lookup FK in admin view
- core: fix pagination class for tasks to show always the counter of them by type
- core: fix task counters by type on pagination class
- contracts: fix tasks ordering fields and queryset
- contracts: fix today filter for tasks endpoint
- core: fix task counter by priority in pagination class
- contracts: fix default timezone on task serializer
- contracts: fix inventory serializer for app
- contracts: add missing fiels in finished service serialized response
- contracts: return '-' on nul signed user name in inventory contract serializer response
- contracts: fix ordering in service viewset
- intranet: add comment to fix an error with endpoint
- core: fix bug when filtering services by duplicates values on a given field
- settings: add comment in staging

## [v3.4.52] - 24-09-2024
### add Helena in the SUPPORT_TEAM_ATC_EMAILS setting
- settings: add <EMAIL> as new ATC email
- github: restore paths-ignore in master
- github: fix getting version from downloaded artifact
- github: debug job for master
- github: force redeploy in master
- github: update versions of upload/download artifacts

## [v3.4.53] - 26-09-2024
### fix chargebee date formatting on subscription created event
- webhooks: fix chargebee conversion date to set cf_start_date custom field

## [v3.4.54] - 07-10-2024
### added coupon entity to handle discounts for bi/annual planes and modify business logic to set every plan as monthly on quote creation with the right coupon 
- contracts: restrict plan discount to initial pickup services only
- webhooks: add plan discount on quote creation in initial pickup parser
- payments: populate db with chargebee coupons
- payments: add unique constraint to coupon
- payments: add coupon admin view
- settings: add chargebee bi/annual coupon internal ids
- contracts: add applicable_coupons property in model service
- webhooks: restore extra_pickup charge
- payments: add coupon model
- payments: add coupon_ids attribute in quote model
- webhooks: remove addon concept on initial pickups
- webhooks: fix addon creation on initial pickup
- webhooks: set extra charges taking into account if is a new plan
- contracts: add property on service model to set the right pickup charge for new planes
- webhooks: add addon on initial pickup quote creation
- contracts: set fallback billing cycle for non defined plans in some countries

## [v3.4.55] - 17-10-2024
### update deposit amount for ES and PT markets
- settings: update amount comment
- settings: update deposit amount for ES and PT to 30€

## [v3.4.56] - 24-10-2024
### add ATC email in settings (<EMAIL>)
- settings: add <EMAIL> ATC email
- github: restore pipeline
- github: force deployment

## [v3.4.57] - 05-11-2024
### update trustpilot email
- contracts: update truspilot link

## [v3.4.58] - 11-11-2024
### add temporal promotion field in service model to display on intranet if a given service has a promotion (i.e. black friday)
- contracts: add promotion_code attribute in service model

## [v3.4.59] - 22-11-2024
### update general conditions and send pdf when payment link succeeded
- webhooks: set bucket_name per environment when uploading the tos file on payment link succeeded
- contracts: fix conditions_file call in service model
- webhooks: add conditions file as attachment on payment link succeeded
- locale: remove unused FR and IT translations
- locale: remove unused ES translations
- locale : update PT conditions
- contracts: split ConditionsTemplate for ES/PT adding new clause
- locale: add new clauses to ES conditions
- locale: update ES conditions

## [v3.4.60] - 25-11-2024
### add image optimizations for rendering contract pdfs to avoid mail gateway errors 
- pipfile: update dependencies
- templates: use optimize_image filter to render contract pdfs
- core: add optimize_image filter for templates
- contracts: send img urls as string to template to build contract pdf files
- contracts: add filter by type in service admin view
- pipfile: add pillow dependency
- pipfile: fix git installation package in private dependency

## [v3.4.61] - 13-01-2025
### added moving pages integration
- webhooks: add order_type on hubspot form submission
- webhooks/hubspot_public_forms: break the callback when current_step is negative

## [v3.4.62] - 28-01-2025
### add abstraction to choose the right welcome email for movings or normal forms
- webhooks: add generic way to choose the welcome email template
- github: fix paths-ignore
- github: set all occurrences of runs-on machine to ubuntu 22.04
- github: update master CI/CD
- github: update dev CI/CD

## [v3.4.63] - 31-01-2025
### restore google review but only for Madrid
- contracts: restore google reviews instead of trustpilot

## [v3.4.64] - 17-02-2025
### add susana email to atc support emails
- settings: add susana email to support emails

## [v3.4.65] - 04-03-2025
### add wuolah integration
- webhooks: add wuolah integration with hubspot

## [v3.4.66] - 10-03-2025
### add amro integration
- webhooks: add amro promo hubspot integration

## [v3.4.67] - 18-03-2025
### move hubspot deals to close stage on movings
- webhooks: move deal to close stage in hubspot for movings on payment succeeded

## [v3.4.68] - 20-03-2025
### add yugo and xior integrations with hubspot
- webhooks: add yugo and xior integrations with hubspot

## [v3.4.69] - 24-03-2025
### update conditions for MX contracts
- contracts: update conditions template for MX contracts

## [v3.4.70] - 28-03-2025
### add nido integration with hubspot
- webhooks: add nido_promo integration

## [v3.5.0] - 05-05-2025
### add inventory tracking and ATC feedback
- intranet: add service_id on budget estimation update serializer to dump the inventory in the event
- intranet: remove uneeded fields from intranet partial update serialized response
- contracts: add migration to set warehouses where differences are allowed
- contracts: add migration to populate calendars by rider
- several: replace StringErrorsMixin and serializers.ModelSerializer by ModelSerializer class
- contracts: fix service_file url for boxes
- contracts: update pdf template to hide space in students contracts if set
- webhooks: fix additional charges for promo pack services
- webhooks: add helper method to set up the right space key on promo pack
- webhooks: allow updating description when updating services
- intranet: fix contracts serializer
- intranet: fix field type on global settings model
- intranet: remove prefetch in contracts viewset
- intranet: fix warehouse user warehouse_id filter
- intranet: clear attendees on event update when list is empty
- contracts: fix safe_space and current_space business logic
- intranet: add filter by calendar in riders filter
- intranet: add chosen_space in concrete event serializer response
- webhooks: replace the warehouse_space_key in promo_pack by chargebee_mapping_key
- contracts: add chargebee_mapping_key to strategies to get the right product
- core: fix add_warehouse_movements command
- intranet: fix order migration to enable GIN indexes
- core: update command to add warehouse movements
- intranet: fix link_riders endpoint to set only defaults
- intranet: add link-calendars endpoint in RiderViewset
- core: update redoc
- several: fix redoc
- contracts: remove default field from WarehouseConfiguration model
- intranet: update riders available in calendar endpoint
- intranet: fix validation on intranet rider default serializer
- intranet: add default field in CalendarRiderConfig model
- core: fix errors_dict_to_string function
- intranet: fix update call on calendar serializer
- intranet: fix update calendar serializer
- intranet: check that calendar is created on remote before trying to delete
- core: create ModelSerializer class to standardize model serializer definition
- core: force distinct on filter_queryset on FilterListMixin
- intranet: refactor calendar filters to new style
- intranet: add endpoint in calendar to set riders as default
- core: fix errors_dict_to_string to handle nested lists
- intranet: add missing fields in calendar config serializer response
- intranet: update get_queryset in calendar viewset
- intranet: add calendar configs endpoint
- calendar: add calendar rider config model
- intanet: add calendar rider config admin view
- intranet: fix list viewset passing the request to filters
- intranet: fix only_active filter for riders
- iintranet: fix bulk_update endpoint for reviews
- intranet: fix event title on list endpoint
- intranet: add blank_slot in list_display for event admin view
- intranet: add filters by residence and residence group in services
- core: override filter_queryset to return Q objects instead of qs
- intranet: fix MRO in filters with mixin
- intranet: optimize get_queryset for residences endpoints
- contracts: set residence in service admin view as raw_id_field
- contracts: add residence FK into service model
- intranet: add residences endpoints
- intranet: add normalized_name to Residence model to fast lookups
- residence_groups: fix mutating endpoints
- intranet: add residence groups endpoints - WIP -
- intranet: add command to populate residences
- intranet: add residence and residence group models
- intranet: fix default annotation on riders endpoint inside ui-calendars
- contracts: add photos column in item admin view
- webhooks: remove placement_id on tracking handler
- contracts: update students pack business logic
- intranet: drop multiple deletions on WarehouseMovement admin view
- intranet: fix delete model for warehouse movements admin
- contracts: fix warehouse movements relocation endpoint
- contracts: update warehouse movement serializer
- contracts: refactor relocation endpoint from app
- intranet: fix relocation endpoint for warehouse movements
- contracts: add version in warehouse movement serialized response
- core: add versioned mixin to handle versioned_updates in partial updates
- intranet: add version in serializers for optimistic locking
- webhooks: add strategies to handle warehouse movements
- webhooks: update relocate warehouse movement strategy business logic
- intranet: update warehouse movement relocation endpoint to handle the update
- core: add versioned manager to handle optimistic locking
- webhooks: refactor tracking callback in strategies
- contracts: update GET endpoint for warehouse movements and add filters
- intranet: fix recursive_case on warehouse movement bulk destroy endpoint
- contracts: return is_outdated in warehouse movement serialized response
- contracts: add view_pdf and create quote admin actions for movings and boxes
- intranet: prevent rider deletion if has finished services/tasks
- intranet: fix event creation and updating for booked events
- intranet: fix create_event endpoint to handle booked events aswell
- intranet: fix tasks filters and status retrieved on serializer
- intranet: add endpoints for tasks and fix calendar and events viewsets
- intranet: add filters for tasks
- intranet: add gin index on event title field
- intranet: enable delete endpoint for services
- intranet: wrap delete_related entries on event model in a transaction
- intranet: add missing filters for services
- contracts: add services files remote url as properties in service model
- intranet: update services endpoints WIP
- intranet: add services filters - WIP -
- contracts: add chosen_warehouse_id in service admin view
- contracts: add autoincrement field for warehouse differences and remove unused field
- core: add mixin to handle pagination for bulk endpoints
- intranet: improve validations on perform differences endpoint
- intranet: remove field and set the warehouse_differences_id to perform differences
- core: add autoincrement field
- contracts: add allows warehouse differences in warehouse differences admin view
- intranet: add retry endpoint - WIP -
- intranet: add warehouse differences filters
- intranet: add allows_warehouse_differences filtering for warehouses
- intranet: add filters for riders endpoints
- users: add gin indexes on first name and last name
- intranet: add pagination for riders endpoints
- intranet: add filters for warehouse user endpoints
- intranet: add patch endpoint for cities
- core: create generic manager to check if given model is not related to any other instances
- intranet: add cities endpoint -WIP-
- intranet: add city filters
- intranet: add normalized_name and remove review_link fields from city model
- intranet: check if warehouse is not used on related models to prevent the deletion
- core: handle related models on bulk delete non recursive model
- intranet: add bulk_update for warehouses - WIP -
- intranet: add create and update serializers for warehouses
- intranet: add only_active filter for warehouses
- contracts: add deactivated field into warehouse model
- contracts: add warehouse manager
- contracts: fix tracking inventory in admin view
- intranet: prepare warehouse endpoints for bulk deletions
- intranet: add missing filters to warehouse filterset
- core: add operator concept to filterlistmixin
- intranet: add max_created_at in warehouse pagination
- users: add gin index into user email field
- intranet: change ordering fields in warehouse endpoints
- intranet: add gin index into warehouse user email field
- intranet: add warehouse filters - WIP -
- contracts: add gin index for faster lookups on warehouse names
- intranet: add pagination for warehouse endpoints
- intranet: fix warehousedifference admin view
- intranet: add warehouses as FK into warehousedifference model
- intranet: fix update reviews endpoint and disable enabled reviews on create if it has been created enabled
- intranet: fix query to update all in reviews
- intranet: fix bulk_update on reviews when all items are selected
- intranet: add filter by sistem for reviews
- core: update chunks endpoint mixin to handle bulk_updates
- intranet: add bulk update endpoint to reviews
- intranet: remove LIMIT 100 on export placements
- intranet: add endpoints to retrieve and delete reviews
- intranet: add reviews filters
- intranet: add reviews model
- settings: add custom headers on CORS
- core: update TemporaryDeletionManager to handle non recursive models
- intranet: remove border_color field in serializer when updating events
- intranet: remove color=border_color on fresh blank slot updating
- intranet: fix update event serializer
- intranet: fix event color_id for blank_slots on event update
- intranet: fix blank_slot creation on event update
- intranet: fix update event serializer for blank slots
- intranet: fix update event business logic - WIP -
- intranet: fix event creation for booked slots
- intranet: rename description field in blank slot model
- intranet: return is_all_day in concrete event serializer
- intranet: add one date to end date if is_all_day event
- intranet: add by_range filter for calendar events
- intranet: fix event create/update for blank slots
- core: add manager mixin to truncate tables
- intranet: fix relocation endpoint in warehouse movement
- intranet: allow filtering by pk and contract_document id in warehouse movements
- intranet: optimize get_queryset for warehouse movements
- intranet: add active_contract filter for warehouse movements
- core: add chunk_size to exporters
- intranet: add export endpoint into warehouse movements
- core: add exporter class to export querysets into different formats (CSV)
- intranet: add patch endpoint for warehouse movements
- contracts: refactor send_pdf_email to use the review model
- core: refactor modifier metaclass to create one generic to reuse it
- core: add review model
- core: add review model to handle reviews on contract signing
- intranet: fix needed fields  warehouse movement create serializer
- intranet: add create endpoint in warehouse movements
- intranet: fix pagination error on contracts list endpoint
- IntranetWarehouseMovementViewSet: fix typo in destroy method
- core: update bulk_delete method in temporary deletion manager
- intranet: drop recursive fields in favor of recursive model class
- core: add recursive model to handle tracking model queries
- intranet: update recursive references when destroying a warehouse movement
- intranet: allow dynamic pagination classes for warhouse movements
- core: add get_pagination_class in pagination mixin to override the defaulted one
- intranet: add relocation method for warehouse movements
- intranet: add pagination class for tracking movements
- intranet: add root_warehouse_movement FK in warehouse movement model
- intranet: add filter by warehouse movement id for warehouse movements
- intranet: fix bulk_destroy endpoint for warehouse movements
- core: add ChunkAPIMixin to handle chunked payloads for bulk deletes
- core: add PaginationMixin to inject queryset for cursor pagination
- core: add TemporaryDeletionEntriesManager
- intranet: add index in sent_at attribute in warehouse movements model
- intranet: add pagination for warehouse movements ready to use cursor or page number
- core: add TemporaryDeletionEntries admin view
- core: fix complex filters for warehouse movements
- core: add serializer to handle  chunked requests
- core: add TemporaryDeletionEntries model
- intranet: add serializer to create warehouse movements
- intranet: add CTEManager to warehouse movement model
- intranet: add bulk_destroy to warehouse movements endpoint
- core: add BulkRouter to allow multiple deletions
- Pipfile: add django-cte package
- core: add IN modifier
- intranet: add max_created_at in warehouse movement pagination for cutoff deletes
- intranet: update warehouse movement serializer content for list endpoint
- ntranet: fix warehouse movements filters
- webhooks/hubspot_public_forms: break the callback when current_step is negative
- intranet/warehouse_movement: remove contract_id from get_queryset as required field to list
- intranet/filters: add filter fields for warehouse movements
- core: add filter modifiers and form dictfield
- core/filters: add mixin to handle filters defined by modifiers
- intranet: add help_text to warehouse_movement FK
- intranet: fix services list endpoint
- intranet: change event title order in event serializer
- intranet: add new FK as read only fields in warehouse movement admin view
- intranet: add intranet_user and warehouse_movement FK in warehousemovement model
- intranet: send calendar color for routes
- intranet: avoid filter queryset if routes parameter is set
- intranet: handle tasks in create-routes endpoint
- intranet: update queryset adding tasks for routes
- intranet: add task filters
- intranet: fix task creation/updating
- contracts: update remote calendar event content
- locale: update locale
- contracts: add billing_cycle_notes field in service
- intranet: fix create/update events when no blank_slot metadata is present
- intranet: add event__start_time ordering field for services and set start/end times to slot times
- intranet: fetch permanencia from hubspot deal in deal/ endpoint
- contracts: add compat method to translate hubspot internal keys into valid keys for billing cycle
- intranet: handle blank_slot sync on remote calendar
- intranet: handle blank slot event creation on event create/update
- intranet: set on_delete cascade on blank_slot FK in event model
- intranet: update blank slot field name in event admin view
- intranet: rename blank FK to blank_slot in event model
- intranet: add blank slot admin view
- intranet: add blank slot model
- intranet: filter providers in event serialized response
- intranet: add providers_team in event seriaized response
- intranet: add warehouse user team admin view
- contracts: add providers team inline in service admin view
- intranet: add providers_team on event creation and updating
- intranet: add model to link warehouse user with services
- intranet: add providers filtering in warehouse endpoint
- intranet: add link-warehouses endpoint in warehouseuser viewset
- core: add searchable filter implementation for WarehouseUser
- intranet: add searchablefilters in warehouseuserconfiguration admin view
- templates: update searchable_filter.html to use default django styles
- pre-commit: add djlint
- intranet: add ordering to warehouse user admin view
- intranet: add serializer to create/patch warehouse user
- intranet: add date_joined field to warehouse user model
- intranet: remove print
- intranet: add link providers endpoint to warehouse viewset and optimize queryset
- intranet: add warehouse users endpoints
- core: add mixin to choose a subset of fields defined in query params
- moreapp: fix send_email admin action
- intranet: fix migrations
- contracts: fix list_filter for riders in warehouse configuration admin view
- core: subclass searchable filter for riders
- contracts: add country filter in warehouse admin view
- contracts: add admin filter for countries
- several: fix migrations
- intranet: fix migration 0046
- contracts: fix migration dependency in 0073
- core: set imported as true in add warehouse movements command
- intranet: add imported field in warehousemovement model to identify what placements have been added outside the app
- riders: fix import to filter warehouses
- core: set the contract_id when generating the internal_id on add warehouse movements command
- core: add command to fill in warehouse movements
- payments: add service type filter in quote admin view
- webhooks: fix quote creation in movings due to wrong country
- contracts: add versioning to service viewset
- webhooks: add temporal handler to get rid of errors due to missing warehouse
- contracts: add compatibility serializer for today services
- riders: drop v in versioned methods
- riders: add versioning mixin to retrieve content based on specific app version
- riders: restore warehouse serializer for old compat
- settings: add versioning
- core: add compat layer to allow different behaviors according to version numbers
- webhooks: fix warehouse movement creation on ridersapp callback
- intranet: order by sent_at in warehouse movement admin view
- core: add searchable admin filter
- contracts: add rider filter in contract admin view
- webhooks: fix update_or_create default lookup field in pickup parser
- contracts: optimize services endpoints
- payments: check if coupon_id has any value before creating quote
- several: replace encoder in JSONField
- contracts: override encoder in JSONFields
- core: override django json encoder to serialize CountryFields
- several: create shared update_or_create behavior overriden the default manager
- riders: set ordering for warehouses by name instead of created_at
- contracts: fix filter condition for warehouses
- contracts: add admin filter for warehouses
- riders: add dynamic filters in warehouses endpoint
- riders: update rider query serializer for redoc
- riders: add warehouse filters
- intranet: fix tracking_inventory_options endpoint docs
- core: update redoc
- core: fix ordering filter with get_ordering returns null
- riders: remove "owner" from riders warehouses serialized response
- intranet: return contract id on concrete event serialized response
- contracts: replace warehouse_location field for textfield in service model
- contracts: fix tracking filter
- intranet: fix by_city filter for services
- locale: update translations for ES and EN
- intranet: add patch in warehouse viewset
- several: drop owner field from warehouse serialized responses
- contracts: add tracking_options into warehouse admin view
- contracts: add tracking inventory options to take a more granular control
- webhooks: drop warehouse lookup in services callbacks to use the injected one
- webhooks: parse warehouse to get a db entry of the model
- contracts: add chosen_warehouse_id in service model with related migrations
- core: create mixin to handle GeneratedField savings
- core: add custom field to handle GeneratedFields
- intranet: add fields in warehouse and warehouse movement serializers
- contracts: fix pagination warning setting up the ordering field in contractviewset
- intranet: check if account is verified before sending activation email for riders
- riders: add property to check if account is verified
- intranet: fix warehouse movements filter
- contracts: update warehouse_city field in warehouse model
- intranet: update contract serializers
- intranet: add paginations and filters for warehouse movements
- intranet: optimize retrieve contracts queryset
- intranet: add placements viewset
- moreapp: display sent in list admin view
- webhooks: fix pending_inventory_email command
- settings: remove moreapp and warehouse emails settings
- webhooks: fix ridersapp business logic 
- webhooks: update lookup when setting up the cf_base in chargebee subscription
- moreapp: use warehouse model data instead of hardcoded one
- intranet: add admin views for user and configuration models
- intranet: update delivery model to use the warehouse model instead of harcoded data
- intranet: add user and configuration models to remove harcoded emails
- core: update unique index in product model
- core: fix product admin view
- contracts: optimize queryset joining with warehouse
- contracts: remove unused constants
- contracts: fix several admin views
- contracts: add fields in several models to drop hardcoded warehouses
- webhooks: remove unused helper functions
- webhooks: update movings service parser to use the new warehouse/city business logic
- webhooks: update boxes service parser to use the new warehouse/city business logic
- contracts: fix admin view replacing city by warehouse field
- contracts: replace city by warehouse field in models
- moreapp: fix base and add custom action to send email in admin view
- intranet: fix city in contract serialized response in list endpoint
- intranet: add review_link field in city model
- core: update load_chargebee_products command to set up city and legacy fields
- core: add city and is_legacy attributes to Product model
- contracts: add city as FK and fix related business logic
- contracts: update admin view to add the concept of City
- settings: add TRUSPILOT_EMAIL and FALLBACK_BASE
- intranet: fix warehouse city lookup on route validations
- intranet: fix warehouse city lookup in create_route endpoint
- intranet: fix event creation to be compliant with warehouse refactor
- intranet: fix destroy endpoint to address remote deletion of events
- contracts: fix discard_tracking endpoint signature
- contracts: add discard_tracking endpoint in service viewset
- settings: add MAX_TIME_TO_DISPLAY_PENDING_TRACKING_SERVICES
- contracts: add riders who submitted the tracking service into serialized response
- contracts: fix tracking filter to retrieve rider-only services
- github: add deployment to remove tracking services
- deployment: add cron to remove old tracking services
- riders: add tracking status choice in team model
- riders: add command to discard old services from being tracked
- contracts: mask sent_at as created_at in warehouse movement serialized response
- contracts: merge migrations
- core: fix 0017 migration dependency
- intranet: fix by_city filter on warehouse viewset
- intranet: fix by_city filter on calendar viewset
- riders: clear warehouses endpoint and filter by rider
- intranet: fix serializers to be compliant with warehouses refator
- intranet: fix filters by_city to be compliant with warehouses refactor
- intranet: add city as raw field in Calendar admin view
- webhooks: update remote_dir on warehousemovement creation
- contracts: optimize query to get warehouse movements
- contracts :update warehousemovement serializer to fetch movements in the app
- core: replace key attribute type on APIGatewayQuerySetSerializer
- contracts: add ordering to warehouse movement endpoint
- contracts: exclude meta in warehouse movements serialized response
- core: add warehouse movement pagination
- core: add to multiple field lookup mixin related fields to lookup
- contracts: add placements endpoint
- contracts: add team in tracking service serializer
- webhooks: add warehouse movement handler to track inventory movements
- contracts: update tracking services filter and serializer
- contracts: remove unused tracking field
- riders: add field to team model to track warehouse locations
- intranet: rename tracking model to warehousemovement
- intranet: simplify WarehouseBuildingTracking model
- intranet: add warehousebuildingtracking admin view
- several: add owner attribute in warehouse serializers
- riders: add city in rider warehouses serialized response
- intranet: refactor calendar model adding city FK
- contracts: add attributes to warehouse
- contracts: update warehouse serializer response sending city attribute
- contracts: select related warehouse city on service queryset
- core: remove spaces in normalize_string function
- intranet: update warehouse admin view
- intranet: delete unused WarehouseBuilding model and update migrations
- contracts: update tracking serializer
- contracts: add serializer for tracking services
- intranet: add migration to set services as tracked
- contracts: add service to filter to get pending tracking inventories
- intranet: fix form in warehousebuildingconfiguration admin view
- contracts: add tracking status attribute in service model
- intranet: add tracking models in admin view
- core: add helper method to get the canonical form of a given string
- intranet: add tracking models to keep movements of the goods
- contracts: add unique constraint on warehouse configuration model

## [v3.6.0] - 05-05-2025
### add tracking inventory

## [v3.6.1] - 06-05-2025
### fix bugs on contract hubspot endpoint and document serialized response
- intranet: fix warehouse_name on documents serialized response
- intranet: prefetch more relationships to retrieve data in ui-contracts-hs
- intranet: add prefetch on ui-contracts-hs endpoint
- github: remove unused callback from master deployment

## [v3.6.2] - 06-05-2025
### fix document serialized response 
- intranet: fix warehouse_name in document serialized response

## [v3.6.3] - 06-05-2025
### fix admin action
- contracts: add coupon ids in create_quote admin action

## [v3.6.4] - 06-05-2025
### fix services filter
- intranet: fix submitted_at service filter

## [v3.6.5] - 06-05-2025
### fix date filters in several filterset classes
- several: fix date filters using __date modifier

## [v3.6.6] - 07-05-2025
### update warehouse movements endpoints and fix remote_dir value on serialized response
- intranet: enable retrieve endpoint for warehouse movements
- webhooks: fix command to send email on annex signed

## [v3.6.7] - 08-05-2025
### fix reported bugs
- intranet: fix canceling event for attendees if not update_team or update_providers_team
- core: fix credit notes generation for moloni
- contracts: fix permissions to get placements data from the app

## [v3.6.8] - 09-05-2025
### fix chargebee metadata base set on subscription created
- webhooks: fix cf_base on subscription created

## [v3.6.9] - 12-05-2025
### add chargebee and hubspot full urls in contracts concrete serialized response
- intranet: add chargebee and hubspot full url in contract serialized response
- contracts: clean unused code

## [v3.6.10] - 12-05-2025
### add student pack instead of number space in contracts for students
- contracts: add admin actions to render and upload pdf in services
- contracts: add strategies to set the right space in contract .pdf files
- contracts: add pdf_space to strategy and fix pdf generation for student packs
- locale: add student packs for pdf contracts

## [v3.6.11] - 13-05-2025
### fix routes preventing skip attendees on events and filtering data
- webhooks: fix route attendees adding providers if any
- intranet: retrieve providers on routes
- intranet: pass default in warehouse user configuration serialized response
- intranet: add city_id filter in tasks

## [v3.6.12] - 13-05-2025
### fix translation on contracts pdf
- contracts: fix translation in contracts pdf forcing str when space is formatted

## [v3.6.13] - 13-05-2025
### add range versions to versioned decorator and fix allowed versions for riders app
- several: fix allowed version from 2.1.0 to >=2.1.0
- core: add range versions to versioning decorator

## [v3.6.14] - 14-05-2025
### fix link riders to warehouse endpoint
- intranet: remove non existent field in model to link riders to warehouse

## [v3.6.15] - 14-05-2025
### add room field in service model and retrieve service duration on services response
- intranet: retrieve country in calendar configuration to handle room translation
- contracts: add room field in service for residence services
- intranet: send the service duration on serialized response

## [v3.6.16] - 21-05-2025
### send back the currency code in contracts serialized response
- contracts: pass in contract response the currency code
- contract: add currency in contracts serialized response

## [v3.6.17] - 23-05-2025
### add attendees in google calendar events for tasks in routes
- webhooks: add tasks attendees in routes endpoint

## [v3.6.18] - 26-05-2025
### fix update remote calendar events call
- intranet: fix update_remove_calendar_events call

## [v3.6.19] - 27-05-2025
### add holidays events
- intranet: fix filters for riders and users
- core: fix add spanish holidays command
- Pipfile.lock: update
- Pipfile: remove unused dependency (pycountry)
- intranet: order cities by normalized name in warehouse/cities endpoint
- core: fix regional holidays in add spanish holidays command
- intranet: remove unique index on iso_code field from city model
- intranet: update holidays in background on calendar/city update
- intranet: allow link calendars to riders when is in pending state
- webhooks: add async endpoint to update holidays on city/calendar change
- core: fix commands to add holidays
- intranet: add helper functions to lookup iso codes in ISO3166_2 model
- core: add functional index and iso_code field to holidays model
- core: fix add_iso31662_data
- core: add holidays_from_api command to populate database using openholidays api
- core: fix add_spanish_holidays command
- core: add command to populate ISO3166_2 table
- city: add ISO3166_2 model for holidays using openholiday api
- intranet: add is_for_holiday field in blank slot model for holidays
- core: add helper function to get iso 3166-2 from subdivision
- intranet: add iso 3166-2 code field
- pipfile: update dependencies
- intranet: fix task filters
- intranet: add support user viewset and clean unused endpoints and code
- intranet: fix service filters
- core: add command to set portuguese holidays WIP
- core: delete holidays entries only for spanish country
- intranet: add display holidays business logic in calendar config
- intranet: update filter events by calendars to exclude holidays
- intranet: retrieve is_holiday on concrete event serialized response
- intranet: add is_holiday field in event model
- core: add command to set spanish holidays
- intranet: add area levels to city model
- core: add spanish holiday model

## [v3.6.20] - 06-06-2025
### update conditions
- locale: update conditions clause

## [v3.6.21] - 06-06-2025
### fix space key used for annexes .pdf
- contracts: fix space key used for rendering annexes .pdf

## [v3.6.22] - 06-06-2025
### add residences information in routes GET endpoint
- intranet: add residences in routes endpoint
- github: update python version for collect_static job

## [v3.6.23] - 17-06-2025
### fix build products on services callback to handle legacy contracts
- webhooks: fix quote creation for legacy contracts in boxes and movings
- webhooks: fix services build_products callback to handle legacy contracts
