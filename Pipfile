[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "~=3.2"
djangorestframework = "~=3.12"
django-environ = "==0.5"
psycopg2-binary = "==2.9.1"  # 2.8.6
django-allauth = "~=0.45"
dj-rest-auth = "~=2.1"
djangorestframework-simplejwt = "~=4.8"
django-redis = "~=5.0"
drf-yasg = {version = "~=1.20", extras = ["validation", "coreapi"]}
"django-phonenumber-field[phonenumbers]" = "~=5.2"
django-countries = "~=7.2"
sentry-sdk = "~=1.0"
boxtobox-network-clients = {ref = "v1.2.68", git = "ssh://**************/box2box-SL/clients.git"}
django-cors-headers = "~=4.0"
mailjet-rest = "~=1.3"
django-db-geventpool = "~=4.0"
gevent = "~=23.9"
gunicorn = "~=22.0"
py-moneyed = "~=2.0"
pyjwt = "==2.1.0"
hubspot-api-client = "~=9.0"
python-stdnum = "~=1.17"
hashids = "~=1.3"
django-object-actions = "~=3.1"
django-allow-cidr = "~=0.3.1"
openpyxl = "~=3.0"
google-api-python-client = "~=2.40"
django-simple-history = "~=3.0"
django-filter = "~=21.1"
django-otp = "~=1.1"
qrcode = "~=7.3"
pymongo = "~=4.1"
firebase-admin = "~=5.0"
google-cloud-api-keys = "~=0.5"
pdfkit = "~=1.0"
pandas = "~=2.0"
grpcio-status = "~=1.60,<1.62"  # Necessary for resolving clashes between pubsub and tasks packages
dateparser = "~=1.2"
pillow = "~=10.4"
psycogreen = "~=1.0.2"
django-cte = "~=1.3.3"

[dev-packages]
black = "==21.12b0"
pytest = "*"
pytest-django = "*"
pytest-cov = "*"
ipython = "*"
pre-commit = "*"
jedi = "==0.17.2"
parso = "==0.7.1"
django-debug-toolbar = "~=4.0"
django-extensions = "*"
