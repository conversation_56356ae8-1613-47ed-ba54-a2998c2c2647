[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"


[packages]
fastapi = "~=0.0"
uvicorn = "~=0.17"
boxtobox-network-clients = {ref = "v1.2.53",git = "**************:box2box-SL/clients.git"}
gunicorn = "~=20.1"
python-dotenv = "~=0.20"
pandas = "~=1.4"
"fastapi-jwt-auth[asymmetric]" = "~=0.0"
jinja2 = "~=3.1"
sentry-sdk = "~=1.0"

[dev-packages]
black = "==21.12b0"
pytest = "*"
ipython = "*"
jedi = "==0.17.2"
parso = "==0.7.1"
pytest-cov = "*"
