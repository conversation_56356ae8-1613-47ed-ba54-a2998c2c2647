version: '3'

volumes:
  redisdata:

services:
  saas:
    image: eu.gcr.io/adtuo-cloud/saas-prod:${VERSION}
    build:
      context: ../../
      dockerfile: ./deployment_files/prod/Dockerfile
    depends_on:
      - redis
      - cloudsql
    command: ./deployment_files/prod/start.sh
    ports:
      - "8000:8000"
    restart: always
    ulimits:
      nofile:
        hard: 4096
        soft: 4096
    sysctls:
      net.core.somaxconn: 4096
    networks:
      - saas-net

  cloudsql:
    restart: always
    image: gcr.io/cloudsql-docker/gce-proxy:1.21.0
    volumes:
      - ./credentials.json:/config/credentials.json
    ports:
      - "5432:5432"
    command: /cloud_sql_proxy -dir=/cloudsql -instances=adtuo-cloud:europe-west1:saas-prod=tcp:0.0.0.0:5432 -credential_file=/config/credentials.json
    networks:
      - saas-net

  redis:
    restart: always
    image: redis:latest
    volumes:
      - redisdata:/data
    networks:
      - saas-net
      - default

networks:
  saas-net:
    driver: bridge
