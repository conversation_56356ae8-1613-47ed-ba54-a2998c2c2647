from primes import is_prime, BinaryTree


class TestPrimeNumbers:
    def test_is_prime(self):
        assert is_prime(5)

    def test_is_no_prime(self):
        assert not is_prime(9)


class TestBinaryTree:
    def test_empty_tree(self):
        my_tree = BinaryTree()

        assert my_tree.is_empty

    def test_no_empty_tree(self):
        my_tree = BinaryTree()
        my_tree = my_tree.add_node(BinaryTree(node=5), 3, BinaryTree(node=4))

        assert not my_tree.is_empty

    def test_inorder(self):
        my_tree = BinaryTree()
        left_child = BinaryTree(BinaryTree(node=4), 2, None)
        right_child = BinaryTree(None, 3, BinaryTree(node=5))

        my_tree = my_tree.add_node(left_child, 1, right_child)

        assert my_tree.in_order == [4, 2, 1, 3, 5]

    def test_preorder(self):
        my_tree = BinaryTree()
        left_child = BinaryTree(BinaryTree(node=4), 2, None)
        right_child = BinaryTree(None, 3, BinaryTree(node=5))

        my_tree = my_tree.add_node(left_child, 1, right_child)

        assert my_tree.pre_order == [1, 2, 4, 3, 5]

    def test_postorder(self):
        my_tree = BinaryTree()
        left_child = BinaryTree(BinaryTree(node=4), 2, None)
        right_child = BinaryTree(None, 3, BinaryTree(node=5))

        my_tree = my_tree.add_node(left_child, 1, right_child)

        assert my_tree.post_order == [4, 2, 5, 3, 1]

    def test_height(self):
        my_tree = BinaryTree()
        left_child = BinaryTree(BinaryTree(node=4), 2, None)
        right_child = BinaryTree(None, 3, BinaryTree(BinaryTree(node=2), node=5))

        my_tree = my_tree.add_node(left_child, 1, right_child)

        assert my_tree.height == 4

    def test_leafs(self):
        my_tree = BinaryTree()
        left_child = BinaryTree(BinaryTree(node=4), 2, None)
        right_child = BinaryTree(None, 3, BinaryTree(BinaryTree(node=2), node=5))

        my_tree = my_tree.add_node(left_child, 1, right_child)

        assert my_tree.leafs == [4, 2]
