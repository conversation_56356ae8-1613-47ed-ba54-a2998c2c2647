from django.contrib import admin, messages
from django.utils.translation import gettext_lazy as _

from core.admin_utils.utils import DjangoAdminActions
from moreapp.forms import MoreappEmailForm
from moreapp.models import MoreappEmail


@admin.register(MoreappEmail)
class MoreappEmailAdmin(DjangoAdminActions, admin.ModelAdmin):
    list_display = ("__str__", "service_type", "in_queue", "sent")
    form = MoreappEmailForm
    change_actions = [
        "send_email",
    ]
    raw_id_fields = ("annex",)
    ordering = ("-in_queue",)

    @admin.display(description=_("Service Type"))
    def service_type(self, moreapp_email):
        return moreapp_email.annex.displayed_type if hasattr(moreapp_email, "annex") else "-"

    @admin.action(description=_("Send email"))
    def send_email(self, request, obj):
        email_sent = obj.send_email()

        message, level = (
            _("Warehouse notification email could not be sent. Check logs for details"),
            messages.ERROR,
        )

        if email_sent:
            message, level = (
                _("Warehouse notification email sent successfully"),
                messages.SUCCESS,
            )

            obj.sent = True
            obj.save(update_fields=["sent"])

        self.message_user(request, message=message, level=level)
