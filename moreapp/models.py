import base64
import logging
import uuid

from django.db import models
from django.utils import timezone
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _

from core.emails.message import EmailMessage
from intranet.models import WarehouseUserConfiguration
from moreapp.parsers.moreapp import MoreappDocumentParser
from moreapp.parsers.ridersapp import RidersDocumentParser

logger = logging.getLogger(__name__)


class MoreappEmail(models.Model):
    """Model to save the needed information to emulate the warehouse email integration"""

    MOREAPP = "moreapp"
    RIDERSAPP = "ridersapp"

    SOURCE_CHOICES = (
        (MOREAPP, _("Moreapp")),
        (RIDERSAPP, _("Ridersapp")),
    )

    PARSERS = {
        RIDERSAPP: RidersDocumentParser(),
        MOREAPP: MoreappDocumentParser(),
    }

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Internal metadata
    sent = models.BooleanField(default=False, help_text=_("If this file has been sent"))
    in_queue = models.DateTimeField(default=timezone.now, help_text=_("Date where document was sent to the queue"))
    source = models.CharField(max_length=100, choices=SOURCE_CHOICES, default=MOREAPP, null=True, blank=True)

    # Foreign keys
    annex = models.OneToOneField("contracts.Annex", null=True, blank=False, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.annex)

    def build_document(self):
        document_parser = self.PARSERS[self.source]
        return document_parser.build(self.annex)

    def send_email(self):
        """Populate the template and send the email"""
        if (
            not self.annex
            or (self.annex and not self.annex.contract)
            or (self.annex and not self.annex.warehouse)
            or (self.annex and self.annex.warehouse and not self.annex.warehouse.city)
            or (self.annex and self.annex.contract and not self.annex.contract.user)
        ):
            logger.error(f"Precondition failed to send inventory email {self.pk}")
            return False

        if self.sent:
            logger.warning(f"Moreapp inventory email already sent {self.pk}")
            return False

        sender_email = list(
            WarehouseUserConfiguration.objects.select_related("user")
            .filter(should_be_notified=True, warehouse=self.annex.warehouse)
            .values_list("user__email", flat=True)
        )
        if not sender_email:
            logger.warning(
                "Moreapp inventory email skipped temporarily because warehouse does not have users to be sent:"
                f" {self.pk}"
            )
            return False

        raw_response = self.annex.raw_data
        date = (raw_response.get("data", {}) or {}).get("date") or ""
        excel_document = self.build_document()

        attachments = (
            [{
                "content_type": "application/vnd.ms-excel",
                "filename": "warehouse_movement.xlsx",
                "encoded_file": base64.b64encode(excel_document.getvalue()).decode("utf-8"),
            }]
            if excel_document
            else None
        )

        # Activate locale from user
        activate(self.annex.contract.user.email_domain)
        message = EmailMessage(
            subject=_(
                "Customer notification {contract_id} BOX2BOX SL {base} - SERVICE {service} - MOVEMENT {date}"
            ).format(
                contract_id=self.annex.contract_document_id,
                base=self.annex.city.name,
                service=self.annex.displayed_type,
                date=date,
            ),
            to=sender_email,
            sender=self.annex.contract.user.email_domain,
            template="inventory_email",
            bcc=[
                "<EMAIL>",
            ],
            context=None,
            attachments=attachments,
        )

        # Send email
        return message.send() > 0
