from contracts.models import Service
from core.utils import sanitize
from moreapp.parsers import BaseDocumentParser, DocumentParserMixin


class RidersDocumentParser(BaseDocumentParser, DocumentParserMixin):
    INVENTORY_COLUMN_INDEX = {
        Service.INITIAL_PICKUP: 3,
        Service.EXTRA_PICKUP: 3,
        Service.PARTIAL_DELIVERY: 5,
        Service.FINAL_DELIVERY: 5,
    }

    def get_inventory_column(self, service_type):
        return self.INVENTORY_COLUMN_INDEX[service_type]

    def get_inventory_data(self, template_metadata, data, date, column_index, odoo_number, contract_id):
        inventory_data = []
        for index, item in enumerate(data):
            code = item.get("tag_id", None)
            description = item.get("description", None)

            inventory_data.extend([
                [template_metadata.first_row + index, 2, date],
                [template_metadata.first_row + index, column_index, sanitize(code)],
                [template_metadata.first_row + index, column_index + 1, description],
                [template_metadata.first_row + index, 7, odoo_number],
                [template_metadata.first_row + index, 8, contract_id],
            ])

        return inventory_data

    def parse_data(self, template_metadata, annex):
        def get_client_info(data):
            """Returns the dictionary 'key: [cell_position, value]' of the client info"""
            odoo_id = data.get("odoo_number")
            contract_id = data.get("contract_id")

            service_data = data.get("data", {}) or {}
            first_name = service_data.get("first_name")
            last_name = service_data.get("last_name")
            national_id = service_data.get("national_id")
            address = service_data.get("address")

            return [
                [13, 4, contract_id],
                [14, 4, odoo_id],
                [16, 4, first_name],
                [17, 4, last_name],
                [18, 4, national_id],
                [19, 4, address],
            ]

        def get_additional_services_info(data):
            """Returns the dictionary 'key: [cell_position, value]' of the additional services info"""
            # Additional services info data
            service_type = data.get("type", None)

            service_data = data.get("data", {}) or {}
            warehouse_space = service_data.get("warehouse_space", 0)
            furniture_assembly_options = service_data.get("furniture_assembly_options", None)
            furniture_assembly = service_data.get("furniture_assembly", None)
            packaging_options = service_data.get("packaging_box_normal_options", None) or service_data.get(
                "packaging_box_closet_options", None
            )
            furniture_assembly_units_more_than_upper_case = service_data.get("furniture_assembly_extra", None)
            amount_box_normal_packaging = service_data.get("amount_box_normal_packaging", 0)
            amount_box_closet_packaging = service_data.get("amount_box_closet_packaging", 0)
            stairs_options = service_data.get("stairs_options", None)
            stairs_floor = service_data.get("stairs_floor", 0)
            stairs_volume = service_data.get("stairs_volume", 0)
            parking = service_data.get("parking", None)
            clean_space_options = service_data.get("clean_space_options", None)
            clean_space = service_data.get("clean_space", 0)
            kilometers_options = service_data.get("kilometers_options", None)
            kilometers = service_data.get("kilometers", 0)
            additional_stop = service_data.get("additional_stop", None)
            additional_stop_comments = service_data.get("additional_stop_comments", "Empty")
            comment = service_data.get("comment", "Empty")

            # Set furniture assembly accordingly
            furniture_assembly = self.set_assembly_in_correct_units(
                furniture_assembly, furniture_assembly_units_more_than_upper_case
            )

            return [
                [23, 4, service_type],
                [self.SPACE_CELL.get(service_type) or 24, 4, warehouse_space],
                [28, 4, furniture_assembly_options],
                [29, 4, furniture_assembly],
                [31, 4, packaging_options],
                [32, 4, amount_box_normal_packaging],
                [33, 4, amount_box_closet_packaging],
                [35, 4, stairs_options],
                [36, 4, stairs_floor],
                [37, 4, stairs_volume],
                [39, 4, parking],
                [41, 4, clean_space_options],
                [42, 4, clean_space],
                [44, 4, kilometers_options],
                [45, 4, kilometers],
                [47, 4, additional_stop],
                [48, 4, additional_stop_comments or "-"],
                [51, 4, comment or "-"],
            ]

        # Parse partials
        title_info = self.get_title_info(annex.city)
        raw_data = annex.raw_data
        client_info = get_client_info(raw_data)
        additional_services = get_additional_services_info(raw_data)

        data = raw_data.get("data", {}) or {}
        date = data.get("date")
        column_index = self.get_inventory_column(raw_data.get("type"))
        odoo_number = raw_data.get("odoo_number")
        contract_id = raw_data.get("contract_id")
        inventory_data = self.get_inventory_data(
            template_metadata,
            (data.get("added_items", []) or []),
            date,
            column_index,
            odoo_number,
            contract_id,
        )

        return title_info, client_info, additional_services, inventory_data
