from core.utils import sanitize
from moreapp.parsers import BaseDocumentParser, DocumentParserMixin


class MoreappDocumentParser(BaseDocumentParser, DocumentParserMixin):
    def get_inventory_data(self, template_metadata, data, date, column_index, odoo_number, contract_id):
        inventory_data = []
        for index, item in enumerate(data):
            code = (
                item.get("barcode_pickup", None)
                or (item.get("barcode_auto", {}) or {}).get("objectCode", None)
                or item.get("barcode_manual", None)
            )
            description = (
                item.get("description_pickup", None)
                or (item.get("barcode_auto", {}) or {}).get("objectDescription", None)
                or item.get("description_delivery", None)
            )

            inventory_data.extend([
                [template_metadata.first_row + index, 2, date],
                [template_metadata.first_row + index, column_index, sanitize(code)],
                [template_metadata.first_row + index, column_index + 1, description],
                [template_metadata.first_row + index, 7, odoo_number],
                [template_metadata.first_row + index, 8, contract_id],
            ])

        return inventory_data

    def parse_data(self, template_metadata, annex):
        def get_client_info(data):
            """Returns the dictionary 'key: [cell_position, value]' of the client info"""
            odoo_id = data.get("odoo_number")
            contract_id = data.get("contract_id")
            first_name = data.get("first_name")
            last_name = data.get("last_name")
            national_id = data.get("dni")
            address = data.get("address")

            return [
                [13, 4, contract_id],
                [14, 4, odoo_id],
                [16, 4, first_name],
                [17, 4, last_name],
                [18, 4, national_id],
                [19, 4, address],
            ]

        def get_additional_services_info(data):
            """Returns the dictionary 'key: [cell_position, value]' of the additional services info"""

            # Additional services info data
            service_type = data.get("service_type", None)
            warehouse_space = data.get("warehouse_space", 0)
            furniture_assembly_options = data.get("furniture_assembly_options", None)
            furniture_assembly = data.get("furniture_assembly", None)
            packaging_options = data.get("packaging_box_normal_options", None) or data.get(
                "packaging_box_closet_options", None
            )
            furniture_assembly_units_more_than_upper_case = data.get("furniture_assembly_extra", None)
            amount_box_normal_packaging = data.get("amount_box_normal_packaging", 0)
            amount_box_closet_packaging = data.get("amount_box_closet_packaging", 0)
            stairs_options = data.get("stairs_options", None)
            stairs_floor = data.get("stairs_floor", 0)
            stairs_volume = data.get("stairs_volume", 0)
            parking = data.get("parking", None)
            clean_space_options = data.get("clean_space_options", None)
            clean_space = data.get("clean_space", 0)
            kilometers_options = data.get("kilometers_options", None)
            kilometers = data.get("kilometers", 0)
            additional_stop = data.get("additional_stop", None)
            additional_stop_comments = data.get("additional_stop_comments", "Empty")
            comment = data.get("comment", "Empty")

            # Set furniture assembly accordingly
            furniture_assembly = self.set_assembly_in_correct_units(
                furniture_assembly, furniture_assembly_units_more_than_upper_case
            )

            return [
                [23, 4, service_type],
                [self.SPACE_CELL.get(service_type) or 24, 4, warehouse_space],
                [28, 4, furniture_assembly_options],
                [29, 4, furniture_assembly],
                [31, 4, packaging_options],
                [32, 4, amount_box_normal_packaging],
                [33, 4, amount_box_closet_packaging],
                [35, 4, stairs_options],
                [36, 4, stairs_floor],
                [37, 4, stairs_volume],
                [39, 4, parking],
                [41, 4, clean_space_options],
                [42, 4, clean_space],
                [44, 4, kilometers_options],
                [45, 4, kilometers],
                [47, 4, additional_stop],
                [48, 4, additional_stop_comments or "-"],
                [51, 4, comment or "-"],
            ]

        # Parse partials
        title_info = self.get_title_info(annex.city)
        raw_data = annex.raw_data.get("data", {}) or {}
        client_info = get_client_info(raw_data)
        additional_services = get_additional_services_info(raw_data)

        date = raw_data.get("date")
        column_index = 3 if raw_data.get("inventory_pickup") else 5
        odoo_number = raw_data.get("odoo_number")
        contract_id = raw_data.get("contract_id")
        inventory_data = self.get_inventory_data(
            template_metadata,
            (raw_data.get("inventory_delivery", {}) or {}) or (raw_data.get("inventory_pickup", {}) or {}),
            date,
            column_index,
            odoo_number,
            contract_id,
        )

        return title_info, client_info, additional_services, inventory_data
