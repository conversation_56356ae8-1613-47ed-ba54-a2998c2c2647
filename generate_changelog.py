import subprocess
import sys
from datetime import datetime

if sys.version_info.major < 3:
    input_func = raw_input  # NOQA: F821
else:
    input_func = input


def git_tag():
    options = {
        "j": "MAJOR",
        "n": "MINOR",
        "p": "PATCH",
    }

    first_log = False

    print("What kind of version is this?")  # NOQA: T201
    for key, value in options.items():
        print("{}: {}".format(key, value))  # NOQA: T201

    # Take user input and get the corresponding item from the list
    inp = input_func("Enter a letter: ")
    if inp not in options.keys():
        print("Invalid input!")  # NOQA: T201
        return

    try:
        last_tag = subprocess.check_output(
            "git describe --tags $(git rev-list --tags --max-count=1)", shell=True
        )
        last_tag = last_tag.strip().decode("utf-8")
    except subprocess.CalledProcessError:
        last_tag = "v0.0.0"  # This means first tag
        inp = "j"
        first_log = True

    major_v = int(last_tag.split(".")[0][1:])
    minor_v = int(last_tag.split(".")[1])
    patch_v = int(last_tag.split(".")[2])

    if inp == "j":
        major_v += 1
        minor_v = 0
        patch_v = 0

    if inp == "n":
        minor_v += 1
        patch_v = 0

    if inp == "p":
        patch_v += 1

    new_tag = "v{major}.{minor}.{patch}".format(
        major=major_v, minor=minor_v, patch=patch_v
    )
    update_version("./network_clients/__init__.py", new_tag)

    write_log(last_tag, new_tag, first_log=first_log)
    commit_and_push(new_tag)


def update_version(file_path, new_version):
    # Read the file content
    try:
        with open(file_path, "r") as file:
            lines = file.readlines()
    except FileNotFoundError:
        print(f"The file {file_path} does not exist.")  # NOQA: T201
        return

    # Update the version line
    for i in range(len(lines)):
        if lines[i].strip().startswith("__version__"):
            lines[i] = f'__version__ = "{new_version}"\n'
            break

    # Write the updated content back to the file
    with open(file_path, "w") as file:
        file.writelines(lines)


def write_log(last_tag, new_tag, first_log=False):
    if first_log:
        messages = subprocess.check_output(["git", "log", "HEAD", "--format=%B"])
    else:
        messages = subprocess.check_output(
            ["git", "log", "{}..HEAD".format(last_tag), "--format=%B"]
        )

    inp = input_func("Enter a changelog message: ")

    changelog = open("CHANGELOG.md", "a")
    today = datetime.now()
    today = today.strftime("%d-%m-%Y")

    changelog.write("\n")
    changelog.write("## [{}] - {}".format(new_tag, today))
    changelog.write("\n")
    changelog.write("### {}".format(inp))
    changelog.write("\n")

    for message in messages.splitlines():
        message = message.decode("utf-8")
        if message and not message.startswith(("WIP", "Merge", "Update changelog")):
            changelog.write("- {}".format(message))
            changelog.write("\n")

    changelog.close()


def commit_and_push(new_tag):
    input_func("Please finish editing CHANGELOG.md and press any key: ")

    subprocess.check_output(["git", "add", "."])
    subprocess.check_output(["git", "commit", "-m", "Update changelog", "--no-verify"])

    subprocess.check_output(
        ["git", "tag", "-a", new_tag, "-m", "Release {}".format(new_tag)]
    )
    subprocess.check_output(["git", "push", "origin", "--tags"])


def main():
    branch = subprocess.check_output(["git", "rev-parse", "--abbrev-ref", "HEAD"])
    branch = branch.strip().decode("utf-8")

    if branch != "master":
        print("You must be working on master to generate change log")  # NOQA: T201
        return

    git_tag()


if __name__ == "__main__":
    main()
