import sentry_sdk

from .common import *  # NOQA: F401, F403, F405

# Environment
ENVIRONMENT = "STAGING"

# Static url for serving static files
STATIC_URL = "/static/"

# Adtuo-django-email topic for staging
PUBSUB_EMAIL_TOPIC = "adtuo-testemails-cio"

# Remove email subject prefix
ACCOUNT_EMAIL_SUBJECT_PREFIX = "[TEST] "

# Websocket microservice url
HIPERION_WORKERS_MICROSERVICE_URL = "https://devhiperionworker.adtuo.com/"
HIPERION_API_MICROSERVICE_URL = "https://devhiperionone.adtuo.com/"
WEBSOCKETS_MICROSERVICE_URL = "https://devws.adtuo.com/"
SOCIAL_MICROSERVICE_URL = "https://devsocial.adtuo.com/"
NOTIFICATION_MICROSERVICE_URL = "https://devnotification.adtuo.com/"
RECOMMENDATIONS_MICROSERVICE_URL = "https://devrecommendation.adtuo.com/"

# Webapp url
WEBAPP_URL = "https://testapp.adtuo.com/"

# Set sentry config for staging environment
sentry_sdk.init(**SENTRY_CONFIGURATION, environment=ENVIRONMENT)  # NOQA: F405 # Will be staging

# Debounce api key to email validation
DEBOUNCE_API_KEY = env("DEBOUNCE_API_KEY")  # NOQA: F405

# Slack callback url
SLACK_CALLBACK_URL = "https://testapp.adtuo.com/oauth/slack"
