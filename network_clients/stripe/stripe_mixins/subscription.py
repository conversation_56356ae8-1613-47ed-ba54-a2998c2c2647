import uuid


class SubscriptionMixin:
    def create_subscription(
        self,
        customer_id,
        one_price_id_items,
        recurring_price_id_items,
        *,
        cancel_at_period_end=False,
        tax=None,
        coupon=None,
        metadata=None,
        trial_period_days=None,
    ):
        """Create a subscription in Stripe monthly renewable"""

        subscription_keys = {
            "customer": customer_id,
            "items": recurring_price_id_items,
            "add_invoice_items": one_price_id_items,
            "cancel_at_period_end": cancel_at_period_end,
            "proration_behavior": "none",
            "off_session": True,
            "expand": ["latest_invoice.payment_intent", "latest_invoice"],
        }

        if tax:
            subscription_keys["default_tax_rates"] = [tax]

        if coupon:
            subscription_keys["coupon"] = coupon

        if metadata:
            subscription_keys["metadata"] = metadata

        if trial_period_days:
            subscription_keys["trial_period_days"] = trial_period_days

        return self.stripe_sdk.Subscription.create(
            idempotency_key=uuid.uuid4().hex, **subscription_keys
        )

    def get_subscription(self, subscription_id):
        """Retrieve subscription data from Stripe"""
        return self.stripe_sdk.Subscription.retrieve(subscription_id)

    def apply_coupon(self, subscription_id, coupon_id):
        """Apply a coupon for one subscription"""

        return self.stripe_sdk.Subscription.modify(
            subscription_id, idempotency_key=uuid.uuid4().hex, coupon=coupon_id
        )

    def update_subscription(
        self,
        subscription_id,
        price_id_items=None,
        billing_cycle_anchor=None,
        proration_behavior=None,
        **updatable_keys,
    ):
        """Update certain data in stripe subscription"""

        if price_id_items:
            updatable_keys.update(**{"items": price_id_items})

        if billing_cycle_anchor:
            updatable_keys.update(**{"billing_cycle_anchor": billing_cycle_anchor})

        if proration_behavior:
            updatable_keys.update(**{"proration_behavior": proration_behavior})

        return self.stripe_sdk.Subscription.modify(
            subscription_id, idempotency_key=uuid.uuid4().hex, **updatable_keys
        )

    def cancel_subscription(self, subscription_id, now=False):
        """Cancel the subscription now or at period end"""
        if now:
            return self.stripe_sdk.Subscription.delete(subscription_id)
        return self.stripe_sdk.Subscription.modify(
            subscription_id, cancel_at_period_end=True
        )
