import uuid


class TaxesMixin:
    def get_customer_taxes(self, customer_id, only_first=False):
        """Retrieve the list of cutomer taxes linked to customer id"""

        # First, we get every taxes linked to customer
        customer_taxes = self.stripe_sdk.Customer.list_tax_ids(customer_id)["data"]

        try:
            # At this point, we only should have one tax linked to this customer at most
            customer_tax = customer_taxes[0]
        except (IndexError, TypeError):
            customer_tax = None

        return customer_tax if only_first else customer_taxes

    def update_customer_tax(self, customer_id, tax_type, tax_value):
        """Replace old tax_id and create a new one
        @see https://stripe.com/docs/billing/customer/tax-ids
        """

        customer_tax = self.get_customer_taxes(customer_id, only_first=True)

        # Avoid duplicating the tax
        if (
            customer_tax
            and customer_tax.get("type") == tax_type
            and customer_tax.get("value") == tax_value
        ):
            return

        # Create the new tax
        self.stripe_sdk.Customer.create_tax_id(
            customer_id,
            type=tax_type,
            value=tax_value,
            idempotency_key=uuid.uuid4().hex,
        )

        if customer_tax:
            # If everything was well... delete the old tax
            self.stripe_sdk.Customer.delete_tax_id(customer_id, customer_tax["id"])

    def delete_customer_tax(self, customer_id):
        """Delete the customer tax from billing configuration of customer, when replacing the country from Spain to
        another one"""

        customer_tax = self.get_customer_taxes(customer_id, only_first=True)

        if customer_tax:
            # If everything was well... delete the old tax
            self.stripe_sdk.Customer.delete_tax_id(customer_id, customer_tax["id"])
