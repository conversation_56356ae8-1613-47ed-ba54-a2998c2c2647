import uuid


class CouponMixin:
    def create_coupon(
        self,
        id,
        amount_off,
        percent_off,
        currency="eur",
        duration="once",
        name=None,
        **kwargs
    ):
        """Create a Stripe coupon"""

        coupon_keys = {"duration": duration, **kwargs}

        if id:
            coupon_keys["id"] = id

        if name:
            coupon_keys["name"] = name

        if percent_off:
            coupon_keys["percent_off"] = percent_off

        if amount_off:
            coupon_keys.update(
                **{"amount_off": amount_off, "currency": currency, "percent_off": None}
            )

        return self.stripe_sdk.Coupon.create(
            idempotency_key=uuid.uuid4().hex, **coupon_keys
        )

    def retrieve_coupon(self, coupon_id):
        """Retrieve coupon data from Stripe"""
        return self.stripe_sdk.Coupon.retrieve(coupon_id)
