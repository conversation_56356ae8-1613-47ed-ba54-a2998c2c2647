from datetime import datetime

from network_clients.odoo.constants import DEFAULT_EMPTY_SINGLE_RESPONSE


class CustomerMixin:
    model = "res.partner"

    def retrieve_customer_by_id(self, customer_id: int, *args, **kwargs):
        """Get a customer by internal id ('numero')"""

        customer = self.execute_query(
            CustomerMixin.model,
            "search_read",
            [[("x_studio_numero", "=", customer_id)]],
            limit=1,
            *args,
            **kwargs
        )

        if customer:
            return customer[0]

        return DEFAULT_EMPTY_SINGLE_RESPONSE

    def retrieve_customers_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime = None,
        limit: int = None,
        *args,
        **kwargs
    ):
        """Get a list of customers by date_range projecting only the needed fields"""

        query_search = [("create_date", ">=", start_date.strftime("%Y-%m-%d %H:%M:%S"))]

        if end_date is not None:
            query_search.append(
                ("create_date", "<=", end_date.strftime("%Y-%m-%d %H:%M:%S"))
            )

        return self.execute_query(
            CustomerMixin.model,
            "search_read",
            [query_search],
            limit=limit,
            *args,
            **kwargs
        )
