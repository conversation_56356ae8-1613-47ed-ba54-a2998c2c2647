import datetime
import json
from typing import Dict, List

from network_clients.base.base import Client<PERSON>rror
from network_clients.base.encoders import J<PERSON>NEncoder


class InvoiceMixin:
    url = "https://api.moloni.pt/v1/invoices/{query}/?access_token={access_token}&json=true"

    def create_invoice(
        self,
        company_id: int,
        date: datetime.date,
        expiration_date: datetime.date,
        document_set_id: int,
        customer_id: int,
        products: List[Dict],
        **kwargs,
    ):
        """Create an invoice in moloni"""

        url = InvoiceMixin.url.format(query="insert", access_token=self.token)
        payload = {
            "company_id": company_id,
            "date": date,
            "expiration_date": expiration_date,
            "document_set_id": document_set_id,
            "customer_id": customer_id,
            "products": products,
            **kwargs,
        }

        response = self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

        try:
            return response.get("document_id")
        except AttributeError:
            raise ClientError(f"Error creating the invoice in Moloni: {response}")

    def get_invoices(self, company_id: int, **filters):
        """Get all invoices according to filters from moloni"""

        url = InvoiceMixin.url.format(query="getAll", access_token=self.token)

        def _get_invoices(company_id, url, offset=None, **filters):
            payload = {"company_id": company_id, "offset": offset, **filters}

            response = self.request(
                endpoint=url,
                method="POST",
                data=json.dumps(payload, cls=JSONEncoder),
                headers={"Content-Type": "application/json"},
            ).json()

            yield from response
            if response:
                yield from _get_invoices(company_id, url, offset + 50, **filters)

        return _get_invoices(company_id, url, offset=0, **filters)

    def get_invoice(self, company_id: int, **filters):
        """Get one invoice from moloni"""

        url = InvoiceMixin.url.format(query="getOne", access_token=self.token)
        payload = {"company_id": company_id, **filters}

        return self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

    def update_invoice(self, company_id: int, document_id: int, **extra_fields):
        """Update an invoice in moloni"""

        url = InvoiceMixin.url.format(query="update", access_token=self.token)
        payload = {"company_id": company_id, "document_id": document_id, **extra_fields}

        return self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()
