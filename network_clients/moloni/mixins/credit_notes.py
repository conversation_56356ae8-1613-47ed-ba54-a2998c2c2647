import json
from datetime import datetime
from typing import Dict, List

from network_clients.base.base import ClientError
from network_clients.base.encoders import JSONEncoder


class CreditNotesMixin:
    url = "https://api.moloni.pt/v1/debitNotes/{query}/?access_token={access_token}&json=true"

    def create_credit_note(
        self,
        company_id: int,
        date: datetime.date,
        expiration_date: datetime.date,
        document_set_id: int,
        customer_id: int,
        associated_documents: List[Dict],
        products: List[Dict],
        **kwargs,
    ):
        """Create a credit note as debit note in moloni"""

        url = CreditNotesMixin.url.format(query="insert", access_token=self.token)
        payload = {
            "company_id": company_id,
            "date": date,
            "expiration_date": expiration_date,
            "document_set_id": document_set_id,
            "customer_id": customer_id,
            "associated_documents": associated_documents,
            "products": products,
            **kwargs,
        }

        response = self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

        try:
            return response.get("document_id")
        except AttributeError:
            raise ClientError(f"Error creating the credit note in Moloni: {response}")

    def get_credit_notes(self, company_id: int, **filters):
        """Get all credit notes according to filters from moloni"""

        url = CreditNotesMixin.url.format(query="getAll", access_token=self.token)

        def _get_credit_notes(company_id, url, offset=None, **filters):
            payload = {"company_id": company_id, "offset": offset, **filters}

            response = self.request(
                endpoint=url,
                method="POST",
                data=json.dumps(payload, cls=JSONEncoder),
                headers={"Content-Type": "application/json"},
            ).json()

            yield from response
            if response:
                yield from _get_credit_notes(company_id, url, offset + 50, **filters)

        return _get_credit_notes(company_id, url, offset=0, **filters)

    def update_credit_note(self, company_id: int, document_id: int, **extra_fields):
        """Update a credit note in moloni"""

        url = CreditNotesMixin.url.format(query="update", access_token=self.token)
        payload = {"company_id": company_id, "document_id": document_id, **extra_fields}

        return self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()
