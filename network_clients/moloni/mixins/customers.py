import json

from network_clients.base.base import ClientError
from network_clients.base.encoders import JSONEncoder


class CustomerMixin:
    url = "https://api.moloni.pt/v1/customers/{query}/?access_token={access_token}&json=true"

    def retrieve_next_free_number(self, company_id: int):
        """Retrieve the next customer code available"""

        url = CustomerMixin.url.format(query="getNextNumber", access_token=self.token)
        payload = {"company_id": company_id}

        return self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

    def retrieve_customer_by_id(self, company_id: int, customer_id: int):
        """Retrieve a customer"""

        url = CustomerMixin.url.format(query="getOne", access_token=self.token)
        payload = {"company_id": company_id, "customer_id": customer_id}

        return self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

    def retrieve_customer_by_number(
        self, company_id: int, number: int, qty: int = None, offset: int = None
    ):
        """Retrieve a customer"""

        url = CustomerMixin.url.format(query="getByNumber", access_token=self.token)
        payload = {"company_id": company_id, "number": number}

        if qty:
            payload["qty"] = min(qty, 50)

        if offset:
            payload["offset"] = offset

        return self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

    def create_customer(
        self,
        company_id: int,
        vat: str,
        number: str,
        name: str,
        language_id: int,
        address: str,
        city: str,
        country_id: int = 1,
        salesman_id: int = 0,
        maturity_date_id: int = 0,
        payment_day: int = 0,
        discount: float = 0,
        credit_limit: float = 0,
        payment_method_id: int = 0,
        delivery_method_id: int = 0,
        zip_code: str = None,
    ):
        """Create a customer"""

        url = CustomerMixin.url.format(query="insert", access_token=self.token)
        payload = {
            "company_id": company_id,
            "vat": vat,
            "number": number,
            "name": name,
            "language_id": language_id,
            "zip_code": zip_code,
            "city": city,
            "country_id": country_id,
            "salesman_id": salesman_id,
            "maturity_date_id": maturity_date_id,
            "payment_day": payment_day,
            "discount": discount,
            "credit_limit": credit_limit,
            "payment_method_id": payment_method_id,
            "delivery_method_id": delivery_method_id,
            "address": address,
        }

        response = self.request(
            endpoint=url,
            method="POST",
            data=json.dumps(payload, cls=JSONEncoder),
            headers={"Content-Type": "application/json"},
        ).json()

        try:
            return response.get("customer_id")
        except AttributeError:
            raise ClientError(f"Error creating the customer in Moloni: {response}")
