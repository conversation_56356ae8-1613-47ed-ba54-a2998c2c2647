"""Handle termination signals in order to exit the block code gracefully"""
import logging
import signal

logger = logging.getLogger(__name__)


class AtomicBlock:
    signal_handled = [signal.SIGINT, signal.SIGTERM]
    handlers = {}
    deferred = []

    def _signal_handler(self, signum, frame):
        """Signal handler to propagate the exception to parent process allowing gracefully shutdown"""
        logger.warning(
            f"Signal {signal.Signals(signum).name} received. Starting grateful shutdown..."
        )
        self.deferred.append((signum, frame))

    def __enter__(self):
        """Defer signal handling to wait until code block ends"""
        for signum in self.signal_handled:
            self.handlers[signum] = signal.signal(signum, self._signal_handler)

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Restore signals and propagate them to parent"""
        for signal_num, handler in self.handlers.items():
            signal.signal(signal_num, handler)

        for deferred_signal in self.deferred:
            signal.default_int_handler(*deferred_signal)


def atomic_block():
    """Dummy method to export AtomicBlock class to be compliant with PEP8"""
    return AtomicBlock()
