from network_clients.base.http_client import HTTPClient


class SlackClient(HTTPClient):
    hook_url = "*********************************************************************************"

    def notify_lead(
        self, odoo_url, user_name, device, country, city, submit_date, email, phone
    ):
        """Send to slack channel new leads notification"""

        slack_message = {
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"Nuevo lead:\n*<{odoo_url}|{user_name}>*",
                    },
                },
                {
                    "type": "section",
                    "fields": [
                        {"type": "mrkdwn", "text": f"*Dispositivo:*\n{device}"},
                        {"type": "mrkdwn", "text": f"*Fecha:*\n{submit_date}"},
                        {"type": "mrkdwn", "text": f"*País:*\n{country}"},
                        {"type": "mrkdwn", "text": f"*Ciudad:*\n{city}"},
                        {"type": "mrkdwn", "text": f"*Correo electrónico:*\n{email}"},
                        {"type": "mrkdwn", "text": f"*Teléfono:*\n{phone}"},
                    ],
                },
            ]
        }

        return self.request(
            endpoint=self.hook_url,
            method="POST",
            json=slack_message,
            headers={"Content-Type": "application/json"},
        )

    def notify_pickup_or_deliver_order(
        self, contract_id, date, address, user, order_type, email
    ):
        """Send to slack channel new order notification"""

        slack_message = {
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"Hola equipo :wave:! Hay una nueva solicitud de *{order_type}*\n\n *Datos de la solicitud:*",
                    },
                },
                {"type": "divider"},
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f">>>*Contrato {contract_id}*\n:man_and_woman_holding_hands: {user}\n:incoming_envelope: {email}\n:calendar: {date}\n:truck: {address}",
                        }
                    ],
                },
            ]
        }

        return self.request(
            endpoint=self.hook_url,
            method="POST",
            json=slack_message,
            headers={"Content-Type": "application/json"},
        )
