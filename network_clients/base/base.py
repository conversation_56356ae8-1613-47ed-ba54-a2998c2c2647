class ClientError(Exception):
    """Base common client error to throw for every client when sync/async call over the network is made"""

    def __init__(self, response=None):
        self.response = response

    def __str__(self):
        return "{self.response}".format(self=self)


class BaseClient:
    def __init__(self, *args, **kwargs):
        self._client = None

    @property
    def client(self):
        """Initialize client if needed"""
        if self._client is None:
            self._client = self.lazy_init_client()
        return self._client

    def lazy_init_client(self):
        """Class to make a lazy initialization for clients if needed. This method must be override on custom implementations"""
        return object()


class ProxyRequestBaseClient(BaseClient):
    def request(self, *args, **kwargs):
        """Common method to be coded by every child class"""
        raise NotImplementedError("Not implemented yet!")
