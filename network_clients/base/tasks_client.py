import json
from datetime import datetime, timedelta
from urllib.parse import urlencode

from google.cloud import tasks_v2
from google.protobuf import duration_pb2, timestamp_pb2

from network_clients.base.base import ClientError, ProxyRequestBaseClient
from network_clients.base.encoders import JSONEncoder


class CloudTaskClient(ProxyRequestBaseClient):
    def __init__(
        self,
        *,
        service_account_json,
        google_cloud_project_name,
        google_cloud_project_location,
    ):
        super(CloudTaskClient, self).__init__(service_account_json)
        self._service_account_json = service_account_json
        self._google_cloud_project_name = google_cloud_project_name
        self._google_cloud_project_location = google_cloud_project_location

    def lazy_init_client(self):
        """Lazy init for clients, delaying it upto first time is used"""
        try:
            publisher = tasks_v2.CloudTasksClient.from_service_account_json(
                self._service_account_json
            )
        except (FileNotFoundError, TypeError):
            raise ClientError(
                f"Wrong `{self._service_account_json}` credentials to init the cloud tasks client"
            )
        return publisher

    def _build_task(
        self,
        url,
        http_method,
        payload,
        in_seconds,
        params,
        dispatch_deadline,
        headers=None,
    ):

        if params:
            url = f"{url}?{urlencode(params)}"

        task = {
            "http_request": {  # Specify the type of request.
                "http_method": http_method,
                "url": url,  # The full url path that the task will be sent to.
                "headers": {"Content-Type": "application/json"},
            }
        }

        if headers is not None:
            task["http_request"]["headers"].update(**headers)

        if payload is not None:
            # The API expects a payload of type bytes.
            converted_payload = json.dumps(payload, cls=JSONEncoder).encode()

            # Add the payload to the request.
            task["http_request"]["body"] = converted_payload

        if in_seconds is not None:
            # Convert "seconds from now" into an rfc3339 datetime string.
            d = datetime.utcnow() + timedelta(seconds=in_seconds)

            # Create Timestamp protobuf.
            timestamp = timestamp_pb2.Timestamp()
            timestamp.FromDatetime(d)

            # Add the timestamp to the tasks.
            task["schedule_time"] = timestamp

        if dispatch_deadline is not None:
            duration = duration_pb2.Duration()

            # Max time for workers to wait before retry
            task["dispatch_deadline"] = duration.FromSeconds(dispatch_deadline)

        return task

    def request(
        self,
        url,
        cloud_queue,
        *,
        method="POST",
        payload=None,
        in_seconds=None,
        params=None,
        headers=None,
        dispatch_deadline=None,
    ):
        """Make a request using cloud task."""

        # Construct the fully qualified queue name
        queue_path = self.client.queue_path(
            self._google_cloud_project_name,
            self._google_cloud_project_location,
            cloud_queue,
        )

        try:
            task = self._build_task(
                url, method, payload, in_seconds, params, dispatch_deadline, headers
            )

            # Build an send task
            response = self._client.create_task(parent=queue_path, task=task)
        except Exception as e:
            raise ClientError(f"Error creating task in cloud. Reason: {str(e)}")

        return response
