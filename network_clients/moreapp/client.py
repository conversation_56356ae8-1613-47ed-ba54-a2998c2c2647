from network_clients.base.http_client import HTTPClient
from network_clients.moreapp.download_mixin import DownloadMixin
from network_clients.moreapp.submission_mixin import SubmissionMixin


class MoreappClient(DownloadMixin, SubmissionMixin, HTTPClient):
    """HTTP client to wrap calls agains moreapp api"""

    def __init__(self, api_key):
        super(MoreappClient, self).__init__()
        self.api_key = api_key
