class InvoiceMixin:
    def retrieve_pdf_invoice(self, invoice_id, disposition_type: str = "attachment"):
        """Retrieve an invoice in pdf"""

        return self.chargebee_sdk.Invoice.pdf(
            invoice_id, {"disposition_type": disposition_type}, env=self.environment
        )

    def list_invoices_in_date_range(
        self, date_from: int, date_to: int, next_offset=None, **kwargs
    ):
        """List invoices from specific range of dates"""

        items = self.chargebee_sdk.Invoice.list(
            {
                "date[between]": [date_from, date_to],
                "limit": 100,
                "offset": next_offset,
            },
            env=self.environment,
        )

        yield from items
        next_offset = items.next_offset if hasattr(items, "next_offset") else None
        if next_offset:
            yield from self.list_invoices_in_date_range(
                date_from, date_to, next_offset=next_offset, **kwargs
            )

    def create_invoice_charge(self, customer_id, **kwargs):
        """Create an invoice for items or one time charges"""

        body = {
            "customer_id": customer_id,
        }

        if kwargs:
            body.update(**kwargs)

        return self.chargebee_sdk.Invoice.create_for_charge_items_and_charges(
            body, env=self.environment
        )

    def get_invoice(self, invoice_id):
        return self.chargebee_sdk.Invoice.retrieve(invoice_id, env=self.environment)
