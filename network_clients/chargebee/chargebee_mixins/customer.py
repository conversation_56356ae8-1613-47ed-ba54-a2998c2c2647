class CustomerMixin:
    def create_customer(
        self,
        user_email,
        user_first_name,
        user_last_name,
        *,
        preferred_currency_code="EUR",
        preferred_locale=None,
        address=None,
        postal_code=None,
        city=None,
        state=None,
        country=None,
        vat_number=None,
        **kwargs,
    ):
        """Create a customer on Chargebee. This method is intended to be used when an organization is created for first
        time"""
        customer_keys = {
            "first_name": user_first_name,
            "last_name": user_last_name,
            "email": user_email,
            "locale": preferred_locale,
            "vat_number": vat_number,
            "preferred_currency_code": preferred_currency_code,
            "billing_address": {
                "first_name": user_first_name,
                "last_name": user_last_name,
                "line1": address,
                "city": city,
                "state": state,
                "zip": postal_code,
                "country": country,
            },
        }

        # Add extra valid parameters to customer object
        if kwargs:
            customer_keys.update(**kwargs)

        return self.chargebee_sdk.Customer.create(customer_keys, env=self.environment)

    def update_customer(
        self,
        customer_id,
        **kwargs,
    ):
        """Update the customer in chargebee. Be careful passing user_email as well due to unexpected side effects with the flow"""

        if kwargs:
            return self.chargebee_sdk.Customer.update(
                customer_id, {**kwargs}, env=self.environment
            )
        return None

    def update_billing_data(
        self,
        customer_id,
        *,
        user_first_name,
        user_last_name,
        address=None,
        postal_code=None,
        city=None,
        state=None,
        country=None,
        vat_number=None,
        company=None,
    ):
        """Update customer billing info in chargebee"""

        customer_keys = {
            "vat_number": vat_number,
            "billing_address": {
                "first_name": user_first_name,
                "last_name": user_last_name,
                "line1": address,
                "city": city,
                "state": state,
                "zip": postal_code,
                "country": country,
                "company": company,
            },
        }

        return self.chargebee_sdk.Customer.update_billing_info(
            customer_id, customer_keys, env=self.environment
        )

    def assign_payment_role(self, customer_id, *, payment_source_id, role="primary"):

        """Update payment method role"""

        return self.chargebee_sdk.Customer.assign_payment_role(
            customer_id,
            {"payment_source_id": payment_source_id, "role": role},
            env=self.environment,
        )
