class ChargeMixin:
    def create_one_time_payment(
        self,
        customer_id,
        expires_at: int,
        *,
        contract_id: str = None,
        change_option: str = "immediately",
        charge_items: list,
        **kwargs
    ):
        """Create a quote in Chargebee. This will be transformed later into subscriptions"""

        quote_keys = {
            "customer_id": customer_id,
            "change_option": change_option,
            "expires_at": expires_at,
            "item_prices": charge_items,
            "po_number": contract_id,
            **kwargs,
        }

        return self.chargebee_sdk.Quote.create_for_charge_items_and_charges(
            quote_keys, env=self.environment
        )
