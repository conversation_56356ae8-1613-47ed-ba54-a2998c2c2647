class CommentMixin:
    def _create_comment(self, entity_id: str, entity_type: str, notes: str):
        """Main method to create a comment in chargebee object"""

        return self.chargebee_sdk.Comment.create(
            {"entity_id": entity_id, "entity_type": entity_type, "notes": notes},
            env=self.environment,
        )

    # Public API
    def create_comment_in_subscription(self, subscription_id, comment):
        return self._create_comment(
            entity_id=subscription_id, entity_type="subscription", notes=comment
        )

    def create_comment_in_quote(self, quote_id, comment):
        return self._create_comment(
            entity_id=quote_id, entity_type="quote", notes=comment
        )
